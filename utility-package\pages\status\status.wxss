/* 功能状态页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 状态卡片 */
.status-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.status-indicator.online .status-dot {
  background: #52c41a;
}

.status-indicator.offline .status-dot {
  background: #ff4d4f;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

.card-content {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  font-weight: 500;
}

.info-value.demo {
  color: #fa8c16;
}

.info-value.normal {
  color: #52c41a;
}

.info-value.ready {
  color: #52c41a;
}

.info-value.error {
  color: #ff4d4f;
}

/* 功能列表 */
.feature-list {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.list-header {
  margin-bottom: 30rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.list-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.feature-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.feature-details {
  flex: 1;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.feature-status {
  margin-left: 20rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.status-badge.normal {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.status-badge.demo {
  background: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffd591;
}

.status-badge.disabled {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1rpx solid #ffb3b3;
}

.badge-text {
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  margin-bottom: 20rpx;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  border: none;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.action-btn.primary {
  background: #ff6b35;
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #666;
  border: 1rpx solid #d9d9d9;
}

.action-btn::after {
  border: none;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 说明信息 */
.info-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.info-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 滚动区域 */
.scrollarea {
  height: 100vh;
}

/* 确保自定义导航栏页面的滚动区域有正确的顶部间距 */
.scrollarea.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 30rpx);
  box-sizing: border-box;
}
