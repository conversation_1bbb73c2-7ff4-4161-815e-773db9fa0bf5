// create.js
import API from '../../../utils/api.js';
import { SERVICE_TYPE_NAMES, SKILL_TYPE_NAMES, DEFAULT_CONFIG } from '../../../utils/constants.js';

const app = getApp();

Page({
  data: {
    editMode: false, // 是否为编辑模式
    editOrderId: '', // 编辑的订单ID
    formData: {
      title: '',
      content: '',
      reward: '',
      platformType: 'pc', // pc: 电脑, mobile: 手游
      serviceType: 'duration', // duration: 按时长, rounds: 按局数
      duration: '2',
      customDuration: '',
      rounds: '5',
      customRounds: '',
      selectedTags: [],
      orderType: 'immediate', // immediate: 立即订单, scheduled: 预约订单
      scheduledDate: '',
      scheduledTime: '',
      scheduledHour: 0, // 选择的小时
      scheduledMinute: 0 // 选择的分钟
    },
    // 时长选项
    durationOptions: [
      { label: '1小时', value: '1' },
      { label: '2小时', value: '2' },
      { label: '3小时', value: '3' },
      { label: '4小时', value: '4' },
      { label: '自定义', value: 'custom' }
    ],
    // 局数选项
    roundsOptions: [
      { label: '3局', value: '3' },
      { label: '5局', value: '5' },
      { label: '10局', value: '10' },
      { label: '15局', value: '15' },
      { label: '20局', value: '20' },
      { label: '自定义', value: 'custom' }
    ],
    // 订单标签（参考图片中的标签）
    orderTags: [
      { label: '高胜率', value: 'high_winrate' },
      { label: '可语音', value: 'voice_chat' },
      { label: '幽默风趣', value: 'humorous' },
      { label: '专业指导', value: 'professional' },
      { label: '新手友好', value: 'newbie_friendly' },
      { label: '急单', value: 'urgent' }
    ],
    orderTagsWithState: [],
    selectedTagsDisplay: [],
    today: '',
    minDate: '', // 最小可选日期
    maxDate: '', // 最大可选日期
    hourSlots: [], // 小时选项
    minuteSlots: [], // 分钟选项
    canSubmit: false,
    submitting: false
  },

  onLoad(options) {
    // 快速设置基础数据
    const today = new Date();
    const minDate = new Date(today.getTime() + 24 * 60 * 60 * 1000); // 明天开始
    const maxDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天内

    // 获取当前时间
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTime = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;

    this.setData({
      today: this.formatDate(today),
      minDate: this.formatDate(minDate),
      maxDate: this.formatDate(maxDate),
      hourSlots: this.generateHourSlots(),
      minuteSlots: this.generateMinuteSlots(),
      'formData.scheduledHour': currentHour,
      'formData.scheduledMinute': currentMinute,
      'formData.scheduledTime': currentTime
    });

    // 异步初始化
    this.initPageAsync(options);
  },

  // 异步初始化页面数据
  async initPageAsync(options) {
    try {
      // 处理编辑模式
      if (options.mode === 'edit') {
        await this.loadEditData(options);
      }

      // 初始化标签状态
      this.updateTagsState();
      this.checkCanSubmit();

      // 页面初始化完成
    } catch (error) {
      console.warn('页面初始化警告:', error.message || error);
    }
  },

  // 加载编辑数据
  async loadEditData(options) {
    const {
      title, content, reward, duration, rounds, serviceType,
      orderType, scheduledDate, scheduledTime, orderId, id, platformType
    } = options;

    // 兼容两种参数名：orderId 和 id
    const finalOrderId = orderId || id;

    // 解码URL参数
    const decodedTitle = title ? decodeURIComponent(title) : '';
    const decodedContent = content ? decodeURIComponent(content) : '';

    // 更新表单数据
    this.setData({
      'formData.title': decodedTitle,
      'formData.content': decodedContent,
      'formData.reward': reward || '',
      'formData.platformType': platformType || 'pc',
      'formData.serviceType': serviceType || 'duration',
      'formData.duration': duration || '2',
      'formData.rounds': rounds || '5',
      'formData.orderType': orderType || 'immediate',
      'formData.scheduledDate': scheduledDate || '',
      'formData.scheduledTime': scheduledTime || '',
      editMode: true,
      editOrderId: finalOrderId
    });

    // 尝试从本地存储获取完整的订单信息（包括标签）
    try {
      const storedOrderData = wx.getStorageSync(`create_order_${finalOrderId}`);
      if (storedOrderData && storedOrderData.tags) {
        this.setData({
          'formData.selectedTags': storedOrderData.tags,
          'formData.orderType': storedOrderData.orderType || 'immediate',
          'formData.scheduledDate': storedOrderData.scheduledDate || '',
          'formData.scheduledTime': storedOrderData.scheduledTime || ''
        });
        console.log('从本地存储加载订单标签:', storedOrderData.tags);
      }
    } catch (error) {
      console.warn('加载本地存储订单数据失败:', error);
    }

    // 更新页面标题
    wx.setNavigationBarTitle({
      title: '修改订单'
    });

    console.log('加载编辑数据:', {
      title: decodedTitle,
      content: decodedContent,
      reward,
      serviceType,
      duration,
      rounds,
      orderType,
      scheduledDate,
      scheduledTime,
      orderId: finalOrderId
    });
  },

  // 更新标签状态
  updateTagsState() {
    const { selectedTags } = this.data.formData;
    const orderTagsWithState = this.data.orderTags.map(tag => ({
      ...tag,
      selected: selectedTags.includes(tag.value)
    }));

    // 计算选中标签的显示数据
    const selectedTagsDisplay = this.data.orderTags.filter(tag =>
      selectedTags.includes(tag.value)
    );

    this.setData({
      orderTagsWithState,
      selectedTagsDisplay
    });
  },

  // 订单标题输入
  onTitleInput(e) {
    const { value } = e.detail;
    this.setData({
      'formData.title': value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 任务内容输入
  onContentInput(e) {
    const { value } = e.detail;
    this.setData({
      'formData.content': value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 清理文本内容，去除多余空格和换行符
  cleanTextContent(text) {
    if (!text) return '';
    // 将换行符替换为空格，然后去除多余的空格
    return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
  },

  // 报酬输入
  onRewardInput(e) {
    const { value } = e.detail;
    this.setData({
      'formData.reward': value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 平台类型切换
  switchPlatformType(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      'formData.platformType': type
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 订单类型切换
  switchOrderType(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      'formData.orderType': type,
      'formData.scheduledDate': '',
      'formData.scheduledTime': ''
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 预约日期选择
  onDateChange(e) {
    const { value } = e.detail;
    this.setData({
      'formData.scheduledDate': value,
      'formData.scheduledTime': '' // 重置时间选择
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 小时选择
  onHourChange(e) {
    const { value } = e.detail;
    this.setData({
      'formData.scheduledHour': value
    }, () => {
      this.updateScheduledTime();
      this.checkCanSubmit();
    });
  },

  // 分钟选择
  onMinuteChange(e) {
    const { value } = e.detail;
    this.setData({
      'formData.scheduledMinute': value
    }, () => {
      this.updateScheduledTime();
      this.checkCanSubmit();
    });
  },

  // 生成小时选项
  generateHourSlots() {
    const slots = [];
    for (let hour = 0; hour < 24; hour++) {
      slots.push({
        label: `${hour.toString().padStart(2, '0')}时`,
        value: hour
      });
    }
    return slots;
  },

  // 生成分钟选项
  generateMinuteSlots() {
    const slots = [];
    for (let minute = 0; minute < 60; minute++) {
      slots.push({
        label: `${minute.toString().padStart(2, '0')}分`,
        value: minute
      });
    }
    return slots;
  },

  // 更新完整时间字符串
  updateScheduledTime() {
    const hour = this.data.formData.scheduledHour;
    const minute = this.data.formData.scheduledMinute;
    const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    this.setData({
      'formData.scheduledTime': timeStr
    });
  },



  // 切换服务类型
  switchServiceType(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      'formData.serviceType': type
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 选择服务时长
  selectDuration(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'formData.duration': value,
      'formData.customDuration': ''
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 自定义时长输入
  onCustomDurationInput(e) {
    const { value } = e.detail;
    this.setData({
      'formData.customDuration': value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 选择局数
  selectRounds(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'formData.rounds': value,
      'formData.customRounds': ''
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 自定义局数输入
  onCustomRoundsInput(e) {
    const { value } = e.detail;
    this.setData({
      'formData.customRounds': value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 切换标签选择
  toggleTag(e) {
    const { value } = e.currentTarget.dataset;
    const { selectedTags } = this.data.formData;

    let newTags = [...selectedTags];
    const index = newTags.indexOf(value);

    if (index > -1) {
      // 如果已选中，则移除
      newTags.splice(index, 1);
    } else {
      // 如果未选中，则添加（最多选择3个）
      if (newTags.length < 3) {
        newTags.push(value);
      } else {
        wx.showToast({
          title: '最多选择3个标签',
          icon: 'none'
        });
        return;
      }
    }

    this.setData({
      'formData.selectedTags': newTags
    }, () => {
      this.updateTagsState();
      this.checkCanSubmit();
    });
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const {
      title, content, reward, serviceType,
      duration, customDuration, rounds, customRounds,
      orderType, scheduledDate, scheduledTime
    } = this.data.formData;

    let canSubmit = title.trim() && content.trim() && reward && parseFloat(reward) > 0;

    // 检查服务时间设置
    if (serviceType === 'duration') {
      // 检查时长
      if (duration === 'custom') {
        canSubmit = canSubmit && customDuration && parseFloat(customDuration) > 0;
      } else {
        canSubmit = canSubmit && duration;
      }
    } else if (serviceType === 'rounds') {
      // 检查局数
      if (rounds === 'custom') {
        canSubmit = canSubmit && customRounds && parseInt(customRounds) > 0;
      } else {
        canSubmit = canSubmit && rounds;
      }
    }

    // 检查预约订单的时间设置
    if (orderType === 'scheduled') {
      canSubmit = canSubmit && scheduledDate && scheduledTime;
    }

    console.log('checkCanSubmit 结果:', {
      canSubmit,
      title: title?.trim(),
      content: content?.trim(),
      reward,
      serviceType,
      duration,
      customDuration,
      rounds,
      customRounds,
      orderType,
      scheduledDate,
      scheduledTime
    });

    this.setData({ canSubmit });
  },

  // 提交订单
  async submitOrder() {
    console.log('submitOrder 被调用，当前状态:', {
      submitting: this.data.submitting,
      canSubmit: this.data.canSubmit,
      formData: this.data.formData
    });

    if (this.data.submitting || !this.data.canSubmit) {
      console.log('提交被阻止:', {
        submitting: this.data.submitting,
        canSubmit: this.data.canSubmit
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      // 计算实际服务时间
      let serviceInfo = {};
      if (this.data.formData.serviceType === 'duration') {
        // 按时长
        let actualDuration = 0;
        if (this.data.formData.duration === 'custom') {
          actualDuration = parseFloat(this.data.formData.customDuration) || 0;
        } else {
          actualDuration = parseFloat(this.data.formData.duration) || 0;
        }
        serviceInfo = {
          serviceType: 'duration',
          duration: actualDuration
        };
      } else {
        // 按局数
        let actualRounds = 0;
        if (this.data.formData.rounds === 'custom') {
          actualRounds = parseInt(this.data.formData.customRounds) || 0;
        } else {
          actualRounds = parseInt(this.data.formData.rounds) || 0;
        }
        serviceInfo = {
          serviceType: 'rounds',
          rounds: actualRounds
        };
      }

      // 构建订单数据
      const orderData = {
        title: this.data.formData.title.trim(),
        content: this.cleanTextContent(this.data.formData.content),
        reward: parseFloat(this.data.formData.reward),
        platformType: this.data.formData.platformType,
        ...serviceInfo, // 包含 serviceType 和 duration/rounds
        tags: this.data.formData.selectedTags,
        orderType: this.data.formData.orderType,
        scheduledDate: this.data.formData.scheduledDate,
        scheduledTime: this.data.formData.scheduledTime
      };

      console.log('🔍 [订单创建/编辑] 准备提交的订单数据:', orderData);
      console.log('🔍 [订单创建/编辑] 平台类型字段:', orderData.platformType);



      let result;
      const isEditMode = this.data.editMode;
      const orderId = this.data.editOrderId;

      // 调用API
      try {
        if (isEditMode) {
          // 编辑模式：更新订单
          console.log('🔍 [API调用] 更新订单，数据:', orderData);
          result = await API.updateOrder(orderId, orderData);
          console.log('🔍 [API调用] 更新订单结果:', result);
        } else {
          // 创建模式：创建新订单
          console.log('🔍 [API调用] 创建订单，数据:', orderData);
          result = await API.createOrder(orderData);
          console.log('🔍 [API调用] 创建订单结果:', result);
          console.log('🔍 [API调用] 返回的订单数据:', JSON.stringify(result.data, null, 2));
        }
      } catch (apiError) {
        console.log('❌ [API调用] API调用失败:', apiError);
        throw apiError;
      }

      if (result.success) {
        // 保存订单数据到本地存储（用于演示模式）
        const finalOrderId = result.data.orderId;
        wx.setStorageSync(`create_order_${finalOrderId}`, orderData);

        wx.showToast({
          title: isEditMode ? '订单修改成功' : '订单创建成功',
          icon: 'success'
        });

        // 根据模式决定跳转逻辑
        console.log('准备跳转到抢单大厅...');
        setTimeout(() => {
          console.log('开始执行跳转...');

          // 检查当前页面栈
          const pages = getCurrentPages();
          console.log('当前页面栈长度:', pages.length);

          // 如果是编辑模式，先返回到订单详情页
          if (isEditMode && pages.length > 1) {
            // 先返回到上一页（订单详情页）
            wx.navigateBack({
              delta: 1,
              success: () => {
                console.log('返回订单详情页成功');
              },
              fail: (error) => {
                console.error('返回上一页失败:', error);
              }
            });
          } else {
            // 创建模式：导航到抢单大厅
            // 设置全局标记，让订单列表页面知道要显示抢单大厅模式
            const app = getApp();
            app.globalData.enterGrabMode = true;

            wx.switchTab({
              url: '/pages/order/list/list',
              success: () => {
                console.log('跳转到抢单大厅成功');
              },
              fail: (error) => {
                console.error('跳转到抢单大厅失败:', error);
              }
            });
          }
        }, 1500);
      } else {
        throw new Error(result.error || (isEditMode ? '修改订单失败' : '创建订单失败'));
      }
    } catch (error) {
      const isEditMode = this.data.editMode;
      console.error(isEditMode ? '修改订单失败:' : '创建订单失败:', error);
      wx.showToast({
        title: isEditMode ? '修改订单失败，请重试' : '创建订单失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
});
