<!--profile-edit.wxml - 用户资料编辑页面-->
<navigation-bar title="编辑资料" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 返回按钮 - 与其他页面保持一致 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
  </view>
</view>

<view class="edit-container page-with-custom-nav" wx:if="{{!loading}}">
  <!-- 头像编辑区域 -->
  <view class="avatar-section cyber-card">
    <view class="card-glow"></view>
    <view class="section-title">
      <text class="title-text">头像</text>
      <view class="title-line"></view>
    </view>
    
    <view class="avatar-edit-container">
      <view class="avatar-preview" bindtap="chooseAvatar">
        <image wx:if="{{userInfo.avatarUrl}}" class="avatar-image" src="{{userInfo.avatarUrl}}" />
        <view wx:else class="avatar-image default-avatar">
          <text class="avatar-text">👤</text>
        </view>
        <view class="avatar-overlay">
          <text class="overlay-text">点击更换</text>
        </view>
        <!-- 移除头像装饰环，保留原始头像显示 -->
        <!-- <view class="avatar-ring"></view> -->
      </view>
    </view>
  </view>

  <!-- 基础信息编辑 -->
  <view class="form-section cyber-card">
    <view class="card-glow"></view>
    <view class="section-title">
      <text class="title-text">基础信息</text>
      <view class="title-line"></view>
    </view>

    <!-- 昵称 -->
    <view class="form-item">
      <view class="item-label">
        <text class="label-text">昵称</text>
        <text class="required-mark">*</text>
      </view>
      <view class="input-container">
        <input
          class="cyber-input {{errors.nickName ? 'error' : ''}}"
          placeholder="请输入昵称"
          value="{{formData.nickName}}"
          data-field="nickName"
          bindinput="onInputChange"
          maxlength="20"
        />
        <view class="input-glow"></view>
      </view>
      <text class="error-text" wx:if="{{errors.nickName}}">{{errors.nickName}}</text>
    </view>

    <!-- 游戏昵称 -->
    <view class="form-item">
      <view class="item-label">
        <text class="label-text">游戏昵称</text>
        <text class="char-count">{{formData.gameNickName.length}}/30</text>
      </view>
      <view class="input-container">
        <input
          class="cyber-input {{errors.gameNickName ? 'error' : ''}}"
          placeholder="请输入游戏昵称（选填）"
          value="{{formData.gameNickName}}"
          data-field="gameNickName"
          bindinput="onInputChange"
          maxlength="30"
        />
        <view class="input-glow"></view>
      </view>
      <text class="error-text" wx:if="{{errors.gameNickName}}">{{errors.gameNickName}}</text>
    </view>

    <!-- 性别 -->
    <view class="form-item">
      <view class="item-label">
        <text class="label-text">性别</text>
      </view>
      <view class="picker-container">
        <picker 
          class="cyber-picker"
          mode="selector"
          range="{{genderOptions}}"
          range-key="label"
          value="{{genderIndex}}"
          bindchange="onGenderChange"
        >
          <view class="picker-display">
            <text class="picker-text">{{genderOptions[genderIndex].label}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <view class="picker-glow"></view>
      </view>
    </view>

    <!-- 年龄 -->
    <view class="form-item">
      <view class="item-label">
        <text class="label-text">年龄</text>
      </view>
      <view class="input-container">
        <input 
          class="cyber-input {{errors.age ? 'error' : ''}}"
          placeholder="请输入年龄"
          value="{{formData.age}}"
          data-field="age"
          bindinput="onInputChange"
          type="number"
        />
        <view class="input-glow"></view>
      </view>
      <text class="error-text" wx:if="{{errors.age}}">{{errors.age}}</text>
    </view>
  </view>

  <!-- 详细信息编辑 -->
  <view class="form-section cyber-card">
    <view class="card-glow"></view>
    <view class="section-title">
      <text class="title-text">详细信息</text>
      <view class="title-line"></view>
    </view>

    <!-- 个人简介 -->
    <view class="form-item">
      <view class="item-label">
        <text class="label-text">个人简介</text>
        <text class="char-count">{{formData.bio.length}}/200</text>
      </view>
      <view class="textarea-container">
        <textarea 
          class="cyber-textarea {{errors.bio ? 'error' : ''}}"
          placeholder="介绍一下自己吧..."
          value="{{formData.bio}}"
          data-field="bio"
          bindinput="onInputChange"
          maxlength="200"
          auto-height
        />
        <view class="textarea-glow"></view>
      </view>
      <text class="error-text" wx:if="{{errors.bio}}">{{errors.bio}}</text>
    </view>

    <!-- 联系方式 -->
    <view class="form-item">
      <view class="item-label">
        <text class="label-text">联系方式</text>
        <text class="char-count">{{formData.contactInfo.length}}/100</text>
      </view>
      <view class="input-container">
        <input 
          class="cyber-input {{errors.contactInfo ? 'error' : ''}}"
          placeholder="QQ、微信等联系方式"
          value="{{formData.contactInfo}}"
          data-field="contactInfo"
          bindinput="onInputChange"
          maxlength="100"
        />
        <view class="input-glow"></view>
      </view>
      <text class="error-text" wx:if="{{errors.contactInfo}}">{{errors.contactInfo}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <view class="cancel-btn cyber-btn secondary-btn" bindtap="cancelEdit">
        <text class="btn-text">取消</text>
        <view class="btn-glow secondary-glow"></view>
      </view>
      <view class="save-btn cyber-btn primary-btn {{saving ? 'loading' : ''}}" bindtap="saveUserInfo">
        <text class="btn-text">{{saving ? '保存中...' : '保存'}}</text>
        <view class="btn-glow primary-glow"></view>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <text class="loading-text">加载中...</text>
</view>
