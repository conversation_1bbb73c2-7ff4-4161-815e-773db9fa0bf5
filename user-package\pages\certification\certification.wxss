/* 实名认证页面样式 */
.certification-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.certification-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 30rpx);
}

/* 认证状态卡片 */
.status-card {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 认证表单 */
.form-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
}

.form-input:focus {
  border-color: #1890ff;
  background: white;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 40rpx;
}

.submit-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

.submit-btn:not(:disabled):active {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
}
