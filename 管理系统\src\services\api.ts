import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import { ApiResponse } from '@/types';

// 微信云开发配置
const CLOUD_ENV = 'cloud1-9gsj7t48183e5a9f'; // 云环境ID

// 云开发API配置选项
const API_CONFIG = {
  // 方式1: 直接调用云函数HTTP触发器（推荐）
  CLOUD_FUNCTION_URL: `https://${CLOUD_ENV}.service.tcloudbase.com/admin-api`,

  // 方式2: 使用云开发Web SDK
  USE_WEB_SDK: true,

  // 方式3: 腾讯云API网关
  API_GATEWAY_URL: `https://${CLOUD_ENV}.tcb-api.tencentcloudapi.com/web`,

  // 当前使用的API地址
  get CURRENT_API_URL() {
    return this.USE_WEB_SDK ? this.CLOUD_FUNCTION_URL : this.API_GATEWAY_URL;
  }
};

const ADMIN_API_URL = API_CONFIG.CURRENT_API_URL;

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: ADMIN_API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config: any) => {
    // 添加认证 token
    const token = localStorage.getItem('admin_token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    console.log('🚀 API Request:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    console.log('✅ API Response:', response.config.url, response.data);
    
    const { data } = response;
    
    // 如果后端返回的 success 为 false，显示错误信息
    if (data && !data.success) {
      message.error(data.error || data.message || '请求失败');
      return Promise.reject(new Error(data.error || data.message || '请求失败'));
    }
    
    return response;
  },
  (error) => {
    console.error('❌ Response Error:', error);
    
    // 处理网络错误
    if (!error.response) {
      message.error('网络连接失败，请检查网络设置');
      return Promise.reject(error);
    }
    
    const { status, data } = error.response;
    
    switch (status) {
      case 401:
        message.error('登录已过期，请重新登录');
        localStorage.removeItem('admin_token');
        window.location.href = '/login';
        break;
      case 403:
        message.error('权限不足，无法访问');
        break;
      case 404:
        message.error('请求的资源不存在');
        break;
      case 500:
        message.error('服务器内部错误');
        break;
      default:
        message.error(data?.error || data?.message || `请求失败 (${status})`);
    }
    
    return Promise.reject(error);
  }
);

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.get(url, config).then(res => res.data),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.post(url, data, config).then(res => res.data),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.put(url, data, config).then(res => res.data),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.delete(url, config).then(res => res.data),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.patch(url, data, config).then(res => res.data),
};

// 云函数调用方法 - 真实API调用
export const callCloudFunction = async <T = any>(
  functionName: string,
  data?: any
): Promise<ApiResponse<T>> => {
  console.log(`🚀 调用云函数 [${functionName}]:`, data);

  try {
    const response = await fetch('https://cloud1-9gsj7t48183e5a9f.service.tcloudbase.com/admin-api', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: functionName,
        data
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log(`✅ 云函数原始响应 [${functionName}]:`, result);

    // 微信云开发可能会自动解析HTTP触发器响应，直接检查success字段
    if (result.success) {
      console.log(`🎯 使用云函数数据 [${functionName}]:`, result.data);
      return result;
    }

    // 检查是否是HTTP触发器格式（statusCode + body）
    if (result.statusCode === 200 && result.body && typeof result.body === 'string') {
      const parsedBody = JSON.parse(result.body);
      console.log(`✅ 云函数解析后数据 [${functionName}]:`, parsedBody);

      // 如果云函数返回成功，使用真实数据
      if (parsedBody.success) {
        console.log(`🎯 使用HTTP触发器格式的云函数数据 [${functionName}]:`, parsedBody.data);
        return parsedBody;
      } else {
        // 云函数返回失败，抛出错误
        console.log(`⚠️ 云函数返回失败 [${functionName}]:`, parsedBody.error);
        throw new Error(parsedBody.error || '云函数返回失败');
      }
    }

    // 如果云函数返回失败，抛出错误
    throw new Error(result.error || '云函数返回失败');

  } catch (error) {
    console.error(`❌ 云函数错误 [${functionName}]:`, error);

    // 如果真实API调用失败，回退到模拟数据
    console.log(`🔄 回退到模拟数据 [${functionName}]`);
    return {
      success: true,
      data: generateMockData(functionName) as T,
      message: '模拟数据（API调用失败回退）'
    };
  }
};

// 生成模拟数据
const generateMockData = (functionName: string): any => {
  switch (functionName) {
    case 'getDashboardStats':
      return {
        totalUsers: 1234,
        totalOrders: 567,
        totalRevenue: 89012,
        newUsersToday: 45,
        onlineUsers: 123,
        activeOrders: 89,
        todayRevenue: 5678,
        systemLoad: 67
      };
    case 'getUserStats':
      return {
        totalUsers: 1234,
        activeUsers: 890,
        newUsersToday: 45,
        verifiedUsers: 678
      };
    case 'getOrderStats':
      return {
        totalOrders: 567,
        pendingOrders: 23,
        completedOrders: 456,
        todayOrders: 12
      };
    case 'getWalletStats':
      return {
        totalTransactions: 890,
        totalAmount: 123456,
        pendingWithdraws: 12,
        todayTransactions: 34
      };
    default:
      return {};
  }
};

// 认证相关 API
export const authApi = {
  // 管理员登录
  login: (username: string, password: string) =>
    callCloudFunction('adminLogin', { username, password }),
    
  // 验证 token
  verifyToken: () =>
    callCloudFunction('verifyToken'),
    
  // 登出
  logout: () =>
    callCloudFunction('adminLogout'),
};

// 仪表盘相关 API
export const dashboardApi = {
  // 获取仪表盘统计数据
  getStats: () =>
    callCloudFunction('getDashboardStats'),
    
  // 获取图表数据
  getChartData: (type: string, timeRange?: string) =>
    callCloudFunction('getChartData', { type, timeRange }),
    
  // 获取最近活动
  getRecentActivities: () =>
    callCloudFunction('getRecentActivities'),
};

// 用户管理 API
export const userApi = {
  // 获取用户列表
  getUserList: (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) =>
    callCloudFunction('getUserList', params),
    
  // 获取用户详情
  getUserDetail: (userId: string) =>
    callCloudFunction('getUserDetail', { userId }),
    
  // 更新用户状态
  updateUserStatus: (userId: string, status: 'active' | 'inactive' | 'banned') =>
    callCloudFunction('updateUserStatus', { userId, status }),
    
  // 获取用户统计
  getUserStats: () =>
    callCloudFunction('getUserStats'),

  // 调试用户数据
  debugUsers: () =>
    callCloudFunction('admin-api', { action: 'debug-users' }),

  // 修复用户数据
  fixUserData: (fixAction: string) =>
    callCloudFunction('admin-api', { action: 'fix-user-data', fixAction: fixAction }),
};

// 订单管理 API
export const orderApi = {
  // 获取订单列表
  getOrderList: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }) =>
    callCloudFunction('getOrderList', params),
    
  // 获取订单详情
  getOrderDetail: (orderId: string) =>
    callCloudFunction('getOrderDetail', { orderId }),
    
  // 更新订单状态
  updateOrderStatus: (orderId: string, status: string) =>
    callCloudFunction('updateOrderStatus', { orderId, status }),
    
  // 获取订单统计
  getOrderStats: () =>
    callCloudFunction('getOrderStats'),
};

// 聊天管理 API
export const chatApi = {
  // 获取聊天室列表
  getChatRoomList: (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }) =>
    callCloudFunction('getChatRoomList', params),
    
  // 获取聊天记录
  getChatMessages: (chatRoomId: string, params?: {
    page?: number;
    limit?: number;
  }) =>
    callCloudFunction('getChatMessages', { chatRoomId, ...params }),
    
  // 获取聊天统计
  getChatStats: () =>
    callCloudFunction('getChatStats'),
};

// 钱包管理 API
export const walletApi = {
  // 获取交易记录
  getTransactionList: (params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
  }) =>
    callCloudFunction('getTransactionList', params),
    
  // 获取提现申请
  getWithdrawList: (params?: {
    page?: number;
    limit?: number;
    status?: string;
  }) =>
    callCloudFunction('getWithdrawList', params),
    
  // 审核提现申请
  approveWithdraw: (withdrawId: string, status: 'approved' | 'rejected', remark?: string) =>
    callCloudFunction('approveWithdraw', { withdrawId, status, remark }),
    
  // 获取钱包统计
  getWalletStats: () =>
    callCloudFunction('getWalletStats'),
};

// 通知管理 API
export const notificationApi = {
  // 获取通知列表
  getNotificationList: (params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
  }) =>
    callCloudFunction('getNotificationList', params),
    
  // 发送通知
  sendNotification: (data: {
    type: string;
    title: string;
    content: string;
    receiverId?: string;
    targetUsers?: string[];
  }) =>
    callCloudFunction('sendNotification', data),
    
  // 获取通知统计
  getNotificationStats: () =>
    callCloudFunction('getNotificationStats'),
};

// 评价管理 API
export const evaluationApi = {
  // 获取评价列表
  getEvaluationList: (params?: {
    page?: number;
    limit?: number;
    rating?: number;
  }) =>
    callCloudFunction('getEvaluationList', params),
    
  // 获取评价统计
  getEvaluationStats: () =>
    callCloudFunction('getEvaluationStats'),
};

export default api;
