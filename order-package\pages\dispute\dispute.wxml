<!--仲裁处理页面-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-bg"></view>
      <view class="header-content">
        <text class="page-title" wx:if="{{mode === 'apply'}}">申请仲裁</text>
        <text class="page-title" wx:else>查看仲裁</text>
        <text class="page-subtitle">{{orderInfo.title}}</text>
      </view>
    </view>

    <!-- 订单信息卡片 -->
    <view class="order-card">
      <view class="card-header">
        <text class="order-title">{{orderInfo.title}}</text>
        <text class="order-no">订单号：{{orderInfo.orderNo}}</text>
      </view>
      <view class="order-info">
        <text class="info-item">服务金额：¥{{orderInfo.pricing.totalAmount}}</text>
        <text class="info-item">当前状态：{{orderInfo.statusName}}</text>
      </view>
    </view>

    <!-- 申请仲裁表单 -->
    <view class="dispute-form" wx:if="{{mode === 'apply'}}">
      <!-- 仲裁原因选择 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">仲裁原因</text>
          <text class="required">*</text>
        </view>
        <view class="reason-list">
          <view 
            class="reason-item {{selectedReason === item.value ? 'selected' : ''}}"
            wx:for="{{disputeReasons}}"
            wx:key="value"
            bindtap="selectReason"
            data-value="{{item.value}}"
          >
            <view class="reason-header">
              <text class="reason-label">{{item.label}}</text>
              <view class="reason-radio">
                <view class="radio-dot" wx:if="{{selectedReason === item.value}}"></view>
              </view>
            </view>
            <text class="reason-description">{{item.description}}</text>
          </view>
        </view>
      </view>

      <!-- 自定义原因输入 -->
      <view class="form-section" wx:if="{{selectedReason === 'other'}}">
        <view class="section-title">
          <text class="title-text">自定义原因</text>
          <text class="required">*</text>
        </view>
        <textarea
          class="custom-reason-input"
          placeholder="请详细说明仲裁原因..."
          value="{{customReason}}"
          bindinput="onCustomReasonInput"
          maxlength="100"
        ></textarea>
        <text class="input-counter">{{customReason.length}}/100</text>
      </view>

      <!-- 详细描述 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">详细描述</text>
          <text class="required">*</text>
        </view>
        <textarea
          class="description-input"
          placeholder="请详细描述遇到的问题，包括具体情况、时间等..."
          value="{{description}}"
          bindinput="onDescriptionInput"
          maxlength="500"
        ></textarea>
        <text class="input-counter">{{description.length}}/500</text>
      </view>

      <!-- 证据图片 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">证据图片</text>
          <text class="optional">（可选）</text>
        </view>
        <view class="evidence-grid">
          <!-- 已选择的图片 -->
          <view 
            class="evidence-item"
            wx:for="{{evidenceImages}}"
            wx:key="index"
          >
            <image 
              class="evidence-image"
              src="{{item.tempFilePath || item.fileID}}"
              mode="aspectFill"
              bindtap="previewEvidenceImage"
              data-index="{{index}}"
            ></image>
            <view 
              class="remove-btn"
              bindtap="removeEvidenceImage"
              data-index="{{index}}"
            >×</view>
          </view>
          
          <!-- 添加图片按钮 -->
          <view 
            class="add-evidence-btn"
            bindtap="chooseEvidenceImages"
            wx:if="{{evidenceImages.length < 9}}"
          >
            <text class="add-icon">+</text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
        <text class="evidence-tip">最多上传9张图片，支持截图、照片等证据材料</text>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-btn cyber-btn primary"
          bindtap="submitDispute"
          disabled="{{submitting}}"
        >
          <text class="btn-text" wx:if="{{!submitting}}">提交仲裁申请</text>
          <text class="btn-text" wx:else>提交中...</text>
          <view class="btn-glow" wx:if="{{!submitting}}"></view>
        </button>
        
        <view class="submit-tips">
          <text class="tip-item">• 仲裁申请提交后将由平台客服处理</text>
          <text class="tip-item">• 请确保提供的信息真实有效</text>
          <text class="tip-item">• 处理结果将通过消息通知您</text>
        </view>
      </view>
    </view>

    <!-- 查看仲裁信息 -->
    <view class="dispute-info" wx:if="{{mode === 'view' && disputeInfo}}">
      <view class="info-section">
        <view class="section-title">
          <text class="title-text">仲裁信息</text>
        </view>
        
        <view class="info-card">
          <view class="info-row">
            <text class="info-label">仲裁状态：</text>
            <text class="info-value status-{{disputeInfo.status}}">{{disputeStatus[disputeInfo.status]}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">申请时间：</text>
            <text class="info-value">{{formatTime(disputeInfo.createTime)}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">仲裁原因：</text>
            <text class="info-value">{{disputeInfo.reason}}</text>
          </view>
          <view class="info-row" wx:if="{{disputeInfo.description}}">
            <text class="info-label">详细描述：</text>
            <text class="info-value description">{{disputeInfo.description}}</text>
          </view>
        </view>

        <!-- 证据图片展示 -->
        <view class="evidence-display" wx:if="{{disputeInfo.evidenceImages && disputeInfo.evidenceImages.length > 0}}">
          <text class="evidence-title">证据图片</text>
          <view class="evidence-grid">
            <view 
              class="evidence-item"
              wx:for="{{disputeInfo.evidenceImages}}"
              wx:key="fileID"
            >
              <image 
                class="evidence-image"
                src="{{item.fileID}}"
                mode="aspectFill"
                bindtap="previewEvidenceImage"
                data-index="{{index}}"
              ></image>
            </view>
          </view>
        </view>

        <!-- 处理结果 -->
        <view class="result-section" wx:if="{{disputeInfo.result}}">
          <text class="result-title">处理结果</text>
          <view class="result-card">
            <text class="result-text">{{disputeInfo.result}}</text>
            <text class="result-time">处理时间：{{formatTime(disputeInfo.resolveTime)}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
