/* 现代化组件样式库 */

/* ==================== 加载动画 ==================== */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10rpx); }
  60% { transform: translateY(-5rpx); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ==================== 现代化卡片变体 ==================== */
.card-elevated {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-xl);
  border: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elevated:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

.card-glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-md);
}

.card-gradient {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-lg);
  border: 1rpx solid var(--border-light);
}

/* ==================== 现代化按钮变体 ==================== */
.btn-gradient {
  background: var(--primary-gradient);
  color: #ffffff;
  border: none;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.btn-gradient:active::before {
  left: 100%;
}

.btn-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

.btn-floating {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  position: fixed;
  bottom: 200rpx;
  right: var(--space-xl);
  z-index: 999;
  box-shadow: var(--shadow-xl);
  background: var(--primary-gradient);
  color: #ffffff;
  font-size: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-floating:active {
  transform: scale(0.9);
  box-shadow: var(--shadow-lg);
}

.floating-icon {
  width: 60rpx;
  height: 60rpx;
  /* 将图标转换为白色，与渐变背景更协调 */
  filter: brightness(0) invert(1);
  /* 微调图标位置，向左移动一点达到视觉居中 */
  display: block;
  margin: auto;
  transform: translateX(-4rpx);
}

.floating-text {
  font-size: 48rpx;
  font-weight: 300;
  color: #ffffff;
}

/* ==================== 现代化输入框变体 ==================== */
.input-floating {
  position: relative;
  margin-bottom: var(--space-2xl);
}

.input-floating .input-field {
  border: none;
  border-bottom: 3rpx solid var(--border-medium);
  border-radius: 0;
  background: transparent;
  padding: var(--space-lg) 0;
  font-size: 32rpx;
}

.input-floating .input-field:focus {
  border-bottom-color: var(--primary-color);
  box-shadow: none;
}

.input-floating .input-label {
  position: absolute;
  top: var(--space-lg);
  left: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  color: var(--text-tertiary);
}

.input-floating .input-field:focus + .input-label,
.input-floating .input-field:not(:placeholder-shown) + .input-label {
  top: -10rpx;
  font-size: 24rpx;
  color: var(--primary-color);
}

/* ==================== 状态指示器 ==================== */
.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  display: inline-block;
  margin-right: var(--space-xs);
  animation: pulse 2s infinite;
}

.status-dot.online {
  background: var(--success-color);
}

.status-dot.offline {
  background: var(--text-tertiary);
  animation: none;
}

.status-dot.busy {
  background: var(--warning-color);
}

/* ==================== 加载状态 ==================== */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 现代化标签 ==================== */
.tag-modern {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 56rpx;
  padding: 0 20rpx 8rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 25rpx;
  margin-right: var(--space-sm);
  margin-bottom: var(--space-sm);
  transition: all 0.3s ease;
  letter-spacing: 0.5rpx;
  box-sizing: border-box;
  white-space: nowrap;
}

.tag-modern.primary {
  background: var(--primary-light);
  color: var(--primary-dark);
  border: 1rpx solid var(--primary-color);
}

.tag-modern.success {
  background: #f6ffed;
  color: var(--success-color);
  border: 1rpx solid var(--success-color);
}

.tag-modern.warning {
  background: #fffbe6;
  color: var(--warning-color);
  border: 1rpx solid var(--warning-color);
}

.tag-modern.info {
  background: #e6f7ff;
  color: var(--info-color);
  border: 1rpx solid var(--info-color);
}

/* ==================== 现代化列表项 ==================== */
.list-item-modern {
  display: flex;
  align-items: center;
  padding: var(--space-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-md);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.list-item-modern::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: var(--primary-gradient);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.list-item-modern:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.list-item-modern:active::before {
  transform: scaleY(1);
}

/* ==================== 工具类 ==================== */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-up {
  animation: slideUp 0.4s ease-out;
}

.glass-effect {
  backdrop-filter: blur(20rpx);
  background: rgba(255, 255, 255, 0.8);
}

.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.shadow-hover {
  transition: box-shadow 0.3s ease;
}

.shadow-hover:hover {
  box-shadow: var(--shadow-lg);
}
