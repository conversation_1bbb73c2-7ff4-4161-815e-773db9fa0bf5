<!-- 现代化自定义 tabBar -->
<view class="tab-bar">
  <view wx:for="{{list}}" wx:key="index"
        class="tab-bar-item {{selected === index ? 'selected' : ''}}"
        bindtap="switchTab"
        data-path="{{item.pagePath}}"
        data-index="{{index}}">

    <!-- 图标容器 -->
    <view class="tab-icon-container">
      <!-- 使用未读徽章组件包装消息图标 -->
      <unread-badge wx:if="{{item.badge}}" count="{{chatBadgeCount}}" type="count" max="99" size="small">
        <!-- 云存储图标 -->
        <image wx:if="{{!useEmoji && item.iconUrl}}"
               class="tab-icon"
               src="{{selected === index ? item.selectedIconUrl : item.iconUrl}}"
               mode="aspectFit"></image>

        <!-- 备选 emoji 图标 -->
        <view wx:else
              class="tab-emoji">{{item.emoji}}</view>
      </unread-badge>

      <!-- 非消息图标（不需要徽章） -->
      <block wx:else>
        <!-- 云存储图标 -->
        <image wx:if="{{!useEmoji && item.iconUrl}}"
               class="tab-icon"
               src="{{selected === index ? item.selectedIconUrl : item.iconUrl}}"
               mode="aspectFit"></image>

        <!-- 备选 emoji 图标 -->
        <view wx:else
              class="tab-emoji">{{item.emoji}}</view>
      </block>
    </view>

    <!-- 文字 -->
    <view class="tab-text" style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</view>
  </view>
</view>
