// 评价详情页面
const app = getApp();

Page({
  data: {
    evaluation: null,
    title: '',
    loading: true
  },

  onLoad(options) {
    console.log('评价详情页面加载，参数:', options);

    if (options.data) {
      try {
        const evaluationData = JSON.parse(decodeURIComponent(options.data));
        const title = options.title ? decodeURIComponent(options.title) : '评价详情';

        // 处理评价数据，添加格式化的时间和评分文本
        const processedEvaluation = {
          ...evaluationData,
          formattedTime: this.formatTime(evaluationData.evaluationTime),
          ratingText: this.getRatingText(evaluationData.rating)
        };

        this.setData({
          evaluation: processedEvaluation,
          title: title,
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: title
        });
      } catch (error) {
        console.error('解析评价数据失败:', error);
        wx.showToast({
          title: '数据解析失败',
          icon: 'none'
        });
        setTimeout(() => wx.navigateBack(), 2000);
      }
    } else {
      wx.showToast({
        title: '缺少评价数据',
        icon: 'none'
      });
      setTimeout(() => wx.navigateBack(), 2000);
    }
  },

  // 格式化时间
  formatTime(time) {
    if (!time) return '';
    const date = new Date(time);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  // 获取星级显示
  getStarDisplay(rating) {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push({
        filled: i <= rating,
        index: i
      });
    }
    return stars;
  },

  // 获取评分文本
  getRatingText(rating) {
    const ratingTexts = ['', '很差', '较差', '一般', '满意', '非常满意'];
    return ratingTexts[rating] || '';
  }
});
