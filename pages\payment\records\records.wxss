/* 交易记录页面样式 - 科技感主题 */
.records-container {
  height: 100vh;
  width: 100vw !important; /* 确保容器占满整个屏幕宽度 */
  background: transparent;
  display: flex;
  flex-direction: column;
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.records-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 30rpx);
  padding-left: 0 !important;
  padding-right: 0 !important;
  box-sizing: border-box;
}

/* 统计卡片 - 科技感样式 */
.stats-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  margin: var(--space-lg);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  color: var(--text-primary);
  border: 1rpx solid var(--border-light);
  box-shadow: var(--shadow-md);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 500;
}

.date-filter {
  display: flex;
  align-items: center;
  background: rgba(0, 212, 255, 0.1);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.date-filter:active {
  background: rgba(0, 212, 255, 0.2);
}

.filter-text {
  font-size: 24rpx;
  margin-right: var(--space-xs);
  color: var(--text-secondary);
}

.filter-icon {
  font-size: 20rpx;
  color: var(--primary-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.stats-item {
  text-align: center;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.stats-value.income {
  color: var(--accent-color);
}

.stats-value.expense {
  color: #ff4d4f;
}

.stats-label {
  font-size: 24rpx;
  color: var(--text-tertiary);
  opacity: 0.9;
}

/* 筛选标签 - 科技感样式 */
.filter-tabs {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  padding: var(--space-lg) 0;
  border-bottom: 1rpx solid var(--border-light);
  margin: 0 var(--space-lg) var(--space-lg) var(--space-lg); /* 与stats-card保持一致的边距 */
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.tabs-scroll {
  white-space: nowrap;
  padding: 0 var(--space-lg);
}

.tab-item {
  display: inline-block;
  position: relative;
  padding: var(--space-md) var(--space-lg);
  margin-right: var(--space-md);
  border-radius: var(--radius-lg);
  background: rgba(0, 212, 255, 0.05);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.tab-item.active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-glow);
  border-color: transparent;
}

.tab-text {
  font-size: 26rpx;
}

.tab-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
  line-height: 1;
}

.tab-item.active .tab-badge {
  background: #ff7875;
}

/* 交易记录列表 - 科技感样式 */
.records-list {
  flex: 1;
  padding: 0 32rpx 32rpx 32rpx !important; /* 强制设置边距，确保生效 */
  box-sizing: border-box !important;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin: 0 0 var(--space-md) 0 !important; /* 确保没有左右边距 */
  border: 1rpx solid var(--border-light);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  width: 100% !important; /* 确保宽度为100% */
  box-sizing: border-box !important;
}

.record-item:active {
  background: rgba(0, 212, 255, 0.08);
  transform: translateX(4rpx);
  box-shadow: var(--shadow-glow);
}

.record-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.record-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-lg);
  flex-shrink: 0;
  background: rgba(0, 212, 255, 0.1);
  border: 1rpx solid rgba(0, 212, 255, 0.2);
}

.record-icon.recharge {
  background: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.2);
}

.record-icon.withdraw {
  background: rgba(255, 77, 79, 0.1);
  border-color: rgba(255, 77, 79, 0.2);
}

.record-icon.income {
  background: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.2);
}

.record-icon.payment {
  background: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.2);
}

.record-icon.refund {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.2);
}

.icon-text {
  font-size: 32rpx;
}

.record-details {
  flex: 1;
  min-width: 0;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.record-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.record-status {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.record-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.record-status.completed {
  background: #f6ffed;
  color: #52c41a;
}

.record-status.failed {
  background: #fff2f0;
  color: #ff4d4f;
}

.record-desc {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-bottom: var(--space-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.record-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-time {
  font-size: 22rpx;
  color: var(--text-disabled);
}

.related-info {
  flex-shrink: 0;
}

.related-text {
  font-size: 22rpx;
  color: var(--primary-color);
}

.record-amount {
  text-align: right;
  flex-shrink: 0;
  margin-left: 16rpx;
}

.amount-value {
  font-size: 32rpx;
  font-weight: bold;
}

.amount-value.income {
  color: var(--accent-color);
}

.amount-value.expense {
  color: #ff4d4f;
}

/* 加载状态 - 科技感样式 */
.load-more, .no-more {
  text-align: center;
  padding: var(--space-3xl) 0;
  font-size: 26rpx;
  color: var(--text-tertiary);
}

/* 现代化空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-3xl) var(--space-xl);
  text-align: center;
  background: transparent;
  border-radius: var(--radius-lg);
  margin: var(--space-lg);
  box-shadow: none;
  border: none;
}

.empty-icon {
  font-size: 140rpx;
  margin-bottom: var(--space-xl);
  opacity: 0.8;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 日期选择弹窗 - 科技感样式 */
.date-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.date-picker-modal.show {
  opacity: 1;
  visibility: visible;
}

.date-picker-content {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  margin: var(--space-3xl);
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.picker-close {
  font-size: 32rpx;
  color: var(--text-tertiary);
  padding: var(--space-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.picker-close:active {
  background: rgba(0, 212, 255, 0.1);
  color: var(--primary-color);
}

.picker-body {
  padding: var(--space-xl);
}

.date-input-group {
  margin-bottom: var(--space-lg);
}

.date-label {
  font-size: 26rpx;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: 500;
}

.date-input {
  background: rgba(0, 212, 255, 0.05);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  font-size: 28rpx;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.date-input:active {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
}

.picker-footer {
  display: flex;
  padding: var(--space-lg) var(--space-xl);
  border-top: 1rpx solid var(--border-light);
  gap: var(--space-md);
}

.picker-btn {
  flex: 1;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-md);
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.picker-btn.cancel {
  background: rgba(0, 212, 255, 0.05);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
  color: var(--text-secondary);
}

.picker-btn.cancel:active {
  background: rgba(0, 212, 255, 0.1);
}

.picker-btn.confirm {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-glow);
}

.picker-btn.confirm:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-md);
}

/* ==================== G.T.I. SECURITY 科技感加载框 ==================== */
.gti-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gti-loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10rpx);
}

.gti-loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 60rpx;
  background: rgba(15, 23, 42, 0.9);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 20rpx;
  box-shadow:
    0 0 40rpx rgba(0, 212, 255, 0.2),
    inset 0 0 40rpx rgba(0, 212, 255, 0.1);
  backdrop-filter: blur(20rpx);
}

.gti-logo-container {
  margin-bottom: 40rpx;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.gti-logo {
  width: 120rpx;
  height: 120rpx;
  filter: drop-shadow(0 0 20rpx rgba(0, 212, 255, 0.6));
}

.gti-loading-spinner {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 40rpx;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3rpx solid transparent;
  border-top: 3rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 60rpx;
  height: 60rpx;
  top: 10rpx;
  left: 10rpx;
  border-top-color: #00ff88;
  animation-duration: 2s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 40rpx;
  height: 40rpx;
  top: 20rpx;
  left: 20rpx;
  border-top-color: #ff6b35;
  animation-duration: 1s;
}

.gti-loading-text {
  color: var(--primary-color);
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 30rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  animation: textPulse 2s ease-in-out infinite;
}

.gti-loading-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background: var(--primary-color);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
  box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.dot-1 {
  animation-delay: -0.32s;
}

.dot-2 {
  animation-delay: -0.16s;
}

.dot-3 {
  animation-delay: 0s;
}

@keyframes logoGlow {
  0% {
    filter: drop-shadow(0 0 20rpx rgba(0, 212, 255, 0.6));
    transform: scale(1);
  }
  100% {
    filter: drop-shadow(0 0 30rpx rgba(0, 212, 255, 0.8));
    transform: scale(1.02);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes textPulse {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  }
  50% {
    opacity: 0.8;
    text-shadow: 0 0 20rpx rgba(0, 212, 255, 0.8);
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
