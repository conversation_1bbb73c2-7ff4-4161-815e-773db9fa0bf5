/* 通用返回按钮组件样式 */
.back-button {
  position: fixed;
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.back-button.active {
  transform: scale(0.95);
}

/* 位置样式 */
.back-button.top-left {
  top: 100rpx;
  left: 5rpx;
  background: transparent;
}

.back-button.top-left-lower {
  top: 105rpx;
  left: 3rpx;
}

.back-button.top-right {
  top: 60rpx;
  right: 30rpx;
}

.back-button.top-center {
  top: 60rpx;
  left: 50%;
  transform: translateX(-50%);
}

.back-button.safe-left {
  top: 225rpx;
  left: 3rpx;
}

.back-button.safe-right {
  top: 225rpx;
  right: 30rpx;
}

.back-button.below-header {
  top: 265rpx;
  left: 5rpx;
}

.back-button.avoid-avatar {
  top: 305rpx;
  left: 5rpx;
}

.back-button.auto-position {
  /* 默认位置，会被JavaScript动态调整 */
  top: 45rpx;
  left: 5rpx;
}

/* 大小样式 */
.back-button.small {
  width: 60rpx;
  height: 60rpx;
}

.back-button.normal {
  width: 80rpx;
  height: 80rpx;
}

.back-button.large {
  width: 100rpx;
  height: 100rpx;
}

/* 图标样式 */
.back-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button.small .back-icon {
  width: 36rpx;
  height: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

.back-button.normal .back-icon {
  width: 48rpx;
  height: 48rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

.back-button.large .back-icon {
  width: 60rpx;
  height: 60rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

/* 加载状态 */
.back-icon.loading .loading-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  animation: pulse 1.5s ease-in-out infinite;
}

/* 默认图标 */
.back-icon.default .default-icon {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.8);
}

.back-button.small .back-icon.default .default-icon {
  font-size: 28rpx;
}

.back-button.large .back-icon.default .default-icon {
  font-size: 44rpx;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 悬浮效果 */
.back-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 150, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.back-button.active::before {
  opacity: 1;
}
