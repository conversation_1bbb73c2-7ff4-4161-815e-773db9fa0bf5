import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/toaster';
import { useAuthStore } from '@/stores/authStore';
import Layout from '@/components/layout/Layout';
import LoginPage from '@/pages/auth/LoginPage';
import DashboardPage from '@/pages/dashboard/DashboardPage';
import UsersPage from '@/pages/users/UsersPage';
import OrdersPage from '@/pages/orders/OrdersPage';
import ChatPage from '@/pages/chat/ChatPage';
import WalletPage from '@/pages/wallet/WalletPage';
import NotificationsPage from '@/pages/notifications/NotificationsPage';
import EvaluationsPage from '@/pages/evaluations/EvaluationsPage';

// 路由守卫组件
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuthStore();
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="admin-ui-theme">
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            {/* 登录页面 */}
            <Route path="/login" element={<LoginPage />} />
            
            {/* 受保护的管理页面 */}
            <Route
              path="/*"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Routes>
                      <Route path="/" element={<Navigate to="/dashboard" replace />} />
                      <Route path="/dashboard" element={<DashboardPage />} />
                      <Route path="/users" element={<UsersPage />} />
                      <Route path="/orders" element={<OrdersPage />} />
                      <Route path="/chat" element={<ChatPage />} />
                      <Route path="/wallet" element={<WalletPage />} />
                      <Route path="/notifications" element={<NotificationsPage />} />
                      <Route path="/evaluations" element={<EvaluationsPage />} />
                    </Routes>
                  </Layout>
                </ProtectedRoute>
              }
            />
          </Routes>
        </div>
        <Toaster />
      </Router>
    </ThemeProvider>
  );
}

export default App;