// 更新用户信息云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const {
    action,
    userId,
    nickName,
    gameNickName,
    avatarUrl,
    phone,
    gender,
    age,
    bio,
    contactInfo,
    lastActiveTime
  } = event;

  console.log('🔄 [更新用户信息] 开始更新用户信息');
  console.log('🔄 [更新用户信息] 用户openid:', wxContext.OPENID);
  console.log('🔄 [更新用户信息] 更新数据:', event);

  try {
    // 处理特殊操作：更新活跃时间
    if (action === 'updateLastActiveTime') {
      if (!userId) {
        return {
          success: false,
          error: '缺少用户ID'
        };
      }

      await db.collection('users').doc(userId).update({
        data: {
          lastActiveTime: lastActiveTime || new Date(),
          updateTime: new Date()
        }
      });

      console.log('✅ [更新用户信息] 用户活跃时间更新成功');
      return {
        success: true,
        message: '活跃时间更新成功'
      };
    }
    // 查找用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const userId = userResult.data[0]._id;

    // 构建更新数据
    const updateData = {
      updateTime: new Date()
    };

    // 基础信息字段
    if (nickName !== undefined) {
      // 昵称长度验证
      if (nickName.length < 1 || nickName.length > 20) {
        return {
          success: false,
          error: '昵称长度必须在1-20个字符之间'
        };
      }
      updateData.nickName = nickName;
    }

    if (gameNickName !== undefined) {
      // 游戏昵称长度验证（选填）
      if (gameNickName.length > 30) {
        return {
          success: false,
          error: '游戏昵称不能超过30个字符'
        };
      }
      updateData.gameNickName = gameNickName;
    }

    if (avatarUrl !== undefined) {
      updateData.avatarUrl = avatarUrl;
    }

    if (phone !== undefined) {
      // 手机号格式验证
      if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
        return {
          success: false,
          error: '手机号格式不正确'
        };
      }
      updateData.phone = phone;
    }

    // 新增字段
    if (gender !== undefined) {
      // 性别验证：0-未知，1-男，2-女
      if (![0, 1, 2].includes(gender)) {
        return {
          success: false,
          error: '性别参数不正确'
        };
      }
      updateData.gender = gender;
    }

    if (age !== undefined) {
      // 年龄验证
      if (age < 16 || age > 100) {
        return {
          success: false,
          error: '年龄必须在16-100岁之间'
        };
      }
      updateData.age = age;
    }

    if (bio !== undefined) {
      // 个人简介长度验证
      if (bio.length > 200) {
        return {
          success: false,
          error: '个人简介不能超过200个字符'
        };
      }
      updateData.bio = bio;
    }

    if (contactInfo !== undefined) {
      // 联系方式长度验证
      if (contactInfo.length > 100) {
        return {
          success: false,
          error: '联系方式不能超过100个字符'
        };
      }
      updateData.contactInfo = contactInfo;
    }

    // 更新用户信息
    console.log('🔄 [更新用户信息] 准备更新数据库，updateData:', updateData);
    await db.collection('users').doc(userId).update({
      data: updateData
    });

    // 返回更新后的用户信息
    const updatedUserResult = await db.collection('users').doc(userId).get();
    console.log('✅ [更新用户信息] 数据库更新成功');
    console.log('✅ [更新用户信息] 更新后的用户信息:', updatedUserResult.data);

    return {
      success: true,
      message: '更新成功',
      data: updatedUserResult.data
    };
  } catch (error) {
    console.error('更新用户信息失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
