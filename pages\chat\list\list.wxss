/* 聊天列表页面样式 - 科技主题版 */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0f1419 !important; /* 强制深色背景，避免白色闪烁 */
  background-color: #0f1419 !important;
}

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 0 10rpx var(--primary-color);
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.particle:nth-child(3) {
  top: 80%;
  left: 40%;
  animation-delay: 4s;
}

@keyframes dataFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-100rpx, -100rpx); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) scale(1); opacity: 0.7; }
  50% { transform: translateY(-20rpx) scale(1.2); opacity: 1; }
}

.container {
  height: 100vh;
  background: transparent;
  display: flex;
  flex-direction: column;
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 10rpx);
  box-sizing: border-box;
}







/* 加载状态 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid var(--bg-tertiary);
  border-top: 6rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 聊天列表 */
.chat-list {
  flex: 1;
  background: transparent;
  padding-top: 10rpx;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid var(--border-color);
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  margin: 0 20rpx 16rpx;
  border-radius: var(--radius-lg);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
}

.chat-item:active {
  background: var(--bg-secondary);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 212, 255, 0.15);
}

.chat-item:last-child {
  border-bottom: none;
}

/* 头像容器 */
.avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: 2rpx solid var(--primary-color);
}

.avatar.default-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
}

.avatar .avatar-text {
  font-size: 40rpx;
  color: var(--primary-color);
}

.role-badge {
  position: absolute;
  bottom: -4rpx;
  right: -4rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: var(--text-primary);
  font-weight: bold;
  border: 4rpx solid var(--bg-primary);
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.3);
}

.role-customer {
  background: var(--primary-color);
  box-shadow: 0 0 15rpx rgba(0, 212, 255, 0.5);
}

.role-accepter {
  background: var(--accent-color);
  box-shadow: 0 0 15rpx rgba(0, 255, 136, 0.5);
}

/* 聊天信息 */
.chat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.3);
}

.time {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.order-title {
  font-size: 28rpx;
  color: var(--text-secondary);
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-reward {
  font-size: 24rpx;
  color: var(--accent-color);
  font-weight: 500;
  text-shadow: 0 0 8rpx rgba(0, 255, 136, 0.3);
}

.last-message {
  font-size: 26rpx;
  color: var(--text-tertiary);
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 右侧箭头 */
.arrow {
  margin-left: 20rpx;
}

.arrow-icon {
  font-size: 28rpx;
  color: var(--text-tertiary);
}

/* 加载更多 */
.load-more {
  padding: 40rpx;
  text-align: center;
}

.load-more-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.no-more {
  padding: 40rpx;
  text-align: center;
}

.no-more-text {
  font-size: 26rpx;
  color: var(--text-tertiary);
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.empty-icon-text {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
  filter: drop-shadow(0 0 20rpx rgba(0, 212, 255, 0.3));
}

.empty-title {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.3);
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-tertiary);
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action {
  background: var(--primary-gradient);
  color: var(--text-primary);
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 8rpx 25rpx rgba(0, 212, 255, 0.3);
  transition: all 0.3s ease;
}

.empty-action:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 35rpx rgba(0, 212, 255, 0.4);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
