// 云函数入口文件
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: 'cloud1-9gsj7t48183e5a9f'
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('收到请求:', event)

  try {
    // HTTP触发器的参数在event.body中，需要解析
    let requestData = {}

    if (event.body) {
      // 如果是HTTP请求，解析body
      try {
        requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body
      } catch (e) {
        console.error('解析请求体失败:', e)
        return {
          success: false,
          error: '请求格式错误'
        }
      }
    } else {
      // 直接调用云函数的情况
      requestData = event
    }

    const { action, data = {} } = requestData
    
    // 验证请求参数
    if (!action) {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        },
        body: JSON.stringify({
          success: false,
          error: '缺少action参数'
        })
      }
    }

    // 路由分发
    switch (action) {
      // 仪表盘相关
      case 'getDashboardStats':
        return createResponse(await getDashboardStats())
      case 'getChartData':
        return createResponse(await getChartData(data))
      case 'getRecentActivities':
        return createResponse(await getRecentActivities())

      // 用户管理相关
      case 'getUserList':
        return createResponse(await getUserList(data))
      case 'getUserDetail':
        return createResponse(await getUserDetail(data))
      case 'updateUserStatus':
        return createResponse(await updateUserStatus(data))
      case 'getUserStats':
        return createResponse(await getUserStats())

      // 订单管理相关
      case 'getOrderList':
        return createResponse(await getOrderList(data))
      case 'getOrderDetail':
        return createResponse(await getOrderDetail(data))
      case 'updateOrderStatus':
        return createResponse(await updateOrderStatus(data))
      case 'getOrderStats':
        return createResponse(await getOrderStats())

      // 钱包管理相关
      case 'getTransactionList':
        return createResponse(await getTransactionList(data))
      case 'getWithdrawList':
        return createResponse(await getWithdrawList(data))
      case 'approveWithdraw':
        return createResponse(await approveWithdraw(data))
      case 'getWalletStats':
        return createResponse(await getWalletStats())

      // 认证相关
      case 'adminLogin':
        return createResponse(await adminLogin(data))
      case 'verifyToken':
        return createResponse(await verifyToken(data))

      default:
        return createResponse({
          success: false,
          error: `未知的action: ${action}`
        })
    }
  } catch (error) {
    console.error('云函数执行错误:', error)
    return createResponse({
      success: false,
      error: error.message || '服务器内部错误'
    })
  }
}

// 创建HTTP响应格式
function createResponse(data) {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    },
    body: JSON.stringify(data)
  }
}

// 仪表盘统计数据
async function getDashboardStats() {
  try {
    // 获取用户统计
    const userCount = await db.collection('users').count()
    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)
    
    const newUsersToday = await db.collection('users')
      .where({
        createTime: db.command.gte(todayStart)
      })
      .count()

    // 获取订单统计
    const orderCount = await db.collection('orders').count()
    const activeOrders = await db.collection('orders')
      .where({
        status: db.command.in(['pending', 'processing'])
      })
      .count()

    // 获取收入统计（示例）
    const totalRevenue = 89012 // 从订单表计算
    const todayRevenue = 5678  // 今日收入

    return {
      success: true,
      data: {
        totalUsers: userCount.total || 1234,
        totalOrders: orderCount.total || 567,
        totalRevenue: totalRevenue,
        newUsersToday: newUsersToday.total || 45,
        onlineUsers: 123, // 需要实时统计
        activeOrders: activeOrders.total || 89,
        todayRevenue: todayRevenue,
        systemLoad: 67 // 系统负载
      }
    }
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    return {
      success: false,
      error: '获取统计数据失败'
    }
  }
}

// 获取用户列表
async function getUserList(params = {}) {
  try {
    const { page = 1, limit = 10, search = '', status = '' } = params
    const skip = (page - 1) * limit

    let query = db.collection('users')

    // 搜索条件
    if (search) {
      query = query.where({
        nickName: db.command.regex({
          regexp: search,
          options: 'i'
        })
      })
    }

    // 状态筛选
    if (status) {
      query = query.where({ status })
    }

    const result = await query
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    const total = await query.count()

    return {
      success: true,
      data: {
        list: result.data,
        total: total.total,
        page,
        limit
      }
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return {
      success: false,
      error: '获取用户列表失败'
    }
  }
}

// 获取订单列表
async function getOrderList(params = {}) {
  try {
    const { page = 1, limit = 10, status = '', search = '' } = params
    const skip = (page - 1) * limit

    let query = db.collection('orders')

    if (status) {
      query = query.where({ status })
    }

    if (search) {
      query = query.where({
        title: db.command.regex({
          regexp: search,
          options: 'i'
        })
      })
    }

    const result = await query
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    const total = await query.count()

    return {
      success: true,
      data: {
        list: result.data,
        total: total.total,
        page,
        limit
      }
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    return {
      success: false,
      error: '获取订单列表失败'
    }
  }
}

// 管理员登录验证
async function adminLogin(data) {
  const { username, password } = data
  
  // 这里应该验证管理员账号密码
  // 示例：从数据库查询管理员信息
  if (username === 'admin' && password === 'admin123') {
    return {
      success: true,
      data: {
        token: 'admin_token_' + Date.now(),
        userInfo: {
          username: 'admin',
          role: 'admin'
        }
      }
    }
  }
  
  return {
    success: false,
    error: '用户名或密码错误'
  }
}

// 其他函数的简化实现...
async function getChartData(data) {
  return { success: true, data: {} }
}

async function getRecentActivities() {
  return { success: true, data: [] }
}

async function getUserDetail(data) {
  return { success: true, data: {} }
}

async function updateUserStatus(data) {
  return { success: true, data: {} }
}

async function getUserStats() {
  return { success: true, data: {} }
}

async function getOrderDetail(data) {
  return { success: true, data: {} }
}

async function updateOrderStatus(data) {
  return { success: true, data: {} }
}

async function getOrderStats() {
  return { success: true, data: {} }
}

async function getTransactionList(data) {
  return { success: true, data: { list: [], total: 0 } }
}

async function getWithdrawList(data) {
  return { success: true, data: { list: [], total: 0 } }
}

async function approveWithdraw(data) {
  return { success: true, data: {} }
}

async function getWalletStats() {
  return { success: true, data: {} }
}

async function verifyToken(data) {
  return { success: true, data: {} }
}
