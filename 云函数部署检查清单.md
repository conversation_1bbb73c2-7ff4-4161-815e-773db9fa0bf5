# 云函数部署检查清单

## 🎯 问题现状
- 前端正确发送: `platformType: "mobile"` ✅
- API调用成功: `{success: true, message: "订单创建成功"}` ✅
- 云函数日志缺失: 没有看到调试输出 ❌
- 数据库存储错误: `platformType: undefined` ❌

## 🔧 云函数部署步骤

### 1. 检查云函数代码
确认 `cloudfunctions/createOrder/index.js` 包含以下调试代码：
```javascript
console.log('🔍 [云函数] 平台类型:', platformType);
console.log('🔍 [云函数] 平台类型类型:', typeof platformType);
console.log('🔍 [云函数] 完整event对象:', JSON.stringify(event, null, 2));
```

### 2. 重新部署云函数
1. 在微信开发者工具中打开项目
2. 右键点击 `cloudfunctions/createOrder` 文件夹
3. 选择 **"上传并部署：云端安装依赖"**
4. 等待部署完成

### 3. 检查部署状态
1. 打开微信开发者工具的 **"云开发"** 控制台
2. 进入 **"云函数"** 页面
3. 确认 `createOrder` 函数状态为 **"部署成功"**
4. 检查最后更新时间是否为最新

### 4. 查看云函数日志
1. 在云开发控制台中点击 **"日志"**
2. 选择 `createOrder` 云函数
3. 查看最近的调用日志

## 🧪 测试验证

### 测试步骤
1. 重新部署云函数后
2. 发布一个新的手游订单
3. 观察以下日志输出：

#### 前端日志（应该看到）
```
🔍 [API调用] 创建订单，数据: {platformType: "mobile", ...}
🔍 [API调用] 创建订单结果: {success: true, ...}
🔍 [API调用] 返回的订单数据: {...}
```

#### 云函数日志（应该看到）
```
🔍 [云函数] 平台类型: mobile
🔍 [云函数] 平台类型类型: string
🔍 [云函数] 格式判断: isNewFormat: true
🔍 [云函数] 完整订单数据: {platformType: "mobile", ...}
```

#### 数据库结果（期望看到）
```
🔍 [订单列表实时格式化] 原始platformType字段: mobile
```

## 🚨 故障排除

### 如果云函数日志仍然没有输出

#### 1. 检查云函数语法
```bash
# 在 cloudfunctions/createOrder 目录下运行
node -c index.js
```

#### 2. 检查云函数权限
- 确认云函数有数据库读写权限
- 确认云函数环境配置正确

#### 3. 手动测试云函数
在云开发控制台中手动调用云函数：
```json
{
  "title": "测试订单",
  "content": "测试内容", 
  "reward": 10,
  "platformType": "mobile",
  "serviceType": "duration",
  "duration": 1
}
```

#### 4. 检查API调用
确认 `utils/api.js` 中的 `createOrder` 方法正确：
```javascript
async createOrder(orderData) {
  return await this.callFunction('createOrder', orderData);
}
```

### 如果数据库仍然存储 undefined

#### 1. 检查格式判断逻辑
观察云函数日志中的格式判断结果：
- `isNewFormat: true` - 应该使用新格式处理
- `isOldFormat: true` - 可能使用了旧格式处理

#### 2. 检查数据传递
确认云函数接收到的完整 event 对象包含 platformType

#### 3. 检查数据库写入
确认 orderData 对象在写入数据库前包含正确的 platformType

## 📋 检查清单

- [ ] 云函数代码包含调试日志
- [ ] 云函数重新部署成功
- [ ] 云函数状态显示"部署成功"
- [ ] 发布测试订单
- [ ] 前端API调用日志正常
- [ ] 云函数调试日志出现
- [ ] 数据库platformType字段正确
- [ ] 其他用户能看到正确的平台类型

## 🎯 预期结果

修复完成后，应该看到：
1. **云函数日志**: 完整的调试输出
2. **数据库存储**: `platformType: "mobile"`
3. **前端显示**: 所有用户都能看到"平台类型：手游"

## 📞 如需帮助

如果按照以上步骤仍然无法解决问题，请提供：
1. 云函数部署状态截图
2. 云函数日志截图
3. 前端完整日志输出
4. 数据库中订单数据截图
