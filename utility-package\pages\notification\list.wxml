<!--通知中心页面-->
<navigation-bar title="通知中心" back="{{true}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
  </view>
</view>

<view class="notification-container page-with-custom-nav">
  <!-- 头部统计和操作 -->
  <view class="header-section cyber-card">
    <view class="card-glow"></view>
    <view class="header-content">
      <view class="stats-info">
        <text class="unread-count">{{unreadCount}}</text>
        <text class="stats-label">条未读</text>
      </view>
      <view class="header-actions">
        <view class="action-btn" bindtap="markAllAsRead" wx:if="{{unreadCount > 0}}">
          <text class="action-text">全部已读</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-section cyber-card">
    <view class="card-glow"></view>
    <view class="filter-content">
      <text class="filter-label">筛选：</text>
      <picker
        class="filter-picker"
        mode="selector"
        range="{{filterOptions}}"
        range-key="label"
        bindchange="onFilterChange"
      >
        <view class="picker-display">
          <text class="picker-text">全部</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 通知列表 -->
  <view class="notification-list">
    <view 
      class="notification-item cyber-card {{!item.isRead ? 'unread' : ''}}"
      wx:for="{{notificationList}}"
      wx:key="_id"
      data-notification="{{item}}"
      bindtap="onNotificationTap"
    >
      <view class="card-glow"></view>
      <view class="notification-content">
        <view class="notification-header">
          <view class="type-info">
            <text class="type-icon">{{getTypeIcon(item.type)}}</text>
            <text class="type-text">{{getTypeText(item.type)}}</text>
          </view>
          <view class="time-info">
            <text class="time-text">{{item.timeText}}</text>
            <view class="unread-dot" wx:if="{{!item.isRead}}"></view>
          </view>
        </view>
        
        <view class="notification-body">
          <text class="notification-title">{{item.title}}</text>
          <text class="notification-desc">{{item.content}}</text>
        </view>
        
        <view class="notification-actions" catchtap="stopPropagation">
          <view 
            class="delete-btn"
            data-notification="{{item}}"
            bindtap="deleteNotification"
          >
            <text class="delete-text">删除</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state cyber-card" wx:if="{{notificationList.length === 0 && !loading}}">
      <view class="card-glow"></view>
      <view class="empty-content">
        <text class="empty-icon">🔔</text>
        <text class="empty-text">暂无通知</text>
        <text class="empty-desc">您的通知消息会在这里显示</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && notificationList.length > 0}}">
      <text class="no-more-text">没有更多通知了</text>
    </view>
  </view>
</view>
