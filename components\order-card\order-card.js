// 统一订单卡片组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 订单数据
    orderData: {
      type: Object,
      value: {}
    },
    // 是否显示详细信息
    showDetail: {
      type: Boolean,
      value: true
    },
    // 卡片模式：'normal' | 'compact'
    mode: {
      type: String,
      value: 'normal'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    displayTitle: '',
    formattedCreateTime: ''
  },

  /**
   * 组件的观察器
   */
  observers: {
    'orderData': function(orderData) {
      if (orderData) {
        this.updateDisplayTitle();
        this.updateFormattedTime();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 更新显示标题
    updateDisplayTitle() {
      const orderData = this.data.orderData;
      if (!orderData) {
        this.setData({ displayTitle: '' });
        return;
      }

      const originalTitle = orderData.title || orderData.gameInfo?.gameName || '5元战神';
      const maxLength = 10;

      let displayTitle = originalTitle;
      if (originalTitle.length > maxLength) {
        displayTitle = originalTitle.substring(0, maxLength) + '...';
      }

      this.setData({ displayTitle });
    },

    // 更新格式化时间
    updateFormattedTime() {
      const orderData = this.data.orderData;

      if (!orderData || !orderData.createTime) {
        this.setData({ formattedCreateTime: '未知' });
        return;
      }

      const formattedTime = this.formatTime(orderData.createTime);
      this.setData({ formattedCreateTime: formattedTime });
    },

    // 格式化时间
    formatTime(dateStr) {
      if (!dateStr || typeof dateStr === 'object') {
        return '未知';
      }

      const date = new Date(dateStr);

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('Invalid date:', dateStr);
        return '未知';
      }

      const now = new Date();
      const diff = now - date;

      if (diff < 60000) { // 1分钟内
        return '刚刚';
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`;
      } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`;
      } else {
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${month}-${day}`;
      }
    },

    // 卡片点击事件
    onCardTap(e) {
      const { id } = e.currentTarget.dataset;
      this.triggerEvent('cardtap', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 编辑订单
    onEditOrder(e) {
      console.log('onEditOrder 被调用', e);

      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }

      // 安全地获取数据
      const id = e && e.currentTarget && e.currentTarget.dataset ? e.currentTarget.dataset.id : null;
      if (!id) {
        console.error('无法获取订单ID');
        return;
      }



      this.triggerEvent('editorder', {
        orderId: id,
        orderData: this.data.orderData
      });
    },



    // 取消订单
    onCancelOrder(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;

      // 直接触发事件到父页面，让父页面处理取消原因选择
      this.triggerEvent('cancelorder', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 查看详情
    onViewDetail(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;
      console.log('🔍 [订单卡片] 查看详情点击:', {
        datasetId: id,
        orderData_id: this.data.orderData?._id,
        orderData_orderNo: this.data.orderData?.orderNo,
        orderData_orderId: this.data.orderData?.orderId,
        fullOrderData: this.data.orderData
      });
      this.triggerEvent('viewdetail', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 抢单
    onGrabOrder(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;
      const order = this.data.orderData;



      // 构建服务时间显示文本
      let serviceText = '';
      if (order.serviceType === 'rounds' || (order.rounds && !order.serviceType)) {
        serviceText = `局数：${order.rounds || 5}局`;
      } else {
        serviceText = `时长：${order.duration || 2}小时`;
      }

      wx.showModal({
        title: '确认抢单',
        content: `确定要抢这个战术任务吗？\n金额：¥${order.totalAmount || order.reward || 0}\n${serviceText}`,
        confirmText: '确认抢单',
        cancelText: '取消',
        confirmColor: '#4ab37e',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('graborder', {
              orderId: id,
              orderData: order
            });
          }
        }
      });
    },

    // 联系接单者
    onContactAccepter(e) {
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;

      this.triggerEvent('contactaccepter', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 进入聊天
    onEnterChat(e) {
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;

      this.triggerEvent('enterchat', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 评价订单
    onEvaluateOrder(e) {
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;

      this.triggerEvent('evaluateorder', {
        orderId: id,
        orderData: this.data.orderData
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树时执行
      console.log('order-card attached', this.data.orderData);
      this.updateDisplayTitle();
      this.updateFormattedTime();
    }
  }
});
