// 获取订单统计数据云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const {
    action,
    timeRange = 'month', // week, month, quarter, year
    type = 'overview' // overview, income, service, trend
  } = event;

  try {
    // 处理特殊action：今日统计（不需要用户身份验证）
    if (action === 'getTodayStats') {
      return await getTodayOrderStats();
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 计算时间范围
    const timeFilter = getTimeFilter(timeRange);

    let statisticsData = {};

    switch (type) {
      case 'overview':
        statisticsData = await getOverviewStatistics(user._id, timeFilter);
        break;
      case 'income':
        statisticsData = await getIncomeStatistics(user._id, timeFilter);
        break;
      case 'service':
        statisticsData = await getServiceStatistics(user._id, timeFilter);
        break;
      case 'trend':
        statisticsData = await getTrendStatistics(user._id, timeFilter);
        break;
      default:
        statisticsData = await getOverviewStatistics(user._id, timeFilter);
    }

    return {
      success: true,
      data: statisticsData
    };
  } catch (error) {
    console.error('获取订单统计失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 获取今日订单统计（全局统计，不限用户）
async function getTodayOrderStats() {
  try {
    // 获取今日开始时间（中国时区）
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

    console.log('📊 [今日统计] 统计时间范围:', {
      start: todayStart,
      end: todayEnd
    });

    // 检查orders集合是否存在
    let ordersExist = true;
    try {
      await db.collection('orders').limit(1).get();
    } catch (e) {
      console.log('📊 [今日统计] orders集合不存在');
      ordersExist = false;
    }

    if (!ordersExist) {
      return {
        success: true,
        data: {
          total: 0,
          pending: 0,
          accepted: 0,
          in_progress: 0,
          completed: 0,
          cancelled: 0
        }
      };
    }

    // 获取今日所有订单
    const todayOrdersResult = await db.collection('orders')
      .where({
        createTime: _.gte(todayStart).and(_.lt(todayEnd))
      })
      .get();

    const todayOrders = todayOrdersResult.data;
    console.log('📊 [今日统计] 今日订单数量:', todayOrders.length);

    // 统计各状态订单数量
    const stats = {
      total: todayOrders.length,
      pending: 0,
      accepted: 0,
      in_progress: 0,
      completed: 0,
      cancelled: 0
    };

    todayOrders.forEach(order => {
      const status = order.status || 'pending';
      if (stats.hasOwnProperty(status)) {
        stats[status]++;
      }
    });

    console.log('📊 [今日统计] 统计结果:', stats);

    return {
      success: true,
      data: stats
    };

  } catch (error) {
    console.error('❌ [今日统计] 获取今日订单统计失败:', error);
    throw error;
  }
}

// 获取时间过滤条件
function getTimeFilter(timeRange) {
  const now = new Date();
  let startTime, endTime = now;

  switch (timeRange) {
    case 'week':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      startTime = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case 'quarter':
      const quarterStart = Math.floor(now.getMonth() / 3) * 3;
      startTime = new Date(now.getFullYear(), quarterStart, 1);
      break;
    case 'year':
      startTime = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      startTime = new Date(now.getFullYear(), now.getMonth(), 1);
  }

  return {
    createTime: _.and(_.gte(startTime), _.lte(endTime))
  };
}

// 获取概览统计
async function getOverviewStatistics(userId, timeFilter) {
  // 基础查询条件
  const baseCondition = _.or([
    { customerId: userId },
    { accepterId: userId }
  ]);

  const whereCondition = { ...baseCondition, ...timeFilter };

  // 获取订单总数
  const totalOrdersResult = await db.collection('orders')
    .where(whereCondition)
    .count();

  // 获取各状态订单数量
  const statusCounts = await Promise.all([
    db.collection('orders').where({ ...whereCondition, status: 'pending' }).count(),
    db.collection('orders').where({ ...whereCondition, status: 'in_progress' }).count(),
    db.collection('orders').where({ ...whereCondition, status: 'completed' }).count(),
    db.collection('orders').where({ ...whereCondition, status: 'cancelled' }).count()
  ]);

  // 获取收入统计（作为接单者）
  const incomeOrdersResult = await db.collection('orders')
    .where({
      accepterId: userId,
      status: 'completed',
      ...timeFilter
    })
    .get();

  const totalIncome = incomeOrdersResult.data.reduce((sum, order) => {
    return sum + (order.reward || order.totalAmount || 0);
  }, 0);

  // 获取支出统计（作为发单者）
  const expenseOrdersResult = await db.collection('orders')
    .where({
      customerId: userId,
      status: 'completed',
      ...timeFilter
    })
    .get();

  const totalExpense = expenseOrdersResult.data.reduce((sum, order) => {
    return sum + (order.reward || order.totalAmount || 0);
  }, 0);

  // 计算服务时长
  const serviceHours = incomeOrdersResult.data.reduce((sum, order) => {
    return sum + (order.duration || 0);
  }, 0);

  return {
    overview: {
      totalOrders: totalOrdersResult.total || 0,
      pendingOrders: statusCounts[0].total || 0,
      inProgressOrders: statusCounts[1].total || 0,
      completedOrders: statusCounts[2].total || 0,
      cancelledOrders: statusCounts[3].total || 0,
      totalIncome,
      totalExpense,
      netIncome: totalIncome - totalExpense,
      serviceHours,
      completionRate: statusCounts[2].total > 0 ? 
        ((statusCounts[2].total / (statusCounts[2].total + statusCounts[3].total)) * 100).toFixed(1) : 0
    }
  };
}

// 获取收入统计
async function getIncomeStatistics(userId, timeFilter) {
  const incomeOrdersResult = await db.collection('orders')
    .where({
      accepterId: userId,
      status: 'completed',
      ...timeFilter
    })
    .orderBy('createTime', 'asc')
    .get();

  // 按日期分组统计
  const dailyIncome = {};
  const monthlyIncome = {};

  incomeOrdersResult.data.forEach(order => {
    const date = new Date(order.createTime);
    const dateKey = date.toISOString().split('T')[0];
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    const income = order.reward || order.totalAmount || 0;

    dailyIncome[dateKey] = (dailyIncome[dateKey] || 0) + income;
    monthlyIncome[monthKey] = (monthlyIncome[monthKey] || 0) + income;
  });

  // 转换为图表数据格式
  const dailyData = Object.entries(dailyIncome).map(([date, amount]) => ({
    date,
    amount,
    label: new Date(date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  }));

  const monthlyData = Object.entries(monthlyIncome).map(([month, amount]) => ({
    month,
    amount,
    label: month
  }));

  return {
    income: {
      total: incomeOrdersResult.data.reduce((sum, order) => sum + (order.reward || order.totalAmount || 0), 0),
      orderCount: incomeOrdersResult.data.length,
      averagePerOrder: incomeOrdersResult.data.length > 0 ? 
        (incomeOrdersResult.data.reduce((sum, order) => sum + (order.reward || order.totalAmount || 0), 0) / incomeOrdersResult.data.length).toFixed(2) : 0,
      dailyData,
      monthlyData
    }
  };
}

// 获取服务统计
async function getServiceStatistics(userId, timeFilter) {
  const serviceOrdersResult = await db.collection('orders')
    .where({
      accepterId: userId,
      status: 'completed',
      ...timeFilter
    })
    .get();

  // 按服务类型统计
  const serviceTypeStats = {};
  let totalHours = 0;
  let totalRounds = 0;

  serviceOrdersResult.data.forEach(order => {
    const type = order.serviceType || 'duration';
    if (!serviceTypeStats[type]) {
      serviceTypeStats[type] = {
        count: 0,
        totalAmount: 0,
        totalDuration: 0,
        totalRounds: 0
      };
    }

    serviceTypeStats[type].count++;
    serviceTypeStats[type].totalAmount += (order.reward || order.totalAmount || 0);
    
    if (type === 'duration') {
      serviceTypeStats[type].totalDuration += (order.duration || 0);
      totalHours += (order.duration || 0);
    } else if (type === 'rounds') {
      serviceTypeStats[type].totalRounds += (order.rounds || 0);
      totalRounds += (order.rounds || 0);
    }
  });

  return {
    service: {
      totalOrders: serviceOrdersResult.data.length,
      totalHours,
      totalRounds,
      averageHoursPerOrder: serviceOrdersResult.data.length > 0 ? (totalHours / serviceOrdersResult.data.length).toFixed(1) : 0,
      serviceTypeStats
    }
  };
}

// 获取趋势统计
async function getTrendStatistics(userId, timeFilter) {
  // 获取最近30天的数据用于趋势分析
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const trendTimeFilter = {
    createTime: _.gte(thirtyDaysAgo)
  };

  const trendOrdersResult = await db.collection('orders')
    .where({
      accepterId: userId,
      ...trendTimeFilter
    })
    .orderBy('createTime', 'asc')
    .get();

  // 按日期统计订单数量和收入
  const dailyTrend = {};
  
  for (let i = 0; i < 30; i++) {
    const date = new Date(thirtyDaysAgo.getTime() + i * 24 * 60 * 60 * 1000);
    const dateKey = date.toISOString().split('T')[0];
    dailyTrend[dateKey] = {
      date: dateKey,
      orders: 0,
      income: 0,
      label: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    };
  }

  trendOrdersResult.data.forEach(order => {
    const dateKey = new Date(order.createTime).toISOString().split('T')[0];
    if (dailyTrend[dateKey]) {
      dailyTrend[dateKey].orders++;
      if (order.status === 'completed') {
        dailyTrend[dateKey].income += (order.reward || order.totalAmount || 0);
      }
    }
  });

  return {
    trend: {
      dailyTrend: Object.values(dailyTrend)
    }
  };
}
