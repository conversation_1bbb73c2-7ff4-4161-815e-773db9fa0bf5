# 部署后台管理API云函数

## 部署步骤

### 1. 确保云函数代码是最新的
```bash
cd cloudfunctions/adminApi
```

### 2. 检查package.json依赖
```json
{
  "name": "adminapi",
  "version": "1.0.0",
  "description": "后台管理系统API云函数",
  "main": "index.js",
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  }
}
```

### 3. 使用微信开发者工具部署
1. 打开微信开发者工具
2. 选择云开发控制台
3. 进入云函数管理
4. 找到 `adminApi` 函数
5. 点击"上传并部署"

### 4. 配置HTTP触发器
1. 在云函数详情页面
2. 点击"触发器"标签
3. 添加HTTP触发器
4. 记录触发器URL

### 5. 更新前端配置
在 `后台管理系统/admin-dashboard/src/services/cloudApi.ts` 中更新：
```typescript
const CLOUD_BASE_URL = 'YOUR_HTTP_TRIGGER_URL';
```

## 测试部署结果

### 使用测试脚本
```bash
cd 后台管理系统
node test-dashboard-api.js
```

### 手动测试API
```bash
curl -X POST "YOUR_HTTP_TRIGGER_URL" \
  -H "Content-Type: application/json" \
  -H "X-Admin-Request: true" \
  -d '{"action": "getDashboardStats"}'
```

## 预期响应格式

### 仪表盘统计数据
```json
{
  "success": true,
  "data": {
    "totalUsers": 1250,
    "activeUsers": 890,
    "totalOrders": 2340,
    "completedOrders": 2100,
    "totalRevenue": 125000,
    "averageScore": 4.2,
    "newUsersToday": 15,
    "ordersToday": 28,
    "pendingEvaluations": 5,
    "evaluationCount": 450
  }
}
```

### 图表数据
```json
{
  "success": true,
  "data": {
    "chartData": [
      {"date": "2024-01-01", "value": 25},
      {"date": "2024-01-02", "value": 30},
      ...
    ]
  }
}
```

### 最近活动
```json
{
  "success": true,
  "data": {
    "activities": [
      {
        "id": "order_123",
        "type": "order",
        "description": "新订单：小程序开发需求",
        "timestamp": "2024-01-07T10:30:00Z"
      },
      ...
    ]
  }
}
```

## 故障排除

### 1. 云函数部署失败
- 检查代码语法错误
- 确认依赖包版本兼容性
- 查看云开发控制台错误日志

### 2. HTTP触发器无法访问
- 确认触发器已正确配置
- 检查CORS设置
- 验证请求头格式

### 3. 数据库查询失败
- 确认数据库集合存在
- 检查字段名称是否正确
- 验证数据库权限设置

### 4. 前端无法获取数据
- 检查API URL配置
- 验证请求格式
- 查看浏览器网络面板错误信息
