// 发送通知云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { 
    type, 
    targetUserId, 
    templateId, 
    data, 
    page,
    miniprogramState = 'formal'
  } = event;

  try {
    // 验证必填参数
    if (!type || !targetUserId) {
      return {
        success: false,
        error: '通知类型和目标用户ID不能为空'
      };
    }

    // 获取目标用户信息
    const userResult = await db.collection('users').doc(targetUserId).get();
    if (!userResult.data) {
      return {
        success: false,
        error: '目标用户不存在'
      };
    }

    const targetUser = userResult.data;

    // 创建通知记录
    const notificationData = {
      type,
      targetUserId,
      senderId: wxContext.OPENID,
      title: data.title || '',
      content: data.content || '',
      data: data.extraData || {},
      page: page || '',
      status: 'pending',
      createTime: new Date(),
      readTime: null
    };

    const notificationResult = await db.collection('notifications').add({
      data: notificationData
    });

    let sendResult = null;

    // 根据通知类型发送不同的通知
    switch (type) {
      case 'orderStatus':
        sendResult = await sendOrderStatusNotification(targetUser, data, templateId, page, miniprogramState);
        break;
      case 'newOrder':
        sendResult = await sendNewOrderNotification(targetUser, data, templateId, page, miniprogramState);
        break;
      case 'chatMessage':
        sendResult = await sendChatMessageNotification(targetUser, data, templateId, page, miniprogramState);
        break;
      case 'system':
        sendResult = await sendSystemNotification(targetUser, data, templateId, page, miniprogramState);
        break;
      default:
        return {
          success: false,
          error: '不支持的通知类型'
        };
    }

    // 更新通知状态
    const updateData = {
      status: sendResult.success ? 'sent' : 'failed',
      sendTime: new Date()
    };

    if (!sendResult.success) {
      updateData.errorMessage = sendResult.error;
    }

    await db.collection('notifications').doc(notificationResult._id).update({
      data: updateData
    });

    return {
      success: true,
      data: {
        notificationId: notificationResult._id,
        sendResult
      }
    };
  } catch (error) {
    console.error('发送通知失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 发送订单状态变更通知
async function sendOrderStatusNotification(targetUser, data, templateId, page, miniprogramState) {
  if (!templateId) {
    return { success: false, error: '缺少模板ID' };
  }

  const { orderNo, status, statusText, orderTitle } = data;

  try {
    const result = await cloud.openapi.subscribeMessage.send({
      touser: targetUser.openid,
      template_id: templateId,
      page: page || 'order-package/pages/detail/detail',
      miniprogram_state: miniprogramState,
      data: {
        thing1: {
          value: orderTitle || '订单'
        },
        character_string2: {
          value: orderNo || ''
        },
        phrase3: {
          value: statusText || '已更新'
        },
        time4: {
          value: new Date().toLocaleString('zh-CN')
        }
      }
    });

    return { success: true, data: result };
  } catch (error) {
    console.error('发送订单状态通知失败:', error);
    return { success: false, error: error.message };
  }
}

// 发送新订单通知
async function sendNewOrderNotification(targetUser, data, templateId, page, miniprogramState) {
  if (!templateId) {
    return { success: false, error: '缺少模板ID' };
  }

  const { orderTitle, reward, serviceType } = data;

  try {
    const result = await cloud.openapi.subscribeMessage.send({
      touser: targetUser.openid,
      template_id: templateId,
      page: page || 'pages/order/list/list?mode=grab',
      miniprogram_state: miniprogramState,
      data: {
        thing1: {
          value: orderTitle || '新订单'
        },
        amount2: {
          value: `¥${reward || 0}`
        },
        thing3: {
          value: serviceType || '服务'
        },
        time4: {
          value: new Date().toLocaleString('zh-CN')
        }
      }
    });

    return { success: true, data: result };
  } catch (error) {
    console.error('发送新订单通知失败:', error);
    return { success: false, error: error.message };
  }
}

// 发送聊天消息通知
async function sendChatMessageNotification(targetUser, data, templateId, page, miniprogramState) {
  if (!templateId) {
    return { success: false, error: '缺少模板ID' };
  }

  const { senderName, content, chatRoomId } = data;

  try {
    const result = await cloud.openapi.subscribeMessage.send({
      touser: targetUser.openid,
      template_id: templateId,
      page: page || `chat-package/pages/room/room?roomId=${chatRoomId}`,
      miniprogram_state: miniprogramState,
      data: {
        name1: {
          value: senderName || '用户'
        },
        thing2: {
          value: content || '发送了一条消息'
        },
        time3: {
          value: new Date().toLocaleString('zh-CN')
        }
      }
    });

    return { success: true, data: result };
  } catch (error) {
    console.error('发送聊天消息通知失败:', error);
    return { success: false, error: error.message };
  }
}

// 发送系统通知
async function sendSystemNotification(targetUser, data, templateId, page, miniprogramState) {
  if (!templateId) {
    return { success: false, error: '缺少模板ID' };
  }

  const { title, content } = data;

  try {
    const result = await cloud.openapi.subscribeMessage.send({
      touser: targetUser.openid,
      template_id: templateId,
      page: page || 'pages/index/index',
      miniprogram_state: miniprogramState,
      data: {
        thing1: {
          value: title || '系统通知'
        },
        thing2: {
          value: content || '您有新的系统消息'
        },
        time3: {
          value: new Date().toLocaleString('zh-CN')
        }
      }
    });

    return { success: true, data: result };
  } catch (error) {
    console.error('发送系统通知失败:', error);
    return { success: false, error: error.message };
  }
}
