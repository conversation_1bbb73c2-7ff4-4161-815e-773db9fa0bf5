/**
 * 更新聊天室lastMessage字段的云函数
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { chatRoomId, messageData } = event;

  try {

    // 验证参数
    if (!chatRoomId) {
      return {
        success: false,
        error: '缺少聊天室ID'
      };
    }

    if (!messageData || !messageData.content) {
      return {
        success: false,
        error: '缺少消息数据'
      };
    }

    // 检查聊天室是否存在
    const chatRoomResult = await db.collection('chatRooms').doc(chatRoomId).get();

    if (!chatRoomResult.data) {
      return {
        success: false,
        error: '聊天室不存在'
      };
    }

    // 准备更新数据
    const newLastMessage = {
      content: messageData.content.trim(),
      type: messageData.type || 'text',
      senderId: messageData.senderId,
      createTime: messageData.createTime || new Date()
    };

    // 执行更新 - 使用set而不是update来避免null字段问题
    if (chatRoomResult.data.lastMessage === null) {
      // 当前lastMessage为null，使用set方式更新
      const currentData = chatRoomResult.data;
      delete currentData.lastMessage;
      delete currentData._id; // 删除_id字段，避免冲突

      await db.collection('chatRooms').doc(chatRoomId).set({
        data: {
          ...currentData,
          lastMessage: newLastMessage,
          updateTime: new Date()
        }
      });
    } else {
      // 当前lastMessage不为null，使用update方式更新
      await db.collection('chatRooms').doc(chatRoomId).update({
        data: {
          lastMessage: newLastMessage,
          updateTime: new Date()
        }
      });
    }

    // 验证更新是否成功
    const verifyResult = await db.collection('chatRooms').doc(chatRoomId).get();
    const updatedLastMessage = verifyResult.data.lastMessage;

    if (updatedLastMessage && updatedLastMessage.content === messageData.content.trim()) {
      return {
        success: true,
        data: {
          chatRoomId: chatRoomId,
          lastMessage: updatedLastMessage,
          updateTime: verifyResult.data.updateTime
        },
        message: '聊天室lastMessage更新成功'
      };
    } else {
      return {
        success: false,
        error: '更新验证失败'
      };
    }

  } catch (error) {
    console.error('❌ [更新] 云函数执行失败:', error);
    return {
      success: false,
      error: error.message || '服务器内部错误'
    };
  }
};
