/* 科技感个人中心样式 */

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: var(--cyber-blue);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
  box-shadow: 0 0 10rpx var(--cyber-blue);
}

.particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 1s;
}

.particle:nth-child(3) {
  top: 40%;
  left: 60%;
  animation-delay: 2s;
}

.particle:nth-child(4) {
  top: 80%;
  left: 30%;
  animation-delay: 1.5s;
}

.particle:nth-child(5) {
  top: 10%;
  left: 90%;
  animation-delay: 0.5s;
}

@keyframes dataFlow {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-100rpx) translateY(-100rpx); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

.profile-container {
  min-height: 100vh;
  background: transparent;
  padding: var(--space-lg);
  /* 为底部导航栏预留空间，避免退出登录按钮被遮挡 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom) + var(--space-lg));
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.profile-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 10rpx);
}

/* 科技感卡片样式 */
.cyber-card {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

.card-glow {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(0, 212, 255, 0.1) 50%,
    transparent 70%);
  border-radius: var(--radius-xl);
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.user-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--space-xl);
  position: relative;
  z-index: 1;
  min-height: 120rpx;
}

.avatar-container {
  position: relative;
  margin-right: var(--space-lg);
  flex-shrink: 0;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  object-fit: cover;
}

.default-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border: 2rpx solid var(--primary-color);
}

.avatar-text {
  font-size: 40rpx;
  color: var(--primary-color);
}

/* 完全移除头像外圈效果 */

/* 移除旋转动画关键帧 */

/* 移除头像点击缩放效果 */

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 0;
  padding-top: var(--space-xs);
}

.user-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: var(--space-sm);
  display: block;
  letter-spacing: 0.5rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.3;
  max-width: 100%;
}

.game-nickname {
  font-size: 24rpx;
  font-weight: 500;
  color: rgba(0, 212, 255, 0.8);
  margin-bottom: var(--space-sm);
  display: block;
  letter-spacing: 0.3rpx;
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.3);
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.2;
}

.user-basic-info {
  display: flex;
  gap: var(--space-md);
  margin-bottom: var(--space-sm);
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  flex-shrink: 0;
}

.info-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.info-value {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.user-status {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  margin-top: var(--space-xs);
}

.status-badge {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: 6rpx var(--space-sm);
  border-radius: var(--radius-md);
  align-self: flex-start;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  position: relative;
  overflow: hidden;
}

.status-badge.verified {
  background: rgba(34, 197, 94, 0.2);
  border: 1rpx solid rgba(34, 197, 94, 0.5);
  color: #22c55e;
  box-shadow: 0 0 10rpx rgba(34, 197, 94, 0.3);
}

.status-badge.unverified {
  background: rgba(251, 191, 36, 0.2);
  border: 1rpx solid rgba(251, 191, 36, 0.5);
  color: #fbbf24;
  box-shadow: 0 0 10rpx rgba(251, 191, 36, 0.3);
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-size: 22rpx;
}

.credit-score {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: var(--space-xs);
  text-shadow: 0 0 5rpx rgba(0, 212, 255, 0.3);
}

.user-actions {
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
  margin-left: var(--space-sm);
  padding-top: var(--space-xs);
}

/* 科技感按钮样式 */
.cyber-btn {
  position: relative;
  background: rgba(0, 212, 255, 0.1);
  border: 2rpx solid rgba(0, 212, 255, 0.5);
  border-radius: var(--radius-md);
  padding: var(--space-xs) var(--space-md);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 80rpx;
  /* 添加flex布局使文字居中 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: 24rpx;
  font-weight: 600;
  color: var(--cyber-blue);
  position: relative;
  z-index: 2;
  letter-spacing: 0.5rpx;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(0, 212, 255, 0.3),
    transparent);
  transition: left 0.5s ease;
}

.cyber-btn:active {
  transform: scale(0.98);
  border-color: var(--cyber-blue);
  box-shadow:
    0 0 20rpx rgba(0, 212, 255, 0.5),
    inset 0 0 20rpx rgba(0, 212, 255, 0.1);
}

.cyber-btn:active .btn-glow {
  left: 100%;
}

/* 科技感用户统计 */
.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: var(--space-xl);
  border-top: 1rpx solid rgba(0, 212, 255, 0.3);
  position: relative;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: var(--space-md);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

.stat-icon {
  font-size: 32rpx;
  margin-bottom: var(--space-xs);
  filter: drop-shadow(0 0 5rpx rgba(0, 212, 255, 0.5));
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: var(--space-xs);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 0.5rpx;
}

.stat-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle,
    rgba(0, 212, 255, 0.1) 0%,
    transparent 70%);
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-item:active .stat-glow {
  opacity: 1;
}

/* 详细信息卡片 */
.detail-info-card {
  margin-bottom: var(--space-lg);
}

.detail-section {
  position: relative;
  z-index: 1;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--cyber-blue);
  margin-right: var(--space-md);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.title-line {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(90deg, var(--cyber-blue), transparent);
}

.detail-item {
  margin-bottom: var(--space-lg);
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.label-icon {
  font-size: 28rpx;
  margin-right: var(--space-sm);
  filter: drop-shadow(0 0 5rpx rgba(0, 212, 255, 0.5));
}

.label-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.detail-content {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  padding: var(--space-md);
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  border-left: 3rpx solid var(--cyber-blue);
}

/* 科技感菜单区域 */
.menu-section {
  margin-bottom: var(--space-lg);
}

.menu-group {
  margin-bottom: var(--space-lg);
}

.cyber-menu-item {
  display: flex;
  align-items: center;
  padding: var(--space-xl);
  position: relative;
  transition: all 0.3s ease;
  border-bottom: none;
}

.cyber-menu-item:active {
  transform: translateX(10rpx);
  background: rgba(0, 212, 255, 0.05);
}

.menu-divider {
  height: 1rpx;
  background: linear-gradient(90deg,
    transparent,
    rgba(0, 212, 255, 0.3),
    transparent);
  margin: 0 var(--space-lg);
}

.menu-icon-container {
  position: relative;
  margin-right: var(--space-lg);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon-emoji {
  font-size: 36rpx;
  filter: drop-shadow(0 0 5rpx rgba(0, 212, 255, 0.5));
}

.icon-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: radial-gradient(circle,
    rgba(0, 212, 255, 0.2) 0%,
    transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cyber-menu-item:active .icon-glow {
  opacity: 1;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.menu-arrow {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-text {
  font-size: 24rpx;
  color: var(--cyber-blue);
  font-weight: 600;
  transition: transform 0.3s ease;
}

.cyber-menu-item:active .arrow-text {
  transform: translateX(5rpx);
}

.arrow-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle,
    rgba(0, 212, 255, 0.3) 0%,
    transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cyber-menu-item:active .arrow-glow {
  opacity: 1;
}

.menu-badge {
  position: relative;
  width: 32rpx;
  height: 32rpx;
  background: rgba(239, 68, 68, 0.2);
  border: 2rpx solid #ef4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-sm);
}

.badge-text {
  font-size: 20rpx;
  color: #ef4444;
  font-weight: 700;
}

.badge-glow {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: radial-gradient(circle,
    rgba(239, 68, 68, 0.3) 0%,
    transparent 70%);
  border-radius: 50%;
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}


/* 科技感退出登录 */
.logout-section {
  padding: var(--space-xl) 0;
  /* 确保退出登录按钮有足够的底部间距 */
  margin-bottom: var(--space-lg);
}

.danger-btn {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.5);
}

.danger-btn .btn-text {
  color: #ef4444;
}

.danger-glow {
  background: linear-gradient(90deg,
    transparent,
    rgba(239, 68, 68, 0.3),
    transparent);
}

.danger-btn:active {
  border-color: #ef4444;
  box-shadow:
    0 0 20rpx rgba(239, 68, 68, 0.5),
    inset 0 0 20rpx rgba(239, 68, 68, 0.1);
}

/* ==================== G.T.I. SECURITY 科技感加载框 ==================== */
.gti-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gti-loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10rpx);
}

.gti-loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 60rpx;
  background: rgba(15, 23, 42, 0.9);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 20rpx;
  box-shadow:
    0 0 40rpx rgba(0, 212, 255, 0.2),
    inset 0 0 40rpx rgba(0, 212, 255, 0.1);
  backdrop-filter: blur(20rpx);
}

.gti-logo-container {
  margin-bottom: 40rpx;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.gti-logo {
  width: 120rpx;
  height: 120rpx;
  filter: drop-shadow(0 0 20rpx rgba(0, 212, 255, 0.6));
}

.gti-loading-spinner {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 40rpx;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3rpx solid transparent;
  border-top: 3rpx solid var(--cyber-blue);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 60rpx;
  height: 60rpx;
  top: 10rpx;
  left: 10rpx;
  border-top-color: var(--cyber-green);
  animation-duration: 2s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 40rpx;
  height: 40rpx;
  top: 20rpx;
  left: 20rpx;
  border-top-color: var(--cyber-purple);
  animation-duration: 1s;
}

.gti-loading-text {
  color: var(--cyber-blue);
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 30rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  animation: textPulse 2s ease-in-out infinite;
}

.gti-loading-dots {
  display: flex;
  gap: 10rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background: var(--cyber-blue);
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;
  box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.6);
}

.dot-1 {
  animation-delay: -0.32s;
}

.dot-2 {
  animation-delay: -0.16s;
}

.dot-3 {
  animation-delay: 0s;
}

@keyframes logoGlow {
  0% {
    filter: drop-shadow(0 0 20rpx rgba(0, 212, 255, 0.6));
    transform: scale(1);
  }
  100% {
    filter: drop-shadow(0 0 30rpx rgba(0, 212, 255, 0.9));
    transform: scale(1.05);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes textPulse {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  }
  50% {
    opacity: 0.7;
    text-shadow: 0 0 20rpx rgba(0, 212, 255, 0.8);
  }
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
