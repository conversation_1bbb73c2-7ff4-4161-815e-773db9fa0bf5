// 标记通知已读云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { notificationId, markAllRead = false } = event;

  try {
    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    if (markAllRead) {
      // 标记所有未读通知为已读
      const updateResult = await db.collection('notifications')
        .where({
          targetUserId: user._id,
          readTime: null
        })
        .update({
          data: {
            readTime: new Date()
          }
        });

      return {
        success: true,
        data: {
          updatedCount: updateResult.stats.updated
        }
      };
    } else {
      // 标记单个通知为已读
      if (!notificationId) {
        return {
          success: false,
          error: '通知ID不能为空'
        };
      }

      // 验证通知是否属于当前用户
      const notificationResult = await db.collection('notifications').doc(notificationId).get();
      
      if (!notificationResult.data) {
        return {
          success: false,
          error: '通知不存在'
        };
      }

      if (notificationResult.data.targetUserId !== user._id) {
        return {
          success: false,
          error: '无权限操作此通知'
        };
      }

      // 更新通知状态
      await db.collection('notifications').doc(notificationId).update({
        data: {
          readTime: new Date()
        }
      });

      return {
        success: true,
        data: {
          notificationId
        }
      };
    }
  } catch (error) {
    console.error('标记通知已读失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
