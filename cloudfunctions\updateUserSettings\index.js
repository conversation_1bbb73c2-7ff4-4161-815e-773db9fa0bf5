// 更新用户设置云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { settings } = event;

  try {
    // 验证设置数据
    if (!settings || typeof settings !== 'object') {
      return {
        success: false,
        error: '设置数据格式错误'
      };
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 构建设置数据
    const settingsData = {
      // 通知设置
      orderNotification: settings.orderNotification !== false,
      messageNotification: settings.messageNotification !== false,
      newOrderNotification: settings.newOrderNotification !== false,
      evaluationNotification: settings.evaluationNotification !== false,
      systemNotification: settings.systemNotification !== false,
      soundNotification: settings.soundNotification !== false,
      vibrateNotification: settings.vibrateNotification !== false,

      // 隐私设置
      allowStrangerContact: settings.allowStrangerContact || false,
      showOnlineStatus: settings.showOnlineStatus !== false
    };

    // 同时更新通知偏好设置
    const notificationPreferences = {
      orderStatus: settings.orderNotification !== false,
      chatMessage: settings.messageNotification !== false,
      newOrder: settings.newOrderNotification !== false,
      evaluation: settings.evaluationNotification !== false,
      system: settings.systemNotification !== false,
      sound: settings.soundNotification !== false,
      vibrate: settings.vibrateNotification !== false
    };

    // 更新用户设置
    await db.collection('users').doc(user._id).update({
      data: {
        settings: settingsData,
        notificationPreferences: notificationPreferences,
        updateTime: new Date()
      }
    });

    return {
      success: true,
      message: '设置更新成功',
      data: {
        settings: settingsData,
        notificationPreferences: notificationPreferences
      }
    };
  } catch (error) {
    console.error('更新用户设置失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
