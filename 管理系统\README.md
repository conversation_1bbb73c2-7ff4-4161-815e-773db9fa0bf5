# 三角洲任务平台后台管理系统

## 📋 项目概述

这是一个基于 React + TypeScript + Ant Design 构建的现代化后台管理系统，专为三角洲任务平台设计，提供用户管理、订单管理、聊天监控、财务管理等核心功能。

## ✨ 主要特性

### 🎨 界面设计
- **深色主题** - 护眼的深色配色方案
- **响应式布局** - 适配各种屏幕尺寸
- **现代化UI** - 基于 Ant Design 5.x 组件库
- **科技感设计** - 渐变背景和动画效果

### 📊 功能模块
- **仪表盘** - 实时数据监控和可视化图表
- **用户管理** - 用户列表、状态管理、实名认证
- **订单管理** - 订单查看、状态更新、统计分析
- **聊天监控** - 聊天记录查看、用户管理
- **钱包系统** - 交易记录、提现审核、财务统计
- **通知管理** - 系统通知、用户反馈处理
- **评价系统** - 评价查看、统计分析

### 🚀 技术特性
- **TypeScript** - 类型安全的开发体验
- **Vite** - 快速的开发构建工具
- **React Router** - 客户端路由管理
- **Axios** - HTTP 请求库
- **云函数集成** - 与微信云开发无缝对接

## 🛠️ 技术栈

- **前端框架**: React 18
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI 组件库**: Ant Design 5.x
- **路由管理**: React Router 6
- **HTTP 客户端**: Axios
- **图标库**: Ant Design Icons
- **样式方案**: CSS-in-JS + Less

## 📦 项目结构

```
管理系统/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── components/         # 公共组件
│   ├── services/          # API 服务
│   │   └── api.ts         # API 接口定义
│   ├── types/             # 类型定义
│   │   └── index.ts       # 通用类型
│   ├── tests/             # 测试文件
│   │   ├── api.test.ts    # API 测试
│   │   └── performance.test.ts # 性能测试
│   ├── App.tsx            # 主应用组件
│   └── main.tsx           # 应用入口
├── scripts/               # 构建脚本
│   └── deploy.js          # 部署脚本
├── deploy.config.js       # 部署配置
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript 配置
├── vite.config.ts         # Vite 配置
└── README.md              # 项目文档
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd 管理系统
npm install
```

### 开发模式
```bash
npm run dev
```
访问 http://localhost:3001

### 构建项目
```bash
# 开发构建
npm run build

# 测试环境构建
npm run build:staging

# 生产环境构建
npm run build
```

### 代码检查
```bash
# 检查代码规范
npm run lint

# 自动修复代码问题
npm run lint:fix
```

### 测试
```bash
# 运行所有测试
npm run test

# API 功能测试
npm run test:api

# 性能测试
npm run test:performance
```

## 🔧 配置说明

### API 配置
在 `src/services/api.ts` 中配置云函数地址：
```typescript
const CLOUD_ENV = 'your-cloud-env-id';
const ADMIN_API_URL = `https://${CLOUD_ENV}.tcb-api.tencentcloudapi.com/web`;
```

### 环境变量
创建 `.env` 文件：
```env
VITE_API_URL=https://your-cloud-function-url
VITE_CLOUD_ENV=your-cloud-env-id
```

## 📊 功能说明

### 仪表盘
- **实时统计** - 在线用户、活跃订单、今日收入等
- **数据可视化** - 用户活跃度、订单趋势、收入分析图表
- **系统监控** - CPU、内存、磁盘使用率监控
- **快速操作** - 常用功能快捷入口

### 用户管理
- **用户列表** - 支持搜索、筛选、分页
- **状态管理** - 用户启用/禁用/封禁
- **实名认证** - 认证状态查看和管理
- **用户统计** - 用户数量、活跃度统计

### 订单管理
- **订单列表** - 订单查看、搜索、筛选
- **状态更新** - 订单状态实时更新
- **订单详情** - 完整订单信息展示
- **统计分析** - 订单量、收入趋势分析

## 🔐 权限管理

系统采用基于角色的权限控制：
- **超级管理员** - 所有权限
- **普通管理员** - 基础管理权限
- **客服人员** - 用户服务相关权限

## 🚀 部署指南

### 开发环境部署
```bash
npm run deploy
```

### 测试环境部署
```bash
npm run deploy:staging
```

### 生产环境部署
```bash
npm run deploy:production
```

## 📈 性能优化

- **代码分割** - 按路由和功能模块分割代码
- **懒加载** - 组件和路由懒加载
- **缓存策略** - 静态资源和 API 缓存
- **压缩优化** - 代码和资源压缩

## 🔍 监控和日志

- **性能监控** - 页面加载时间、API 响应时间
- **错误监控** - 前端错误捕获和上报
- **用户行为** - 用户操作行为分析

## 🤝 开发规范

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 TypeScript 严格模式
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

### Git 提交规范
```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📞 技术支持

如有问题，请联系开发团队或查看相关文档。

## 📄 许可证

本项目仅供内部使用，未经授权不得外传。
