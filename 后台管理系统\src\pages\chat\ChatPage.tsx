import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare } from 'lucide-react';

export default function ChatPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">聊天监控</h1>
        <p className="text-gray-600">监控平台聊天消息和用户行为</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="mr-2 h-5 w-5" />
            聊天监控功能
          </CardTitle>
          <CardDescription>
            聊天监控模块正在开发中...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-gray-500">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>聊天监控功能即将上线</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}