import axios from 'axios';
import type { 
  User, 
  Order, 
  ChatRoom, 
  ChatMessage, 
  WalletTransaction, 
  Notification, 
  Evaluation, 
  DashboardStats,
  ApiResponse,
  PaginationParams 
} from '@/types';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api',
  timeout: 10000,
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('admin_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器 - 统一错误处理
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 认证相关API
export const authApi = {
  login: (username: string, password: string) =>
    api.post<ApiResponse<{ token: string; admin: any }>>('/auth/login', { username, password }),
  
  logout: () => api.post('/auth/logout'),
  
  getCurrentAdmin: () => api.get<ApiResponse<any>>('/auth/me'),
};

// 仪表盘数据API
export const dashboardApi = {
  getStats: () => api.get<ApiResponse<DashboardStats>>('/dashboard/stats'),

  getRecentActivities: () => api.get<ApiResponse<any[]>>('/dashboard/activities'),

  getChartData: (type: string) => api.post<ApiResponse<any>>('/', {
    action: 'getChartData',
    data: { type }
  }),
};

// 用户管理API
export const userApi = {
  getUsers: (params: PaginationParams & { 
    search?: string; 
    status?: string; 
    verified?: boolean 
  }) => api.get<ApiResponse<User[]>>('/users', { params }),
  
  getUserById: (id: string) => api.get<ApiResponse<User>>(`/users/${id}`),
  
  updateUser: (id: string, data: Partial<User>) => 
    api.put<ApiResponse<User>>(`/users/${id}`, data),
  
  toggleUserStatus: (id: string) => 
    api.patch<ApiResponse<User>>(`/users/${id}/toggle-status`),
  
  batchUpdateUsers: (ids: string[], action: string) =>
    api.post<ApiResponse<any>>('/users/batch', { ids, action }),
};

// 订单管理API
export const orderApi = {
  getOrders: (params: PaginationParams & {
    status?: string;
    userId?: string;
    dateRange?: [string, string];
    amountRange?: [number, number];
  }) => api.get<ApiResponse<Order[]>>('/orders', { params }),
  
  getOrderById: (id: string) => api.get<ApiResponse<Order>>(`/orders/${id}`),
  
  updateOrderStatus: (id: string, status: string, note?: string) =>
    api.patch<ApiResponse<Order>>(`/orders/${id}/status`, { status, note }),
  
  getOrderStats: (dateRange?: [string, string]) =>
    api.get<ApiResponse<any>>('/orders/stats', { params: { dateRange } }),
};

// 聊天监控API
export const chatApi = {
  getChatRooms: (params: PaginationParams & {
    status?: string;
    hasUnread?: boolean;
  }) => api.get<ApiResponse<ChatRoom[]>>('/chat/rooms', { params }),
  
  getChatMessages: (roomId: string, params?: PaginationParams) =>
    api.get<ApiResponse<ChatMessage[]>>(`/chat/rooms/${roomId}/messages`, { params }),
  
  getSensitiveMessages: (params: PaginationParams) =>
    api.get<ApiResponse<ChatMessage[]>>('/chat/sensitive-messages', { params }),
  
  updateSensitiveWords: (words: string[]) =>
    api.post<ApiResponse<any>>('/chat/sensitive-words', { words }),
  
  getChatStats: () => api.get<ApiResponse<any>>('/chat/stats'),
};

// 钱包管理API
export const walletApi = {
  getTransactions: (params: PaginationParams & {
    userId?: string;
    type?: string;
    status?: string;
    dateRange?: [string, string];
  }) => api.get<ApiResponse<WalletTransaction[]>>('/wallet/transactions', { params }),
  
  getWalletStats: () => api.get<ApiResponse<any>>('/wallet/stats'),
  
  approveWithdraw: (transactionId: string, note?: string) =>
    api.patch<ApiResponse<WalletTransaction>>(`/wallet/transactions/${transactionId}/approve`, { note }),
  
  rejectWithdraw: (transactionId: string, reason: string) =>
    api.patch<ApiResponse<WalletTransaction>>(`/wallet/transactions/${transactionId}/reject`, { reason }),
  
  adjustBalance: (userId: string, amount: number, reason: string) =>
    api.post<ApiResponse<any>>('/wallet/adjust-balance', { userId, amount, reason }),
};

// 通知管理API
export const notificationApi = {
  getNotifications: (params: PaginationParams & {
    type?: string;
    userId?: string;
  }) => api.get<ApiResponse<Notification[]>>('/notifications', { params }),
  
  sendNotification: (data: {
    title: string;
    content: string;
    type: string;
    userIds?: string[];
    isBroadcast?: boolean;
  }) => api.post<ApiResponse<Notification>>('/notifications/send', data),
  
  getNotificationTemplates: () => api.get<ApiResponse<any[]>>('/notifications/templates'),
  
  createTemplate: (template: any) => api.post<ApiResponse<any>>('/notifications/templates', template),
};

// 评价管理API
export const evaluationApi = {
  getEvaluations: (params: PaginationParams & {
    status?: string;
    score?: number;
    orderId?: string;
  }) => api.get<ApiResponse<Evaluation[]>>('/evaluations', { params }),
  
  reviewEvaluation: (id: string, action: 'approve' | 'reject', note?: string) =>
    api.patch<ApiResponse<Evaluation>>(`/evaluations/${id}/review`, { action, note }),
  
  getEvaluationStats: () => api.get<ApiResponse<any>>('/evaluations/stats'),
  
  batchReviewEvaluations: (ids: string[], action: 'approve' | 'reject') =>
    api.post<ApiResponse<any>>('/evaluations/batch-review', { ids, action }),
};

export default api;