/* 提现页面样式 - 科技感主题 */
.withdraw-container {
  min-height: 100vh;
  background: transparent;
  padding: var(--space-lg);
  padding-bottom: var(--space-3xl);
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.withdraw-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + var(--space-lg));
}

/* 余额卡片 - 科技感样式 */
.balance-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(115, 209, 61, 0.1));
  z-index: -1;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.balance-title {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
  opacity: 0.9;
}

.history-btn {
  font-size: 24rpx;
  padding: var(--space-sm) var(--space-md);
  background: rgba(82, 196, 26, 0.1);
  border: 1rpx solid rgba(82, 196, 26, 0.3);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.history-btn:active {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.currency {
  font-size: 36rpx;
  margin-right: 8rpx;
}

.amount {
  font-size: 64rpx;
  font-weight: bold;
}

.frozen-info {
  opacity: 0.8;
}

.frozen-text {
  font-size: 24rpx;
}

/* 提现金额 */
.amount-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 16rpx;
}

.currency-symbol {
  font-size: 28rpx;
  color: #666;
  margin-right: 12rpx;
}

.amount-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.all-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  margin: 0;
  line-height: 1;
}

.amount-tips {
  display: flex;
  justify-content: space-between;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

/* 提现方式 */
.method-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.method-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.method-item.selected {
  background: #f0f8ff;
  border-color: #1890ff;
}

.method-info {
  display: flex;
  align-items: center;
}

.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.method-details {
  display: flex;
  flex-direction: column;
}

.method-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #666;
}

.method-check {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #d9d9d9;
  color: white;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.check-icon.checked {
  background: #1890ff;
}

/* 银行卡信息 */
.bank-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.bank-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.form-input {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
}

/* 提现说明 */
.notice-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 20rpx;
  border-top: 2rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.amount-summary {
  text-align: center;
  margin-bottom: 16rpx;
}

.summary-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.fee-text {
  font-size: 24rpx;
  color: #999;
}

.withdraw-btn {
  width: 100%;
  background: #d9d9d9;
  color: #999;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.withdraw-btn.active {
  background: #52c41a;
  color: white;
}

.withdraw-btn[disabled] {
  background: #d9d9d9;
  color: #999;
}
