// 获取交易记录云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  const { page = 1, pageSize = 10, type = 'all' } = event;

  try {
    console.log('🔄 获取交易记录 - 开始', { openid, page, pageSize, type });

    // 构建查询条件
    let whereCondition = {
      userId: openid
    };

    // 根据类型筛选
    if (type !== 'all') {
      whereCondition.type = type;
    }

    // 获取总数
    const countResult = await db.collection('transactions')
      .where(whereCondition)
      .count();

    const total = countResult.total;

    // 获取分页数据
    const skip = (page - 1) * pageSize;
    const transactionsResult = await db.collection('transactions')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();

    const transactions = transactionsResult.data || [];

    // 格式化交易记录
    const formattedTransactions = transactions.map(transaction => {
      return {
        _id: transaction._id,
        type: transaction.type,
        amount: transaction.amount,
        description: transaction.description || getDefaultDescription(transaction.type),
        status: transaction.status || 'completed',
        createTime: transaction.createTime,
        relatedInfo: transaction.relatedInfo || null,
        // 添加显示用的文本
        typeText: getTransactionTypeText(transaction.type),
        statusText: getTransactionStatusText(transaction.status || 'completed'),
        createTimeText: formatTime(transaction.createTime)
      };
    });

    // 获取各类型的计数统计（仅在获取全部记录时计算）
    let stats = null;
    if (type === 'all') {
      const [rechargeCount, withdrawCount, incomeCount, paymentCount] = await Promise.all([
        db.collection('transactions').where({ userId: openid, type: 'recharge' }).count(),
        db.collection('transactions').where({ userId: openid, type: 'withdraw' }).count(),
        db.collection('transactions').where({ userId: openid, type: 'income' }).count(),
        db.collection('transactions').where({ userId: openid, type: 'payment' }).count()
      ]);

      stats = {
        transactionCount: total,
        rechargeCount: rechargeCount.total,
        withdrawCount: withdrawCount.total,
        incomeCount: incomeCount.total,
        paymentCount: paymentCount.total
      };
    }

    return {
      success: true,
      data: {
        records: formattedTransactions,
        stats: stats,
        pagination: {
          page: page,
          pageSize: pageSize,
          total: total,
          totalPages: Math.ceil(total / pageSize),
          hasMore: skip + pageSize < total
        }
      }
    };

  } catch (error) {
    console.error('❌ 获取交易记录失败:', error);
    
    return {
      success: false,
      error: error.message || '获取交易记录失败',
      data: {
        records: [],
        pagination: {
          page: page,
          pageSize: pageSize,
          total: 0,
          totalPages: 0,
          hasMore: false
        }
      }
    };
  }
};

// 获取默认描述
function getDefaultDescription(type) {
  const descriptions = {
    'recharge': '账户充值',
    'withdraw': '账户提现',
    'income': '订单收入',
    'payment': '订单支付',
    'refund': '订单退款'
  };
  return descriptions[type] || '其他交易';
}

// 获取交易类型文本
function getTransactionTypeText(type) {
  const typeMap = {
    'recharge': '充值',
    'withdraw': '提现',
    'income': '收入',
    'payment': '支付',
    'refund': '退款'
  };
  return typeMap[type] || '其他';
}

// 获取交易状态文本
function getTransactionStatusText(status) {
  const statusMap = {
    'pending': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  };
  return statusMap[status] || '未知';
}

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now - date;
  
  // 如果是今天
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }
  
  // 如果是昨天
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth()) {
    return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }
  
  // 其他情况显示完整日期
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
}
