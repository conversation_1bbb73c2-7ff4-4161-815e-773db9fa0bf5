{"appid": "wx4cf7429dd741903d", "compileType": "miniprogram", "libVersion": "3.8.8", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerModule": false, "userConfirmedUseCompilerModuleSwitch": false}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "projectArchitecture": "miniProgram", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "cloudfunctionRoot": "cloudfunctions/"}