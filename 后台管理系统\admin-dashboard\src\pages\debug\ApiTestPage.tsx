import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { orderApi, dashboardApi } from '@/services/cloudApi';

export default function ApiTestPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testOrderApi = async () => {
    try {
      setLoading(true);
      console.log('开始测试订单API...');

      const response = await orderApi.getOrderList({
        page: 1,
        limit: 10
      });

      console.log('API测试响应:', response);
      setResult(response);

    } catch (error) {
      console.error('API测试失败:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testChartApi = async () => {
    try {
      setLoading(true);
      console.log('开始测试图表API...');

      const response = await dashboardApi.getChartData('order');

      console.log('图表API测试响应:', response);
      setResult(response);

    } catch (error) {
      console.error('图表API测试失败:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>API调试测试</CardTitle>
          <CardDescription>测试订单API的响应</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-x-4">
            <Button onClick={testOrderApi} disabled={loading}>
              {loading ? '测试中...' : '测试订单API'}
            </Button>
            <Button onClick={testChartApi} disabled={loading} variant="outline">
              {loading ? '测试中...' : '测试图表API'}
            </Button>
          </div>
          
          {result && (
            <div className="mt-4">
              <h3 className="text-lg font-semibold mb-2">API响应结果：</h3>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
