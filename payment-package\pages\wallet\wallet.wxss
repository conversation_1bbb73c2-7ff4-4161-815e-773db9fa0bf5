/* 钱包页面样式 - 科技感主题 */
.wallet-container {
  min-height: 100vh;
  background: transparent;
  padding: var(--space-lg);
  padding-bottom: var(--space-3xl);
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.wallet-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + var(--space-lg));
}

/* 科技感余额卡片 */
.balance-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  border: 1rpx solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.balance-title-section {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.balance-icon {
  font-size: 32rpx;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.balance-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
}

.balance-actions {
  display: flex;
  gap: var(--space-sm);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  background: rgba(0, 212, 255, 0.1);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.action-btn:active {
  background: rgba(0, 212, 255, 0.2);
  transform: scale(0.95);
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 余额金额显示 */
.balance-amount-section {
  margin-bottom: var(--space-xl);
}

.main-balance {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--space-sm);
}

.currency {
  font-size: 48rpx;
  font-weight: 300;
  color: var(--text-secondary);
  margin-right: var(--space-xs);
}

.amount {
  font-size: 72rpx;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 1rpx;
}

.balance-details {
  margin-top: var(--space-sm);
}

.frozen-text {
  font-size: 24rpx;
  color: var(--text-tertiary);
  padding: var(--space-xs) var(--space-sm);
  background: rgba(255, 193, 7, 0.1);
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  border-radius: var(--radius-sm);
}

/* 余额统计网格 */
.balance-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-md);
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1rpx solid var(--border-light);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  padding: var(--space-md);
  border-radius: var(--radius-md);
  background: rgba(0, 212, 255, 0.05);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-glow);
}

.stat-icon {
  font-size: 32rpx;
  margin-bottom: var(--space-xs);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

.stat-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  border-radius: var(--radius-md);
  transition: opacity 0.3s ease;
  z-index: -1;
}

.stat-item:active .stat-glow {
  opacity: 0.1;
}

/* 快捷操作区域 */
.quick-actions {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  border: 1rpx solid var(--border-light);
}

.actions-header {
  margin-bottom: var(--space-lg);
}

.actions-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-md);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--space-lg) var(--space-sm);
  background: rgba(0, 212, 255, 0.05);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-item:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-glow);
}

.action-icon-container {
  position: relative;
  margin-bottom: var(--space-sm);
  overflow: visible;
}

.action-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #ff4d4f;
  color: white;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  height: 20rpx;
  text-align: center;
  line-height: 20rpx;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

.action-emoji {
  font-size: 40rpx;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.action-name {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.icon-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: var(--primary-gradient);
  opacity: 0;
  border-radius: 50%;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.action-item:active .icon-glow {
  opacity: 0.2;
}

/* 最近交易区域 */
.recent-transactions {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  border: 1rpx solid var(--border-light);
  overflow: hidden; /* 确保内容不会溢出 */
}

.transactions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.transactions-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
}

.more-link {
  font-size: 24rpx;
  color: var(--primary-color);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.more-link:active {
  background: rgba(0, 212, 255, 0.1);
}

/* 交易列表 */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  width: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  background: rgba(0, 212, 255, 0.03);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.transaction-item:active {
  background: rgba(0, 212, 255, 0.08);
  transform: translateX(4rpx);
}

.transaction-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 160rpx); /* 为右侧部分预留更多空间 */
}

.transaction-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-md);
  flex-shrink: 0;
  background: rgba(0, 212, 255, 0.1);
  border: 1rpx solid rgba(0, 212, 255, 0.2);
}

.transaction-icon.recharge {
  background: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.2);
}

.transaction-icon.withdraw {
  background: rgba(255, 77, 79, 0.1);
  border-color: rgba(255, 77, 79, 0.2);
}

.transaction-icon.income {
  background: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.2);
}

.transaction-icon.payment {
  background: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.2);
}

.transaction-icon.refund {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.2);
}

.icon-text {
  font-size: 28rpx;
}

.transaction-details {
  flex: 1;
  min-width: 0;
  overflow: hidden; /* 防止内容溢出 */
}

.transaction-title {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: var(--space-xs);
}

.transaction-status {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: var(--radius-sm);
  flex-shrink: 0; /* 防止状态标签被压缩 */
  white-space: nowrap; /* 防止文字换行 */
  text-align: center;
  min-width: 60rpx;
}

.transaction-status.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #fa8c16;
  border: 1rpx solid rgba(255, 193, 7, 0.3);
}

.transaction-status.completed {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border: 1rpx solid rgba(82, 196, 26, 0.3);
}

.transaction-status.failed {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border: 1rpx solid rgba(255, 77, 79, 0.3);
}

.transaction-desc {
  font-size: 22rpx;
  color: var(--text-tertiary);
  margin-bottom: var(--space-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%; /* 确保不超出容器宽度 */
}

.transaction-time {
  font-size: 20rpx;
  color: var(--text-disabled);
}

/* 交易右侧区域 - 包含状态和金额 */
.transaction-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  flex-shrink: 0;
  margin-left: var(--space-md);
  min-width: 140rpx; /* 确保右侧区域有足够宽度 */
  gap: var(--space-xs);
}

.amount-value {
  font-size: 28rpx;
  font-weight: 600;
  text-align: right;
  white-space: nowrap;
}

.amount-value.income {
  color: #52c41a;
}

.amount-value.expense {
  color: #ff4d4f;
}

/* 空状态 */
.empty-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-3xl) var(--space-xl);
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: var(--space-lg);
  opacity: 0.6;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: var(--text-tertiary);
  line-height: 1.5;
}

/* 安全提示区域 */
.security-tips {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  border: 1rpx solid var(--border-light);
}

.tips-header {
  margin-bottom: var(--space-lg);
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-sm);
  padding: var(--space-md);
  background: rgba(255, 193, 7, 0.05);
  border: 1rpx solid rgba(255, 193, 7, 0.1);
  border-radius: var(--radius-md);
}

.tip-icon {
  font-size: 24rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.tip-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  flex: 1;
}

/* 动画效果 */
.fade-in {
  opacity: 0;
  transform: translateY(20rpx);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== G.T.I. SECURITY 科技感加载框 ==================== */
.gti-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gti-loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10rpx);
}

.gti-loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 60rpx;
  background: rgba(15, 23, 42, 0.9);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 20rpx;
  box-shadow:
    0 0 40rpx rgba(0, 212, 255, 0.2),
    inset 0 0 40rpx rgba(0, 212, 255, 0.1);
  backdrop-filter: blur(20rpx);
}

.gti-logo-container {
  margin-bottom: 40rpx;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.gti-logo {
  width: 120rpx;
  height: 120rpx;
  filter: drop-shadow(0 0 20rpx rgba(0, 212, 255, 0.6));
}

.gti-loading-spinner {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 40rpx;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3rpx solid transparent;
  border-top: 3rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 60rpx;
  height: 60rpx;
  top: 10rpx;
  left: 10rpx;
  border-top-color: #00ff88;
  animation-duration: 2s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 40rpx;
  height: 40rpx;
  top: 20rpx;
  left: 20rpx;
  border-top-color: #ff6b35;
  animation-duration: 1s;
}

.gti-loading-text {
  color: var(--primary-color);
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 30rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  animation: textPulse 2s ease-in-out infinite;
}

.gti-loading-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background: var(--primary-color);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
  box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.dot-1 {
  animation-delay: -0.32s;
}

.dot-2 {
  animation-delay: -0.16s;
}

.dot-3 {
  animation-delay: 0s;
}

@keyframes logoGlow {
  0% {
    filter: drop-shadow(0 0 20rpx rgba(0, 212, 255, 0.6));
    transform: scale(1);
  }
  100% {
    filter: drop-shadow(0 0 30rpx rgba(0, 212, 255, 0.8));
    transform: scale(1.02);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes textPulse {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  }
  50% {
    opacity: 0.8;
    text-shadow: 0 0 20rpx rgba(0, 212, 255, 0.8);
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
