// 获取提现记录云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { page = 1, pageSize = 20, status } = event;

  try {
    // 查找用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 构建查询条件
    let whereCondition = {
      userId: user._id
    };

    if (status) {
      whereCondition.status = status;
    }

    // 查询提现记录
    const withdrawResult = await db.collection('withdraws')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();

    // 格式化数据
    const withdrawList = withdrawResult.data.map(withdraw => ({
      ...withdraw,
      createTimeText: formatTime(withdraw.createTime),
      processTimeText: withdraw.processTime ? formatTime(withdraw.processTime) : '',
      completeTimeText: withdraw.completeTime ? formatTime(withdraw.completeTime) : '',
      statusText: getStatusText(withdraw.status)
    }));

    return {
      success: true,
      data: {
        list: withdrawList,
        hasMore: withdrawList.length === pageSize
      }
    };
  } catch (error) {
    console.error('获取提现记录失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 格式化时间
function formatTime(dateStr) {
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}`;
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    'pending': '待审核',
    'processing': '处理中',
    'completed': '已完成',
    'rejected': '已拒绝'
  };
  
  return statusMap[status] || status;
}
