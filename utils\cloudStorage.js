// 云存储图标管理器
class CloudStorageManager {
  constructor() {
    // 云存储环境ID，从您的截图中获取的完整格式
    this.envId = 'cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750';
    
    // 图标文件映射
    this.iconMap = {
      // tabBar 图标
      'tab-home': 'icons/tab/home.png',
      'tab-home-active': 'icons/tab/home-active.png',

      'tab-order': 'icons/tab/order.png',
      'tab-order-active': 'icons/tab/order-active.png',
      'tab-chat': 'icons/tab/chat.png',
      'tab-chat-active': 'icons/tab/chat-active.png',
      'tab-user': 'icons/tab/user.png',
      'tab-user-active': 'icons/tab/user-active.png',
      
      // 其他图标（可根据需要添加）
      'default-avatar': 'icons/default-avatar.png',
      'floating-add': 'images/icons/your-icon.png', // 浮动按钮专用图标（飞机图标）
      'order-icon': 'icons/tab/order.png', // 订单图标备选
      'back': 'images/icons/back.png', // 返回按钮图标
      'filter': 'images/icons/filter.png', // 筛选按钮图标

      // 聊天室图标
      'voice': 'images/icons/voice.png', // 语音图标
      'image': 'images/icons/image.png', // 图片图标
      'keyboard': 'images/icons/keyboard.png', // 键盘图标
      'order': 'images/icons/order.png', // 订单图标
      'empty-chat': 'images/icons/message.png' // 空聊天状态图标（暂用消息图标）
    };

    // 轮播图配置
    this.bannerConfig = [
      {
        fileName: 'banner1.jpg',
        title: ''
      },
      {
        fileName: 'banner2.jpg',
        title: ''
      },
      {
        fileName: 'banner3.jpg',
        title: ''
      }
    ];
  }

  // 获取云存储文件URL
  getCloudUrl(iconKey) {
    const filePath = this.iconMap[iconKey];
    if (!filePath) {
      console.warn(`图标 ${iconKey} 不存在于映射表中`);
      return '';
    }

    const cloudUrl = `cloud://${this.envId}/${filePath}`;
    return cloudUrl;
  }

  // 获取所有 tabBar 图标URL
  getTabBarIcons() {
    const icons = {
      home: {
        iconPath: this.getCloudUrl('tab-home'),
        selectedIconPath: this.getCloudUrl('tab-home-active')
      },
      order: {
        iconPath: this.getCloudUrl('tab-order'),
        selectedIconPath: this.getCloudUrl('tab-order-active')
      },
      chat: {
        iconPath: this.getCloudUrl('tab-chat'),
        selectedIconPath: this.getCloudUrl('tab-chat-active')
      },
      user: {
        iconPath: this.getCloudUrl('tab-user'),
        selectedIconPath: this.getCloudUrl('tab-user-active')
      }
    };

    return icons;
  }

  // 验证图标是否存在（可选功能）
  async checkIconExists(iconKey) {
    try {
      const cloudUrl = this.getCloudUrl(iconKey);
      const result = await wx.cloud.downloadFile({
        fileID: cloudUrl
      });
      return result.tempFilePath ? true : false;
    } catch (error) {
      console.warn(`图标 ${iconKey} 不存在或无法访问:`, error);
      return false;
    }
  }

  // 获取轮播图列表
  getBanners() {
    return this.bannerConfig.map(banner => ({
      image: `cloud://${this.envId}/images/banners/${banner.fileName}`,
      title: banner.title
    }));
  }

  // 获取背景图片
  getBackgroundImage() {
    return `cloud://${this.envId}/images/backgrounds/tech-grid.jpg`;
  }

  // 设置环境ID
  setEnvId(envId) {
    this.envId = envId;
  }

  // 统一的云存储文件获取方法
  async getTempFileURL(fileId) {
    try {
      const result = await wx.cloud.getTempFileURL({
        fileList: [fileId]
      });

      if (result.fileList && result.fileList.length > 0 && result.fileList[0].status === 0) {
        return {
          success: true,
          tempFileURL: result.fileList[0].tempFileURL
        };
      } else {
        throw new Error('文件获取失败');
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取特定图标的临时URL
  async getIconTempURL(iconKey) {
    const cloudUrl = this.getCloudUrl(iconKey);
    if (!cloudUrl) {
      return { success: false, error: '图标不存在' };
    }
    return await this.getTempFileURL(cloudUrl);
  }
}

// 创建全局实例
const cloudStorage = new CloudStorageManager();

module.exports = cloudStorage;
