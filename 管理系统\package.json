{"name": "delta-admin-system", "version": "1.0.0", "description": "三角洲任务平台后台管理系统", "type": "module", "scripts": {"dev": "vite --port 3001", "build": "tsc && vite build", "build:staging": "tsc && vite build --mode staging", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "test": "echo \"Running tests...\" && exit 0", "test:api": "echo \"API tests would run here\"", "test:performance": "echo \"Performance tests would run here\"", "analyze": "echo \"Bundle analysis would run here\"", "compress": "echo \"Asset compression would run here\"", "upload": "echo \"Cloud upload would run here\"", "cdn:refresh": "echo \"CDN refresh would run here\"", "deploy": "node scripts/deploy.js", "deploy:staging": "node scripts/deploy.js staging", "deploy:production": "node scripts/deploy.js production"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "zustand": "^4.4.7", "axios": "^1.6.2", "recharts": "^2.8.0", "dayjs": "^1.11.10", "lucide-react": "^0.294.0", "@ant-design/icons": "^5.2.6", "classnames": "^2.3.2", "styled-components": "^6.1.6"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "less": "^4.2.0"}}