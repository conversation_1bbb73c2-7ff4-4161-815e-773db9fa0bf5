// 用户设置页面
import API from '../../../utils/api.js';
const errorHandler = require('../../../utils/errorHandler.js');

const app = getApp();

Page({
  data: {
    userInfo: {
      isVerified: false,
      phone: '',
      nickName: '',
      avatarUrl: ''
    },
    settings: {
      // 通知设置
      orderNotification: true,        // 订单状态通知
      messageNotification: true,      // 聊天消息通知
      newOrderNotification: true,     // 新订单通知
      evaluationNotification: true,   // 评价通知
      systemNotification: true,       // 系统通知
      soundNotification: true,        // 声音提醒
      vibrateNotification: true,      // 震动提醒

      // 隐私设置
      allowStrangerContact: false,    // 允许陌生人联系
      showOnlineStatus: true,         // 显示在线状态
      showPhoneNumber: false,         // 显示手机号
      allowOrderHistory: true,        // 允许查看订单历史

      // 功能设置
      autoAcceptOrder: false,         // 自动接单
      orderReminder: true,            // 订单提醒
      darkMode: false,                // 深色模式
      dataSync: true,                 // 数据同步

      // 安全设置
      loginVerification: false,       // 登录验证
      paymentPassword: false,         // 支付密码
      biometricAuth: false           // 生物识别认证
    },
    cacheSize: '0MB',
    appVersion: '1.0.0'
  },

  onLoad(options) {
    this.loadUserInfo();
    this.loadSettings();
    this.calculateCacheSize();
  },

  onShow() {
    // 页面显示时刷新用户信息
    this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 从本地存储获取基本信息
      const localUserInfo = wx.getStorageSync('userInfo') || {};

      // 从服务器获取最新信息
      const result = await API.getUserInfo();
      if (result.success) {
        const userInfo = {
          isVerified: result.data.isVerified || false,
          phone: result.data.phone || '',
          nickName: result.data.nickName || localUserInfo.nickName || '',
          avatarUrl: result.data.avatarUrl || localUserInfo.avatarUrl || ''
        };

        this.setData({ userInfo });

        // 更新本地存储
        wx.setStorageSync('userInfo', { ...localUserInfo, ...userInfo });
      } else {
        // 服务器获取失败，使用本地信息
        this.setData({
          userInfo: {
            isVerified: localUserInfo.isVerified || false,
            phone: localUserInfo.phone || '',
            nickName: localUserInfo.nickName || '',
            avatarUrl: localUserInfo.avatarUrl || ''
          }
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      // 使用本地信息作为备选
      const localUserInfo = wx.getStorageSync('userInfo') || {};
      this.setData({
        userInfo: {
          isVerified: localUserInfo.isVerified || false,
          phone: localUserInfo.phone || '',
          nickName: localUserInfo.nickName || '',
          avatarUrl: localUserInfo.avatarUrl || ''
        }
      });
    }
  },

  // 加载设置信息
  loadSettings() {
    const settings = wx.getStorageSync('settings') || {};
    this.setData({
      settings: {
        orderNotification: settings.orderNotification !== false,
        messageNotification: settings.messageNotification !== false,
        soundNotification: settings.soundNotification !== false,
        allowStrangerContact: settings.allowStrangerContact || false,
        showOnlineStatus: settings.showOnlineStatus !== false
      }
    });
  },

  // 计算缓存大小
  calculateCacheSize() {
    try {
      wx.getStorageInfo({
        success: (res) => {
          const sizeKB = res.currentSize;
          let sizeText = '';

          if (sizeKB < 1024) {
            sizeText = `${sizeKB}KB`;
          } else {
            const sizeMB = (sizeKB / 1024).toFixed(1);
            sizeText = `${sizeMB}MB`;
          }

          this.setData({ cacheSize: sizeText });
        },
        fail: () => {
          this.setData({ cacheSize: '未知' });
        }
      });
    } catch (error) {
      console.error('计算缓存大小失败:', error);
      this.setData({ cacheSize: '未知' });
    }
  },

  /**
   * 跳转到实名认证页面
   */
  navigateToCertification() {
    wx.navigateTo({
      url: '/pages/user/certification/certification'
    });
  },

  /**
   * 修改密码
   */
  changePassword() {
    wx.showModal({
      title: '修改密码',
      content: '请前往个人中心修改密码',
      showCancel: false
    });
  },

  // 绑定手机号
  bindPhone() {
    if (this.data.userInfo.phone) {
      wx.showModal({
        title: '更换手机号',
        content: `当前绑定：${this.data.userInfo.phone}\n是否要更换手机号？`,
        confirmText: '更换',
        success: (res) => {
          if (res.confirm) {
            this.showPhoneInput();
          }
        }
      });
    } else {
      this.showPhoneInput();
    }
  },

  // 显示手机号输入框
  showPhoneInput() {
    wx.showModal({
      title: '绑定手机号',
      content: '请输入手机号码',
      editable: true,
      placeholderText: '请输入11位手机号',
      success: (res) => {
        if (res.confirm && res.content) {
          const phone = res.content.trim();
          if (this.validatePhone(phone)) {
            this.bindPhoneNumber(phone);
          } else {
            wx.showToast({
              title: '手机号格式不正确',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 验证手机号格式
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  // 绑定手机号
  async bindPhoneNumber(phone) {
    try {
      wx.showLoading({ title: '绑定中...' });

      const result = await API.bindPhone({ phone });
      if (result.success) {
        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });

        // 更新用户信息
        this.setData({
          'userInfo.phone': phone
        });

        // 更新本地存储
        const userInfo = wx.getStorageSync('userInfo') || {};
        wx.setStorageSync('userInfo', { ...userInfo, phone });
      } else {
        wx.showToast({
          title: result.error || '绑定失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('绑定手机号失败:', error);
      wx.showToast({
        title: '绑定失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 切换设置开关
  async toggleSetting(e) {
    const key = e.currentTarget.dataset.key;
    const value = e.detail.value;

    this.setData({
      [`settings.${key}`]: value
    });

    // 保存到本地存储
    const settings = this.data.settings;
    wx.setStorageSync('settings', settings);

    // 同步到服务器
    try {
      await API.updateUserSettings(settings);
    } catch (error) {
      console.error('同步设置到服务器失败:', error);
    }

    wx.showToast({
      title: '设置已保存',
      icon: 'success',
      duration: 1000
    });
  },

  // 显示功能状态
  showStatus() {
    wx.navigateTo({
      url: '/pages/common/status/status'
    });
  },

  // 清理缓存
  clearCache() {
    wx.showModal({
      title: '清理缓存',
      content: `当前缓存：${this.data.cacheSize}\n确定要清理缓存吗？清理后需要重新登录。`,
      confirmText: '清理',
      confirmColor: '#ff6b35',
      success: (res) => {
        if (res.confirm) {
          try {
            // 保留重要信息
            const userInfo = wx.getStorageSync('userInfo');
            const settings = wx.getStorageSync('settings');

            // 清理缓存
            wx.clearStorageSync();

            // 恢复重要信息
            if (userInfo) wx.setStorageSync('userInfo', userInfo);
            if (settings) wx.setStorageSync('settings', settings);

            this.setData({ cacheSize: '0KB' });

            wx.showToast({
              title: '缓存已清理',
              icon: 'success'
            });

            // 重新计算缓存大小
            setTimeout(() => {
              this.calculateCacheSize();
            }, 1000);
          } catch (error) {
            console.error('清理缓存失败:', error);
            wx.showToast({
              title: '清理失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 检查更新
  checkUpdate() {
    wx.showLoading({ title: '检查中...' });

    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: '版本信息',
        content: `当前版本：${this.data.appVersion}\n已是最新版本`,
        showCancel: false,
        confirmText: '知道了'
      });
    }, 1500);
  },

  // 关于我们
  showAbout() {
    const aboutContent = `三角洲陪玩 v${this.data.appVersion}

专业的游戏陪玩平台
为玩家提供优质的游戏陪伴服务

开发团队：三角洲工作室
技术支持：微信小程序云开发`;

    wx.showModal({
      title: '关于我们',
      content: aboutContent,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ['在线客服', '客服电话', '意见反馈'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 在线客服
            wx.showToast({
              title: '客服功能开发中',
              icon: 'none'
            });
            break;
          case 1:
            // 客服电话
            wx.showModal({
              title: '客服电话',
              content: '************\n工作时间：9:00-21:00',
              showCancel: true,
              confirmText: '拨打',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.makePhoneCall({
                    phoneNumber: '************',
                    fail: () => {
                      wx.showToast({
                        title: '拨打失败',
                        icon: 'none'
                      });
                    }
                  });
                }
              }
            });
            break;
          case 2:
            // 意见反馈
            this.showFeedback();
            break;
        }
      }
    });
  },

  // 意见反馈
  showFeedback() {
    wx.showModal({
      title: '意见反馈',
      content: '请描述您遇到的问题或建议',
      editable: true,
      placeholderText: '请输入您的反馈内容...',
      success: (res) => {
        if (res.confirm && res.content && res.content.trim()) {
          this.submitFeedback(res.content.trim());
        }
      }
    });
  },

  // 提交反馈
  async submitFeedback(content) {
    try {
      wx.showLoading({ title: '提交中...' });

      // 这里可以调用API提交反馈
      // const result = await API.submitFeedback({ content });

      // 模拟提交
      setTimeout(() => {
        wx.hideLoading();
        wx.showToast({
          title: '反馈已提交',
          icon: 'success'
        });
      }, 1000);
    } catch (error) {
      console.error('提交反馈失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      });
    }
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？退出后需要重新登录。',
      confirmText: '退出',
      confirmColor: '#ff6b35',
      success: (res) => {
        if (res.confirm) {
          try {
            // 清除用户数据
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('token');

            // 重置全局状态
            const app = getApp();
            app.globalData.userInfo = null;
            app.globalData.isLogin = false;

            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            });

            // 跳转到登录页
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/common/login/login'
              });
            }, 1000);
          } catch (error) {
            console.error('退出登录失败:', error);
            wx.showToast({
              title: '退出失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 用户信息持久化测试
  testUserPersistence() {
    wx.navigateTo({
      url: '/pages/test/user-persistence/user-persistence'
    });
  },



  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
})