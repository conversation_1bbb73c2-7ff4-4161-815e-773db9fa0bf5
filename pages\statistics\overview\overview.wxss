/* 统计概览页面样式 */

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: var(--cyber-blue);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
  box-shadow: 0 0 10rpx var(--cyber-blue);
}

.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 80%; animation-delay: 1s; }
.particle:nth-child(3) { top: 80%; left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { top: 40%; left: 70%; animation-delay: 0.5s; }
.particle:nth-child(5) { top: 10%; left: 90%; animation-delay: 1.5s; }

/* 主容器 */
.statistics-container {
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
  min-height: 100vh;
}

/* 确保自定义导航栏页面的统计容器有正确的顶部间距 */
.statistics-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + var(--space-lg) + 20rpx);
}

/* 科技感卡片 */
.cyber-card {
  position: relative;
  background: rgba(15, 23, 42, 0.8);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  backdrop-filter: blur(10rpx);
  overflow: hidden;
  transition: all 0.3s ease;
}

.cyber-card:active {
  transform: scale(0.98);
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.1) 0%,
    transparent 50%,
    rgba(0, 212, 255, 0.05) 100%);
  border-radius: var(--radius-lg);
  pointer-events: none;
}

/* 时间范围选择 */
.time-range-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--cyber-blue);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.time-range-picker {
  flex: 1;
  margin-left: var(--space-lg);
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  min-width: 200rpx;
}

.picker-text {
  color: #ffffff;
  font-size: 26rpx;
}

.picker-arrow {
  color: var(--cyber-blue);
  font-size: 20rpx;
}

/* 概览统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
}

.stat-card {
  padding: var(--space-lg);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  position: relative;
  z-index: 1;
}

.stat-icon {
  font-size: 48rpx;
  filter: drop-shadow(0 0 10rpx rgba(0, 212, 255, 0.5));
}

.stat-info {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: var(--cyber-blue);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  margin-bottom: var(--space-xs);
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 订单状态分布 */
.section-content {
  position: relative;
  z-index: 1;
}

.status-chart {
  margin-top: var(--space-lg);
}

.status-item {
  margin-bottom: var(--space-lg);
}

.status-bar {
  height: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: var(--space-sm);
}

.status-fill {
  height: 100%;
  border-radius: 10rpx;
  transition: width 0.8s ease;
}

.status-fill.pending {
  background: linear-gradient(90deg, #ff6b35, #ff8c42);
}

.status-fill.in-progress {
  background: linear-gradient(90deg, #00d4ff, #0099cc);
}

.status-fill.completed {
  background: linear-gradient(90deg, #2ed573, #26d0ce);
}

.status-fill.cancelled {
  background: linear-gradient(90deg, #ff4757, #ff3838);
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.status-count {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--cyber-blue);
}

/* 收入和服务统计 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.view-more {
  font-size: 24rpx;
  color: var(--cyber-blue);
  opacity: 0.8;
}

.income-summary,
.service-summary {
  display: flex;
  justify-content: space-between;
  gap: var(--space-md);
}

.income-item,
.service-item {
  flex: 1;
  text-align: center;
}

.income-label,
.service-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: var(--space-xs);
}

.income-value,
.service-value {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--cyber-blue);
}

/* 简化图表 */
.simple-chart {
  margin-top: var(--space-lg);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
}

.legend-color.orders {
  background: var(--cyber-blue);
}

.legend-color.income {
  background: #2ed573;
}

.legend-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.chart-container {
  height: 200rpx;
  position: relative;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 160rpx;
  padding: 0 var(--space-sm);
}

.chart-day {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 2rpx;
}

.day-bars {
  display: flex;
  align-items: flex-end;
  gap: 2rpx;
  height: 120rpx;
  margin-bottom: var(--space-xs);
}

.bar {
  width: 8rpx;
  min-height: 4rpx;
  border-radius: 2rpx;
  transition: height 0.8s ease;
}

.bar.orders {
  background: var(--cyber-blue);
}

.bar.income {
  background: #2ed573;
}

.day-label {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.5);
  transform: rotate(-45deg);
  white-space: nowrap;
}

/* 操作按钮 */
.action-section {
  margin-top: var(--space-xl);
}

.cyber-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, var(--cyber-blue), #0066cc);
  border: 1rpx solid var(--cyber-blue);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
}

.cyber-btn:active {
  transform: scale(0.95);
  box-shadow: 0 0 30rpx rgba(0, 212, 255, 0.5);
}

.btn-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 212, 255, 0.3);
  border-top: 4rpx solid var(--cyber-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-md);
}

.loading-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 动画 */
@keyframes dataFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(100rpx, 100rpx); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
