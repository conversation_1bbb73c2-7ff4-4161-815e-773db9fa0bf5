// 数据库初始化工具
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 需要创建的集合列表
const COLLECTIONS = [
  'users',        // 用户表
  'orders',       // 订单表
  'chatRooms',    // 聊天房间表
  'messages',     // 聊天消息表
  'transactions', // 交易记录表
  'evaluations',  // 评价表
  'recharges',    // 充值记录表
  'withdraws'     // 提现记录表
];

/**
 * 检查集合是否存在
 */
async function checkCollectionExists(collectionName) {
  try {
    const result = await db.collection(collectionName).limit(1).get();
    return true;
  } catch (error) {
    if (error.errCode === -502002) {
      // 集合不存在
      return false;
    }
    throw error;
  }
}

/**
 * 创建集合
 */
async function createCollection(collectionName) {
  try {
    console.log(`正在创建集合: ${collectionName}`);
    
    // 通过插入一个临时文档来创建集合
    const result = await db.collection(collectionName).add({
      data: {
        _temp: true,
        createTime: new Date()
      }
    });
    
    // 删除临时文档
    await db.collection(collectionName).doc(result._id).remove();
    
    console.log(`集合 ${collectionName} 创建成功`);
    return true;
  } catch (error) {
    console.error(`创建集合 ${collectionName} 失败:`, error);
    return false;
  }
}

/**
 * 初始化数据库
 */
async function initDatabase() {
  console.log('开始初始化数据库...');
  
  const results = [];
  
  for (const collectionName of COLLECTIONS) {
    try {
      const exists = await checkCollectionExists(collectionName);
      
      if (exists) {
        console.log(`集合 ${collectionName} 已存在`);
        results.push({ collection: collectionName, status: 'exists' });
      } else {
        const created = await createCollection(collectionName);
        results.push({ 
          collection: collectionName, 
          status: created ? 'created' : 'failed' 
        });
      }
    } catch (error) {
      console.error(`处理集合 ${collectionName} 时出错:`, error);
      results.push({ collection: collectionName, status: 'error', error: error.message });
    }
  }
  
  console.log('数据库初始化完成');
  return results;
}



module.exports = {
  initDatabase,
  checkCollectionExists,
  createCollection
};
