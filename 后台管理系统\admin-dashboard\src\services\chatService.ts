// 聊天服务 - 直接调用微信云开发
import { callCloudFunction } from './wxCloudService';

// 获取聊天消息（使用现有的 chatMessage 云函数）
export const getChatMessages = async (chatRoomId: string, page = 1, pageSize = 50) => {
  console.log('🔍 [聊天服务] 开始获取聊天消息:', { chatRoomId, page, pageSize });
  
  try {
    // 调用现有的 chatMessage 云函数
    const response = await callCloudFunction('chatMessage', {
      action: 'get',
      chatRoomId: chatRoomId,
      page: page,
      pageSize: pageSize
    });
    
    console.log('📥 [聊天服务] 云函数响应:', response);
    
    if (!response.success) {
      throw new Error(response.error || '获取聊天消息失败');
    }
    
    // 转换数据格式以匹配前端期望
    const messages = response.data.list.map((msg: any) => ({
      id: msg._id,
      roomId: msg.chatRoomId,
      senderId: msg.senderId,
      content: msg.content,
      type: msg.type || 'text',
      timestamp: msg.createTime,
      isRead: true,
      senderInfo: msg.senderInfo || {
        nickName: '未知用户',
        avatarUrl: '/placeholder.svg?height=32&width=32'
      }
    }));
    
    console.log('✅ [聊天服务] 成功获取消息:', messages.length, '条');
    
    return {
      messages: messages,
      hasMore: response.data.hasMore || false,
      page: response.data.page || page
    };
    
  } catch (error) {
    console.error('❌ [聊天服务] 获取消息失败:', error);
    throw error;
  }
};

// 发送聊天消息 - 性能优化版
export const sendChatMessage = async (chatRoomId: string, content: string, type = 'text') => {
  console.log('📤 [聊天服务] 发送消息 - 性能优化版:', { chatRoomId, content, type });
  const startTime = Date.now();

  try {
    const response = await callCloudFunction('chatMessage', {
      action: 'send',
      chatRoomId: chatRoomId,
      content: content,
      type: type
    });

    console.log(`⚡ [性能优化] 消息发送完成，耗时: ${Date.now() - startTime}ms`);

    if (!response.success) {
      throw new Error(response.error || '发送消息失败');
    }

    return response.data;

  } catch (error) {
    console.error('❌ [聊天服务] 发送消息失败:', error);
    throw error;
  }
};

// 批量获取聊天消息 - 性能优化版
export const getChatMessagesBatch = async (chatRoomId: string, pages: number[] = [1], pageSize = 50) => {
  console.log('🔍 [聊天服务] 批量获取聊天消息:', { chatRoomId, pages, pageSize });

  try {
    // 并行请求多页数据
    const promises = pages.map(page =>
      callCloudFunction('chatMessage', {
        action: 'get',
        chatRoomId: chatRoomId,
        page: page,
        pageSize: pageSize
      })
    );

    const responses = await Promise.all(promises);

    // 合并所有页面的消息
    let allMessages: any[] = [];
    let hasMore = false;

    responses.forEach((response, index) => {
      if (!response.success) {
        throw new Error(response.error || `获取第${pages[index]}页消息失败`);
      }

      const messages = response.data.list.map((msg: any) => ({
        id: msg._id,
        roomId: msg.chatRoomId,
        senderId: msg.senderId,
        content: msg.content,
        type: msg.type || 'text',
        timestamp: msg.createTime,
        isRead: true,
        senderInfo: msg.senderInfo || {
          nickName: '未知用户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        }
      }));

      allMessages = allMessages.concat(messages);
      hasMore = hasMore || response.data.hasMore || false;
    });

    console.log('✅ [聊天服务] 批量获取消息成功:', allMessages.length, '条');

    return {
      messages: allMessages,
      hasMore: hasMore,
      page: Math.max(...pages)
    };

  } catch (error) {
    console.error('❌ [聊天服务] 批量获取消息失败:', error);
    throw error;
  }
};