// 云函数入口文件 - 调试版本
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: 'cloud1-9gsj7t48183e5a9f'
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('=== 调试信息 ===')
  console.log('完整event对象:', JSON.stringify(event, null, 2))
  console.log('event.body类型:', typeof event.body)
  console.log('event.body内容:', event.body)
  console.log('context:', JSON.stringify(context, null, 2))
  
  // 创建HTTP响应格式
  const createResponse = (data) => {
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      },
      body: JSON.stringify(data)
    }
  }
  
  try {
    let requestData = {}
    
    // 处理不同的请求格式
    if (event.httpMethod) {
      // HTTP触发器请求
      console.log('检测到HTTP触发器请求')
      console.log('HTTP方法:', event.httpMethod)
      console.log('请求头:', event.headers)
      
      if (event.body) {
        try {
          requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body
          console.log('解析后的请求数据:', requestData)
        } catch (e) {
          console.error('解析请求体失败:', e)
          return createResponse({
            success: false,
            error: '请求格式错误',
            debug: {
              bodyType: typeof event.body,
              bodyContent: event.body
            }
          })
        }
      }
    } else {
      // 直接调用云函数
      console.log('检测到直接云函数调用')
      requestData = event
    }
    
    const { action, data = {} } = requestData
    console.log('提取的action:', action)
    console.log('提取的data:', data)
    
    // 验证请求参数
    if (!action) {
      return createResponse({
        success: false,
        error: '缺少action参数',
        debug: {
          receivedEvent: event,
          parsedData: requestData,
          hasAction: !!action
        }
      })
    }

    // 路由分发
    switch (action) {
      case 'getDashboardStats':
        return createResponse(await getDashboardStats())

      case 'getUserStats':
        return createResponse(await getUserStats())

      case 'getOrderStats':
        return createResponse(await getOrderStats())

      case 'getWalletStats':
        return createResponse(await getWalletStats())

      case 'getUserList':
        return createResponse(await getUserList(data))

      case 'getOrderList':
        return createResponse(await getOrderList(data))

      default:
        return createResponse({
          success: false,
          error: `未知的action: ${action}`,
          debug: {
            receivedAction: action,
            availableActions: ['getDashboardStats', 'getUserStats', 'getOrderStats', 'getWalletStats']
          }
        })
    }
  } catch (error) {
    console.error('云函数执行错误:', error)
    return createResponse({
      success: false,
      error: error.message || '服务器内部错误',
      debug: {
        errorStack: error.stack,
        errorMessage: error.message
      }
    })
  }
}

// ==================== 数据库查询函数 ====================

// 获取仪表盘统计数据
async function getDashboardStats() {
  try {
    console.log('开始获取仪表盘统计数据...')

    // 获取今日开始时间（中国时区）
    const now = new Date()
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    console.log('今日开始时间:', todayStart)

    // 并行查询各种统计数据
    const [
      totalUsersResult,
      totalOrdersResult,
      newUsersTodayResult,
      todayOrdersResult
    ] = await Promise.all([
      // 总用户数
      db.collection('users').count(),
      // 总订单数
      db.collection('orders').count(),
      // 今日新用户
      db.collection('users').where({
        createTime: db.command.gte(todayStart)
      }).count(),
      // 今日订单
      db.collection('orders').where({
        createTime: db.command.gte(todayStart)
      }).count()
    ])

    console.log('查询结果:', {
      totalUsers: totalUsersResult.total,
      totalOrders: totalOrdersResult.total,
      newUsersToday: newUsersTodayResult.total,
      todayOrders: todayOrdersResult.total
    })

    // 计算今日收入（需要查询今日已完成的订单）
    const todayCompletedOrders = await db.collection('orders').where({
      createTime: db.command.gte(todayStart),
      status: 'completed'
    }).get()

    const todayRevenue = todayCompletedOrders.data.reduce((sum, order) => {
      return sum + (order.amount || 0)
    }, 0)

    // 计算总收入
    const allCompletedOrders = await db.collection('orders').where({
      status: 'completed'
    }).get()

    const totalRevenue = allCompletedOrders.data.reduce((sum, order) => {
      return sum + (order.amount || 0)
    }, 0)

    // 获取在线用户数（假设有lastActiveTime字段）
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    const onlineUsersResult = await db.collection('users').where({
      lastActiveTime: db.command.gte(fiveMinutesAgo)
    }).count()

    // 获取活跃订单数（进行中的订单）
    const activeOrdersResult = await db.collection('orders').where({
      status: db.command.in(['pending', 'processing', 'confirmed'])
    }).count()

    return {
      success: true,
      data: {
        totalUsers: totalUsersResult.total || 0,
        totalOrders: totalOrdersResult.total || 0,
        totalRevenue: totalRevenue || 0,
        newUsersToday: newUsersTodayResult.total || 0,
        onlineUsers: onlineUsersResult.total || 0,
        activeOrders: activeOrdersResult.total || 0,
        todayRevenue: todayRevenue || 0,
        todayOrders: todayOrdersResult.total || 0,
        systemLoad: Math.floor(Math.random() * 100) // 系统负载可以从服务器监控获取
      },
      message: '仪表盘数据获取成功'
    }
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    return {
      success: false,
      error: '获取仪表盘数据失败: ' + error.message
    }
  }
}

// 获取用户统计数据
async function getUserStats() {
  try {
    console.log('开始获取用户统计数据...')

    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)

    const [
      totalUsersResult,
      newUsersTodayResult,
      verifiedUsersResult
    ] = await Promise.all([
      db.collection('users').count(),
      db.collection('users').where({
        createTime: db.command.gte(todayStart)
      }).count(),
      db.collection('users').where({
        verified: true
      }).count()
    ])

    // 活跃用户（最近7天有活动）
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    const activeUsersResult = await db.collection('users').where({
      lastActiveTime: db.command.gte(sevenDaysAgo)
    }).count()

    return {
      success: true,
      data: {
        totalUsers: totalUsersResult.total || 0,
        activeUsers: activeUsersResult.total || 0,
        newUsersToday: newUsersTodayResult.total || 0,
        verifiedUsers: verifiedUsersResult.total || 0
      },
      message: '用户统计获取成功'
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return {
      success: false,
      error: '获取用户统计失败: ' + error.message
    }
  }
}

// 获取订单统计数据
async function getOrderStats() {
  try {
    console.log('开始获取订单统计数据...')

    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)

    const [
      totalOrdersResult,
      pendingOrdersResult,
      completedOrdersResult,
      todayOrdersResult
    ] = await Promise.all([
      db.collection('orders').count(),
      db.collection('orders').where({
        status: 'pending'
      }).count(),
      db.collection('orders').where({
        status: 'completed'
      }).count(),
      db.collection('orders').where({
        createTime: db.command.gte(todayStart)
      }).count()
    ])

    return {
      success: true,
      data: {
        totalOrders: totalOrdersResult.total || 0,
        pendingOrders: pendingOrdersResult.total || 0,
        completedOrders: completedOrdersResult.total || 0,
        todayOrders: todayOrdersResult.total || 0
      },
      message: '订单统计获取成功'
    }
  } catch (error) {
    console.error('获取订单统计失败:', error)
    return {
      success: false,
      error: '获取订单统计失败: ' + error.message
    }
  }
}

// 获取钱包统计数据
async function getWalletStats() {
  try {
    console.log('开始获取钱包统计数据...')

    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)

    const [
      totalTransactionsResult,
      pendingWithdrawsResult,
      todayTransactionsResult
    ] = await Promise.all([
      db.collection('transactions').count(),
      db.collection('withdraws').where({
        status: 'pending'
      }).count(),
      db.collection('transactions').where({
        createTime: db.command.gte(todayStart)
      }).count()
    ])

    // 计算总交易金额
    const allTransactions = await db.collection('transactions').get()
    const totalAmount = allTransactions.data.reduce((sum, transaction) => {
      return sum + (transaction.amount || 0)
    }, 0)

    return {
      success: true,
      data: {
        totalTransactions: totalTransactionsResult.total || 0,
        totalAmount: totalAmount || 0,
        pendingWithdraws: pendingWithdrawsResult.total || 0,
        todayTransactions: todayTransactionsResult.total || 0
      },
      message: '钱包统计获取成功'
    }
  } catch (error) {
    console.error('获取钱包统计失败:', error)
    return {
      success: false,
      error: '获取钱包统计失败: ' + error.message
    }
  }
}

// 获取用户列表
async function getUserList(data = {}) {
  try {
    console.log('开始获取用户列表...', data)

    const { page = 1, pageSize = 20, keyword = '' } = data
    const skip = (page - 1) * pageSize

    let query = db.collection('users')

    // 如果有搜索关键词，添加搜索条件
    if (keyword) {
      query = query.where({
        $or: [
          { nickName: db.command.regex({ regexp: keyword, options: 'i' }) },
          { phone: db.command.regex({ regexp: keyword, options: 'i' }) }
        ]
      })
    }

    // 获取总数和列表数据
    const [countResult, listResult] = await Promise.all([
      query.count(),
      query.orderBy('createTime', 'desc').skip(skip).limit(pageSize).get()
    ])

    return {
      success: true,
      data: {
        list: listResult.data || [],
        total: countResult.total || 0,
        page,
        pageSize
      },
      message: '用户列表获取成功'
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return {
      success: false,
      error: '获取用户列表失败: ' + error.message
    }
  }
}

// 获取订单列表
async function getOrderList(data = {}) {
  try {
    console.log('开始获取订单列表...', data)

    const { page = 1, pageSize = 20, status = '', keyword = '' } = data
    const skip = (page - 1) * pageSize

    let query = db.collection('orders')

    // 状态筛选
    if (status) {
      query = query.where({ status })
    }

    // 关键词搜索
    if (keyword) {
      query = query.where({
        $or: [
          { orderNo: db.command.regex({ regexp: keyword, options: 'i' }) },
          { title: db.command.regex({ regexp: keyword, options: 'i' }) }
        ]
      })
    }

    // 获取总数和列表数据
    const [countResult, listResult] = await Promise.all([
      query.count(),
      query.orderBy('createTime', 'desc').skip(skip).limit(pageSize).get()
    ])

    return {
      success: true,
      data: {
        list: listResult.data || [],
        total: countResult.total || 0,
        page,
        pageSize
      },
      message: '订单列表获取成功'
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    return {
      success: false,
      error: '获取订单列表失败: ' + error.message
    }
  }
}
