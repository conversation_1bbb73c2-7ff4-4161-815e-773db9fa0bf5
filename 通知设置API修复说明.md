# 通知设置API修复说明

## 🐛 问题描述
用户在设置页面切换"震动提醒"开关时出现错误：
```
TypeError: _api.default.updateUserSettings is not a function
```

## 🔍 问题分析

### 1. 缺失的API函数
- **问题**: `utils/api.js` 中缺少 `updateUserSettings` 函数
- **现象**: 设置页面调用 `API.updateUserSettings(settings)` 时报错
- **原因**: 虽然有对应的云函数 `updateUserSettings`，但API层没有封装

### 2. 云函数数据格式不匹配
- **问题**: 云函数期望的数据格式与设置页面传递的格式不一致
- **现象**: 新的通知设置项（如 `vibrateNotification`）无法正确处理
- **原因**: 云函数只处理了部分通知设置，缺少新增的设置项

## ✅ 修复内容

### 1. 添加API函数
**文件**: `utils/api.js`
```javascript
// 更新用户设置
async updateUserSettings(settings) {
  return await this.callFunction('updateUserSettings', { settings });
}
```

### 2. 更新云函数数据处理
**文件**: `cloudfunctions/updateUserSettings/index.js`

**新增支持的设置项**:
- `newOrderNotification` - 新订单通知
- `evaluationNotification` - 评价通知  
- `systemNotification` - 系统通知
- `vibrateNotification` - 震动提醒

**新增通知偏好映射**:
```javascript
const notificationPreferences = {
  orderStatus: settings.orderNotification !== false,
  chatMessage: settings.messageNotification !== false,
  newOrder: settings.newOrderNotification !== false,
  evaluation: settings.evaluationNotification !== false,
  system: settings.systemNotification !== false,
  sound: settings.soundNotification !== false,
  vibrate: settings.vibrateNotification !== false
};
```

### 3. 数据库存储优化
- **设置数据**: 存储在 `users.settings` 字段
- **通知偏好**: 存储在 `users.notificationPreferences` 字段
- **双重存储**: 确保设置页面和通知系统都能正确读取

## 🔄 数据流程

### 设置更新流程
1. **用户操作**: 在设置页面切换开关
2. **本地更新**: 更新页面数据和本地存储
3. **通知系统**: 调用 `app.setNotificationPreferences()` 更新通知偏好
4. **服务器同步**: 调用 `API.updateUserSettings()` 同步到服务器
5. **数据库存储**: 云函数更新用户设置和通知偏好

### 设置项映射关系
| 设置页面Key | 通知系统Key | 功能描述 |
|------------|------------|----------|
| `orderNotification` | `orderStatus` | 订单状态通知 |
| `messageNotification` | `chatMessage` | 聊天消息通知 |
| `newOrderNotification` | `newOrder` | 新订单通知 |
| `evaluationNotification` | `evaluation` | 评价通知 |
| `systemNotification` | `system` | 系统通知 |
| `soundNotification` | `sound` | 声音提醒 |
| `vibrateNotification` | `vibrate` | 震动提醒 |

## 🎯 修复结果
- ✅ 设置页面所有开关都能正常工作
- ✅ 设置变更能正确同步到服务器
- ✅ 通知系统能正确响应设置变更
- ✅ 数据库存储格式统一完整
- ✅ 用户体验流畅无错误

## 🔧 测试验证
1. **功能测试**: 切换各种通知设置开关
2. **数据同步**: 检查本地存储和服务器数据一致性
3. **通知响应**: 验证通知系统是否正确响应设置变更
4. **错误处理**: 确保网络异常时的优雅降级
