// 监听器状态保护工具 - 防止状态冲突错误
class WatcherStateGuard {
  constructor() {
    this.watcherStates = new Map();
    this.errorCounts = new Map();
    this.maxErrors = 5; // 最大错误次数
    this.cooldownTime = 10000; // 冷却时间 10秒
  }

  // 包装监听器创建，添加状态保护
  wrapWatcher(watcherId, createWatcherFn) {
    const state = this.watcherStates.get(watcherId) || {
      isActive: false,
      lastError: null,
      errorCount: 0,
      lastErrorTime: 0,
      watcher: null
    };

    // 检查是否在冷却期
    if (state.lastErrorTime && Date.now() - state.lastErrorTime < this.cooldownTime) {
      console.log(`⏳ [状态保护] 监听器 ${watcherId} 在冷却期，跳过创建`);
      return null;
    }

    // 检查错误次数
    if (state.errorCount >= this.maxErrors) {
      console.log(`❌ [状态保护] 监听器 ${watcherId} 错误次数过多，停止创建`);
      return null;
    }

    try {
      // 如果已有活跃的监听器，先关闭
      if (state.watcher && state.isActive) {
        console.log(`🔄 [状态保护] 关闭现有监听器 ${watcherId}`);
        try {
          state.watcher.close();
        } catch (closeError) {
          console.error(`❌ [状态保护] 关闭监听器失败:`, closeError);
        }
      }

      // 创建新的监听器
      const watcher = createWatcherFn();
      
      if (watcher) {
        // 更新状态
        this.watcherStates.set(watcherId, {
          isActive: true,
          lastError: null,
          errorCount: 0,
          lastErrorTime: 0,
          watcher: watcher,
          createTime: Date.now()
        });

        console.log(`✅ [状态保护] 监听器 ${watcherId} 创建成功`);
        return watcher;
      }
    } catch (error) {
      this.handleError(watcherId, error);
      return null;
    }
  }

  // 处理监听器错误
  handleError(watcherId, error) {
    const errorMsg = error.message || error.errMsg || '';
    console.error(`❌ [状态保护] 监听器 ${watcherId} 错误:`, errorMsg);

    // 检查是否是状态冲突错误
    const isStateConflictError = errorMsg.includes('current state') && 
                                errorMsg.includes('does not accept');

    const state = this.watcherStates.get(watcherId) || {
      isActive: false,
      lastError: null,
      errorCount: 0,
      lastErrorTime: 0,
      watcher: null
    };

    // 更新错误状态
    const newState = {
      ...state,
      isActive: false,
      lastError: error,
      errorCount: state.errorCount + 1,
      lastErrorTime: Date.now(),
      watcher: null
    };

    this.watcherStates.set(watcherId, newState);

    // 对于状态冲突错误，增加额外的冷却时间
    if (isStateConflictError) {
      console.log(`⚠️ [状态保护] 检测到状态冲突错误，延长冷却时间`);
      newState.lastErrorTime = Date.now() + this.cooldownTime; // 额外冷却时间
    }

    return isStateConflictError;
  }

  // 标记监听器为活跃状态
  markActive(watcherId, watcher) {
    const state = this.watcherStates.get(watcherId) || {};
    this.watcherStates.set(watcherId, {
      ...state,
      isActive: true,
      watcher: watcher,
      lastError: null
    });
  }

  // 标记监听器为非活跃状态
  markInactive(watcherId) {
    const state = this.watcherStates.get(watcherId) || {};
    this.watcherStates.set(watcherId, {
      ...state,
      isActive: false,
      watcher: null
    });
  }

  // 检查监听器是否可以创建
  canCreateWatcher(watcherId) {
    const state = this.watcherStates.get(watcherId);
    if (!state) return true;

    // 检查冷却期
    if (state.lastErrorTime && Date.now() - state.lastErrorTime < this.cooldownTime) {
      return false;
    }

    // 检查错误次数
    if (state.errorCount >= this.maxErrors) {
      return false;
    }

    return true;
  }

  // 重置监听器状态
  resetWatcher(watcherId) {
    console.log(`🔄 [状态保护] 重置监听器状态: ${watcherId}`);
    this.watcherStates.set(watcherId, {
      isActive: false,
      lastError: null,
      errorCount: 0,
      lastErrorTime: 0,
      watcher: null
    });
  }

  // 获取监听器状态
  getWatcherState(watcherId) {
    return this.watcherStates.get(watcherId);
  }

  // 获取所有状态
  getAllStates() {
    const states = {};
    this.watcherStates.forEach((state, watcherId) => {
      states[watcherId] = {
        ...state,
        watcher: state.watcher ? 'exists' : 'null'
      };
    });
    return states;
  }

  // 清理过期状态
  cleanup() {
    const now = Date.now();
    const expireTime = 30 * 60 * 1000; // 30分钟

    this.watcherStates.forEach((state, watcherId) => {
      if (state.createTime && now - state.createTime > expireTime && !state.isActive) {
        this.watcherStates.delete(watcherId);
      }
    });
  }
}

// 创建全局实例
const watcherStateGuard = new WatcherStateGuard();

// 定期清理过期状态
setInterval(() => {
  watcherStateGuard.cleanup();
}, 5 * 60 * 1000); // 每5分钟清理一次

module.exports = watcherStateGuard;
