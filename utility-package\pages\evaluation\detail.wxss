/* 评价详情页面样式 - 优化设计 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e13 0%, #1a2332 50%, #0f1419 100%);
  color: #ffffff;
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 75%, rgba(0, 212, 255, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 75% 25%, rgba(255, 107, 53, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.04) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* 主要内容 */
.evaluation-content {
  padding: 24rpx;
  position: relative;
  z-index: 1;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 0.5rpx;
}

/* 评价者信息 - 优化设计 */
.evaluator-info {
  display: flex;
  align-items: center;
  padding: 28rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(0, 212, 255, 0.25);
  backdrop-filter: blur(15rpx);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

.avatar-container {
  margin-right: 20rpx;
}

.avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 107, 53, 0.4);
  box-shadow: 0 0 12rpx rgba(255, 107, 53, 0.2);
}

.avatar.default-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 107, 53, 0.1);
}

.avatar .avatar-text {
  font-size: 30rpx;
  color: rgba(255, 107, 53, 0.8);
}

.user-info {
  flex: 1;
}

.nickname {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6rpx;
  letter-spacing: 0.5rpx;
}

.role-tag {
  font-size: 22rpx;
  color: #ff8a65;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.15) 0%, rgba(255, 107, 53, 0.08) 100%);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  border: 1rpx solid rgba(255, 107, 53, 0.3);
  letter-spacing: 0.3rpx;
}

/* 评分显示 - 优化设计 */
.rating-section {
  text-align: center;
  padding: 32rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(0, 212, 255, 0.25);
  backdrop-filter: blur(15rpx);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

.stars-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}

.star-item {
  margin: 0 6rpx;
}

.star {
  font-size: 44rpx;
  transition: all 0.3s ease;
}

.star.filled {
  color: #ffd700;
  text-shadow:
    0 0 8rpx rgba(255, 215, 0, 0.6),
    0 0 16rpx rgba(255, 215, 0, 0.4);
}

.star.empty {
  color: rgba(255, 255, 255, 0.15);
}

.rating-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

/* 通用区块样式 - 优化设计 */
.tags-section,
.content-section,
.time-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(0, 212, 255, 0.25);
  backdrop-filter: blur(15rpx);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff8a65;
  margin-bottom: 16rpx;
  letter-spacing: 0.5rpx;
}

/* 标签样式 - 优化设计 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.15) 0%, rgba(255, 107, 53, 0.08) 100%);
  color: #ff8a65;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  border: 1rpx solid rgba(255, 107, 53, 0.3);
  letter-spacing: 0.3rpx;
}

/* 内容样式 - 优化设计 */
.content-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.85);
  letter-spacing: 0.3rpx;
}

/* 时间样式 */
.time-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.time-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.6);
}
