// 微信云开发服务
import axios from 'axios';

// 云开发环境配置
const CLOUD_ENV_ID = 'cloud1-9gsj7t48183e5a9f';
const CLOUD_BASE_URL = 'https://cloud1-9gsj7t48183e5a9f-1366958750.ap-shanghai.app.tcloudbase.com';

// 创建axios实例用于云函数调用
const wxCloudApi = axios.create({
  baseURL: CLOUD_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
wxCloudApi.interceptors.request.use(
  (config) => {
    // 添加管理员认证
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 添加云开发标识
    config.headers['X-WX-Cloud-Request'] = 'true';
    config.headers['X-Admin-Request'] = 'true';
    
    console.log('🌥️ [云开发] 发送请求:', {
      url: config.url,
      method: config.method,
      data: config.data
    });
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
wxCloudApi.interceptors.response.use(
  (response) => {
    console.log('🌥️ [云开发] 收到响应:', response.data);
    return response.data; // 直接返回数据部分
  },
  (error) => {
    console.error('❌ [云开发] 请求失败:', error);
    return Promise.reject(error);
  }
);

// 调用云函数的通用方法
export const callCloudFunction = async (functionName: string, data: any) => {
  console.log(`🚀 [云函数] 调用 ${functionName}:`, data);
  
  try {
    // 构造云函数调用URL
    const functionUrl = `/${functionName}`;
    
    // 发送请求
    const response = await wxCloudApi.post(functionUrl, data);
    
    console.log(`✅ [云函数] ${functionName} 响应:`, response);
    
    return response;
    
  } catch (error) {
    console.error(`❌ [云函数] ${functionName} 调用失败:`, error);
    
    // 如果是网络错误或服务器错误，提供更友好的错误信息
    if (error.code === 'ECONNABORTED') {
      throw new Error('请求超时，请检查网络连接');
    } else if (error.response?.status >= 500) {
      throw new Error('服务器内部错误，请稍后重试');
    } else if (error.response?.status === 404) {
      throw new Error(`云函数 ${functionName} 不存在或未部署`);
    } else {
      throw new Error(error.message || '云函数调用失败');
    }
  }
};

// 专门用于管理员操作的云函数调用
export const callAdminCloudFunction = async (functionName: string, action: string, data: any) => {
  return callCloudFunction(functionName, {
    action: action,
    data: data,
    adminRequest: true
  });
};

export default wxCloudApi;