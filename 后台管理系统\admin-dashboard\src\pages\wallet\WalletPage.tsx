import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Wallet, 
  Search, 
  Filter, 
  Download,
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  ArrowUpRight,
  ArrowDownLeft,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  RefreshCw
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { walletApi, userApi } from '@/services/cloudApi';
import type { WalletTransaction } from '@/types';

export default function WalletPage() {
  const [transactions, setTransactions] = useState<WalletTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // 用户信息缓存
  const [userCache, setUserCache] = useState<Record<string, any>>({});

  const { toast } = useToast();

  // 获取用户显示名称
  const getUserDisplayName = (userId: string) => {
    if (!userId) return '未知用户';

    // 检查缓存
    const cachedUser = userCache[userId];
    if (cachedUser) {
      return cachedUser.nickname || cachedUser.nickName || cachedUser.name || `用户${userId.slice(-6)}`;
    }

    // 返回用户ID的后6位作为显示名
    return `用户${userId.slice(-6)}`;
  };

  // 异步获取用户信息（后台加载）
  const loadUserInfo = async (userId: string) => {
    if (!userId || userCache[userId]) return;

    try {
      const response = await userApi.getUserDetail(userId);

      // 处理不同的响应格式
      let userInfo = null;
      if (response?.data?.success && response.data.data) {
        userInfo = response.data.data;
      } else if (response?.data && !response.data.success) {
        userInfo = response.data;
      } else if (response?.data) {
        userInfo = response.data;
      }

      if (userInfo) {
        setUserCache(prev => ({
          ...prev,
          [userId]: {
            _id: userInfo._id || userId,
            nickname: userInfo.nickname || userInfo.nickName || userInfo.name,
            avatar: userInfo.avatar || userInfo.avatarUrl,
            phone: userInfo.phone,
            ...userInfo
          }
        }));
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 设置默认用户信息
      setUserCache(prev => ({
        ...prev,
        [userId]: {
          _id: userId,
          nickname: `用户${userId.slice(-6)}`,
          avatar: null
        }
      }));
    }
  };

  // 批量加载用户信息
  const loadUsersInfo = async (transactions: any[]) => {
    const userIds = new Set<string>();

    // 收集所有用户ID
    transactions.forEach(transaction => {
      if (transaction.userId) userIds.add(transaction.userId);
    });

    // 批量获取用户信息
    const userPromises = Array.from(userIds).map(userId => loadUserInfo(userId));

    // 等待所有用户信息加载完成
    try {
      await Promise.allSettled(userPromises);
      console.log('用户信息加载完成，缓存状态:', userCache);
    } catch (error) {
      console.error('批量加载用户信息失败:', error);
    }
  };

  // 加载钱包数据
  const loadWalletData = async () => {
    try {
      setLoading(true);
      
      // 调用真实的API获取钱包交易数据
      console.log('🔄 开始加载钱包交易数据...');
      const response = await walletApi.getTransactionList({
        page: 1,
        limit: 100,
        type: activeTab === 'all' ? undefined : activeTab,
        search: searchTerm
      });

      console.log('📥 钱包API响应:', response);

      if (response.data && response.data.success) {
        const transactions = response.data.data?.transactions || [];
        console.log('✅ 成功获取交易数据:', transactions);

        // 转换数据格式以匹配前端期望的结构
        const formattedTransactions = transactions.map((tx: any) => ({
          id: tx._id,
          userId: tx.userId,
          type: tx.type,
          amount: tx.amount,
          status: tx.status,
          description: tx.description,
          createdAt: tx.createTime,
          completedAt: tx.completedAt || tx.createTime,
        }));

        setTransactions(formattedTransactions);

        // 异步加载用户信息
        loadUsersInfo(transactions);

        toast({
          title: '数据加载成功',
          description: `已从云数据库加载 ${formattedTransactions.length} 条真实交易记录`,
        });

        return; // 成功加载真实数据，直接返回
      } else {
        throw new Error(response.data?.error || '获取钱包数据失败');
      }
    } catch (error: any) {
      console.error('加载钱包数据失败:', error);

      toast({
        title: '数据加载失败',
        description: '无法连接到服务器，请检查网络连接后重试',
        variant: 'destructive',
      });

      // 不再使用模拟数据，保持空状态
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWalletData();
  }, [activeTab]);

  // 搜索交易
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm) {
        loadWalletData();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  const filteredTransactions = transactions.filter(txn => {
    const matchesSearch = txn.userId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         txn.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = activeTab === 'all' || txn.type === activeTab;
    return matchesSearch && matchesType;
  });

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'recharge':
        return <ArrowDownLeft className="h-4 w-4 text-green-600" />;
      case 'withdraw':
        return <ArrowUpRight className="h-4 w-4 text-red-600" />;
      case 'payment':
        return <CreditCard className="h-4 w-4 text-blue-600" />;
      case 'refund':
        return <ArrowDownLeft className="h-4 w-4 text-purple-600" />;
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: '处理中', variant: 'secondary' as const, icon: Clock },
      completed: { label: '已完成', variant: 'default' as const, icon: CheckCircle },
      failed: { label: '失败', variant: 'destructive' as const, icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className={
        status === 'completed' ? 'bg-green-100 text-green-800' :
        status === 'pending' ? 'bg-yellow-100 text-yellow-800' : ''
      }>
        <Icon className="mr-1 h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getTypeLabel = (type: string) => {
    const typeLabels = {
      recharge: '充值',
      withdraw: '提现',
      payment: '支付',
      refund: '退款',
    };
    return typeLabels[type as keyof typeof typeLabels] || type;
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleApproveWithdraw = async (txnId: string) => {
    try {
      // 调用API批准提现
      const response = await walletApi.approveWithdraw(txnId);
      
      if (response.success) {
        setTransactions(transactions.map(txn => 
          txn.id === txnId 
            ? { ...txn, status: 'completed', completedAt: new Date().toISOString() }
            : txn
        ));
        
        toast({
          title: '操作成功',
          description: '提现申请已批准',
        });
      } else {
        throw new Error(response.error || '操作失败');
      }
    } catch (error: any) {
      console.error('批准提现失败:', error);
      
      toast({
        title: '操作失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      });
    }
  };

  const handleRejectWithdraw = async (txnId: string) => {
    try {
      // 调用API拒绝提现
      const response = await walletApi.rejectWithdraw(txnId);
      
      if (response.success) {
        setTransactions(transactions.map(txn => 
          txn.id === txnId 
            ? { ...txn, status: 'failed' }
            : txn
        ));
        
        toast({
          title: '操作成功',
          description: '提现申请已拒绝',
        });
      } else {
        throw new Error(response.error || '操作失败');
      }
    } catch (error: any) {
      console.error('拒绝提现失败:', error);
      
      toast({
        title: '操作失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 统计数据
  const totalRecharge = transactions
    .filter(txn => txn.type === 'recharge' && txn.status === 'completed')
    .reduce((sum, txn) => sum + txn.amount, 0);
  
  const totalWithdraw = Math.abs(transactions
    .filter(txn => txn.type === 'withdraw' && txn.status === 'completed')
    .reduce((sum, txn) => sum + txn.amount, 0));
  
  const pendingWithdraws = transactions.filter(txn => txn.type === 'withdraw' && txn.status === 'pending').length;

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">钱包管理</h1>
            <p className="text-gray-600">管理用户钱包余额和交易记录</p>
          </div>
          <Button disabled>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            加载中...
          </Button>
        </div>
        
        <div className="grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">钱包管理</h1>
          <p className="text-gray-600">管理用户钱包余额和交易记录</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadWalletData} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总充值金额</CardTitle>
            <ArrowDownLeft className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">¥{totalRecharge.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +12% 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总提现金额</CardTitle>
            <ArrowUpRight className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">¥{totalWithdraw.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingDown className="inline h-3 w-3 mr-1" />
              -5% 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待审核提现</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{pendingWithdraws}</div>
            <p className="text-xs text-muted-foreground">需要处理</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平台余额</CardTitle>
            <Wallet className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">¥{(totalRecharge - totalWithdraw).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">当前平台资金池</p>
          </CardContent>
        </Card>
      </div>

      {/* 交易记录 */}
      <Card>
        <CardHeader>
          <CardTitle>交易记录</CardTitle>
          <CardDescription>
            管理所有用户的钱包交易记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="recharge">充值</TabsTrigger>
              <TabsTrigger value="withdraw">提现</TabsTrigger>
              <TabsTrigger value="payment">支付</TabsTrigger>
              <TabsTrigger value="refund">退款</TabsTrigger>
            </TabsList>

            {/* 搜索和筛选 */}
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索用户ID或交易描述..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                高级筛选
              </Button>
            </div>

            <TabsContent value={activeTab}>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>交易信息</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>完成时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getTransactionIcon(transaction.type)}
                          <div>
                            <div className="font-medium">#{transaction.id}</div>
                            <div className="text-sm text-gray-500">{transaction.description}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={userCache[transaction.userId]?.avatar || "/placeholder.svg?height=32&width=32"} />
                            <AvatarFallback>用</AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">{getUserDisplayName(transaction.userId)}</span>
                            <span className="text-xs text-gray-500">#{transaction.userId.slice(-8)}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getTypeLabel(transaction.type)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${
                          transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.amount > 0 ? '+' : ''}¥{Math.abs(transaction.amount).toLocaleString()}
                        </span>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(transaction.status)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {formatDate(transaction.createdAt)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {transaction.completedAt ? formatDate(transaction.completedAt) : '-'}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuItem>查看详情</DropdownMenuItem>
                            {transaction.type === 'withdraw' && transaction.status === 'pending' && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleApproveWithdraw(transaction.id)}
                                  className="text-green-600"
                                >
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  批准提现
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleRejectWithdraw(transaction.id)}
                                  className="text-red-600"
                                >
                                  <XCircle className="mr-2 h-4 w-4" />
                                  拒绝提现
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredTransactions.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>没有找到匹配的交易记录</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}