// 云函数通用工具
const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

class CloudFunctionHelper {
  // 验证用户并获取用户信息
  static async validateUser(wxContext) {
    try {
      const userResult = await db.collection('users').where({
        openid: wxContext.OPENID
      }).get();

      if (userResult.data.length === 0) {
        return {
          success: false,
          error: '用户不存在，请先登录'
        };
      }

      return {
        success: true,
        user: userResult.data[0]
      };
    } catch (error) {
      return {
        success: false,
        error: '用户验证失败: ' + error.message
      };
    }
  }

  // 标准化云函数返回格式
  static successResponse(data, message = '操作成功') {
    return {
      success: true,
      message,
      data
    };
  }

  static errorResponse(error, message = '操作失败') {
    return {
      success: false,
      error: typeof error === 'string' ? error : error.message,
      message
    };
  }

  // 验证必需参数
  static validateParams(params, requiredFields) {
    const missing = [];
    for (const field of requiredFields) {
      if (!params[field]) {
        missing.push(field);
      }
    }

    if (missing.length > 0) {
      return {
        valid: false,
        error: `缺少必需参数: ${missing.join(', ')}`
      };
    }

    return { valid: true };
  }

  // 安全的数据库操作包装
  static async safeDbOperation(operation, errorMessage = '数据库操作失败') {
    try {
      return await operation();
    } catch (error) {
      console.error(errorMessage, error);
      throw new Error(errorMessage + ': ' + error.message);
    }
  }
}

module.exports = CloudFunctionHelper;
