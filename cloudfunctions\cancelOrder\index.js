// 取消订单云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 工具函数：验证订单权限
async function validateOrderPermission(orderId, userId, userRole) {
  try {
    const orderResult = await db.collection('orders').doc(orderId).get();

    if (!orderResult.data) {
      throw new Error('订单不存在');
    }

    const order = orderResult.data;

    // 检查订单状态 - 不能取消已完成或已取消的订单
    if (order.status === 'completed' || order.status === 'cancelled') {
      throw new Error('已完成或已取消的订单无法再次取消');
    }

    // 检查用户权限
    let hasPermission = false;



    if (userRole === 'customer' && String(order.customerId) === String(userId)) {
      // 发单者可以取消自己发布的订单（待接单、已接单、进行中）
      hasPermission = true;
    } else if ((userRole === 'accepter' || userRole === 'acceptor') && String(order.accepterId) === String(userId)) {
      // 接单者可以取消已接单或进行中的订单
      hasPermission = (order.status === 'accepted' || order.status === 'in_progress');
    }

    if (!hasPermission) {
      throw new Error('您没有权限取消此订单');
    }

    return order;
  } catch (error) {
    console.error('❌ [权限验证] 验证失败:', error);
    throw error;
  }
}

// 工具函数：更新聊天室状态
async function updateChatRoomStatus(orderNo) {
  try {
    console.log('🔄 [聊天室更新] 开始更新聊天室状态, 订单号:', orderNo);

    // 查找相关聊天室
    const chatRoomsResult = await db.collection('chatRooms').where({
      orderNo: orderNo
    }).get();

    if (chatRoomsResult.data.length === 0) {
      console.log('⚠️ [聊天室更新] 未找到相关聊天室');
      return;
    }

    // 批量更新聊天室状态
    const updatePromises = chatRoomsResult.data.map(chatRoom => {
      return db.collection('chatRooms').doc(chatRoom._id).update({
        data: {
          status: 'cancelled',
          updateTime: new Date()
        }
      });
    });

    await Promise.all(updatePromises);
    console.log(`✅ [聊天室更新] 成功更新 ${chatRoomsResult.data.length} 个聊天室状态`);

  } catch (error) {
    console.error('❌ [聊天室更新] 更新失败:', error);
    // 聊天室更新失败不影响订单取消
  }
}

// 工具函数：发送实时通知
async function sendRealtimeNotification(order, cancellerInfo, userRole) {
  try {
    console.log('📢 [实时通知] 开始发送取消通知');

    // 确定接收通知的用户
    let receiverId, receiverName;

    console.log('📢 [实时通知] 用户角色判断:', {
      userRole,
      cancellerUserId: cancellerInfo._id,
      orderCustomerId: order.customerId,
      orderAccepterId: order.accepterId
    });

    if (userRole === 'customer') {
      // 发单者取消，通知接单者
      receiverId = order.accepterId;
      receiverName = order.acceptorInfo?.nickName || '接单者';
      console.log('📢 [实时通知] 发单者取消，通知接单者:', receiverId);
    } else {
      // 接单者取消，通知发单者
      receiverId = order.customerId;
      receiverName = order.customerInfo?.nickName || '发单者';
      console.log('📢 [实时通知] 接单者取消，通知发单者:', receiverId);
    }

    if (!receiverId) {
      console.log('⚠️ [实时通知] 未找到接收者ID');
      return;
    }

    // 确保不通知取消者自己（使用字符串比较确保类型一致）
    if (String(receiverId) === String(cancellerInfo._id)) {
      console.log('⚠️ [实时通知] 跳过通知：接收者就是取消者本人', {
        receiverId: receiverId,
        cancellerUserId: cancellerInfo._id,
        receiverIdType: typeof receiverId,
        cancellerUserIdType: typeof cancellerInfo._id
      });
      return;
    }

    console.log('✅ [实时通知] 通知对象确认:', {
      receiverId: receiverId,
      cancellerUserId: cancellerInfo._id,
      isDifferent: String(receiverId) !== String(cancellerInfo._id)
    });

    // 创建通知记录
    const notificationData = {
      type: 'order_cancelled',
      title: order.title || '订单已取消',
      content: `此订单已被${cancellerInfo.nickName}取消`,
      orderId: order._id,
      orderNo: order.orderNo,
      receiverId: receiverId,
      senderId: cancellerInfo._id,
      senderName: cancellerInfo.nickName,
      status: 'unread',
      createTime: new Date()
    };

    console.log('📢 [实时通知] 准备创建通知记录:', {
      title: notificationData.title,
      content: notificationData.content,
      receiverId: notificationData.receiverId,
      senderId: notificationData.senderId,
      orderTitle: order.title
    });

    await db.collection('notifications').add({
      data: notificationData
    });

    console.log('✅ [实时通知] 通知记录创建成功');

  } catch (error) {
    console.error('❌ [实时通知] 发送失败:', error);
    // 通知发送失败不影响订单取消
  }
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { orderId, reason, userRole } = event;

  console.log('=== 取消订单云函数开始 ===');
  console.log('📋 [请求参数] 订单ID:', orderId, '原因:', reason, '用户角色:', userRole);
  console.log('📋 [用户信息] OpenID:', wxContext.OPENID);

  try {
    // 参数验证
    if (!orderId) {
      return {
        success: false,
        error: '订单ID不能为空'
      };
    }

    if (!userRole) {
      return {
        success: false,
        error: '用户角色不能为空'
      };
    }

    // 获取用户信息
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在，请先登录'
      };
    }

    const user = userResult.data[0];
    console.log('✅ [用户验证] 当前用户:', user._id, user.nickName);

    // 验证订单权限并获取订单信息
    const order = await validateOrderPermission(orderId, user._id, userRole);

    // 更新订单状态
    const updateResult = await db.collection('orders').doc(orderId).update({
      data: {
        status: 'cancelled',
        cancelReason: reason || 'problem',
        cancelledBy: user._id,
        cancelledAt: new Date(),
        updateTime: new Date()
      }
    });

    console.log('✅ [订单取消] 订单状态更新成功:', updateResult);

    // 更新聊天室状态
    await updateChatRoomStatus(order.orderNo);

    // 发送实时通知
    await sendRealtimeNotification(order, user, userRole);

    return {
      success: true,
      message: '订单取消成功',
      data: {
        orderId: orderId,
        status: 'cancelled'
      }
    };

  } catch (error) {
    console.error('❌ [云函数错误]:', error);
    return {
      success: false,
      error: error.message || '取消订单失败'
    };
  }
};
