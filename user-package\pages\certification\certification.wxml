<!--certification.wxml-->
<navigation-bar title="实名认证" back="{{true}}"></navigation-bar>

<!-- 返回按钮 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<view class="certification-container page-with-custom-nav">
  <!-- 认证状态 -->
  <view class="status-card">
    <view class="status-header">
      <image class="status-icon" src="{{userInfo.isVerified ? '/images/icons/verified.png' : '/images/icons/unverified.png'}}" />
      <text class="status-text">{{userInfo.isVerified ? '已完成实名认证' : '未完成实名认证'}}</text>
    </view>
    <text class="status-desc" wx:if="{{!userInfo.isVerified}}">
      为了保障您的账户安全和交易安全，请完成实名认证
    </text>
    <text class="status-desc" wx:else>
      您已完成实名认证，可以正常使用所有功能
    </text>
  </view>

  <!-- 认证表单 -->
  <view class="form-card" wx:if="{{!userInfo.isVerified}}">
    <view class="card-title">实名认证信息</view>
    
    <view class="form-group">
      <text class="form-label">真实姓名</text>
      <input class="form-input" 
             placeholder="请输入真实姓名" 
             value="{{formData.realName}}"
             bindinput="onInputChange"
             data-field="realName" />
    </view>

    <view class="form-group">
      <text class="form-label">身份证号</text>
      <input class="form-input" 
             placeholder="请输入身份证号码" 
             value="{{formData.idCard}}"
             bindinput="onInputChange"
             data-field="idCard" />
    </view>

    <view class="form-group">
      <text class="form-label">手机号码</text>
      <input class="form-input" 
             placeholder="请输入手机号码" 
             value="{{formData.phone}}"
             bindinput="onInputChange"
             data-field="phone" />
    </view>

    <view class="form-group">
      <text class="form-label">身份证照片</text>
      <view class="upload-section">
        <view class="upload-item">
          <text class="upload-label">身份证正面</text>
          <view class="upload-area" bindtap="uploadIdCardFront">
            <image class="upload-image" src="{{formData.idCardFront}}" wx:if="{{formData.idCardFront}}" />
            <view class="upload-placeholder" wx:else>
              <image class="upload-icon" src="/images/icons/camera.png" />
              <text class="upload-text">点击上传</text>
            </view>
          </view>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">身份证反面</text>
          <view class="upload-area" bindtap="uploadIdCardBack">
            <image class="upload-image" src="{{formData.idCardBack}}" wx:if="{{formData.idCardBack}}" />
            <view class="upload-placeholder" wx:else>
              <image class="upload-icon" src="/images/icons/camera.png" />
              <text class="upload-text">点击上传</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="form-tips">
      <text class="tip-text">• 请确保身份证照片清晰完整</text>
      <text class="tip-text">• 身份证信息将严格保密，仅用于实名认证</text>
      <text class="tip-text">• 认证通过后无法修改，请仔细核对信息</text>
    </view>

    <button class="submit-btn" 
            bindtap="submitCertification"
            disabled="{{!canSubmit || submitting}}">
      {{submitting ? '提交中...' : '提交认证'}}
    </button>
  </view>

  <!-- 已认证信息 -->
  <view class="info-card" wx:if="{{userInfo.isVerified}}">
    <view class="card-title">认证信息</view>
    
    <view class="info-item">
      <text class="info-label">真实姓名</text>
      <text class="info-value">{{userInfo.realName}}</text>
    </view>
    
    <view class="info-item">
      <text class="info-label">身份证号</text>
      <text class="info-value">{{maskedIdCard}}</text>
    </view>
    
    <view class="info-item">
      <text class="info-label">手机号码</text>
      <text class="info-value">{{maskedPhone}}</text>
    </view>
    
    <view class="info-item">
      <text class="info-label">认证时间</text>
      <text class="info-value">{{certificationTime}}</text>
    </view>
  </view>
</view>
