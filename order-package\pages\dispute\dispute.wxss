/* 仲裁处理页面样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow-x: hidden;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #00d4ff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 255, 0.3);
  border-top: 3px solid #00d4ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 16px;
  color: #00d4ff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主要内容 */
.content {
  position: relative;
  z-index: 1;
  padding: 20px;
  padding-bottom: 40px;
}

/* 页面头部 */
.page-header {
  position: relative;
  margin-bottom: 30px;
  text-align: center;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 80px;
  background: linear-gradient(45deg, rgba(255, 119, 198, 0.1), rgba(0, 212, 255, 0.1));
  border-radius: 40px;
  filter: blur(20px);
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 20px 0;
}

.page-title {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #ff77c6;
  text-shadow: 0 0 10px rgba(255, 119, 198, 0.5);
  margin-bottom: 8px;
}

.page-subtitle {
  display: block;
  font-size: 14px;
  color: #888;
}

/* 订单信息卡片 */
.order-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.card-header {
  margin-bottom: 15px;
}

.order-title {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
}

.order-no {
  display: block;
  font-size: 14px;
  color: #888;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  font-size: 14px;
  color: #ccc;
}

/* 表单区域 */
.dispute-form {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 119, 198, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.form-section {
  margin-bottom: 30px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.title-text {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-right: 5px;
}

.required {
  color: #ff77c6;
  font-size: 14px;
}

.optional {
  color: #888;
  font-size: 12px;
}

/* 仲裁原因选择 */
.reason-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reason-item {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s;
}

.reason-item.selected {
  border-color: #ff77c6;
  background: rgba(255, 119, 198, 0.1);
}

.reason-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reason-label {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
}

.reason-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #666;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.reason-item.selected .reason-radio {
  border-color: #ff77c6;
}

.radio-dot {
  width: 10px;
  height: 10px;
  background: #ff77c6;
  border-radius: 50%;
}

.reason-description {
  font-size: 14px;
  color: #888;
  line-height: 1.4;
}

/* 输入框样式 */
.custom-reason-input,
.description-input {
  width: 100%;
  min-height: 80px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  color: #fff;
  font-size: 14px;
  line-height: 1.5;
  box-sizing: border-box;
}

.description-input {
  min-height: 120px;
}

.input-counter {
  display: block;
  text-align: right;
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

/* 证据图片 */
.evidence-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 10px;
}

.evidence-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.evidence-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  background: rgba(255, 0, 0, 0.8);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

.add-evidence-btn {
  aspect-ratio: 1;
  border: 2px dashed rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.add-evidence-btn:active {
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
}

.add-icon {
  font-size: 24px;
  color: #00d4ff;
  margin-bottom: 5px;
}

.add-text {
  font-size: 12px;
  color: #888;
}

.evidence-tip {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 提交区域 */
.submit-section {
  margin-top: 30px;
}

.submit-btn {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  background: linear-gradient(135deg, #ff77c6, #ff4081);
  border: 1px solid #ff77c6;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: glow 2s infinite;
}

@keyframes glow {
  0% { left: -100%; }
  100% { left: 100%; }
}

.submit-tips {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.tip-item {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 查看仲裁信息 */
.dispute-info {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 119, 198, 0.2);
  border-radius: 12px;
  padding: 20px;
}

.info-card {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #888;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #fff;
  flex: 1;
}

.info-value.description {
  line-height: 1.5;
}

.status-pending { color: #ffa500; }
.status-investigating { color: #00d4ff; }
.status-resolved { color: #4caf50; }
.status-rejected { color: #f44336; }

.evidence-display {
  margin-bottom: 20px;
}

.evidence-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 15px;
}

.result-section {
  margin-top: 20px;
}

.result-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 15px;
}

.result-card {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.result-text {
  display: block;
  font-size: 14px;
  color: #4caf50;
  line-height: 1.5;
  margin-bottom: 10px;
}

.result-time {
  display: block;
  font-size: 12px;
  color: #888;
}
