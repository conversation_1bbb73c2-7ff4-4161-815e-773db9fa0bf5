import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataPagination } from '@/components/ui/data-pagination';
import { PermissionCheck } from '@/components/auth/ProtectedRoute';
import { 
  Search, 
  RefreshCw, 
  Download, 
  Calendar,
  User,
  Activity,
  Clock,
  Monitor,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useToast } from '@/hooks/use-toast';

// 操作日志接口
interface OperationLog {
  id: string;
  userId: string;
  username: string;
  action: string;
  description: string;
  details?: any;
  timestamp: Date;
  ip: string;
  userAgent: string;
  status: 'success' | 'error' | 'warning';
  duration?: number; // 操作耗时（毫秒）
}

export default function OperationLogsPage() {
  const [logs, setLogs] = useState<OperationLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const { toast } = useToast();

  // 加载操作日志
  const loadLogs = async (page = 1, size = pageSize) => {
    try {
      setLoading(true);
      
      // 从本地存储获取日志（实际应用中应该从API获取）
      const storedLogs = JSON.parse(localStorage.getItem('operation_logs') || '[]');
      
      // 过滤日志
      let filteredLogs = storedLogs.filter((log: any) => {
        const matchesSearch = !searchTerm || 
          log.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.description.toLowerCase().includes(searchTerm.toLowerCase());
        
        const matchesAction = !selectedAction || log.action === selectedAction;
        const matchesStatus = !selectedStatus || log.status === selectedStatus;
        
        return matchesSearch && matchesAction && matchesStatus;
      });

      // 转换日期格式
      filteredLogs = filteredLogs.map((log: any) => ({
        ...log,
        timestamp: new Date(log.timestamp),
        status: log.status || 'success'
      }));

      // 排序（最新的在前）
      filteredLogs.sort((a: any, b: any) => b.timestamp.getTime() - a.timestamp.getTime());

      // 分页
      const startIndex = (page - 1) * size;
      const endIndex = startIndex + size;
      const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

      setLogs(paginatedLogs);
      setTotalItems(filteredLogs.length);
      setCurrentPage(page);
    } catch (error) {
      console.error('加载操作日志失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载操作日志',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLogs();
  }, [searchTerm, selectedAction, selectedStatus, pageSize]);

  // 获取操作类型列表
  const getActionTypes = () => {
    const storedLogs = JSON.parse(localStorage.getItem('operation_logs') || '[]');
    const actions = [...new Set(storedLogs.map((log: any) => log.action))];
    return actions;
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 导出日志
  const handleExportLogs = () => {
    try {
      const csvContent = [
        ['时间', '用户', '操作', '描述', '状态', 'IP地址'].join(','),
        ...logs.map(log => [
          format(log.timestamp, 'yyyy-MM-dd HH:mm:ss'),
          log.username,
          log.action,
          log.description,
          log.status,
          log.ip
        ].join(','))
      ].join('\n');

      const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `操作日志_${format(new Date(), 'yyyy-MM-dd')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: '导出成功',
        description: '操作日志已导出到CSV文件',
      });
    } catch (error) {
      toast({
        title: '导出失败',
        description: '导出操作日志时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 清理旧日志
  const handleCleanOldLogs = () => {
    if (confirm('确定要清理30天前的日志吗？此操作不可恢复。')) {
      try {
        const storedLogs = JSON.parse(localStorage.getItem('operation_logs') || '[]');
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        const filteredLogs = storedLogs.filter((log: any) => 
          new Date(log.timestamp) > thirtyDaysAgo
        );
        
        localStorage.setItem('operation_logs', JSON.stringify(filteredLogs));
        loadLogs();
        
        toast({
          title: '清理完成',
          description: `已清理 ${storedLogs.length - filteredLogs.length} 条旧日志`,
        });
      } catch (error) {
        toast({
          title: '清理失败',
          description: '清理旧日志时发生错误',
          variant: 'destructive',
        });
      }
    }
  };

  const totalPages = Math.ceil(totalItems / pageSize);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">操作日志</h1>
          <p className="text-gray-600">查看系统操作记录和审计日志</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => loadLogs()} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
          <PermissionCheck permission="system.logs">
            <Button variant="outline" onClick={handleExportLogs}>
              <Download className="mr-2 h-4 w-4" />
              导出日志
            </Button>
          </PermissionCheck>
          <PermissionCheck role="super_admin">
            <Button variant="outline" onClick={handleCleanOldLogs}>
              <Calendar className="mr-2 h-4 w-4" />
              清理旧日志
            </Button>
          </PermissionCheck>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总操作数</p>
                <p className="text-2xl font-bold text-gray-900">{totalItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">成功操作</p>
                <p className="text-2xl font-bold text-gray-900">
                  {logs.filter(log => log.status === 'success').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">失败操作</p>
                <p className="text-2xl font-bold text-gray-900">
                  {logs.filter(log => log.status === 'error').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <User className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">活跃用户</p>
                <p className="text-2xl font-bold text-gray-900">
                  {new Set(logs.map(log => log.userId)).size}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜索用户名、操作或描述..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedAction}
                onChange={(e) => setSelectedAction(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="">所有操作</option>
                {getActionTypes().map(action => (
                  <option key={action} value={action}>{action}</option>
                ))}
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="">所有状态</option>
                <option value="success">成功</option>
                <option value="error">失败</option>
                <option value="warning">警告</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 日志列表 */}
      <Card>
        <CardHeader>
          <CardTitle>操作记录</CardTitle>
          <CardDescription>
            显示 {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, totalItems)} 条，共 {totalItems} 条记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span>加载中...</span>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无操作日志
            </div>
          ) : (
            <div className="space-y-4">
              {logs.map((log) => (
                <div key={log.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      {getStatusIcon(log.status)}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium text-gray-900">{log.username}</span>
                          <Badge variant="outline">{log.action}</Badge>
                          <Badge className={getStatusColor(log.status)}>
                            {log.status === 'success' ? '成功' : 
                             log.status === 'error' ? '失败' : '警告'}
                          </Badge>
                        </div>
                        <p className="text-gray-700 mb-2">{log.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {format(log.timestamp, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
                          </div>
                          <div className="flex items-center">
                            <Monitor className="h-3 w-3 mr-1" />
                            {log.ip}
                          </div>
                          {log.duration && (
                            <div>耗时: {log.duration}ms</div>
                          )}
                        </div>
                        {log.details && (
                          <details className="mt-2">
                            <summary className="text-sm text-blue-600 cursor-pointer">查看详情</summary>
                            <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                              {JSON.stringify(log.details, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 分页 */}
          {totalItems > 0 && (
            <div className="mt-6">
              <DataPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={setPageSize}
                pageSizeOptions={[10, 20, 50, 100]}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
