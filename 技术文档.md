# 三角洲任务平台 - 技术文档

## 1. 项目概览

### 1.1 项目简介
三角洲任务平台是一个基于微信小程序的任务发布与接单平台，采用微信云开发技术栈，支持用户发布游戏陪玩任务、接单服务、实时聊天等功能。

### 1.2 技术栈
- **前端**: 微信小程序原生框架
- **后端**: 微信云开发 (云函数、云数据库、云存储)
- **数据库**: 微信云数据库 (MongoDB)
- **实时通信**: 微信云数据库实时监听
- **存储**: 微信云存储

## 2. 项目结构

### 2.1 主包结构
```
├── app.js                 # 小程序入口文件
├── app.json               # 全局配置文件
├── app.wxss               # 全局样式文件
├── pages/                 # 主包页面
│   ├── splash/            # 启动页
│   ├── index/             # 首页 (抢单大厅)
│   ├── order/list/        # 我的订单列表
│   ├── chat/list/         # 消息列表
│   ├── user/profile/      # 个人中心
│   └── common/login/      # 登录页
├── components/            # 全局组件
├── utils/                 # 工具类
└── style/                 # 全局样式
```

### 2.2 子包结构
项目采用分包加载策略，包含5个子包：

#### order-package (订单业务包)
```
order-package/
├── pages/
│   ├── create/            # 创建订单
│   ├── detail/            # 订单详情
│   ├── completed/         # 已完成订单
│   └── evaluation/        # 订单评价
```

#### chat-package (聊天通信包)
```
chat-package/
├── pages/
│   └── room/              # 聊天室
```

#### user-package (用户服务包)
```
user-package/
├── pages/
│   ├── certification/     # 实名认证
│   ├── settings/          # 用户设置
│   ├── profile-edit/      # 编辑资料
│   └── evaluation-history/ # 评价历史
```

#### payment-package (支付钱包包)
```
payment-package/
├── pages/
│   ├── wallet/            # 钱包
│   ├── recharge/          # 充值
│   ├── withdraw/          # 提现
│   └── records/           # 交易记录
```

#### utility-package (辅助功能包)
```
utility-package/
├── pages/
│   ├── statistics/        # 统计数据
│   ├── webview/           # 网页视图
│   ├── status/            # 状态页面
│   ├── notification/      # 通知列表
│   └── evaluation/        # 评价详情
```

### 2.3 云函数结构
```
cloudfunctions/
├── 用户相关
│   ├── login              # 用户登录
│   ├── getUserInfo        # 获取用户信息
│   ├── updateUserInfo     # 更新用户信息
│   └── certification      # 实名认证
├── 订单相关
│   ├── createOrder        # 创建订单
│   ├── getOrderList       # 获取订单列表
│   ├── getOrderDetail     # 获取订单详情
│   ├── getGrabOrderList   # 获取抢单大厅列表
│   ├── acceptOrder        # 接单/抢单
│   ├── updateOrderStatus  # 更新订单状态
│   └── cancelOrder        # 取消订单
├── 聊天相关
│   ├── chatRoom           # 聊天室管理
│   ├── chatMessage        # 聊天消息
│   ├── chatList           # 聊天列表
│   └── updateChatRoomLastMessage # 更新最后消息
├── 评价相关
│   ├── submitEvaluation   # 提交评价
│   ├── getEvaluationInfo  # 获取评价信息
│   └── getUserEvaluationStats # 获取用户评价统计
├── 通知相关
│   ├── sendNotification   # 发送通知
│   ├── getNotificationList # 获取通知列表
│   ├── markNotificationRead # 标记通知已读
│   └── notificationManager # 通知管理器
├── 支付相关
│   ├── createRecharge     # 创建充值
│   ├── createWithdraw     # 创建提现
│   ├── getTransactionList # 获取交易列表
│   └── paymentCallback    # 支付回调
├── 统计相关
│   ├── getOrderStatistics # 获取订单统计
│   ├── getOrderCounts     # 获取订单数量
│   └── getWalletStats     # 获取钱包统计
└── 系统相关
    ├── initDatabase       # 初始化数据库
    ├── initNotifications  # 初始化通知
    ├── securityMiddleware # 安全中间件
    └── reputationSystem   # 信誉系统
```

## 3. 数据库设计

### 3.1 核心集合

#### users (用户表)
```javascript
{
  _id: ObjectId,
  openid: String,           // 微信openid
  unionid: String,          // 微信unionid
  nickName: String,         // 昵称
  avatarUrl: String,        // 头像URL
  gameNickName: String,     // 游戏昵称
  phone: String,            // 手机号
  realName: String,         // 真实姓名
  idCard: String,           // 身份证号
  isVerified: Boolean,      // 是否实名认证
  balance: Number,          // 账户余额
  creditScore: Number,      // 信用分
  status: String,           // 账户状态 (active/banned)
  createTime: Date,         // 创建时间
  updateTime: Date          // 更新时间
}
```

#### orders (订单表)
```javascript
{
  _id: ObjectId,
  orderNo: String,          // 订单号
  customerId: ObjectId,     // 发布者ID
  customerOpenid: String,   // 发布者openid
  accepterId: ObjectId,     // 接单者ID
  title: String,            // 订单标题
  content: String,          // 任务内容
  reward: Number,           // 悬赏金额
  platformType: String,     // 平台类型 (pc/mobile)
  serviceType: String,      // 服务类型 (duration/rounds)
  duration: Number,         // 时长(小时)
  rounds: Number,           // 局数
  tags: Array,              // 标签
  orderType: String,        // 订单类型 (immediate/scheduled)
  status: String,           // 订单状态 (pending/accepted/in_progress/completed/cancelled)
  scheduledDate: String,    // 预约日期
  scheduledTime: String,    // 预约时间
  chatRoomId: ObjectId,     // 聊天室ID
  evaluation: {             // 评价信息
    customerRating: Number,
    accepterRating: Number,
    customerComment: String,
    accepterComment: String
  },
  createTime: Date,         // 创建时间
  updateTime: Date          // 更新时间
}
```

#### chatRooms (聊天室表)
```javascript
{
  _id: ObjectId,
  orderNo: String,          // 关联订单号
  orderDbId: ObjectId,      // 关联订单ID
  customerId: ObjectId,     // 发布者ID
  accepterId: ObjectId,     // 接单者ID
  customerInfo: {           // 发布者信息快照
    nickName: String,
    avatarUrl: String
  },
  accepterInfo: {           // 接单者信息快照
    nickName: String,
    avatarUrl: String
  },
  orderInfo: {              // 订单信息快照
    title: String,
    reward: Number,
    status: String
  },
  lastMessage: Object,      // 最后一条消息
  status: String,           // 聊天室状态 (active/closed)
  createTime: Date,         // 创建时间
  updateTime: Date          // 更新时间
}
```

#### messages (消息表)
```javascript
{
  _id: ObjectId,
  chatRoomId: ObjectId,     // 聊天室ID
  senderId: ObjectId,       // 发送者ID
  senderInfo: {             // 发送者信息
    nickName: String,
    avatarUrl: String
  },
  messageType: String,      // 消息类型 (text/image/system)
  content: String,          // 消息内容
  imageUrl: String,         // 图片URL (图片消息)
  createTime: Date          // 创建时间
}
```

#### evaluations (评价表)
```javascript
{
  _id: ObjectId,
  orderId: ObjectId,        // 订单ID
  orderNo: String,          // 订单号
  evaluatorId: ObjectId,    // 评价者ID
  evaluatedId: ObjectId,    // 被评价者ID
  rating: Number,           // 评分 (1-5)
  comment: String,          // 评价内容
  tags: Array,              // 评价标签
  evaluationType: String,   // 评价类型 (customer_to_accepter/accepter_to_customer)
  createTime: Date          // 创建时间
}
```

#### notifications (通知表)
```javascript
{
  _id: ObjectId,
  type: String,             // 通知类型 (order/chat/system)
  title: String,            // 通知标题
  content: String,          // 通知内容
  receiverId: ObjectId,     // 接收者ID
  senderId: ObjectId,       // 发送者ID
  senderName: String,       // 发送者名称
  status: String,           // 状态 (unread/read)
  orderId: ObjectId,        // 关联订单ID
  orderNo: String,          // 关联订单号
  createTime: Date          // 创建时间
}
```

## 4. 核心功能模块

### 4.1 订单系统

#### 订单状态流转
```
pending (待接单) → accepted (已接单) → in_progress (进行中) → completed (已完成)
                                   ↘ cancelled (已取消)
```

#### 主要功能
- **订单发布**: 用户可创建即时或预约订单
- **抢单大厅**: 显示所有待接单的订单
- **订单管理**: 查看、编辑、取消订单
- **状态更新**: 自动和手动状态流转
- **订单评价**: 双向评价系统

### 4.2 聊天系统

#### 实时通信机制
- 基于微信云数据库的实时监听
- 支持文本和图片消息
- 自动创建聊天室
- 消息状态同步

#### 主要功能
- **聊天室管理**: 基于订单自动创建
- **实时消息**: 双向实时通信
- **消息历史**: 持久化存储
- **未读提醒**: 实时未读数量更新

### 4.3 用户系统

#### 用户角色
- **订单发布者**: 发布任务的用户
- **订单接单者**: 接受任务的用户

#### 主要功能
- **用户认证**: 微信登录 + 实名认证
- **个人资料**: 昵称、头像、游戏信息
- **信用系统**: 基于评价的信用分
- **评价历史**: 收到和给出的评价记录

### 4.4 通知系统

#### 通知类型
- **订单通知**: 接单、状态变更等
- **聊天通知**: 新消息提醒
- **系统通知**: 系统公告等

#### 实现机制
- 云函数触发通知
- 实时数据库监听
- 应用内通知组件

## 5. 技术架构

### 5.1 前端架构

#### 页面路由
- **TabBar页面**: 首页、订单、消息、个人中心
- **子包页面**: 按功能模块分包加载
- **导航管理**: 统一的页面跳转和参数传递

#### 组件化设计
- **全局组件**: 导航栏、按钮、加载器等
- **业务组件**: 订单卡片、聊天气泡等
- **工具组件**: 返回按钮、通知提示等

#### 状态管理
- **全局状态**: app.globalData
- **页面状态**: 页面级data
- **实时同步**: 数据库监听器

### 5.2 云开发架构

#### 云函数设计
- **单一职责**: 每个云函数专注特定功能
- **错误处理**: 统一的错误处理机制
- **权限控制**: 基于用户身份的权限验证
- **性能优化**: 数据库查询优化和缓存

#### 数据库设计
- **集合设计**: 按业务领域划分
- **索引优化**: 关键字段建立索引
- **数据冗余**: 适度冗余提升查询性能
- **实时监听**: 关键数据变更实时同步

### 5.3 实时数据同步

#### 监听机制
```javascript
// 订单状态监听
db.collection('orders').where({
  $or: [
    { customerId: userId },
    { accepterId: userId }
  ]
}).watch({
  onChange: (snapshot) => {
    // 处理数据变更
  }
});
```

#### 数据流向
```
用户操作 → 云函数处理 → 数据库更新 → 实时监听 → 前端更新
```

## 6. 开发规范

### 6.1 代码组织规范

#### 文件命名
- **页面文件**: 小写字母 + 连字符 (kebab-case)
- **组件文件**: 小写字母 + 连字符
- **云函数**: 驼峰命名 (camelCase)
- **工具类**: 驼峰命名

#### 目录结构
- **按功能模块**: 相关文件放在同一目录
- **分包策略**: 按业务领域分包
- **资源管理**: 图片等资源统一管理

### 6.2 命名约定

#### 变量命名
- **页面数据**: 驼峰命名
- **事件处理**: on + 动作名称
- **API方法**: 动词 + 名词

#### 数据库字段
- **ID字段**: 统一使用 _id
- **时间字段**: createTime, updateTime
- **状态字段**: status
- **关联字段**: xxxId

### 6.3 数据流向说明

#### 订单流程
```
创建订单 → 抢单大厅 → 接单 → 聊天沟通 → 完成订单 → 双向评价
```

#### 消息流程
```
发送消息 → 云函数处理 → 数据库存储 → 实时推送 → 接收方显示
```

#### 通知流程
```
触发事件 → 云函数生成通知 → 数据库存储 → 实时推送 → 用户接收
```

## 7. 核心页面功能详解

### 7.1 首页 (pages/index/index)

#### 主要功能
- **抢单大厅**: 显示所有可接单的订单
- **轮播图**: 展示平台公告和活动
- **快速操作**: 发布订单、查看消息等
- **实时更新**: 订单状态实时同步

#### 关键代码逻辑
```javascript
// 订单实时监听
startOrderWatcher() {
  const db = wx.cloud.database();
  this.orderWatcher = db.collection('orders')
    .where({ status: 'pending' })
    .orderBy('createTime', 'desc')
    .limit(20)
    .watch({
      onChange: (snapshot) => {
        this.handleOrderChange(snapshot);
      }
    });
}
```

### 7.2 订单详情页 (order-package/pages/detail/detail)

#### 主要功能
- **订单信息展示**: 完整的订单详情
- **状态管理**: 根据用户角色显示不同操作
- **实时更新**: 订单状态变更实时同步
- **聊天入口**: 快速进入聊天室

#### 用户角色权限
- **发布者**: 可以取消订单、联系接单者、评价
- **接单者**: 可以开始服务、完成订单、评价
- **访客**: 只能查看基本信息

#### 状态按钮逻辑
```javascript
getActionButtons(orderInfo) {
  const { status, userRole } = orderInfo;
  const buttons = [];

  if (userRole === 'customer') {
    // 发布者按钮逻辑
    if (status === 'pending') {
      buttons.push({ type: 'cancel', text: '取消订单' });
    } else if (status === 'accepted') {
      buttons.push({ type: 'chat', text: '联系接单者' });
    }
  } else if (userRole === 'accepter') {
    // 接单者按钮逻辑
    if (status === 'accepted') {
      buttons.push({ type: 'start', text: '开始服务' });
    } else if (status === 'in_progress') {
      buttons.push({ type: 'complete', text: '完成订单' });
    }
  }

  return buttons;
}
```

### 7.3 聊天室 (chat-package/pages/room/room)

#### 主要功能
- **实时消息**: 双向实时通信
- **消息类型**: 支持文本、图片、表情
- **历史记录**: 消息持久化存储
- **输入优化**: 动态按钮切换

#### 消息监听机制
```javascript
startMessageWatcher() {
  const db = wx.cloud.database();
  this.messageWatcher = db.collection('messages')
    .where({ chatRoomId: this.data.chatRoomId })
    .orderBy('createTime', 'desc')
    .limit(50)
    .watch({
      onChange: (snapshot) => {
        this.handleMessageChange(snapshot);
      }
    });
}
```

#### 消息发送流程
```javascript
async sendMessage(content, type = 'text') {
  const result = await wx.cloud.callFunction({
    name: 'chatMessage',
    data: {
      action: 'send',
      chatRoomId: this.data.chatRoomId,
      content: content,
      messageType: type
    }
  });

  if (result.result.success) {
    // 消息发送成功，实时监听会自动更新界面
    this.scrollToBottom();
  }
}
```

### 7.4 我的订单 (pages/order/list/list)

#### 主要功能
- **订单筛选**: 按状态筛选订单
- **双重模式**: 普通列表 + 抢单大厅
- **下拉刷新**: 支持手动刷新
- **分页加载**: 无限滚动加载

#### 订单状态筛选
```javascript
const statusTabs = [
  { key: 'all', name: '全部', count: 0 },
  { key: 'pending', name: '待接单', count: 0 },
  { key: 'accepted', name: '已接单', count: 0 },
  { key: 'in_progress', name: '进行中', count: 0 },
  { key: 'completed', name: '已完成', count: 0 }
];
```

## 8. 云函数详细说明

### 8.1 用户相关云函数

#### login (用户登录)
**功能**: 处理用户微信登录，创建或更新用户信息
**输入参数**:
```javascript
{
  nickName: String,    // 微信昵称
  avatarUrl: String,   // 微信头像
  gender: Number       // 性别
}
```
**返回值**:
```javascript
{
  success: Boolean,
  data: {
    userInfo: Object,  // 用户信息
    isNewUser: Boolean // 是否新用户
  }
}
```

#### getUserInfo (获取用户信息)
**功能**: 获取用户详细信息和统计数据
**输入参数**: 无 (通过openid识别用户)
**返回值**:
```javascript
{
  success: Boolean,
  data: {
    userInfo: Object,    // 用户基本信息
    userStats: {         // 用户统计
      totalOrders: Number,
      completedOrders: Number,
      rating: Number
    }
  }
}
```

### 8.2 订单相关云函数

#### createOrder (创建订单)
**功能**: 创建新订单
**输入参数**:
```javascript
{
  title: String,         // 订单标题
  content: String,       // 任务内容
  reward: Number,        // 悬赏金额
  platformType: String,  // 平台类型
  serviceType: String,   // 服务类型
  duration: Number,      // 时长
  rounds: Number,        // 局数
  tags: Array,          // 标签
  orderType: String,    // 订单类型
  scheduledDate: String, // 预约日期
  scheduledTime: String  // 预约时间
}
```

#### acceptOrder (接单/抢单)
**功能**: 处理用户接单请求，包含并发控制
**核心逻辑**:
```javascript
// 原子更新确保并发安全
const updateResult = await transaction.collection('orders').where({
  _id: orderId,
  status: 'pending',
  accepterId: _.eq(null)
}).update({
  data: {
    accepterId: user._id,
    status: 'accepted',
    acceptTime: new Date()
  }
});
```

#### getGrabOrderList (获取抢单大厅列表)
**功能**: 获取可抢单的订单列表
**筛选条件**:
- 状态为 'pending'
- 排除用户自己发布的订单
- 按创建时间倒序

### 8.3 聊天相关云函数

#### chatRoom (聊天室管理)
**功能**: 创建或获取聊天室信息
**自动创建逻辑**:
```javascript
// 基于订单自动创建聊天室
const chatRoomData = {
  orderNo: orderNo,
  orderDbId: order._id,
  customerId: order.customerId,
  accepterId: accepterId,
  customerInfo: customerInfo,
  accepterInfo: accepterInfo,
  orderInfo: {
    title: order.title,
    reward: order.reward,
    status: order.status
  },
  status: 'active',
  createTime: new Date()
};
```

#### chatMessage (聊天消息)
**功能**: 发送和获取聊天消息
**支持操作**:
- send: 发送消息
- getHistory: 获取历史消息
- markRead: 标记消息已读

### 8.4 通知相关云函数

#### sendNotification (发送通知)
**功能**: 创建和发送系统通知
**通知类型**:
- order: 订单相关通知
- chat: 聊天消息通知
- system: 系统公告通知

#### notificationManager (通知管理器)
**功能**: 批量处理通知相关操作
**支持操作**:
- create: 创建通知
- markRead: 标记已读
- delete: 删除通知
- getUnreadCount: 获取未读数量

## 9. 组件系统

### 9.1 全局组件

#### navigation-bar (导航栏组件)
**功能**: 统一的页面导航栏
**属性**:
```javascript
{
  title: String,        // 标题
  back: Boolean,        // 是否显示返回按钮
  color: String,        // 文字颜色
  background: String,   // 背景色
  loading: Boolean      // 是否显示加载状态
}
```

#### order-card (订单卡片组件)
**功能**: 统一的订单展示卡片
**支持事件**:
- cardtap: 卡片点击
- graborder: 抢单
- viewdetail: 查看详情
- editorder: 编辑订单
- cancelorder: 取消订单

#### ui-button (统一按钮组件)
**功能**: 标准化的按钮组件
**类型支持**:
- primary: 主要按钮
- secondary: 次要按钮
- success: 成功按钮
- warning: 警告按钮
- danger: 危险按钮

#### ui-loading (加载组件)
**功能**: 科技感加载动画
**特点**:
- G.T.I. SECURITY 主题
- 多层旋转动画
- 发光效果

### 9.2 业务组件

#### custom-tabbar (自定义TabBar)
**功能**: 底部导航栏
**页面配置**:
```javascript
const tabList = [
  { pagePath: '/pages/index/index', text: '首页', iconPath: 'home' },
  { pagePath: '/pages/order/list/list', text: '我的订单', iconPath: 'order' },
  { pagePath: '/pages/chat/list/list', text: '消息', iconPath: 'chat' },
  { pagePath: '/pages/user/profile/profile', text: '我的', iconPath: 'user' }
];
```

#### notification-toast (通知提示组件)
**功能**: 应用内通知展示
**支持类型**:
- order: 订单通知
- chat: 消息通知
- system: 系统通知

## 10. 工具类系统

### 10.1 API工具 (utils/api.js)
**功能**: 统一的云函数调用封装
**主要方法**:
```javascript
// 订单相关
createOrder(data)
getOrderList(params)
getOrderDetail(orderId)
acceptOrder(orderId)

// 用户相关
login(userInfo)
getUserInfo()
updateUserInfo(data)

// 聊天相关
getChatList(params)
sendMessage(data)
createChatRoom(data)
```

### 10.2 实时管理器 (utils/realtimeManager.js)
**功能**: 统一的实时数据监听管理
**主要功能**:
- 监听器生命周期管理
- 自动重连机制
- 内存泄漏防护

### 10.3 云存储工具 (utils/cloudStorage.js)
**功能**: 云存储文件上传和管理
**支持功能**:
- 图片上传
- 文件压缩
- 进度回调

### 10.4 错误处理 (utils/errorHandler.js)
**功能**: 统一的错误处理机制
**错误类型**:
- 网络错误
- 权限错误
- 业务逻辑错误
- 系统错误

## 11. 配置文件详解

### 11.1 app.json (全局配置)

#### 页面配置
```javascript
{
  "pages": [
    "pages/splash/splash",      // 启动页
    "pages/index/index",        // 首页
    "pages/order/list/list",    // 订单列表
    "pages/chat/list/list",     // 消息列表
    "pages/user/profile/profile", // 个人中心
    "pages/common/login/login"  // 登录页
  ]
}
```

#### 分包配置
```javascript
{
  "subpackages": [
    {
      "root": "order-package",
      "name": "order",
      "pages": [
        "pages/create/create",      // 创建订单
        "pages/detail/detail",      // 订单详情
        "pages/completed/completed", // 已完成订单
        "pages/evaluation/evaluation" // 订单评价
      ]
    }
    // ... 其他分包
  ]
}
```

#### 预加载规则
```javascript
{
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["order"]        // 首页预加载订单包
    },
    "pages/order/list/list": {
      "network": "all",
      "packages": ["chat"]         // 订单页预加载聊天包
    }
  }
}
```

#### 全局组件注册
```javascript
{
  "usingComponents": {
    "navigation-bar": "/components/navigation-bar/navigation-bar",
    "order-card": "/components/order-card/order-card",
    "ui-button": "/components/ui-button/ui-button",
    "ui-loading": "/components/ui-loading/ui-loading"
  }
}
```

### 11.2 project.config.json (项目配置)

#### 云函数配置
```javascript
{
  "cloudfunctionRoot": "cloudfunctions/",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "preloadBackgroundData": false,
    "minified": true,
    "newFeature": true,
    "coverView": true,
    "nodeModules": true,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "uglifyFileName": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "lazyloadPlaceholderEnable": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    }
  }
}
```

### 11.3 package.json (依赖配置)

#### 主要依赖
```javascript
{
  "dependencies": {
    "wx-server-sdk": "~2.6.3",    // 微信云开发SDK
    "axios": "^0.27.2",           // HTTP请求库
    "jsonwebtoken": "^8.5.1",     // JWT处理
    "bson": "^4.6.5"              // MongoDB数据处理
  }
}
```

## 12. 性能优化策略

### 12.1 分包加载优化

#### 分包策略
- **主包**: 只包含TabBar页面和核心功能
- **按需加载**: 功能模块按需分包
- **预加载**: 关键路径预加载相关分包

#### 分包大小控制
```javascript
// 各分包大小限制
主包: < 2MB
order-package: < 2MB
chat-package: < 2MB
user-package: < 2MB
payment-package: < 2MB
utility-package: < 2MB
```

### 12.2 数据库查询优化

#### 索引策略
```javascript
// 关键字段建立索引
orders: ['customerId', 'accepterId', 'status', 'createTime']
messages: ['chatRoomId', 'createTime']
users: ['openid']
notifications: ['receiverId', 'status', 'createTime']
```

#### 查询优化
```javascript
// 使用复合查询减少请求次数
const result = await db.collection('orders')
  .where({
    $or: [
      { customerId: userId },
      { accepterId: userId }
    ],
    status: _.in(['pending', 'accepted', 'in_progress'])
  })
  .field({
    _id: true,
    title: true,
    status: true,
    reward: true,
    createTime: true
  })
  .orderBy('createTime', 'desc')
  .limit(20)
  .get();
```

### 12.3 实时监听优化

#### 监听器管理
```javascript
// 页面生命周期管理监听器
onShow() {
  if (!this.data.isWatcherActive) {
    this.startWatcher();
  }
},

onHide() {
  // 保持监听器活跃，只在onUnload时关闭
},

onUnload() {
  this.stopWatcher();
}
```

#### 内存泄漏防护
```javascript
// 监听器状态守护
class WatcherStateGuard {
  constructor(page) {
    this.page = page;
    this.watchers = new Map();
  }

  addWatcher(name, watcher) {
    this.stopWatcher(name);
    this.watchers.set(name, watcher);
  }

  stopWatcher(name) {
    const watcher = this.watchers.get(name);
    if (watcher) {
      watcher.close();
      this.watchers.delete(name);
    }
  }

  stopAllWatchers() {
    this.watchers.forEach(watcher => watcher.close());
    this.watchers.clear();
  }
}
```

### 12.4 图片和资源优化

#### 云存储策略
```javascript
// 图片上传优化
const uploadImage = async (filePath) => {
  // 压缩图片
  const compressedPath = await compressImage(filePath);

  // 上传到云存储
  const result = await wx.cloud.uploadFile({
    cloudPath: `images/${Date.now()}-${Math.random()}.jpg`,
    filePath: compressedPath
  });

  return result.fileID;
};
```

#### 图片懒加载
```xml
<!-- 使用lazy-load属性 -->
<image
  src="{{item.image}}"
  mode="aspectFill"
  lazy-load="{{true}}"
  loading="lazy" />
```

## 13. 安全机制

### 13.1 权限控制

#### 云函数权限验证
```javascript
// 统一权限中间件
const securityMiddleware = {
  async checkUserPermission(wxContext, requiredRole = null) {
    const user = await db.collection('users')
      .where({ openid: wxContext.OPENID })
      .get();

    if (user.data.length === 0) {
      throw new Error('用户不存在');
    }

    const userData = user.data[0];

    // 检查用户状态
    if (userData.status === 'banned') {
      throw new Error('账户已被禁用');
    }

    return userData;
  }
};
```

#### 订单权限控制
```javascript
// 订单操作权限检查
const checkOrderPermission = (order, user, action) => {
  const isCustomer = String(order.customerId) === String(user._id);
  const isAccepter = String(order.accepterId) === String(user._id);

  switch (action) {
    case 'view':
      return isCustomer || isAccepter;
    case 'cancel':
      return isCustomer && order.status === 'pending';
    case 'accept':
      return !isCustomer && order.status === 'pending';
    case 'complete':
      return isAccepter && order.status === 'in_progress';
    default:
      return false;
  }
};
```

### 13.2 数据验证

#### 输入数据清理
```javascript
// 文本内容清理
function cleanTextContent(text) {
  if (!text) return '';
  // 移除恶意脚本和特殊字符
  return text
    .replace(/[<>]/g, '')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()
    .substring(0, 500); // 限制长度
}
```

#### 参数验证
```javascript
// 云函数参数验证
const validateOrderData = (data) => {
  const errors = [];

  if (!data.title || data.title.length < 2) {
    errors.push('订单标题至少2个字符');
  }

  if (!data.content || data.content.length < 5) {
    errors.push('任务内容至少5个字符');
  }

  if (!data.reward || data.reward < 1) {
    errors.push('悬赏金额不能少于1元');
  }

  if (errors.length > 0) {
    throw new Error(errors.join(', '));
  }
};
```

### 13.3 并发控制

#### 订单抢单并发控制
```javascript
// 使用数据库事务确保原子性
const grabOrder = async (orderId, userId) => {
  return await db.runTransaction(async transaction => {
    // 1. 查询订单当前状态
    const order = await transaction.collection('orders').doc(orderId).get();

    if (!order.data || order.data.status !== 'pending' || order.data.accepterId) {
      throw new Error('订单已被抢');
    }

    // 2. 原子更新
    const updateResult = await transaction.collection('orders')
      .where({
        _id: orderId,
        status: 'pending',
        accepterId: _.eq(null)
      })
      .update({
        data: {
          accepterId: userId,
          status: 'accepted',
          acceptTime: new Date()
        }
      });

    if (updateResult.updated === 0) {
      throw new Error('抢单失败，订单可能已被其他用户抢走');
    }

    return order.data;
  });
};
```

## 14. 错误处理和日志

### 14.1 统一错误处理

#### 前端错误处理
```javascript
// 全局错误处理器
const errorHandler = {
  handleApiError(error, context = '') {
    console.error(`[${context}] API错误:`, error);

    let message = '操作失败，请重试';

    if (error.message) {
      if (error.message.includes('网络')) {
        message = '网络连接异常，请检查网络';
      } else if (error.message.includes('权限')) {
        message = '权限不足';
      } else {
        message = error.message;
      }
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
};
```

#### 云函数错误处理
```javascript
// 云函数统一错误处理
exports.main = async (event, context) => {
  try {
    // 业务逻辑
    const result = await businessLogic(event);
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('云函数执行错误:', error);

    // 记录错误日志
    await logError(error, event, context);

    return {
      success: false,
      error: error.message || '服务器内部错误',
      code: error.code || 'INTERNAL_ERROR'
    };
  }
};
```

### 14.2 日志系统

#### 操作日志记录
```javascript
// 关键操作日志
const logOperation = async (operation, userId, details) => {
  await db.collection('operation_logs').add({
    data: {
      operation,
      userId,
      details,
      timestamp: new Date(),
      ip: context.CLIENTIP,
      userAgent: context.CLIENTIPV6
    }
  });
};
```

#### 性能监控
```javascript
// 性能监控
const performanceMonitor = {
  startTimer(label) {
    console.time(label);
  },

  endTimer(label) {
    console.timeEnd(label);
  },

  logSlowQuery(query, duration) {
    if (duration > 1000) { // 超过1秒的查询
      console.warn('慢查询警告:', { query, duration });
    }
  }
};
```

## 15. 部署和运维

### 15.1 云函数部署

#### 部署脚本
```bash
# 批量部署云函数
npm run deploy:functions

# 单个云函数部署
npm run deploy:function -- login
```

#### 环境配置
```javascript
// 不同环境配置
const config = {
  development: {
    env: 'cloud1-dev',
    debug: true
  },
  production: {
    env: 'cloud1-prod',
    debug: false
  }
};
```

### 15.2 数据库维护

#### 数据备份
```javascript
// 定期数据备份云函数
exports.main = async (event, context) => {
  const collections = ['users', 'orders', 'messages'];

  for (const collection of collections) {
    const data = await db.collection(collection).get();
    // 备份到云存储
    await backupToCloudStorage(collection, data);
  }
};
```

#### 数据清理
```javascript
// 清理过期数据
const cleanExpiredData = async () => {
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

  // 清理过期通知
  await db.collection('notifications')
    .where({
      createTime: _.lt(thirtyDaysAgo),
      status: 'read'
    })
    .remove();
};
```

### 15.3 监控和告警

#### 系统监控
```javascript
// 系统健康检查
const healthCheck = async () => {
  const checks = {
    database: await checkDatabase(),
    cloudFunctions: await checkCloudFunctions(),
    storage: await checkCloudStorage()
  };

  return {
    status: Object.values(checks).every(check => check.status === 'ok') ? 'healthy' : 'unhealthy',
    details: checks,
    timestamp: new Date()
  };
};
```

## 16. 开发指南

### 16.1 本地开发环境搭建

#### 环境要求
- Node.js >= 14.0.0
- 微信开发者工具 >= 1.06.0
- 微信云开发环境

#### 项目初始化
```bash
# 1. 克隆项目
git clone <repository-url>

# 2. 安装依赖
npm install

# 3. 配置云开发环境
# 在微信开发者工具中配置云开发环境ID

# 4. 初始化数据库
# 在云开发控制台运行 initDatabase 云函数
```

#### 开发流程
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和测试
npm run dev

# 3. 部署云函数
npm run deploy:functions

# 4. 提交代码
git add .
git commit -m "feat: 添加新功能"
git push origin feature/new-feature
```

### 16.2 新功能开发指南

#### 添加新页面
```bash
# 1. 创建页面目录
mkdir pages/new-page

# 2. 创建页面文件
touch pages/new-page/new-page.js
touch pages/new-page/new-page.wxml
touch pages/new-page/new-page.wxss
touch pages/new-page/new-page.json

# 3. 在app.json中注册页面
```

#### 添加新云函数
```bash
# 1. 创建云函数目录
mkdir cloudfunctions/newFunction

# 2. 初始化云函数
cd cloudfunctions/newFunction
npm init -y
npm install wx-server-sdk

# 3. 创建index.js
touch index.js

# 4. 部署云函数
# 在微信开发者工具中右键上传并部署
```

#### 添加新组件
```bash
# 1. 创建组件目录
mkdir components/new-component

# 2. 创建组件文件
touch components/new-component/new-component.js
touch components/new-component/new-component.wxml
touch components/new-component/new-component.wxss
touch components/new-component/new-component.json

# 3. 在需要使用的页面json中注册组件
```

### 16.3 代码规范

#### JavaScript规范
```javascript
// 1. 使用const/let，避免var
const userInfo = getUserInfo();
let orderList = [];

// 2. 函数命名使用动词+名词
async function loadOrderList() {}
function formatOrderData(order) {}

// 3. 异步操作使用async/await
async function createOrder(data) {
  try {
    const result = await wx.cloud.callFunction({
      name: 'createOrder',
      data
    });
    return result.result;
  } catch (error) {
    console.error('创建订单失败:', error);
    throw error;
  }
}

// 4. 错误处理
try {
  await riskyOperation();
} catch (error) {
  errorHandler.handleApiError(error, '操作上下文');
}
```

#### WXML规范
```xml
<!-- 1. 使用语义化标签 -->
<view class="order-card">
  <view class="order-header">
    <text class="order-title">{{order.title}}</text>
  </view>
  <view class="order-content">
    <text class="order-description">{{order.content}}</text>
  </view>
</view>

<!-- 2. 条件渲染 -->
<view wx:if="{{loading}}">加载中...</view>
<view wx:elif="{{error}}">{{error}}</view>
<view wx:else>{{content}}</view>

<!-- 3. 列表渲染 -->
<view wx:for="{{orderList}}" wx:key="_id" wx:for-item="order">
  <order-card order-data="{{order}}"></order-card>
</view>
```

#### WXSS规范
```css
/* 1. 使用BEM命名规范 */
.order-card {
  padding: 32rpx;
  margin: 24rpx;
  border-radius: 16rpx;
}

.order-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-card__title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 2. 使用CSS变量 */
:root {
  --primary-color: #ff6b35;
  --secondary-color: #00d4ff;
  --text-color: #333;
  --bg-color: #f5f5f5;
}

/* 3. 响应式设计 */
@media (max-width: 750rpx) {
  .order-card {
    margin: 16rpx;
    padding: 24rpx;
  }
}
```

### 16.4 测试指南

#### 单元测试
```javascript
// 测试工具函数
describe('formatOrderData', () => {
  test('应该正确格式化订单数据', () => {
    const rawOrder = {
      _id: '123',
      title: '测试订单',
      reward: 50,
      status: 'pending'
    };

    const formatted = formatOrderData(rawOrder);

    expect(formatted.statusText).toBe('待接单');
    expect(formatted.rewardText).toBe('¥50');
  });
});
```

#### 集成测试
```javascript
// 测试云函数
describe('createOrder云函数', () => {
  test('应该成功创建订单', async () => {
    const orderData = {
      title: '测试订单',
      content: '测试内容',
      reward: 50
    };

    const result = await wx.cloud.callFunction({
      name: 'createOrder',
      data: orderData
    });

    expect(result.result.success).toBe(true);
    expect(result.result.data.orderNo).toBeDefined();
  });
});
```

#### 端到端测试
```javascript
// 测试完整流程
describe('订单流程', () => {
  test('用户应该能够创建并接单', async () => {
    // 1. 创建订单
    const createResult = await createOrder(testOrderData);
    expect(createResult.success).toBe(true);

    // 2. 抢单
    const acceptResult = await acceptOrder(createResult.data.orderId);
    expect(acceptResult.success).toBe(true);

    // 3. 验证状态
    const orderDetail = await getOrderDetail(createResult.data.orderId);
    expect(orderDetail.data.status).toBe('accepted');
  });
});
```

## 17. 常见问题和解决方案

### 17.1 开发常见问题

#### Q1: 云函数调用失败
**问题**: 云函数调用返回错误或超时
**解决方案**:
```javascript
// 1. 检查云函数是否正确部署
// 2. 检查参数格式是否正确
// 3. 添加错误处理和重试机制
const callCloudFunction = async (name, data, retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      const result = await wx.cloud.callFunction({ name, data });
      return result.result;
    } catch (error) {
      console.error(`云函数调用失败 (${i + 1}/${retries}):`, error);
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};
```

#### Q2: 实时监听器内存泄漏
**问题**: 页面切换后监听器未正确关闭
**解决方案**:
```javascript
// 使用监听器状态管理
onLoad() {
  this.watcherGuard = new WatcherStateGuard(this);
},

onUnload() {
  if (this.watcherGuard) {
    this.watcherGuard.stopAllWatchers();
  }
},

startOrderWatcher() {
  const watcher = db.collection('orders').watch({
    onChange: (snapshot) => {
      if (this.data) { // 确保页面仍然存在
        this.handleOrderChange(snapshot);
      }
    }
  });

  this.watcherGuard.addWatcher('orders', watcher);
}
```

#### Q3: 分包加载失败
**问题**: 分包页面无法正常加载
**解决方案**:
```javascript
// 1. 检查分包配置是否正确
// 2. 确保分包大小不超过限制
// 3. 使用预加载优化
{
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["order"]
    }
  }
}
```

### 17.2 性能问题

#### Q1: 页面加载缓慢
**问题**: 页面首次加载时间过长
**解决方案**:
```javascript
// 1. 使用骨架屏
<view class="skeleton" wx:if="{{loading}}">
  <view class="skeleton-item"></view>
</view>

// 2. 分批加载数据
async function loadDataInBatches() {
  // 先加载关键数据
  const criticalData = await loadCriticalData();
  this.setData({ criticalData, loading: false });

  // 后台加载次要数据
  setTimeout(async () => {
    const secondaryData = await loadSecondaryData();
    this.setData({ secondaryData });
  }, 100);
}

// 3. 使用缓存
const cacheKey = 'orderList';
const cachedData = wx.getStorageSync(cacheKey);
if (cachedData) {
  this.setData({ orderList: cachedData });
}
```

#### Q2: 数据库查询慢
**问题**: 数据库查询响应时间过长
**解决方案**:
```javascript
// 1. 添加索引
// 在云开发控制台为常用查询字段添加索引

// 2. 优化查询条件
const result = await db.collection('orders')
  .where({
    customerId: userId,
    status: _.in(['pending', 'accepted']) // 使用in操作符
  })
  .field({ // 只查询需要的字段
    _id: true,
    title: true,
    status: true,
    reward: true
  })
  .orderBy('createTime', 'desc')
  .limit(20) // 限制返回数量
  .get();

// 3. 使用聚合查询
const stats = await db.collection('orders')
  .aggregate()
  .match({ customerId: userId })
  .group({
    _id: '$status',
    count: $.sum(1)
  })
  .end();
```

### 17.3 用户体验问题

#### Q1: 网络异常处理
**问题**: 网络不稳定时用户体验差
**解决方案**:
```javascript
// 1. 网络状态监听
wx.onNetworkStatusChange((res) => {
  if (!res.isConnected) {
    wx.showToast({
      title: '网络连接异常',
      icon: 'none'
    });
  }
});

// 2. 离线缓存
const offlineCache = {
  set(key, data) {
    wx.setStorageSync(`offline_${key}`, {
      data,
      timestamp: Date.now()
    });
  },

  get(key, maxAge = 5 * 60 * 1000) { // 5分钟有效期
    const cached = wx.getStorageSync(`offline_${key}`);
    if (cached && Date.now() - cached.timestamp < maxAge) {
      return cached.data;
    }
    return null;
  }
};
```

#### Q2: 加载状态优化
**问题**: 加载状态不明确，用户体验差
**解决方案**:
```javascript
// 1. 使用统一的加载组件
<ui-loading
  show="{{loading}}"
  text="{{loadingText}}"
  type="tech" />

// 2. 分阶段加载提示
const loadingStates = {
  INIT: '初始化中...',
  LOADING_USER: '获取用户信息...',
  LOADING_ORDERS: '加载订单列表...',
  COMPLETE: '加载完成'
};

// 3. 进度指示
this.setData({
  loadingProgress: 30,
  loadingText: '正在加载订单数据...'
});
```

## 18. 版本更新和维护

### 18.1 版本管理

#### 版本号规范
```
主版本号.次版本号.修订号
例: 1.2.3

主版本号: 不兼容的API修改
次版本号: 向下兼容的功能性新增
修订号: 向下兼容的问题修正
```

#### 更新日志
```markdown
## v1.2.0 (2024-01-15)

### 新增功能
- 添加订单评价系统
- 支持图片消息发送
- 新增用户信用分系统

### 优化改进
- 优化订单列表加载性能
- 改进聊天界面用户体验
- 增强网络异常处理

### 问题修复
- 修复抢单并发问题
- 解决消息重复显示问题
- 修复分包加载异常
```

### 18.2 数据迁移

#### 数据库结构变更
```javascript
// 数据迁移云函数
exports.main = async (event, context) => {
  const { version } = event;

  switch (version) {
    case '1.2.0':
      await migrateToV120();
      break;
    default:
      throw new Error('不支持的版本');
  }
};

async function migrateToV120() {
  // 为orders集合添加新字段
  const orders = await db.collection('orders').get();

  for (const order of orders.data) {
    await db.collection('orders').doc(order._id).update({
      data: {
        evaluation: {
          customerRating: null,
          accepterRating: null,
          customerComment: '',
          accepterComment: ''
        }
      }
    });
  }
}
```

### 18.3 监控和告警

#### 关键指标监控
```javascript
// 业务指标监控
const businessMetrics = {
  // 订单相关
  dailyOrderCount: 0,
  orderSuccessRate: 0,
  averageOrderValue: 0,

  // 用户相关
  activeUsers: 0,
  newUserRegistration: 0,
  userRetentionRate: 0,

  // 系统相关
  apiResponseTime: 0,
  errorRate: 0,
  systemUptime: 0
};

// 告警规则
const alertRules = {
  orderSuccessRate: { threshold: 0.9, operator: '<' },
  errorRate: { threshold: 0.05, operator: '>' },
  apiResponseTime: { threshold: 2000, operator: '>' }
};
```

---

## 附录

### A. API接口文档

#### 用户相关接口
```javascript
// 用户登录
POST /login
参数: { nickName, avatarUrl, gender }
返回: { success, data: { userInfo, isNewUser } }

// 获取用户信息
GET /getUserInfo
返回: { success, data: { userInfo, userStats } }

// 更新用户信息
POST /updateUserInfo
参数: { nickName, gameNickName, phone, ... }
返回: { success, data: userInfo }
```

#### 订单相关接口
```javascript
// 创建订单
POST /createOrder
参数: { title, content, reward, serviceType, ... }
返回: { success, data: { orderId, orderNo } }

// 获取订单列表
GET /getOrderList
参数: { page, pageSize, status, role }
返回: { success, data: { list, total, hasMore } }

// 抢单
POST /acceptOrder
参数: { orderId }
返回: { success, data: orderInfo }
```

### B. 数据库集合索引

```javascript
// orders集合索引
{
  "customerId": 1,
  "status": 1,
  "createTime": -1
}

{
  "accepterId": 1,
  "status": 1,
  "createTime": -1
}

// messages集合索引
{
  "chatRoomId": 1,
  "createTime": -1
}

// notifications集合索引
{
  "receiverId": 1,
  "status": 1,
  "createTime": -1
}
```

### C. 错误码对照表

```javascript
const ERROR_CODES = {
  // 通用错误
  'INVALID_PARAMS': '参数错误',
  'UNAUTHORIZED': '未授权访问',
  'FORBIDDEN': '权限不足',
  'NOT_FOUND': '资源不存在',
  'INTERNAL_ERROR': '服务器内部错误',

  // 用户相关
  'USER_NOT_FOUND': '用户不存在',
  'USER_BANNED': '用户已被禁用',
  'PHONE_ALREADY_BOUND': '手机号已绑定其他账户',

  // 订单相关
  'ORDER_NOT_FOUND': '订单不存在',
  'ORDER_ALREADY_TAKEN': '订单已被抢',
  'INSUFFICIENT_BALANCE': '余额不足',
  'CANNOT_ACCEPT_OWN_ORDER': '不能接受自己的订单',

  // 聊天相关
  'CHATROOM_NOT_FOUND': '聊天室不存在',
  'MESSAGE_SEND_FAILED': '消息发送失败'
};
```

---

*本技术文档详细描述了三角洲任务平台的技术架构、开发规范和最佳实践。*
*文档版本: v1.0.0*
*最后更新: 2024-01-15*
*如有疑问请联系开发团队*
