// 数据库初始化云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 需要创建的集合列表
const COLLECTIONS = [
  'users',          // 用户表
  'orders',         // 订单表
  'chatRooms',      // 聊天房间表
  'messages',       // 聊天消息表
  'transactions',   // 交易记录表
  'evaluations',    // 评价表
  'recharges',      // 充值记录表
  'withdraws',      // 提现记录表
  'daily_passwords' // 每日密码表
];

/**
 * 检查集合是否存在
 */
async function checkCollectionExists(collectionName) {
  try {
    const result = await db.collection(collectionName).limit(1).get();
    return true;
  } catch (error) {
    if (error.errCode === -502002 || error.errCode === -502005 ||
        error.message.includes('collection not exists') ||
        error.message.includes('not exist')) {
      // 集合不存在
      return false;
    }
    console.log(`检查集合 ${collectionName} 时的错误:`, error);
    return false; // 默认认为不存在，尝试创建
  }
}

/**
 * 创建集合
 */
async function createCollection(collectionName) {
  try {
    console.log(`正在创建集合: ${collectionName}`);

    // 方法1: 通过插入一个临时文档来创建集合
    try {
      const result = await db.collection(collectionName).add({
        data: {
          _temp: true,
          createTime: new Date(),
          description: `临时文档用于创建集合 ${collectionName}`
        }
      });

      console.log(`集合 ${collectionName} 创建成功，临时文档ID: ${result._id}`);

      // 删除临时文档
      try {
        await db.collection(collectionName).doc(result._id).remove();
        console.log(`临时文档已删除`);
      } catch (deleteError) {
        console.log(`删除临时文档失败，但集合已创建:`, deleteError);
      }

      return true;
    } catch (addError) {
      console.error(`方法1创建集合失败:`, addError);

      // 方法2: 如果add失败，尝试直接使用数据库API创建集合
      try {
        // 注意：这个方法可能在某些环境下不可用
        if (db.createCollection) {
          await db.createCollection(collectionName);
          console.log(`使用createCollection API创建集合 ${collectionName} 成功`);
          return true;
        }
      } catch (createError) {
        console.error(`方法2创建集合失败:`, createError);
      }

      return false;
    }
  } catch (error) {
    console.error(`创建集合 ${collectionName} 完全失败:`, error);
    return false;
  }
}

/**
 * 创建示例数据
 */
async function createSampleData() {


  try {
    // 先尝试检查是否已有示例数据
    let hasExistingData = false;
    try {
      const existingOrders = await db.collection('orders').limit(1).get();
      if (existingOrders.data.length > 0) {
        return { status: 'exists' };
      }
    } catch (checkError) {
      // 检查现有数据时出错，继续创建新数据
    }



    return { status: 'created', count: result.ids.length };
  } catch (error) {
    console.error('创建示例数据失败:', error);
    return { status: 'error', error: error.message };
  }
}

exports.main = async (event, context) => {
  const { action = 'init' } = event;

  try {
    if (action === 'init') {
      // 初始化数据库集合
      console.log('开始初始化数据库...');

      const results = [];

      for (const collectionName of COLLECTIONS) {
        try {
          const exists = await checkCollectionExists(collectionName);

          if (exists) {
            console.log(`集合 ${collectionName} 已存在`);
            results.push({ collection: collectionName, status: 'exists' });
          } else {
            const created = await createCollection(collectionName);
            results.push({
              collection: collectionName,
              status: created ? 'created' : 'failed'
            });
          }
        } catch (error) {
          console.error(`处理集合 ${collectionName} 时出错:`, error);
          results.push({ collection: collectionName, status: 'error', error: error.message });
        }
      }

      console.log('数据库初始化完成');

      return {
        success: true,
        message: '数据库初始化完成',
        data: results
      };

    } else if (action === 'force') {
      // 强制创建数据和集合
      console.log('强制创建数据和集合...');

      return {
        success: true,
        message: '强制创建完成',
        data: { status: 'completed' }
      };

    } else {
      return {
        success: false,
        error: '未知的操作类型'
      };
    }

  } catch (error) {
    console.error('数据库操作失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
