// 用户活跃时间跟踪器
class UserActivityTracker {
  constructor() {
    this.lastUpdateTime = 0;
    this.updateInterval = 5 * 60 * 1000; // 5分钟更新一次
    this.isTracking = false;
    this.pendingUpdate = false;
  }

  // 初始化活跃时间跟踪
  init() {
    console.log('🕒 [活跃跟踪] 初始化用户活跃时间跟踪器');
    this.isTracking = true;
    this.updateLastActiveTime();
  }

  // 停止跟踪
  stop() {
    console.log('🕒 [活跃跟踪] 停止用户活跃时间跟踪器');
    this.isTracking = false;
  }

  // 记录用户活跃
  recordActivity() {
    if (!this.isTracking) {
      return;
    }

    const now = Date.now();
    const timeSinceLastUpdate = now - this.lastUpdateTime;

    // 如果距离上次更新超过指定间隔，则更新数据库
    if (timeSinceLastUpdate >= this.updateInterval) {
      this.updateLastActiveTime();
    }
  }

  // 更新用户最后活跃时间到数据库
  async updateLastActiveTime() {
    if (this.pendingUpdate) {
      console.log('🕒 [活跃跟踪] 已有更新请求在进行中，跳过');
      return;
    }

    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo._id) {
      console.log('🕒 [活跃跟踪] 用户未登录，跳过活跃时间更新');
      return;
    }

    try {
      this.pendingUpdate = true;
      this.lastUpdateTime = Date.now();

      console.log('🕒 [活跃跟踪] 更新用户最后活跃时间');

      // 调用云函数更新用户活跃时间
      const result = await wx.cloud.callFunction({
        name: 'updateUserInfo',
        data: {
          action: 'updateLastActiveTime',
          userId: app.globalData.userInfo._id,
          lastActiveTime: new Date()
        }
      });

      if (result.result && result.result.success) {
        console.log('✅ [活跃跟踪] 用户活跃时间更新成功');
      } else {
        console.error('❌ [活跃跟踪] 用户活跃时间更新失败:', result.result?.error);
      }

    } catch (error) {
      console.error('❌ [活跃跟踪] 更新用户活跃时间异常:', error);
    } finally {
      this.pendingUpdate = false;
    }
  }

  // 强制更新活跃时间（用于重要操作）
  async forceUpdate() {
    this.lastUpdateTime = 0; // 重置时间，强制更新
    await this.updateLastActiveTime();
  }
}

// 创建全局实例
const userActivityTracker = new UserActivityTracker();

module.exports = userActivityTracker;
