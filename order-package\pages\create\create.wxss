/* 创建订单页面样式 - 三角洲行动主题 */
page {
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
  min-height: 100vh;
}

.create-container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.create-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 10rpx);
}

/* 移除陪玩师相关样式 */

.level-badge.novice {
  background: #52c41a;
}

.level-badge.professional {
  background: #1890ff;
}

.level-badge.master {
  background: #722ed1;
}

.companion-rating {
  display: flex;
  align-items: center;
}

.stars {
  display: flex;
  margin-right: 8rpx;
}

.star {
  font-size: 24rpx;
  margin-right: 4rpx;
}

.star.filled {
  color: #ffa940;
}

.star.empty {
  color: #d9d9d9;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
}

.companion-price {
  text-align: right;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.price-unit {
  font-size: 24rpx;
  color: #999;
}

/* 新版订单表单样式 */
.order-form {
  margin-bottom: 20rpx;
}

.form-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

/* 订单类型选择 */
.order-type-tabs {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.type-tab {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.type-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
  color: #f8fafc;
}

.type-tab.active .tab-text {
  color: white;
}

.tab-desc {
  font-size: 22rpx;
  opacity: 0.8;
  color: #cbd5e0;
}

.type-tab.active .tab-desc {
  color: rgba(255, 255, 255, 0.9);
}

/* 预约时间设置 */
.schedule-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-label {
  font-size: 28rpx;
  color: #f8fafc;
  font-weight: 500;
}

.schedule-value {
  font-size: 28rpx;
  color: #f8fafc;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  min-width: 200rpx;
  text-align: center;
}

.schedule-value.placeholder {
  color: #a0aec0;
}

/* 时间选择器容器 */
.time-picker-container {
  display: flex;
  gap: 16rpx;
}

.time-picker {
  flex: 1;
}

.schedule-tip {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  margin-top: 16rpx;
  padding: 16rpx;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 8rpx;
  border-left: 4rpx solid #667eea;
}

.tip-icon {
  font-size: 24rpx;
  color: #667eea;
}

.tip-text {
  font-size: 24rpx;
  color: #cbd5e0;
  line-height: 1.4;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #f8fafc;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 标题输入 */
.title-input {
  width: 100%;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.03);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 0 16rpx;
  color: #f8fafc;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 80rpx;
  box-sizing: border-box;
  text-align: left;
  vertical-align: middle;
}

.title-input::placeholder {
  color: rgba(248, 250, 252, 0.5);
  font-size: 26rpx;
  font-weight: 400;
  line-height: 80rpx;
}

.title-input-placeholder {
  color: rgba(248, 250, 252, 0.5) !important;
  font-size: 26rpx !important;
  font-weight: 400 !important;
  line-height: 80rpx !important;
  text-align: left !important;
  vertical-align: middle !important;
}

/* 任务内容输入 */
.content-input {
  width: 100%;
  min-height: 120rpx;
  background: rgba(255, 255, 255, 0.03);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 16rpx;
  color: #f8fafc;
  font-size: 26rpx;
  line-height: 1.5;
  box-sizing: border-box;
}

/* 平台类型选择器 - 水平布局 */
.platform-selector-horizontal {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.platform-option-horizontal {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.03);
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 80rpx;
  position: relative;
  overflow: hidden;
}

.platform-option-horizontal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), rgba(0, 153, 204, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.platform-option-horizontal.active::before {
  opacity: 1;
}

.platform-option-horizontal.active {
  background: rgba(0, 212, 255, 0.1);
  border-color: #00d4ff;
  box-shadow: 0 8rpx 24rpx rgba(0, 212, 255, 0.3);
  transform: translateY(-2rpx);
}

.platform-icon-large {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  transition: transform 0.3s ease;
}

.platform-option-horizontal.active .platform-icon-large {
  transform: scale(1.05);
}

.platform-text-large {
  font-size: 24rpx;
  font-weight: 600;
  color: #f8fafc;
  margin-bottom: 4rpx;
  transition: color 0.3s ease;
}

.platform-option-horizontal.active .platform-text-large {
  color: #00d4ff;
}

.platform-desc {
  font-size: 20rpx;
  color: #cbd5e1;
  text-align: center;
  transition: color 0.3s ease;
}

.platform-option-horizontal.active .platform-desc {
  color: #7dd3fc;
}

/* 报酬输入容器 - 全宽版本 */
.reward-input-container-full {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  gap: 12rpx;
  margin-top: 16rpx;
}

.currency-symbol {
  font-size: 28rpx;
  font-weight: 600;
  color: #FFD700;
}

.reward-input {
  flex: 1;
  background: transparent;
  border: none;
  color: #f8fafc;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
}

.reward-input-full {
  flex: 1;
  background: transparent;
  border: none;
  color: #f8fafc;
  font-size: 32rpx;
  font-weight: 700;
  text-align: center;
  padding: 8rpx 0;
}

.reward-unit {
  font-size: 24rpx;
  color: #cbd5e1;
}

/* 服务类型选择器 */
.service-type-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12rpx;
  padding: 6rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.service-type-tab {
  flex: 1;
  padding: 16rpx;
  text-align: center;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.service-type-tab.active {
  background: rgba(0, 212, 255, 0.15);
  border: 1rpx solid #00d4ff;
  box-shadow: 0 2rpx 8rpx rgba(0, 212, 255, 0.3);
}

.service-type-tab .tab-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #f8fafc;
}

.service-type-tab.active .tab-text {
  color: #00d4ff;
}

/* 时间选择网格 */
.time-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}

.time-card {
  background: rgba(255, 255, 255, 0.03);
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 16rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 600;
  color: #f8fafc;
  transition: all 0.3s ease;
  cursor: pointer;
}

.time-card.selected {
  background: rgba(0, 212, 255, 0.15);
  border-color: #00d4ff;
  box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.3);
}

.custom-duration {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-top: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.duration-input {
  flex: 1;
  background: transparent;
  border: none;
  color: #f8fafc;
  font-size: 26rpx;
  padding: 8rpx 0;
}

.duration-unit {
  font-size: 24rpx;
  color: #cbd5e1;
}

/* 局数选择网格 */
.rounds-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}

.rounds-card {
  background: rgba(255, 255, 255, 0.03);
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 16rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 600;
  color: #f8fafc;
  transition: all 0.3s ease;
  cursor: pointer;
}

.rounds-card.selected {
  background: rgba(0, 212, 255, 0.15);
  border-color: #00d4ff;
  box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.3);
}

.custom-rounds {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-top: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.rounds-input {
  flex: 1;
  background: transparent;
  border: none;
  color: #f8fafc;
  font-size: 26rpx;
  padding: 8rpx 0;
}

.rounds-unit {
  font-size: 24rpx;
  color: #cbd5e1;
}

/* 订单标签 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 56rpx;
  padding: 0 20rpx 8rpx 20rpx;
  background: rgba(255, 255, 255, 0.03);
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 25rpx;
  font-size: 24rpx;
  color: #f8fafc;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
  box-sizing: border-box;
}

.tag-item.selected {
  background: rgba(0, 212, 255, 0.15);
  border-color: #00d4ff;
  color: #00d4ff;
  box-shadow: 0 2rpx 8rpx rgba(0, 212, 255, 0.3);
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: #64748b;
  margin-top: 8rpx;
}

/* 预览卡片 */
.preview-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #f8fafc;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.card-title::before {
  content: '👀';
  font-size: 28rpx;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
}

.preview-label {
  font-size: 26rpx;
  color: #cbd5e1;
  min-width: 80rpx;
}

.preview-value {
  font-size: 26rpx;
  color: #f8fafc;
  font-weight: 500;
  flex: 1;
  text-align: right;
  word-wrap: break-word;
}

.reward-highlight {
  font-size: 32rpx;
  font-weight: 700;
  color: #FFD700;
  text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

.preview-tags {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
}

.preview-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  justify-content: flex-start;
  align-items: center;
  flex: 1;
  margin-left: 20rpx;
}

.preview-tag {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 56rpx;
  padding: 0 20rpx 8rpx 20rpx;
  background: rgba(0, 212, 255, 0.15);
  border: 2rpx solid #00d4ff;
  border-radius: 25rpx;
  font-size: 24rpx;
  color: #00d4ff;
  white-space: nowrap;
  box-sizing: border-box;
}

/* 旧样式已移除，使用新版样式 */

/* 时间选择器 */
.time-selector {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.time-option {
  flex: 1;
  padding: 16rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  text-align: center;
  font-size: 26rpx;
  color: #333;
}

.time-option.selected {
  border-color: #1890ff;
  background: #f0f8ff;
  color: #1890ff;
}

.datetime-picker {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
}

.picker-label {
  font-size: 26rpx;
  color: #333;
}

.picker-value {
  font-size: 26rpx;
  color: #666;
}

/* 价格明细 */
.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
}

.price-label {
  font-size: 26rpx;
  color: #cbd5e1;
}

.price-value {
  font-size: 26rpx;
  color: #f8fafc;
  font-weight: 500;
}

.price-divider {
  height: 1rpx;
  background: rgba(255, 255, 255, 0.1);
  margin: 16rpx 0;
}

.price-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
  margin-top: 16rpx;
}

.total-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #f8fafc;
}

.total-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #FFD700;
  text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

/* 提交按钮 */
.submit-section {
  padding: 20rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(0, 212, 255, 0.4);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.6);
}

.submit-btn[disabled] {
  background: rgba(255, 255, 255, 0.1);
  color: #64748b;
  box-shadow: none;
  transform: none;
}
