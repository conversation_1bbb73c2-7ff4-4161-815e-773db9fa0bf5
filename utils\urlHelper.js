// URL工具函数 - 微信小程序兼容版本

/**
 * 构建查询字符串（替代URLSearchParams）
 * @param {Object} params - 参数对象
 * @returns {string} 查询字符串
 */
function buildQueryString(params) {
  if (!params || typeof params !== 'object') {
    return '';
  }

  return Object.keys(params)
    .filter(key => {
      const value = params[key];
      return value !== '' && value !== null && value !== undefined;
    })
    .map(key => {
      const value = params[key];
      return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
    })
    .join('&');
}

/**
 * 解析查询字符串为对象
 * @param {string} queryString - 查询字符串
 * @returns {Object} 参数对象
 */
function parseQueryString(queryString) {
  if (!queryString || typeof queryString !== 'string') {
    return {};
  }

  // 移除开头的 ? 符号
  const cleanQuery = queryString.startsWith('?') ? queryString.slice(1) : queryString;
  
  if (!cleanQuery) {
    return {};
  }

  const params = {};
  const pairs = cleanQuery.split('&');

  pairs.forEach(pair => {
    const [key, value] = pair.split('=');
    if (key) {
      try {
        params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
      } catch (error) {
        console.warn('解析URL参数失败:', pair, error);
      }
    }
  });

  return params;
}

/**
 * 构建完整的URL（包含查询参数）
 * @param {string} baseUrl - 基础URL
 * @param {Object} params - 参数对象
 * @returns {string} 完整URL
 */
function buildUrl(baseUrl, params) {
  if (!baseUrl) {
    return '';
  }

  const queryString = buildQueryString(params);
  if (!queryString) {
    return baseUrl;
  }

  const separator = baseUrl.includes('?') ? '&' : '?';
  return `${baseUrl}${separator}${queryString}`;
}

/**
 * 从当前页面选项中获取参数
 * @param {Object} options - 页面onLoad的options参数
 * @returns {Object} 解析后的参数对象
 */
function getPageParams(options) {
  if (!options || typeof options !== 'object') {
    return {};
  }

  const params = {};
  Object.keys(options).forEach(key => {
    try {
      params[key] = decodeURIComponent(options[key]);
    } catch (error) {
      params[key] = options[key];
    }
  });

  return params;
}

/**
 * 安全的URL编码
 * @param {any} value - 要编码的值
 * @returns {string} 编码后的字符串
 */
function safeEncodeURIComponent(value) {
  if (value === null || value === undefined) {
    return '';
  }
  
  try {
    return encodeURIComponent(String(value));
  } catch (error) {
    console.warn('URL编码失败:', value, error);
    return String(value);
  }
}

/**
 * 安全的URL解码
 * @param {string} value - 要解码的字符串
 * @returns {string} 解码后的字符串
 */
function safeDecodeURIComponent(value) {
  if (!value || typeof value !== 'string') {
    return '';
  }
  
  try {
    return decodeURIComponent(value);
  } catch (error) {
    console.warn('URL解码失败:', value, error);
    return value;
  }
}

module.exports = {
  buildQueryString,
  parseQueryString,
  buildUrl,
  getPageParams,
  safeEncodeURIComponent,
  safeDecodeURIComponent
};
