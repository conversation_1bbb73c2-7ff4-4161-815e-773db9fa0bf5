// 功能状态页面
const app = getApp();

Page({
  data: {
    systemStatus: {
      online: true,
      isDemo: false,
      cloudReady: false,
      version: '1.0.0'
    },
    features: [
      {
        name: '用户登录',
        description: '微信登录和游客模式',
        icon: '👤',
        status: 'normal',
        statusText: '正常'
      },
      {
        name: '订单管理',
        description: '创建、查看、管理订单',
        icon: '📋',
        status: 'demo',
        statusText: '演示'
      },
      {
        name: '聊天功能',
        description: '与接单用户实时聊天',
        icon: '💬',
        status: 'demo',
        statusText: '演示'
      },
      {
        name: '支付功能',
        description: '充值、提现、交易',
        icon: '💰',
        status: 'disabled',
        statusText: '暂停'
      },
      {
        name: '订单评价',
        description: '评价接单用户服务',
        icon: '⭐',
        status: 'demo',
        statusText: '演示'
      },
      {
        name: '用户设置',
        description: '个人信息和偏好设置',
        icon: '⚙️',
        status: 'normal',
        statusText: '正常'
      }
    ]
  },

  onLoad() {
    this.checkSystemStatus();
  },

  // 检查系统状态
  async checkSystemStatus() {
    try {
      // 检查用户信息
      const userInfo = app.globalData.userInfo;
      const isDemo = userInfo && userInfo.isDemo;

      // 检查云开发状态
      let cloudReady = false;
      try {
        await wx.cloud.callFunction({
          name: 'login',
          data: { test: true }
        });
        cloudReady = true;
      } catch (error) {
        console.log('云开发未连接:', error);
      }

      // 更新系统状态
      this.setData({
        'systemStatus.isDemo': isDemo,
        'systemStatus.cloudReady': cloudReady
      });

      // 更新功能状态
      this.updateFeatureStatus(isDemo, cloudReady);
    } catch (error) {
      console.error('检查系统状态失败:', error);
    }
  },

  // 更新功能状态
  updateFeatureStatus(isDemo, cloudReady) {
    const features = this.data.features.map(feature => {
      let status = 'normal';
      let statusText = '正常';

      // 根据功能类型和系统状态确定状态
      switch (feature.name) {
        case '用户登录':
          // 登录功能始终可用
          break;
        case '用户设置':
          // 设置功能始终可用
          break;
        case '支付功能':
          // 支付功能暂停开发
          status = 'disabled';
          statusText = '暂停';
          break;
        default:
          // 其他功能根据云开发状态
          if (isDemo || !cloudReady) {
            status = 'demo';
            statusText = '演示';
          }
          break;
      }

      return { ...feature, status, statusText };
    });

    this.setData({ features });
  },

  // 刷新状态
  refreshStatus() {
    wx.showLoading({ title: '检查中...' });
    
    setTimeout(() => {
      this.checkSystemStatus();
      wx.hideLoading();
      wx.showToast({
        title: '状态已更新',
        icon: 'success'
      });
    }, 1000);
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除所有缓存数据吗？清除后需要重新登录。',
      confirmText: '清除',
      confirmColor: '#ff6b35',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync();
            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            });
            
            // 延迟跳转到登录页
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/common/login/login'
              });
            }, 1500);
          } catch (error) {
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 联系客服
  contactSupport() {
    wx.showActionSheet({
      itemList: ['在线客服', '客服电话', '问题反馈'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.showToast({
              title: '客服功能开发中',
              icon: 'none'
            });
            break;
          case 1:
            wx.showModal({
              title: '客服电话',
              content: '************\n工作时间：9:00-21:00',
              confirmText: '拨打',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.makePhoneCall({
                    phoneNumber: '************',
                    fail: () => {
                      wx.showToast({
                        title: '拨打失败',
                        icon: 'none'
                      });
                    }
                  });
                }
              }
            });
            break;
          case 2:
            wx.showModal({
              title: '问题反馈',
              content: '请描述您遇到的问题',
              editable: true,
              placeholderText: '请输入问题描述...',
              success: (modalRes) => {
                if (modalRes.confirm && modalRes.content) {
                  wx.showToast({
                    title: '反馈已提交',
                    icon: 'success'
                  });
                }
              }
            });
            break;
        }
      }
    });
  }
});
