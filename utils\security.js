// 安全验证工具类
class SecurityManager {
  constructor() {
    this.requestCounts = new Map(); // 请求频率记录
    this.blockedUsers = new Set(); // 被封禁的用户
    this.sensitiveFields = ['phone', 'idCard', 'realName', 'address']; // 敏感字段
  }

  // 数据脱敏处理
  maskSensitiveData(data, fieldName) {
    if (!data || typeof data !== 'string') {
      return data;
    }

    switch (fieldName) {
      case 'phone':
        // 手机号脱敏：138****1234
        return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      
      case 'idCard':
        // 身份证脱敏：110101********1234
        return data.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
      
      case 'realName':
        // 姓名脱敏：张*明
        if (data.length <= 2) {
          return data.charAt(0) + '*';
        }
        return data.charAt(0) + '*'.repeat(data.length - 2) + data.charAt(data.length - 1);
      
      case 'address':
        // 地址脱敏：保留前6个字符，其余用*代替
        if (data.length <= 6) {
          return data.substring(0, 2) + '*'.repeat(data.length - 2);
        }
        return data.substring(0, 6) + '*'.repeat(Math.min(data.length - 6, 10));
      
      case 'email':
        // 邮箱脱敏：abc***@example.com
        const [username, domain] = data.split('@');
        if (username.length <= 3) {
          return username.charAt(0) + '***@' + domain;
        }
        return username.substring(0, 3) + '***@' + domain;
      
      default:
        return data;
    }
  }

  // 批量数据脱敏
  maskObjectData(obj, fieldsToMask = this.sensitiveFields) {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const maskedObj = { ...obj };
    
    for (const field of fieldsToMask) {
      if (maskedObj[field]) {
        maskedObj[field] = this.maskSensitiveData(maskedObj[field], field);
      }
    }

    return maskedObj;
  }

  // 请求频率限制
  checkRateLimit(userId, action, maxRequests = 10, timeWindow = 60000) {
    const key = `${userId}_${action}`;
    const now = Date.now();
    
    if (!this.requestCounts.has(key)) {
      this.requestCounts.set(key, []);
    }
    
    const requests = this.requestCounts.get(key);
    
    // 清理过期的请求记录
    const validRequests = requests.filter(timestamp => now - timestamp < timeWindow);
    
    if (validRequests.length >= maxRequests) {
      console.warn(`🚫 [安全检查] 用户 ${userId} 的 ${action} 操作超出频率限制`);
      return false;
    }
    
    // 记录新请求
    validRequests.push(now);
    this.requestCounts.set(key, validRequests);
    
    return true;
  }

  // 输入验证
  validateInput(input, type, options = {}) {
    if (input === null || input === undefined) {
      return { valid: false, error: '输入不能为空' };
    }

    const value = String(input).trim();
    
    switch (type) {
      case 'phone':
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(value)) {
          return { valid: false, error: '手机号格式不正确' };
        }
        break;
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          return { valid: false, error: '邮箱格式不正确' };
        }
        break;
      
      case 'idCard':
        const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        if (!idCardRegex.test(value)) {
          return { valid: false, error: '身份证号格式不正确' };
        }
        break;
      
      case 'text':
        const minLength = options.minLength || 0;
        const maxLength = options.maxLength || 1000;
        if (value.length < minLength) {
          return { valid: false, error: `文本长度不能少于${minLength}个字符` };
        }
        if (value.length > maxLength) {
          return { valid: false, error: `文本长度不能超过${maxLength}个字符` };
        }
        // 检查是否包含敏感词
        if (this.containsSensitiveWords(value)) {
          return { valid: false, error: '内容包含敏感词汇' };
        }
        break;
      
      case 'number':
        const num = Number(value);
        if (isNaN(num)) {
          return { valid: false, error: '必须是有效数字' };
        }
        const min = options.min !== undefined ? options.min : -Infinity;
        const max = options.max !== undefined ? options.max : Infinity;
        if (num < min || num > max) {
          return { valid: false, error: `数值必须在${min}到${max}之间` };
        }
        break;
      
      default:
        break;
    }

    return { valid: true, value };
  }

  // 敏感词检查
  containsSensitiveWords(text) {
    const sensitiveWords = [
      '赌博', '色情', '暴力', '恐怖', '政治', '反动',
      '欺诈', '诈骗', '传销', '非法', '违法', '犯罪'
    ];
    
    const lowerText = text.toLowerCase();
    return sensitiveWords.some(word => lowerText.includes(word));
  }

  // 权限验证
  checkPermission(userInfo, requiredPermission, resourceId = null) {
    if (!userInfo || !userInfo.openid) {
      return { authorized: false, error: '用户未登录' };
    }

    // 检查用户是否被封禁
    if (this.blockedUsers.has(userInfo.openid)) {
      return { authorized: false, error: '用户已被封禁' };
    }

    switch (requiredPermission) {
      case 'order.view':
        // 订单查看权限：只能查看自己相关的订单
        if (resourceId && !this.isOrderRelatedToUser(resourceId, userInfo.openid)) {
          return { authorized: false, error: '无权查看此订单' };
        }
        break;
      
      case 'order.modify':
        // 订单修改权限：只能修改自己发布的订单
        if (resourceId && !this.isOrderOwner(resourceId, userInfo.openid)) {
          return { authorized: false, error: '无权修改此订单' };
        }
        break;
      
      case 'chat.access':
        // 聊天权限：只能访问自己参与的聊天室
        if (resourceId && !this.isChatParticipant(resourceId, userInfo.openid)) {
          return { authorized: false, error: '无权访问此聊天室' };
        }
        break;
      
      case 'profile.edit':
        // 个人资料编辑权限：只能编辑自己的资料
        if (resourceId && resourceId !== userInfo.openid) {
          return { authorized: false, error: '无权编辑他人资料' };
        }
        break;
      
      default:
        // 默认允许
        break;
    }

    return { authorized: true };
  }

  // 检查订单是否与用户相关
  isOrderRelatedToUser(orderId, userId) {
    // 这里应该查询数据库验证
    // 暂时返回true，实际应用中需要实现具体逻辑
    return true;
  }

  // 检查是否为订单所有者
  isOrderOwner(orderId, userId) {
    // 这里应该查询数据库验证
    // 暂时返回true，实际应用中需要实现具体逻辑
    return true;
  }

  // 检查是否为聊天参与者
  isChatParticipant(chatRoomId, userId) {
    // 这里应该查询数据库验证
    // 暂时返回true，实际应用中需要实现具体逻辑
    return true;
  }

  // 生成安全的随机字符串
  generateSecureToken(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // 清理过期的请求记录
  cleanupExpiredRecords() {
    const now = Date.now();
    const timeWindow = 60000; // 1分钟
    
    for (const [key, requests] of this.requestCounts.entries()) {
      const validRequests = requests.filter(timestamp => now - timestamp < timeWindow);
      if (validRequests.length === 0) {
        this.requestCounts.delete(key);
      } else {
        this.requestCounts.set(key, validRequests);
      }
    }
  }

  // 封禁用户
  blockUser(userId, reason = '违规操作') {
    this.blockedUsers.add(userId);
    console.warn(`🚫 [安全管理] 用户 ${userId} 已被封禁，原因：${reason}`);
  }

  // 解封用户
  unblockUser(userId) {
    this.blockedUsers.delete(userId);
    console.log(`✅ [安全管理] 用户 ${userId} 已解封`);
  }
}

// 创建全局实例
const securityManager = new SecurityManager();

// 定期清理过期记录
setInterval(() => {
  securityManager.cleanupExpiredRecords();
}, 60000); // 每分钟清理一次

module.exports = securityManager;
