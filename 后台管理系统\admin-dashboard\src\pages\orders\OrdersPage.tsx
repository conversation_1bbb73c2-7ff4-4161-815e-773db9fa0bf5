import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Filter, 
  Download, 
  Eye,
  Edit,
  MoreHorizontal,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  User,
  Users
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DataPagination } from '@/components/ui/data-pagination';
import { BulkActions, orderBulkActions } from '@/components/ui/bulk-actions';
import { DataExport, orderExportTemplates } from '@/components/ui/data-export';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { orderApi, userApi } from '@/services/cloudApi';
import { exportData, orderExportFields } from '@/utils/export';
import type { ExportOptions } from '@/components/ui/data-export';

// 真实订单数据类型（基于您提供的数据结构）
interface RealOrder {
  _id: string;
  orderNo: string;
  customerId: string;
  companionId?: string | null;
  accepterId?: string;
  title: string;
  content: string;
  reward: number;
  duration?: number | null;
  rounds?: number | null;
  tags: string[];
  orderType: 'immediate' | 'scheduled';
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  scheduledDate?: string | null;
  scheduledTime?: string;
  serviceType: 'custom' | 'duration' | 'rounds' | 'time';
  requirements: string;
  pricing: {
    totalAmount: number;
    companionAmount?: number;
    accepterAmount?: number;
    platformAmount: number;
  };
  startTime?: string | null;
  endTime?: string | null;
  chatRoomId?: string | null;
  evaluation?: {
    customerRating?: number | null;
    companionRating?: number | null;
    accepterRating?: number | null;
    customerComment?: string;
    companionComment?: string;
    accepterComment?: string;
  };
  createTime: string;
  updateTime: string;
  acceptTime?: string;
  completeTime?: string;
  cancelReason?: string;
  cancelTime?: string;
  cancelledBy?: string;
}

const orderStatuses = [
  { key: 'all', label: '全部', count: 0 },
  { key: 'pending', label: '待接单', count: 0 },
  { key: 'accepted', label: '已接单', count: 0 },
  { key: 'in_progress', label: '进行中', count: 0 },
  { key: 'completed', label: '已完成', count: 0 },
  { key: 'cancelled', label: '已取消', count: 0 },
];

export default function OrdersPage() {
  const [orders, setOrders] = useState<RealOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // 批量选择状态
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isIndeterminate, setIsIndeterminate] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [userCache, setUserCache] = useState<Record<string, any>>({});
  const { toast } = useToast();

  // 格式化日期函数
  const formatDate = (dateStr: string) => {
    try {
      if (!dateStr) return '未知';
      
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '无效日期';
      
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '格式错误';
    }
  };

  // 获取用户显示名称
  const getUserDisplayName = (userId: string) => {
    if (!userId) return '未知用户';
    
    // 检查缓存
    const cachedUser = userCache[userId];
    if (cachedUser) {
      return cachedUser.nickname || cachedUser.nickName || cachedUser.name || `用户${userId.slice(-6)}`;
    }
    
    // 返回用户ID的后6位作为显示名
    return `用户${userId.slice(-6)}`;
  };

  // 异步获取用户信息（后台加载）
  const loadUserInfo = async (userId: string) => {
    if (!userId || userCache[userId]) return;
    
    try {
      const response = await userApi.getUserDetail(userId);
      
      // 处理不同的响应格式
      let userInfo = null;
      if (response?.data?.success && response.data.data) {
        userInfo = response.data.data.user || response.data.data;
      } else if (response?.data && !response.data.success) {
        userInfo = response.data;
      } else if (response?.data) {
        userInfo = response.data;
      }
      
      if (userInfo) {
        setUserCache(prev => ({
          ...prev,
          [userId]: {
            _id: userInfo._id || userId,
            nickname: userInfo.nickname || userInfo.nickName || userInfo.name,
            avatar: userInfo.avatar || userInfo.avatarUrl,
            phone: userInfo.phone,
            ...userInfo
          }
        }));
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 设置默认用户信息
      setUserCache(prev => ({
        ...prev,
        [userId]: {
          _id: userId,
          nickname: `用户${userId.slice(-6)}`,
          avatar: null
        }
      }));
    }
  };

  // 批量加载用户信息
  const loadUsersInfo = async (orders: RealOrder[]) => {
    const userIds = new Set<string>();
    
    // 收集所有用户ID
    orders.forEach(order => {
      if (order.customerId) userIds.add(order.customerId);
      if (order.accepterId) userIds.add(order.accepterId);
      if (order.companionId) userIds.add(order.companionId);
    });
    
    // 批量获取用户信息
    const userPromises = Array.from(userIds).map(userId => loadUserInfo(userId));
    
    // 等待所有用户信息加载完成
    try {
      await Promise.allSettled(userPromises);
      console.log('用户信息加载完成，缓存状态:', userCache);
    } catch (error) {
      console.error('批量加载用户信息失败:', error);
    }
  };

  // 加载订单数据
  const loadOrders = async (page: number = currentPage, size: number = pageSize) => {
    try {
      setLoading(true);

      console.log('正在调用云开发API获取订单数据...', { page, size, searchTerm });

      // 调用真实的API获取订单数据
      const response = await orderApi.getOrderList({
        page,
        limit: size,
        search: searchTerm,
        status: activeTab === 'all' ? undefined : activeTab
      });

      console.log('API响应:', response);
      console.log('API响应详细信息:', {
        status: response?.status,
        data: response?.data,
        success: response?.data?.success,
        error: response?.data?.error,
        orders: response?.data?.data?.orders
      });
      
      // 处理云开发API响应 - 适配不同的响应格式
      let rawOrderData = [];
      
      if (response?.data?.success && response.data.data) {
        // 格式1: { data: { success: true, data: { orders: [...] } } }
        rawOrderData = response.data.data.orders || response.data.data || [];
      } else if (response?.data?.orders) {
        // 格式2: { data: { orders: [...] } }
        rawOrderData = response.data.orders;
      } else if (response?.data && Array.isArray(response.data)) {
        // 格式3: { data: [...] }
        rawOrderData = response.data;
      } else if (Array.isArray(response)) {
        // 格式4: [...]
        rawOrderData = response;
      } else {
        console.warn('未知的API响应格式:', response);
        throw new Error('云开发API响应格式不正确');
      }
      
      console.log('解析的订单数据:', rawOrderData);
      
      // 检查订单状态分布
      const statusDistribution: Record<string, number> = {};
      rawOrderData.forEach((order: any) => {
        const status = order.status || 'unknown';
        statusDistribution[status] = (statusDistribution[status] || 0) + 1;
      });
      console.log('订单状态分布:', statusDistribution);
      
      setOrders(rawOrderData);

      // 处理分页信息
      const pagination = response?.data?.pagination;
      if (pagination) {
        setTotalItems(pagination.totalItems || rawOrderData.length);
        setTotalPages(pagination.totalPages || Math.ceil(rawOrderData.length / size));
        setCurrentPage(pagination.currentPage || page);
      } else {
        // 如果没有分页信息，假设这是所有数据
        setTotalItems(rawOrderData.length);
        setTotalPages(1);
        setCurrentPage(1);
      }

      // 加载用户信息
      await loadUsersInfo(rawOrderData);

      toast({
        title: '数据加载成功',
        description: `已从云开发加载 ${rawOrderData.length} 个真实订单`,
      });
      
    } catch (error: any) {
      console.error('云开发API调用失败:', error);
      
      toast({
        title: '连接云开发失败',
        description: `错误: ${error.message || '未知错误'}，请检查网络连接后重试`,
        variant: 'destructive',
      });

      // 不再使用模拟数据，保持空状态
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载订单数据和状态统计
  useEffect(() => {
    loadOrders();
    loadStatusCounts();
  }, []); // 只在组件挂载时加载一次

  // 搜索订单 - 延迟搜索
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      setCurrentPage(1); // 搜索时重置到第一页
      loadOrders(1, pageSize);
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  // 状态切换时重新加载订单
  useEffect(() => {
    setCurrentPage(1); // 状态切换时重置到第一页
    loadOrders(1, pageSize);
  }, [activeTab]);

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadOrders(page, pageSize);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 改变页面大小时重置到第一页
    loadOrders(1, size);
  };

  // 批量选择处理函数
  const handleSelectAllOrders = (checked: boolean) => {
    if (checked) {
      const allOrderIds = orders.map(order => order._id);
      setSelectedOrders(allOrderIds);
      setIsAllSelected(true);
      setIsIndeterminate(false);
    } else {
      setSelectedOrders([]);
      setIsAllSelected(false);
      setIsIndeterminate(false);
    }
  };

  const handleSelectOrder = (orderId: string, checked: boolean) => {
    let newSelectedOrders: string[];
    if (checked) {
      newSelectedOrders = [...selectedOrders, orderId];
    } else {
      newSelectedOrders = selectedOrders.filter(id => id !== orderId);
    }

    setSelectedOrders(newSelectedOrders);

    // 更新全选状态
    const totalOrders = orders.length;
    if (newSelectedOrders.length === 0) {
      setIsAllSelected(false);
      setIsIndeterminate(false);
    } else if (newSelectedOrders.length === totalOrders) {
      setIsAllSelected(true);
      setIsIndeterminate(false);
    } else {
      setIsAllSelected(false);
      setIsIndeterminate(true);
    }
  };

  // 批量操作处理函数
  const handleBulkOrderAction = async (action: string, orderIds: string[]) => {
    try {
      switch (action) {
        case 'export':
          // 导出选中订单数据
          await exportOrders(orderIds);
          break;
        case 'complete':
          // 批量标记完成
          await bulkUpdateOrderStatus(orderIds, 'completed');
          break;
        case 'cancel':
          // 批量取消订单
          await bulkUpdateOrderStatus(orderIds, 'cancelled');
          break;
        case 'delete':
          // 批量删除订单
          await bulkDeleteOrders(orderIds);
          break;
        default:
          console.warn('未知的批量操作:', action);
      }

      // 清空选择
      setSelectedOrders([]);
      setIsAllSelected(false);
      setIsIndeterminate(false);

      // 重新加载数据
      loadOrders(currentPage, pageSize);

    } catch (error) {
      console.error('批量操作失败:', error);
      toast({
        title: '操作失败',
        description: '批量操作执行失败，请重试',
        variant: 'destructive',
      });
    }
  };

  // 导出订单数据
  const exportOrders = async (orderIds: string[]) => {
    const selectedOrderData = orders.filter(order => orderIds.includes(order._id));
    const csvContent = generateOrderCSV(selectedOrderData);
    downloadCSV(csvContent, `订单数据_${new Date().toISOString().split('T')[0]}.csv`);

    toast({
      title: '导出成功',
      description: `已导出 ${orderIds.length} 个订单的数据`,
    });
  };

  // 批量更新订单状态
  const bulkUpdateOrderStatus = async (orderIds: string[], status: string) => {
    // 这里应该调用批量更新API
    // await orderApi.bulkUpdateStatus(orderIds, status);

    const statusText = {
      'completed': '完成',
      'cancelled': '取消',
      'in_progress': '进行中',
      'pending': '待处理'
    }[status] || status;

    toast({
      title: '状态更新成功',
      description: `已将 ${orderIds.length} 个订单标记为${statusText}`,
    });
  };

  // 批量删除订单
  const bulkDeleteOrders = async (orderIds: string[]) => {
    // 这里应该调用批量删除API
    // await orderApi.bulkDelete(orderIds);

    toast({
      title: '删除成功',
      description: `已删除 ${orderIds.length} 个订单`,
    });
  };

  // 生成订单CSV内容
  const generateOrderCSV = (orderData: RealOrder[]) => {
    const headers = ['订单号', '标题', '金额', '状态', '创建时间', '客户ID', '接单人ID'];
    const rows = orderData.map(order => [
      order.orderNo || order._id,
      order.title,
      order.reward,
      getStatusText(order.status),
      formatDate(order.createTime),
      order.customerId || '',
      order.accepterId || ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  // 下载CSV文件
  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 高级数据导出处理函数
  const handleAdvancedOrderExport = async (options: ExportOptions) => {
    try {
      let exportOrders = orders;

      // 如果有日期范围筛选，需要重新获取数据
      if (options.dateRange) {
        // 这里应该调用API获取指定日期范围的订单数据
        // const response = await orderApi.getOrderList({
        //   page: 1,
        //   limit: 10000, // 获取所有数据用于导出
        //   dateRange: options.dateRange
        // });
        // exportOrders = response.data.orders;
      }

      // 准备导出数据，处理状态翻译
      const enrichedOrders = exportOrders.map(order => ({
        ...order,
        status: getStatusText(order.status), // 翻译状态
        orderType: getOrderTypeText(order.orderType), // 翻译订单类型
        platform: order.platform === 'mobile' ? '手机端' : order.platform === 'pc' ? '电脑端' : order.platform,
      }));

      await exportData(enrichedOrders, orderExportFields, options);

      toast({
        title: '导出成功',
        description: `已成功导出 ${enrichedOrders.length} 条订单数据`,
      });

    } catch (error) {
      console.error('导出失败:', error);
      toast({
        title: '导出失败',
        description: error instanceof Error ? error.message : '导出过程中发生错误',
        variant: 'destructive',
      });
    }
  };

  // 状态统计数据（独立于当前页面数据）
  const [statusCounts, setStatusCounts] = useState(orderStatuses);

  // 加载状态统计数据
  const loadStatusCounts = async () => {
    try {
      console.log('加载订单状态统计...');

      // 获取所有状态的订单数量
      const statusPromises = orderStatuses.map(async (status) => {
        if (status.key === 'all') {
          // 获取总数
          const response = await orderApi.getOrderList({ page: 1, limit: 1 });
          const total = response?.data?.data?.total || 0;
          return { ...status, count: total };
        } else {
          // 获取特定状态的数量
          const response = await orderApi.getOrderList({
            page: 1,
            limit: 1,
            status: status.key
          });
          const count = response?.data?.data?.total || 0;
          return { ...status, count };
        }
      });

      const counts = await Promise.all(statusPromises);
      setStatusCounts(counts);
      console.log('状态统计加载完成:', counts);

    } catch (error) {
      console.error('加载状态统计失败:', error);
    }
  };

  // 由于API已经处理了状态和搜索筛选，这里直接使用orders数据
  const filteredOrders = orders;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: '待接单', variant: 'secondary' as const, icon: Clock },
      accepted: { label: '已接单', variant: 'default' as const, icon: CheckCircle },
      in_progress: { label: '进行中', variant: 'default' as const, icon: AlertCircle },
      completed: { label: '已完成', variant: 'default' as const, icon: CheckCircle },
      cancelled: { label: '已取消', variant: 'destructive' as const, icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className={
        status === 'completed' ? 'bg-green-100 text-green-800' :
        status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
        status === 'accepted' ? 'bg-purple-100 text-purple-800' : ''
      }>
        <Icon className="mr-1 h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  // 获取服务类型标签
  const getServiceTypeBadge = (order: RealOrder) => {
    if (order.serviceType === 'duration') {
      if (order.duration && order.duration > 0) {
        return <Badge variant="outline">{order.duration}小时</Badge>;
      } else {
        return <Badge variant="outline">自定义时长</Badge>;
      }
    } else if (order.serviceType === 'rounds') {
      if (order.rounds && order.rounds > 0) {
        return <Badge variant="outline">{order.rounds}局</Badge>;
      } else {
        return <Badge variant="outline">自定义局数</Badge>;
      }
    } else if (order.serviceType === 'time') {
      return <Badge variant="outline">按时间</Badge>;
    } else if (order.serviceType === 'custom') {
      // 检查是否有具体的时长或局数信息
      if (order.duration && order.duration > 0) {
        return <Badge variant="outline">{order.duration}小时</Badge>;
      } else if (order.rounds && order.rounds > 0) {
        return <Badge variant="outline">{order.rounds}局</Badge>;
      } else {
        return <Badge variant="outline">自定义服务</Badge>;
      }
    }
    return <Badge variant="outline">自定义</Badge>;
  };

  // 获取订单类型标签
  const getOrderTypeBadge = (orderType: string) => {
    return orderType === 'immediate' ?
      <Badge variant="default" className="bg-orange-100 text-orange-800">即时单</Badge> :
      <Badge variant="default" className="bg-blue-100 text-blue-800">预约单</Badge>;
  };

  // 标签翻译映射
  const getTagLabel = (tagValue: string) => {
    const tagMap: Record<string, string> = {
      'high_winrate': '高胜率',
      'voice_chat': '可语音',
      'humorous': '幽默风趣',
      'professional': '专业指导',
      'newbie_friendly': '新手友好',
      'urgent': '急单'
    };
    return tagMap[tagValue] || tagValue;
  };

  // 获取平台类型标签
  const getPlatformTypeBadge = (platformType: string) => {
    if (platformType === 'mobile') {
      return <Badge variant="outline" className="bg-green-50 text-green-700">📱 手游</Badge>;
    }
    return <Badge variant="outline" className="bg-blue-50 text-blue-700">💻 电脑</Badge>;
  };

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    try {
      // 调用API更新订单状态
      const response = await orderApi.updateOrderStatus(orderId, newStatus);
      
      // 处理API响应
      const result = response.data;
      if (result?.success) {
        setOrders(orders.map(order => 
          order._id === orderId 
            ? { ...order, status: newStatus as any, updateTime: new Date().toISOString() }
            : order
        ));
        
        toast({
          title: '操作成功',
          description: `订单状态已更新`,
        });
      } else {
        throw new Error(result?.error || '更新失败');
      }
    } catch (error: any) {
      console.error('更新订单状态失败:', error);
      
      // 即使API失败，也在本地更新状态以提供用户反馈
      setOrders(orders.map(order => 
        order._id === orderId 
          ? { ...order, status: newStatus as any, updateTime: new Date().toISOString() }
          : order
      ));
      
      toast({
        title: '状态已更新',
        description: `订单状态已更新（本地更新）`,
      });
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
            <p className="text-gray-600">管理平台所有订单信息和状态</p>
          </div>
          <Button disabled>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            加载中...
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
          <p className="text-gray-600">管理平台所有订单信息和状态</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => { loadOrders(); loadStatusCounts(); }} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
          <DataExport
            title="导出订单"
            description="自定义导出订单字段、格式和日期范围"
            fields={orderExportFields}
            templates={orderExportTemplates}
            onExport={handleAdvancedOrderExport}
            disabled={loading}
            maxRecords={10000}
          />
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            统计报表
          </Button>
        </div>
      </div>

      {/* 订单状态标签页 */}
      <Card>
        <CardHeader>
          <CardTitle>订单列表</CardTitle>
          <CardDescription>
            共 {orders.length} 个订单，其中 {orders.filter(o => o.status === 'completed').length} 个已完成
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-6">
              {statusCounts.map((status) => (
                <TabsTrigger key={status.key} value={status.key} className="relative">
                  {status.label}
                  {status.count > 0 && (
                    <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                      {status.count}
                    </Badge>
                  )}
                </TabsTrigger>
              ))}
            </TabsList>

            {/* 搜索和筛选 */}
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索订单标题、订单号或内容..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                高级筛选
              </Button>
            </div>

            <TabsContent value={activeTab} className="space-y-4">
              {/* 批量操作 */}
              <BulkActions
                selectedItems={selectedOrders}
                totalItems={filteredOrders.length}
                onSelectAll={handleSelectAllOrders}
                onAction={handleBulkOrderAction}
                actions={orderBulkActions}
                isAllSelected={isAllSelected}
                isIndeterminate={isIndeterminate}
              />

              {/* 订单表格 */}
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={isAllSelected}
                        onCheckedChange={handleSelectAllOrders}
                        ref={(ref) => {
                          if (ref) {
                            ref.indeterminate = isIndeterminate;
                          }
                        }}
                        aria-label="全选"
                      />
                    </TableHead>
                    <TableHead>订单信息</TableHead>
                    <TableHead>发布者</TableHead>
                    <TableHead>接单者</TableHead>
                    <TableHead>金额/类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.map((order) => (
                    <TableRow key={order._id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedOrders.includes(order._id)}
                          onCheckedChange={(checked) => handleSelectOrder(order._id, checked as boolean)}
                          aria-label={`选择订单 ${order.title}`}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{order.title}</div>
                          <div className="text-sm text-gray-500">#{order.orderNo}</div>
                          <div className="flex space-x-1">
                            {getOrderTypeBadge(order.orderType)}
                            {getServiceTypeBadge(order)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={userCache[order.customerId]?.avatar || "/placeholder.svg?height=32&width=32"} />
                            <AvatarFallback><User className="h-4 w-4" /></AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">{getUserDisplayName(order.customerId)}</span>
                            <span className="text-xs text-gray-500">#{order.customerId.slice(-8)}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {order.accepterId || order.companionId ? (
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={userCache[order.accepterId || order.companionId || '']?.avatar || "/placeholder.svg?height=32&width=32"} />
                              <AvatarFallback><Users className="h-4 w-4" /></AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col">
                              <span className="text-sm font-medium">{getUserDisplayName(order.accepterId || order.companionId || '')}</span>
                              <span className="text-xs text-gray-500">#{(order.accepterId || order.companionId || '').slice(-8)}</span>
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">未接单</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium text-green-600">¥{order.reward}</div>
                          <div className="text-xs text-gray-500">
                            平台 ¥{order.pricing?.platformAmount || 0}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(order.status)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {formatDate(order.createTime)}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e: Event) => e.preventDefault()}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  查看详情
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                                <DialogHeader>
                                  <DialogTitle>订单详情</DialogTitle>
                                  <DialogDescription>
                                    订单 #{order.orderNo} 的详细信息
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="space-y-6">
                                  {/* 基本信息 */}
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">订单标题</label>
                                      <p className="text-sm text-gray-600">{order.title}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">订单号</label>
                                      <p className="text-sm text-gray-600">{order.orderNo}</p>
                                    </div>
                                  </div>
                                  
                                  {/* 用户信息 */}
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">发布者</label>
                                      <p className="text-sm text-gray-600">{getUserDisplayName(order.customerId)}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">接单者</label>
                                      <p className="text-sm text-gray-600">
                                        {order.accepterId || order.companionId 
                                          ? getUserDisplayName(order.accepterId || order.companionId || '') 
                                          : '未接单'}
                                      </p>
                                    </div>
                                  </div>
                                  
                                  {/* 订单内容 */}
                                  <div>
                                    <label className="text-sm font-medium">订单描述</label>
                                    <p className="text-sm text-gray-600 mt-1 p-3 bg-gray-50 rounded">{order.content}</p>
                                  </div>
                                  
                                  {/* 需求详情 */}
                                  <div>
                                    <label className="text-sm font-medium">具体要求</label>
                                    <p className="text-sm text-gray-600 mt-1 p-3 bg-gray-50 rounded">{order.requirements}</p>
                                  </div>
                                  
                                  {/* 标签和平台类型 */}
                                  <div className="grid grid-cols-1 gap-4">
                                    {/* 订单标签 */}
                                    {order.tags && order.tags.length > 0 && (
                                      <div>
                                        <label className="text-sm font-medium">订单标签</label>
                                        <div className="flex flex-wrap gap-2 mt-1">
                                          {order.tags.map((tag, index) => (
                                            <Badge key={index} variant="outline">{getTagLabel(tag)}</Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {/* 平台类型 */}
                                    <div>
                                      <label className="text-sm font-medium">平台类型</label>
                                      <div className="mt-1">
                                        {getPlatformTypeBadge(order.platformType || 'pc')}
                                      </div>
                                    </div>
                                  </div>
                                  
                                  {/* 价格信息 */}
                                  <div className="grid grid-cols-3 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">总金额</label>
                                      <p className="text-sm text-gray-600">¥{order.reward}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">接单者收入</label>
                                      <p className="text-sm text-gray-600">¥{order.pricing?.companionAmount || order.pricing?.accepterAmount || 0}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">平台费用</label>
                                      <p className="text-sm text-gray-600">¥{order.pricing?.platformAmount || 0}</p>
                                    </div>
                                  </div>
                                  
                                  {/* 时间信息 */}
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">创建时间</label>
                                      <p className="text-sm text-gray-600">{formatDate(order.createTime)}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">更新时间</label>
                                      <p className="text-sm text-gray-600">{formatDate(order.updateTime)}</p>
                                    </div>
                                  </div>
                                  
                                  {/* 执行时间 */}
                                  {(order.startTime || order.endTime) && (
                                    <div className="grid grid-cols-2 gap-4">
                                      {order.startTime && (
                                        <div>
                                          <label className="text-sm font-medium">开始时间</label>
                                          <p className="text-sm text-gray-600">{formatDate(order.startTime)}</p>
                                        </div>
                                      )}
                                      {order.endTime && (
                                        <div>
                                          <label className="text-sm font-medium">结束时间</label>
                                          <p className="text-sm text-gray-600">{formatDate(order.endTime)}</p>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                  
                                  {/* 取消信息 */}
                                  {order.status === 'cancelled' && order.cancelReason && (
                                    <div>
                                      <label className="text-sm font-medium">取消原因</label>
                                      <p className="text-sm text-red-600 mt-1">{order.cancelReason}</p>
                                      {order.cancelTime && (
                                        <p className="text-xs text-gray-500">取消时间: {formatDate(order.cancelTime)}</p>
                                      )}
                                    </div>
                                  )}
                                  
                                  {/* 评价信息 */}
                                  {order.evaluation && (
                                    <div>
                                      <label className="text-sm font-medium">评价信息</label>
                                      <div className="mt-2 space-y-2">
                                        {order.evaluation.customerRating && (
                                          <div className="p-2 bg-blue-50 rounded">
                                            <p className="text-sm font-medium">客户评价: {order.evaluation.customerRating}星</p>
                                            {order.evaluation.customerComment && (
                                              <p className="text-sm text-gray-600">{order.evaluation.customerComment}</p>
                                            )}
                                          </div>
                                        )}
                                        {(order.evaluation.companionRating || order.evaluation.accepterRating) && (
                                          <div className="p-2 bg-green-50 rounded">
                                            <p className="text-sm font-medium">接单者评价: {order.evaluation.companionRating || order.evaluation.accepterRating}星</p>
                                            {(order.evaluation.companionComment || order.evaluation.accepterComment) && (
                                              <p className="text-sm text-gray-600">{order.evaluation.companionComment || order.evaluation.accepterComment}</p>
                                            )}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </DialogContent>
                            </Dialog>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑订单
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {order.status === 'pending' && (
                              <DropdownMenuItem onClick={() => handleStatusChange(order._id, 'cancelled')}>
                                <XCircle className="mr-2 h-4 w-4" />
                                取消订单
                              </DropdownMenuItem>
                            )}
                            {order.status === 'accepted' && (
                              <DropdownMenuItem onClick={() => handleStatusChange(order._id, 'in_progress')}>
                                <AlertCircle className="mr-2 h-4 w-4" />
                                开始执行
                              </DropdownMenuItem>
                            )}
                            {order.status === 'in_progress' && (
                              <DropdownMenuItem onClick={() => handleStatusChange(order._id, 'completed')}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                标记完成
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredOrders.length === 0 && !loading && (
                <div className="text-center py-8 text-gray-500">
                  <p>没有找到匹配的订单</p>
                </div>
              )}

              {/* 分页组件 */}
              <DataPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                pageSizeOptions={[10, 20, 50, 100]}
                showPageSizeSelector={true}
                showInfo={true}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
