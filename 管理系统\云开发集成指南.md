# 微信云开发API集成指南

## 🌐 环境信息
- **云环境ID**: `cloud1-9gsj7t48183e5a9f`
- **项目类型**: 微信小程序云开发

## 🔑 API密钥和权限获取

### 方案一：云函数HTTP触发器（推荐）

#### 1. 在云开发控制台创建云函数
```javascript
// 云函数名称: admin-api
// 在云开发控制台 -> 云函数 -> 新建云函数

// index.js
const cloud = require('wx-server-sdk');
cloud.init({
  env: 'cloud1-9gsj7t48183e5a9f'
});

exports.main = async (event, context) => {
  const { action, data } = event;
  
  // 验证管理员权限
  const { userInfo } = cloud.getWXContext();
  
  switch (action) {
    case 'getDashboardStats':
      return await getDashboardStats();
    case 'getUserList':
      return await getUserList(data);
    // ... 其他API处理
    default:
      return { success: false, error: 'Unknown action' };
  }
};

async function getDashboardStats() {
  const db = cloud.database();
  
  // 获取用户统计
  const userCount = await db.collection('users').count();
  
  // 获取订单统计
  const orderCount = await db.collection('orders').count();
  
  return {
    success: true,
    data: {
      totalUsers: userCount.total,
      totalOrders: orderCount.total,
      // ... 其他统计数据
    }
  };
}
```

#### 2. 配置HTTP触发器
- 在云函数详情页面 -> 触发器 -> 新建触发器
- 选择"HTTP触发器"
- 获得访问URL: `https://cloud1-9gsj7t48183e5a9f.service.tcloudbase.com/admin-api`

#### 3. 前端调用方式
```javascript
// 直接HTTP请求
const response = await fetch('https://cloud1-9gsj7t48183e5a9f.service.tcloudbase.com/admin-api', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    action: 'getDashboardStats',
    data: {}
  })
});
```

### 方案二：云开发Web SDK

#### 1. 安装SDK
```bash
npm install @cloudbase/js-sdk
```

#### 2. 初始化配置
```javascript
// src/services/cloudbase.ts
import tcb from '@cloudbase/js-sdk';

const app = tcb.init({
  env: 'cloud1-9gsj7t48183e5a9f'
});

// 匿名登录（用于管理后台）
export const auth = app.auth();
export const db = app.database();
export const functions = app.functions();

// 管理员登录
export const adminLogin = async (username: string, password: string) => {
  try {
    // 调用登录云函数验证管理员身份
    const result = await functions.callFunction({
      name: 'adminLogin',
      data: { username, password }
    });
    
    if (result.result.success) {
      // 使用自定义登录
      await auth.signInWithTicket(result.result.ticket);
      return { success: true };
    }
    return { success: false, error: result.result.error };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// 调用云函数
export const callCloudFunction = async (name: string, data: any) => {
  try {
    const result = await functions.callFunction({
      name,
      data
    });
    return result.result;
  } catch (error) {
    throw new Error(error.message);
  }
};
```

### 方案三：腾讯云API密钥

#### 1. 获取API密钥
- 访问: https://console.cloud.tencent.com/cam/capi
- 创建新的API密钥
- 记录 SecretId 和 SecretKey

#### 2. 安装腾讯云SDK
```bash
npm install tencentcloud-sdk-nodejs
```

#### 3. 配置API调用
```javascript
// src/services/tencent-cloud.ts
import { tcb } from 'tencentcloud-sdk-nodejs';

const TcbClient = tcb.v20180608.Client;

const clientConfig = {
  credential: {
    secretId: "您的SecretId",
    secretKey: "您的SecretKey",
  },
  region: "ap-shanghai", // 根据您的云环境区域调整
  profile: {
    httpProfile: {
      endpoint: "tcb.tencentcloudapi.com",
    },
  },
};

const client = new TcbClient(clientConfig);

export const callCloudFunction = async (functionName: string, data: any) => {
  const params = {
    EnvId: "cloud1-9gsj7t48183e5a9f",
    FunctionName: functionName,
    FunctionParameter: JSON.stringify(data)
  };
  
  try {
    const result = await client.CallCloudFunction(params);
    return JSON.parse(result.Result);
  } catch (error) {
    throw new Error(error.message);
  }
};
```

## 🔐 权限配置

### 1. 数据库权限设置
在云开发控制台 -> 数据库 -> 权限设置:

```javascript
// 管理员权限规则示例
{
  "read": "auth.uid != null && get('database').collection('admins').where({uid: auth.uid}).get().data.length > 0",
  "write": "auth.uid != null && get('database').collection('admins').where({uid: auth.uid}).get().data.length > 0"
}
```

### 2. 云函数权限配置
- 在云函数详情页面设置访问权限
- 配置CORS跨域规则
- 设置IP白名单（可选）

### 3. 安全配置
```javascript
// 在云函数中验证管理员权限
const verifyAdmin = async (context) => {
  const { userInfo } = cloud.getWXContext();
  
  if (!userInfo || !userInfo.openId) {
    throw new Error('未登录');
  }
  
  const db = cloud.database();
  const adminRecord = await db.collection('admins')
    .where({ openId: userInfo.openId })
    .get();
    
  if (adminRecord.data.length === 0) {
    throw new Error('无管理员权限');
  }
  
  return adminRecord.data[0];
};
```

## 🚀 推荐实施步骤

### 第一步：选择集成方案
- **简单快速**: 使用方案一（HTTP触发器）
- **功能完整**: 使用方案二（Web SDK）
- **企业级**: 使用方案三（API密钥）

### 第二步：创建云函数
1. 在云开发控制台创建 `admin-api` 云函数
2. 实现基础的API路由和权限验证
3. 配置HTTP触发器或相应的访问方式

### 第三步：前端集成
1. 根据选择的方案修改 `src/services/api.ts`
2. 实现登录认证逻辑
3. 测试API连接和数据获取

### 第四步：权限配置
1. 设置数据库访问权限
2. 配置云函数访问控制
3. 实施安全验证机制

## 📞 需要帮助？

如果您需要具体实施某个方案，请告诉我：
1. 您希望使用哪种集成方案？
2. 您的小程序是否已有用户认证系统？
3. 您希望管理后台如何进行身份验证？

我可以为您提供详细的代码实现和配置指导！
