import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Star, 
  Search, 
  Filter, 
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  ThumbsUp,
  ThumbsDown,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { evaluationApi, userApi } from '@/services/cloudApi';
import type { Evaluation } from '@/types';

export default function EvaluationsPage() {
  const [evaluations, setEvaluations] = useState<Evaluation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    averageScore: 0,
    lowScoreCount: 0,
  });

  // 用户信息缓存
  const [userCache, setUserCache] = useState<Record<string, any>>({});

  const { toast } = useToast();

  // 获取用户显示名称
  const getUserDisplayName = (userId: string) => {
    if (!userId) return '未知用户';

    // 检查缓存
    const cachedUser = userCache[userId];
    if (cachedUser) {
      return cachedUser.nickname || cachedUser.nickName || cachedUser.name || `用户${userId.slice(-6)}`;
    }

    // 返回用户ID的后6位作为显示名
    return `用户${userId.slice(-6)}`;
  };

  // 异步获取用户信息（后台加载）
  const loadUserInfo = async (userId: string) => {
    if (!userId || userCache[userId]) return;

    try {
      const response = await userApi.getUserDetail(userId);

      // 处理不同的响应格式
      let userInfo = null;
      if (response?.data?.success && response.data.data) {
        userInfo = response.data.data;
      } else if (response?.data && !response.data.success) {
        userInfo = response.data;
      } else if (response?.data) {
        userInfo = response.data;
      }

      if (userInfo) {
        setUserCache(prev => ({
          ...prev,
          [userId]: {
            _id: userInfo._id || userId,
            nickname: userInfo.nickname || userInfo.nickName || userInfo.name,
            avatar: userInfo.avatar || userInfo.avatarUrl,
            phone: userInfo.phone,
            ...userInfo
          }
        }));
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 设置默认用户信息
      setUserCache(prev => ({
        ...prev,
        [userId]: {
          _id: userId,
          nickname: `用户${userId.slice(-6)}`,
          avatar: null
        }
      }));
    }
  };

  // 批量加载用户信息
  const loadUsersInfo = async (evaluations: any[]) => {
    const userIds = new Set<string>();

    // 收集所有用户ID
    evaluations.forEach(evaluation => {
      if (evaluation.customerId) userIds.add(evaluation.customerId);
      if (evaluation.accepterId) userIds.add(evaluation.accepterId);
      if (evaluation.evaluatorId) userIds.add(evaluation.evaluatorId);
    });

    // 批量获取用户信息
    const userPromises = Array.from(userIds).map(userId => loadUserInfo(userId));

    // 等待所有用户信息加载完成
    try {
      await Promise.allSettled(userPromises);
      console.log('用户信息加载完成，缓存状态:', userCache);
    } catch (error) {
      console.error('批量加载用户信息失败:', error);
    }
  };

  // 加载评价数据
  const loadEvaluations = async () => {
    try {
      setLoading(true);
      console.log('🔄 开始加载评价数据...');

      const [evaluationsRes, statsRes] = await Promise.all([
        evaluationApi.getEvaluationList({
          status: activeTab === 'all' ? undefined : activeTab,
        }),
        evaluationApi.getEvaluationStats(),
      ]);

      console.log('📥 评价API响应:', { evaluationsRes, statsRes });

      // 处理评价列表数据
      if (evaluationsRes.data && evaluationsRes.data.success) {
        const evaluations = evaluationsRes.data.data?.evaluations || [];
        console.log('✅ 成功获取评价数据:', evaluations);

        // 转换数据格式以匹配前端期望的结构
        const formattedEvaluations = evaluations.map((evaluation: any) => {
          // 根据evaluatorType确定评价者和被评价者ID
          let evaluatorId, evaluatedId;

          if (evaluation.evaluatorType === 'customer') {
            // 客户评价接单者
            evaluatorId = evaluation.customerId;
            evaluatedId = evaluation.accepterId;
          } else if (evaluation.evaluatorType === 'accepter') {
            // 接单者评价客户
            evaluatorId = evaluation.accepterId;
            evaluatedId = evaluation.customerId;
          } else {
            // 兼容旧数据
            evaluatorId = evaluation.customerId || evaluation.evaluatorId;
            evaluatedId = evaluation.accepterId || evaluation.evaluatedId;
          }

          return {
            id: evaluation._id,
            orderId: evaluation.orderId,
            evaluatorId,
            evaluatedId,
            score: evaluation.rating || evaluation.score,
            comment: evaluation.content || evaluation.comment,
            status: evaluation.status || 'approved',
            createdAt: evaluation.createTime,
            reviewedAt: evaluation.reviewedAt,
            // 添加用户信息
            evaluatorInfo: evaluation.evaluatorInfo || {
              nickName: '未知用户',
              avatarUrl: '/placeholder.svg?height=32&width=32'
            },
            evaluatedInfo: evaluation.evaluatedInfo || {
              nickName: '未知用户',
              avatarUrl: '/placeholder.svg?height=32&width=32'
            }
          };
        });

        setEvaluations(formattedEvaluations);

        // 异步加载用户信息
        loadUsersInfo(evaluations);

        toast({
          title: '数据加载成功',
          description: `已从云数据库加载 ${formattedEvaluations.length} 条真实评价`,
        });
      } else {
        throw new Error('获取评价列表失败');
      }

      // 处理统计数据
      if (statsRes.data && statsRes.data.success) {
        setStats(statsRes.data.data || {
          total: 0,
          pending: 0,
          averageScore: 0,
          lowScoreCount: 0,
        });
      }

      return; // 成功加载真实数据，直接返回

    } catch (error) {
      console.error('加载评价数据失败:', error);
      toast({
        title: '加载失败',
        description: '无法连接到服务器，请检查网络连接后重试',
        variant: 'destructive',
      });

      // 不再使用模拟数据，保持空状态
      setEvaluations([]);
      setStats({
        total: 0,
        pending: 0,
        averageScore: 0,
        lowScoreCount: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadEvaluations();
  }, [activeTab]);

  const filteredEvaluations = Array.isArray(evaluations) ? evaluations.filter(evaluation => {
    const matchesSearch = (evaluation.orderId || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (evaluation.comment || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = activeTab === 'all' || evaluation.status === activeTab;
    return matchesSearch && matchesStatus;
  }) : [];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: '待审核', variant: 'secondary' as const, icon: Clock },
      approved: { label: '已通过', variant: 'default' as const, icon: CheckCircle },
      rejected: { label: '已拒绝', variant: 'destructive' as const, icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className={
        status === 'approved' ? 'bg-green-100 text-green-800' :
        status === 'pending' ? 'bg-yellow-100 text-yellow-800' : ''
      }>
        <Icon className="mr-1 h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const renderStars = (score: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < score ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm font-medium">{score}.0</span>
      </div>
    );
  };

  const getScoreColor = (score: number) => {
    if (score >= 4) return 'text-green-600';
    if (score >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleApproveEvaluation = async (id: string) => {
    try {
      await evaluationApi.approveEvaluation(id);
      setEvaluations((evaluations || []).map(evaluation => 
        evaluation.id === id 
          ? { ...evaluation, status: 'approved', reviewedAt: new Date().toISOString() }
          : evaluation
      ));
      toast({
        title: '操作成功',
        description: '评价已通过审核',
      });
    } catch (error) {
      console.error('审核评价失败:', error);
      toast({
        title: '操作失败',
        description: '审核评价失败，请重试',
        variant: 'destructive',
      });
    }
  };

  const handleRejectEvaluation = async (id: string) => {
    try {
      await evaluationApi.rejectEvaluation(id, '不符合平台规范');
      setEvaluations((evaluations || []).map(evaluation => 
        evaluation.id === id 
          ? { ...evaluation, status: 'rejected', reviewedAt: new Date().toISOString() }
          : evaluation
      ));
      toast({
        title: '操作成功',
        description: '评价已拒绝',
      });
    } catch (error) {
      console.error('拒绝评价失败:', error);
      toast({
        title: '操作失败',
        description: '拒绝评价失败，请重试',
        variant: 'destructive',
      });
    }
  };

  // 使用API返回的统计数据
  const totalEvaluations = stats.total;
  const pendingCount = stats.pending;
  const averageScore = (stats.averageScore || 0).toFixed(1);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">评价管理</h1>
          <p className="text-gray-600">管理用户评价和信誉系统</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadEvaluations} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总评价数</CardTitle>
            <Star className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEvaluations}</div>
            <p className="text-xs text-muted-foreground">所有评价记录</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待审核</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{pendingCount}</div>
            <p className="text-xs text-muted-foreground">需要处理</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均评分</CardTitle>
            <ThumbsUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{averageScore}</div>
            <p className="text-xs text-muted-foreground">整体满意度</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">低分评价</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {stats.lowScoreCount}
            </div>
            <p className="text-xs text-muted-foreground">需要关注</p>
          </CardContent>
        </Card>
      </div>

      {/* 评价列表 */}
      <Card>
        <CardHeader>
          <CardTitle>评价列表</CardTitle>
          <CardDescription>
            管理所有用户评价和审核状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="pending">待审核</TabsTrigger>
              <TabsTrigger value="approved">已通过</TabsTrigger>
              <TabsTrigger value="rejected">已拒绝</TabsTrigger>
            </TabsList>

            {/* 搜索和筛选 */}
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索订单号或评价内容..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                筛选
              </Button>
            </div>

            <TabsContent value={activeTab}>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>评价信息</TableHead>
                    <TableHead>评价者</TableHead>
                    <TableHead>被评价者</TableHead>
                    <TableHead>评分</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>审核时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEvaluations.map((evaluation) => (
                    <TableRow key={evaluation.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">订单 {evaluation.orderId}</div>
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {evaluation.comment}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={userCache[evaluation.evaluatorId]?.avatar || "/placeholder.svg?height=32&width=32"} />
                            <AvatarFallback>评</AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">{getUserDisplayName(evaluation.evaluatorId)}</span>
                            <span className="text-xs text-gray-500">#{evaluation.evaluatorId.slice(-8)}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={userCache[evaluation.evaluatedId]?.avatar || "/placeholder.svg?height=32&width=32"} />
                            <AvatarFallback>被</AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">{getUserDisplayName(evaluation.evaluatedId)}</span>
                            <span className="text-xs text-gray-500">#{evaluation.evaluatedId.slice(-8)}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {renderStars(evaluation.score)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(evaluation.status)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {formatDate(evaluation.createdAt)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {evaluation.reviewedAt ? formatDate(evaluation.reviewedAt) : '-'}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e: Event) => e.preventDefault()}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  查看详情
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="max-w-md">
                                <DialogHeader>
                                  <DialogTitle>评价详情</DialogTitle>
                                  <DialogDescription>
                                    订单 {evaluation.orderId} 的评价信息
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4">
                                  <div>
                                    <label className="text-sm font-medium">评分</label>
                                    <div className="mt-1">{renderStars(evaluation.score)}</div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">评价内容</label>
                                    <p className="text-sm text-gray-600 mt-1 p-3 bg-gray-50 rounded">
                                      {evaluation.comment}
                                    </p>
                                  </div>
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">评价者</label>
                                      <div className="flex items-center space-x-2 mt-1">
                                        <Avatar className="h-6 w-6">
                                          <AvatarImage src={userCache[evaluation.evaluatorId]?.avatar || "/placeholder.svg?height=24&width=24"} />
                                          <AvatarFallback className="text-xs">评</AvatarFallback>
                                        </Avatar>
                                        <div className="flex flex-col">
                                          <span className="text-sm font-medium">{getUserDisplayName(evaluation.evaluatorId)}</span>
                                          <span className="text-xs text-gray-500">#{evaluation.evaluatorId.slice(-8)}</span>
                                        </div>
                                      </div>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">被评价者</label>
                                      <div className="flex items-center space-x-2 mt-1">
                                        <Avatar className="h-6 w-6">
                                          <AvatarImage src={userCache[evaluation.evaluatedId]?.avatar || "/placeholder.svg?height=24&width=24"} />
                                          <AvatarFallback className="text-xs">被</AvatarFallback>
                                        </Avatar>
                                        <div className="flex flex-col">
                                          <span className="text-sm font-medium">{getUserDisplayName(evaluation.evaluatedId)}</span>
                                          <span className="text-xs text-gray-500">#{evaluation.evaluatedId.slice(-8)}</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                            {evaluation.status === 'pending' && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleApproveEvaluation(evaluation.id)}
                                  className="text-green-600"
                                >
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  通过审核
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleRejectEvaluation(evaluation.id)}
                                  className="text-red-600"
                                >
                                  <XCircle className="mr-2 h-4 w-4" />
                                  拒绝审核
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredEvaluations.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>没有找到匹配的评价记录</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}