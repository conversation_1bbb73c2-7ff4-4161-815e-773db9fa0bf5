<!--profile.wxml - 科技感个人中心-->
<navigation-bar title="个人中心" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
  </view>
</view>

<!-- G.T.I. SECURITY 科技感加载框 -->
<view wx:if="{{loading}}" class="gti-loading-container">
  <view class="gti-loading-backdrop"></view>
  <view class="gti-loading-content">
    <view class="gti-logo-container">
      <image class="gti-logo" src="cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/logos/gti-security-logo.png" mode="aspectFit"></image>
    </view>
    <view class="gti-loading-spinner">
      <view class="spinner-ring"></view>
      <view class="spinner-ring"></view>
      <view class="spinner-ring"></view>
    </view>
    <text class="gti-loading-text">正在加载个人信息...</text>
    <view class="gti-loading-dots">
      <view class="dot dot-1"></view>
      <view class="dot dot-2"></view>
      <view class="dot dot-3"></view>
    </view>
  </view>
</view>

<view class="profile-container page-with-custom-nav">
  <!-- 科技感用户信息卡片 -->
  <view class="user-card cyber-card">
    <view class="card-glow"></view>
    <view class="user-info">
      <view class="avatar-container">
        <image wx:if="{{userInfo.avatarUrl}}" class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" />
        <view wx:else class="user-avatar default-avatar">
          <text class="avatar-text">👤</text>
        </view>
      </view>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName || '用户'}}</text>
        <text class="game-nickname" wx:if="{{userInfo.gameNickName}}">游戏昵称: {{userInfo.gameNickName}}</text>
        <view class="user-basic-info">
          <view class="info-item" wx:if="{{userInfo.gender !== undefined}}">
            <text class="info-label">性别:</text>
            <text class="info-value">{{userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '保密'}}</text>
          </view>
          <view class="info-item" wx:if="{{userInfo.age}}">
            <text class="info-label">年龄:</text>
            <text class="info-value">{{userInfo.age}}岁</text>
          </view>
        </view>
        <view class="user-status">
          <view class="status-badge {{userInfo.isVerified ? 'verified' : 'unverified'}}">
            <text class="status-icon">{{userInfo.isVerified ? '✓' : '⚠'}}</text>
            <text class="status-text">{{userInfo.isVerified ? '已实名认证' : '未实名认证'}}</text>
          </view>
          <text class="credit-score">信用积分: {{userInfo.creditScore || 100}}</text>
        </view>
      </view>
      <view class="user-actions">
        <view class="edit-btn cyber-btn" bindtap="editProfile">
          <text class="btn-text">编辑</text>
          <view class="btn-glow"></view>
        </view>
      </view>
    </view>
    
    <!-- 科技感用户统计 -->
    <view class="user-stats">
      <view class="stat-item">
        <view class="stat-icon">📊</view>
        <text class="stat-value">{{userStats.totalOrders || 15}}</text>
        <text class="stat-label">总订单</text>
        <view class="stat-glow"></view>
      </view>
      <view class="stat-item">
        <view class="stat-icon">✅</view>
        <text class="stat-value">{{userStats.completedOrders || 12}}</text>
        <text class="stat-label">已完成</text>
        <view class="stat-glow"></view>
      </view>
      <view class="stat-item">
        <view class="stat-icon">⭐</view>
        <text class="stat-value">{{userStats.rating || '4.8'}}</text>
        <text class="stat-label">评分</text>
        <view class="stat-glow"></view>
      </view>
      <view class="stat-item">
        <view class="stat-icon">💰</view>
        <text class="stat-value">¥{{userInfo.balance || '128.5'}}</text>
        <text class="stat-label">余额</text>
        <view class="stat-glow"></view>
      </view>
    </view>
  </view>

  <!-- 详细信息卡片 -->
  <view class="detail-info-card cyber-card" wx:if="{{userInfo.bio || userInfo.contactInfo}}">
    <view class="card-glow"></view>
    <view class="detail-section">
      <view class="section-header">
        <text class="section-title">详细信息</text>
        <view class="title-line"></view>
      </view>

      <!-- 个人简介 -->
      <view class="detail-item" wx:if="{{userInfo.bio}}">
        <view class="detail-label">
          <text class="label-icon">📝</text>
          <text class="label-text">个人简介</text>
        </view>
        <text class="detail-content">{{userInfo.bio}}</text>
      </view>

      <!-- 联系方式 -->
      <view class="detail-item" wx:if="{{userInfo.contactInfo}}">
        <view class="detail-label">
          <text class="label-icon">📞</text>
          <text class="label-text">联系方式</text>
        </view>
        <text class="detail-content">{{userInfo.contactInfo}}</text>
      </view>
    </view>
  </view>

  <!-- 科技感功能菜单 -->
  <view class="menu-section">
    <view class="menu-group cyber-card">
      <view class="card-glow"></view>
      <view class="menu-item cyber-menu-item" bindtap="navigateToOrders">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">✅</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">已完成订单</text>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item cyber-menu-item" bindtap="navigateToWallet">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">💰</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">我的钱包</text>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item cyber-menu-item" bindtap="navigateToCertification">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">🔐</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">实名认证</text>
        <view class="menu-badge" wx:if="{{!userInfo.isVerified}}">
          <text class="badge-text">!</text>
          <view class="badge-glow"></view>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>
    </view>

    <view class="menu-group cyber-card">
      <view class="card-glow"></view>
      <view class="menu-item cyber-menu-item" bindtap="navigateToGameAccounts">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">🎮</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">游戏账号</text>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>
    </view>

    <view class="menu-group cyber-card">
      <view class="card-glow"></view>
      <view class="menu-item cyber-menu-item" bindtap="navigateToCustomerService">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">🎧</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">客服中心</text>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item cyber-menu-item" bindtap="goToStatistics">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">📊</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">数据统计</text>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item cyber-menu-item" bindtap="goToNotifications">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">🔔</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">通知中心</text>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item cyber-menu-item" bindtap="navigateToSettings">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">⚙️</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">设置</text>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item cyber-menu-item" bindtap="navigateToAbout">
        <view class="menu-icon-container">
          <text class="menu-icon-emoji">ℹ️</text>
          <view class="icon-glow"></view>
        </view>
        <text class="menu-text">关于我们</text>
        <view class="menu-arrow">
          <text class="arrow-text">→</text>
          <view class="arrow-glow"></view>
        </view>
      </view>


    </view>
  </view>

  <!-- 科技感退出登录 -->
  <view class="logout-section">
    <view class="logout-btn cyber-btn danger-btn" bindtap="logout">
      <text class="btn-text">退出登录</text>
      <view class="btn-glow danger-glow"></view>
    </view>
  </view>
</view>
