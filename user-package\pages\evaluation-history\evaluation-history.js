// 用户评价历史页面
import API from '../../../utils/api.js';

const app = getApp();

Page({
  data: {
    // 页面状态
    loading: true,
    refreshing: false,
    
    // 评价统计
    stats: null,
    
    // 评价列表
    receivedEvaluations: [], // 收到的评价
    givenEvaluations: [],   // 给出的评价
    
    // 当前显示的标签页
    activeTab: 'received', // received | given | stats
    
    // 分页
    receivedPage: 1,
    givenPage: 1,
    pageSize: 10,
    hasMoreReceived: true,
    hasMoreGiven: true
  },

  onLoad(options) {
    console.log('evaluation-history页面加载');
    this.loadEvaluationStats();
    this.loadReceivedEvaluations();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    if (this.data.activeTab === 'received' && this.data.hasMoreReceived) {
      this.loadMoreReceivedEvaluations();
    } else if (this.data.activeTab === 'given' && this.data.hasMoreGiven) {
      this.loadMoreGivenEvaluations();
    }
  },

  // 刷新数据
  async refreshData() {
    this.setData({
      refreshing: true,
      receivedPage: 1,
      givenPage: 1,
      hasMoreReceived: true,
      hasMoreGiven: true,
      receivedEvaluations: [],
      givenEvaluations: []
    });

    try {
      await Promise.all([
        this.loadEvaluationStats(),
        this.loadReceivedEvaluations(),
        this.data.activeTab === 'given' ? this.loadGivenEvaluations() : Promise.resolve()
      ]);
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // 加载评价统计
  async loadEvaluationStats() {
    try {
      const result = await API.getUserEvaluationStats(null, true);
      
      if (result.success) {
        this.setData({
          stats: result.data.stats,
          loading: false
        });
      } else {
        throw new Error(result.error || '加载统计失败');
      }
    } catch (error) {
      console.error('加载评价统计失败:', error);
      this.setData({ loading: false });
      app.utils.showError('加载统计失败');
    }
  },

  // 加载收到的评价
  async loadReceivedEvaluations() {
    if (!this.data.hasMoreReceived) return;

    try {
      // 这里需要创建一个新的云函数来获取评价列表
      // 暂时使用统计数据中的最近评价
      const stats = this.data.stats;
      if (stats) {
        const allReceived = [
          ...stats.asCustomer.recentEvaluations.map(evaluation => ({
            ...evaluation,
            userRole: 'customer',
            evaluatorRole: 'accepter'
          })),
          ...stats.asAccepter.recentEvaluations.map(evaluation => ({
            ...evaluation,
            userRole: 'accepter',
            evaluatorRole: 'customer'
          }))
        ].sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

        this.setData({
          receivedEvaluations: allReceived,
          hasMoreReceived: false
        });
      }
    } catch (error) {
      console.error('加载收到的评价失败:', error);
      app.utils.showError('加载评价失败');
    }
  },

  // 加载给出的评价
  async loadGivenEvaluations() {
    if (!this.data.hasMoreGiven) return;

    try {
      // TODO: 实现获取用户给出的评价
      // 暂时显示空列表
      this.setData({
        givenEvaluations: [],
        hasMoreGiven: false
      });
    } catch (error) {
      console.error('加载给出的评价失败:', error);
      app.utils.showError('加载评价失败');
    }
  },

  // 加载更多收到的评价
  async loadMoreReceivedEvaluations() {
    // TODO: 实现分页加载
  },

  // 加载更多给出的评价
  async loadMoreGivenEvaluations() {
    // TODO: 实现分页加载
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });

    // 懒加载给出的评价
    if (tab === 'given' && this.data.givenEvaluations.length === 0) {
      this.loadGivenEvaluations();
    }
  },

  // 格式化时间
  formatTime(dateStr) {
    const date = new Date(dateStr);
    const now = new Date();
    const diff = now - date;
    
    // 小于1小时显示分钟
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return minutes < 1 ? '刚刚' : `${minutes}分钟前`;
    }
    
    // 小于24小时显示小时
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours}小时前`;
    }
    
    // 小于7天显示天数
    if (diff < 604800000) {
      const days = Math.floor(diff / 86400000);
      return `${days}天前`;
    }
    
    // 超过7天显示具体日期
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  },

  // 获取评分文本
  getRatingText(rating) {
    const texts = ['', '很差', '较差', '一般', '满意', '非常满意'];
    return texts[rating] || '';
  },

  // 获取星星显示
  getStars(rating) {
    return '★'.repeat(rating) + '☆'.repeat(5 - rating);
  }
});
