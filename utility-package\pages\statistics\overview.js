// 统计概览页面
import API from '../../../utils/api.js';
import errorHandler from '../../../utils/errorHandler.js';

const app = getApp();

Page({
  data: {
    loading: false,
    
    // 时间范围选项
    timeRanges: [
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本季度', value: 'quarter' },
      { label: '本年', value: 'year' }
    ],
    currentTimeRange: 'month',
    currentTimeRangeIndex: 1,

    // 统计数据
    overviewData: {
      totalOrders: 0,
      pendingOrders: 0,
      inProgressOrders: 0,
      completedOrders: 0,
      cancelledOrders: 0,
      totalIncome: 0,
      totalExpense: 0,
      netIncome: 0,
      serviceHours: 0,
      completionRate: 0
    },

    incomeData: {
      total: 0,
      orderCount: 0,
      averagePerOrder: 0,
      dailyData: [],
      monthlyData: []
    },

    serviceData: {
      totalOrders: 0,
      totalHours: 0,
      totalRounds: 0,
      averageHoursPerOrder: 0,
      serviceTypeStats: {}
    },

    trendData: {
      dailyTrend: []
    },

    // 信誉系统数据
    reputationData: {
      totalScore: 0,
      level: 1,
      levelInfo: {},
      progress: {},
      completedOrders: 0,
      goodEvaluations: 0,
      badEvaluations: 0,
      averageRating: 0,
      badges: [],
      recentHistory: []
    },

    // 排行榜数据
    leaderboardData: {
      myRank: 0,
      topUsers: []
    }
  },

  onLoad() {
    this.loadAllStatistics();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadAllStatistics();
  },

  onPullDownRefresh() {
    this.loadAllStatistics().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 时间范围选择
  onTimeRangeChange(e) {
    const index = parseInt(e.detail.value);
    const timeRange = this.data.timeRanges[index];
    
    this.setData({
      currentTimeRangeIndex: index,
      currentTimeRange: timeRange.value
    }, () => {
      this.loadAllStatistics();
    });
  },

  // 加载所有统计数据
  async loadAllStatistics() {
    this.setData({ loading: true });

    try {
      const { currentTimeRange } = this.data;

      // 并行加载所有统计数据
      const [overviewResult, incomeResult, serviceResult, trendResult, reputationResult, leaderboardResult] = await Promise.all([
        API.getOrderStatistics(currentTimeRange, 'overview'),
        API.getOrderStatistics(currentTimeRange, 'income'),
        API.getOrderStatistics(currentTimeRange, 'service'),
        API.getOrderStatistics(currentTimeRange, 'trend'),
        API.getUserReputation(),
        API.getLeaderboard('score', 10)
      ]);

      // 更新数据
      if (overviewResult.success) {
        this.setData({
          overviewData: overviewResult.data.overview || this.data.overviewData
        });
      }

      if (incomeResult.success) {
        this.setData({
          incomeData: incomeResult.data.income || this.data.incomeData
        });
      }

      if (serviceResult.success) {
        this.setData({
          serviceData: serviceResult.data.service || this.data.serviceData
        });
      }

      if (trendResult.success) {
        this.setData({
          trendData: trendResult.data.trend || this.data.trendData
        });
      }

      if (reputationResult.success) {
        this.setData({
          reputationData: reputationResult.data || this.data.reputationData
        });
      }

      if (leaderboardResult.success) {
        const leaderboard = leaderboardResult.data || [];
        const currentUser = app.globalData.userInfo;
        const myRank = leaderboard.findIndex(user => user.userId === currentUser?.openid) + 1;

        this.setData({
          leaderboardData: {
            myRank: myRank || 0,
            topUsers: leaderboard.slice(0, 5) // 只显示前5名
          }
        });
      }

    } catch (error) {
      errorHandler.handle(error, '统计数据加载');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 查看详细收入统计
  viewIncomeDetails() {
    wx.navigateTo({
      url: '/pages/statistics/income/income'
    });
  },

  // 查看详细服务统计
  viewServiceDetails() {
    wx.navigateTo({
      url: '/pages/statistics/service/service'
    });
  },

  // 查看趋势分析
  viewTrendAnalysis() {
    wx.navigateTo({
      url: '/pages/statistics/trend/trend'
    });
  },

  // 导出数据
  exportData() {
    wx.showModal({
      title: '导出数据',
      content: '是否要导出当前统计数据？',
      success: (res) => {
        if (res.confirm) {
          this.performExport();
        }
      }
    });
  },

  // 执行导出
  async performExport() {
    try {
      wx.showLoading({ title: '导出中...' });

      // 构建导出数据
      const exportData = {
        timeRange: this.data.currentTimeRange,
        exportTime: new Date().toLocaleString('zh-CN'),
        overview: this.data.overviewData,
        income: this.data.incomeData,
        service: this.data.serviceData,
        trend: this.data.trendData
      };

      // 转换为JSON字符串
      const jsonString = JSON.stringify(exportData, null, 2);

      // 保存到本地存储（实际项目中可以调用云函数保存到云存储）
      wx.setStorageSync('exported_statistics', exportData);

      wx.hideLoading();
      app.utils.showSuccess('数据已导出到本地');

    } catch (error) {
      wx.hideLoading();
      console.error('导出数据失败:', error);
      app.utils.showError('导出失败');
    }
  },

  // 格式化金额
  formatAmount(amount) {
    return (amount || 0).toFixed(2);
  },

  // 格式化百分比
  formatPercentage(value) {
    return `${(value || 0)}%`;
  },

  // 格式化时长
  formatDuration(hours) {
    if (hours < 1) {
      return `${Math.round(hours * 60)}分钟`;
    }
    return `${hours.toFixed(1)}小时`;
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colors = {
      pending: '#ff6b35',
      in_progress: '#00d4ff',
      completed: '#2ed573',
      cancelled: '#ff4757'
    };
    return colors[status] || '#999999';
  },

  // 简化的图表数据处理（不使用复杂图表库）
  getSimpleTrendData() {
    const { dailyTrend } = this.data.trendData;

    if (!dailyTrend || dailyTrend.length === 0) {
      return {
        maxOrders: 0,
        maxIncome: 0,
        chartData: []
      };
    }

    const maxOrders = Math.max(...dailyTrend.map(item => item.orders));
    const maxIncome = Math.max(...dailyTrend.map(item => item.income));

    const chartData = dailyTrend.map(item => ({
      ...item,
      orderHeight: maxOrders > 0 ? (item.orders / maxOrders) * 100 : 0,
      incomeHeight: maxIncome > 0 ? (item.income / maxIncome) * 100 : 0
    }));

    return {
      maxOrders,
      maxIncome,
      chartData
    };
  }
});
