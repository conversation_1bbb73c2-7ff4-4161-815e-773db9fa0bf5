<!--统计概览页面-->
<navigation-bar title="数据统计" back="{{true}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
  </view>
</view>

<scroll-view class="statistics-container page-with-custom-nav" scroll-y refresher-enabled="{{true}}" bindrefresherrefresh="onPullDownRefresh">
  <!-- 时间范围选择 -->
  <view class="time-range-section cyber-card">
    <view class="card-glow"></view>
    <view class="time-range-content">
      <text class="section-title">📊 统计周期</text>
      <picker 
        class="time-range-picker"
        mode="selector"
        range="{{timeRanges}}"
        range-key="label"
        value="{{currentTimeRangeIndex}}"
        bindchange="onTimeRangeChange"
      >
        <view class="picker-display">
          <text class="picker-text">{{timeRanges[currentTimeRangeIndex].label}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 概览统计卡片 -->
  <view class="overview-section">
    <view class="stats-grid">
      <!-- 订单总数 -->
      <view class="stat-card cyber-card">
        <view class="card-glow"></view>
        <view class="stat-content">
          <view class="stat-icon">📋</view>
          <view class="stat-info">
            <text class="stat-value">{{overviewData.totalOrders}}</text>
            <text class="stat-label">总订单</text>
          </view>
        </view>
      </view>

      <!-- 完成订单 -->
      <view class="stat-card cyber-card">
        <view class="card-glow"></view>
        <view class="stat-content">
          <view class="stat-icon">✅</view>
          <view class="stat-info">
            <text class="stat-value">{{overviewData.completedOrders}}</text>
            <text class="stat-label">已完成</text>
          </view>
        </view>
      </view>

      <!-- 总收入 -->
      <view class="stat-card cyber-card">
        <view class="card-glow"></view>
        <view class="stat-content">
          <view class="stat-icon">💰</view>
          <view class="stat-info">
            <text class="stat-value">¥{{formatAmount(overviewData.totalIncome)}}</text>
            <text class="stat-label">总收入</text>
          </view>
        </view>
      </view>

      <!-- 服务时长 -->
      <view class="stat-card cyber-card">
        <view class="card-glow"></view>
        <view class="stat-content">
          <view class="stat-icon">⏱️</view>
          <view class="stat-info">
            <text class="stat-value">{{formatDuration(overviewData.serviceHours)}}</text>
            <text class="stat-label">服务时长</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单状态分布 -->
  <view class="status-section cyber-card">
    <view class="card-glow"></view>
    <view class="section-content">
      <text class="section-title">📈 订单状态分布</text>
      <view class="status-chart">
        <view class="status-item">
          <view class="status-bar">
            <view class="status-fill pending" style="width: {{overviewData.totalOrders > 0 ? (overviewData.pendingOrders / overviewData.totalOrders * 100) : 0}}%"></view>
          </view>
          <view class="status-info">
            <text class="status-label">待接单</text>
            <text class="status-count">{{overviewData.pendingOrders}}</text>
          </view>
        </view>

        <view class="status-item">
          <view class="status-bar">
            <view class="status-fill in-progress" style="width: {{overviewData.totalOrders > 0 ? (overviewData.inProgressOrders / overviewData.totalOrders * 100) : 0}}%"></view>
          </view>
          <view class="status-info">
            <text class="status-label">进行中</text>
            <text class="status-count">{{overviewData.inProgressOrders}}</text>
          </view>
        </view>

        <view class="status-item">
          <view class="status-bar">
            <view class="status-fill completed" style="width: {{overviewData.totalOrders > 0 ? (overviewData.completedOrders / overviewData.totalOrders * 100) : 0}}%"></view>
          </view>
          <view class="status-info">
            <text class="status-label">已完成</text>
            <text class="status-count">{{overviewData.completedOrders}}</text>
          </view>
        </view>

        <view class="status-item">
          <view class="status-bar">
            <view class="status-fill cancelled" style="width: {{overviewData.totalOrders > 0 ? (overviewData.cancelledOrders / overviewData.totalOrders * 100) : 0}}%"></view>
          </view>
          <view class="status-info">
            <text class="status-label">已取消</text>
            <text class="status-count">{{overviewData.cancelledOrders}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 收入统计 -->
  <view class="income-section cyber-card" bindtap="viewIncomeDetails">
    <view class="card-glow"></view>
    <view class="section-content">
      <view class="section-header">
        <text class="section-title">💵 收入统计</text>
        <text class="view-more">查看详情 →</text>
      </view>
      <view class="income-summary">
        <view class="income-item">
          <text class="income-label">总收入</text>
          <text class="income-value">¥{{formatAmount(incomeData.total)}}</text>
        </view>
        <view class="income-item">
          <text class="income-label">订单数量</text>
          <text class="income-value">{{incomeData.orderCount}}</text>
        </view>
        <view class="income-item">
          <text class="income-label">平均单价</text>
          <text class="income-value">¥{{formatAmount(incomeData.averagePerOrder)}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 服务统计 -->
  <view class="service-section cyber-card" bindtap="viewServiceDetails">
    <view class="card-glow"></view>
    <view class="section-content">
      <view class="section-header">
        <text class="section-title">🎮 服务统计</text>
        <text class="view-more">查看详情 →</text>
      </view>
      <view class="service-summary">
        <view class="service-item">
          <text class="service-label">服务订单</text>
          <text class="service-value">{{serviceData.totalOrders}}</text>
        </view>
        <view class="service-item">
          <text class="service-label">服务时长</text>
          <text class="service-value">{{formatDuration(serviceData.totalHours)}}</text>
        </view>
        <view class="service-item">
          <text class="service-label">平均时长</text>
          <text class="service-value">{{formatDuration(serviceData.averageHoursPerOrder)}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 简化的趋势图表 -->
  <view class="trend-section cyber-card" bindtap="viewTrendAnalysis">
    <view class="card-glow"></view>
    <view class="section-content">
      <view class="section-header">
        <text class="section-title">📊 趋势分析</text>
        <text class="view-more">查看详情 →</text>
      </view>
      <view class="simple-chart">
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color orders"></view>
            <text class="legend-text">订单数量</text>
          </view>
          <view class="legend-item">
            <view class="legend-color income"></view>
            <text class="legend-text">收入金额</text>
          </view>
        </view>
        <view class="chart-container">
          <view class="chart-bars">
            <view 
              class="chart-day"
              wx:for="{{getSimpleTrendData().chartData}}"
              wx:key="date"
              wx:if="{{index % 3 === 0}}"
            >
              <view class="day-bars">
                <view class="bar orders" style="height: {{item.orderHeight}}%"></view>
                <view class="bar income" style="height: {{item.incomeHeight}}%"></view>
              </view>
              <text class="day-label">{{item.label}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-btn cyber-btn" bindtap="exportData">
      <text class="btn-text">📤 导出数据</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</scroll-view>
