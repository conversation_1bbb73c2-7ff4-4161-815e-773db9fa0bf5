// 创建提现申请云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { amount, withdrawMethod = 'wechat', bankInfo = {} } = event;

  try {
    // 验证必填字段
    if (!amount || amount <= 0) {
      return {
        success: false,
        error: '提现金额必须大于0'
      };
    }

    // 验证提现金额范围
    const MIN_WITHDRAW = 10; // 最小提现金额
    const MAX_WITHDRAW = 50000; // 最大提现金额
    
    if (amount < MIN_WITHDRAW) {
      return {
        success: false,
        error: `提现金额不能少于${MIN_WITHDRAW}元`
      };
    }

    if (amount > MAX_WITHDRAW) {
      return {
        success: false,
        error: `提现金额不能超过${MAX_WITHDRAW}元`
      };
    }

    // 查找用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 检查用户余额
    if (user.balance < amount) {
      return {
        success: false,
        error: '余额不足'
      };
    }

    // 检查是否有待处理的提现申请
    const pendingWithdrawResult = await db.collection('withdraws').where({
      userId: user._id,
      status: db.command.in(['pending', 'processing'])
    }).get();

    if (pendingWithdrawResult.data.length > 0) {
      return {
        success: false,
        error: '您有待处理的提现申请，请等待处理完成后再申请'
      };
    }

    // 生成提现申请号
    const withdrawNo = generateWithdrawNo();

    // 创建提现申请
    const withdrawData = {
      withdrawNo,
      userId: user._id,
      amount: parseFloat(amount),
      withdrawMethod,
      bankInfo: withdrawMethod === 'bank' ? bankInfo : {},
      status: 'pending', // pending, processing, completed, rejected
      createTime: new Date(),
      updateTime: new Date(),
      processTime: null,
      completeTime: null,
      rejectReason: '',
      adminId: null
    };

    const withdrawResult = await db.collection('withdraws').add({
      data: withdrawData
    });

    // 冻结用户余额
    await db.collection('users').doc(user._id).update({
      data: {
        balance: db.command.inc(-amount),
        frozenBalance: db.command.inc(amount),
        updateTime: new Date()
      }
    });

    // 创建交易记录
    await db.collection('transactions').add({
      data: {
        userId: user._id,
        withdrawId: withdrawResult._id,
        type: 'withdraw',
        amount: -parseFloat(amount),
        status: 'pending',
        description: `提现申请 - ${withdrawNo}`,
        createTime: new Date()
      }
    });

    return {
      success: true,
      data: {
        withdrawId: withdrawResult._id,
        withdrawNo,
        amount: parseFloat(amount),
        estimatedArrival: '1-3个工作日'
      }
    };
  } catch (error) {
    console.error('创建提现申请失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 生成提现申请号
function generateWithdrawNo() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `WD${year}${month}${day}${hour}${minute}${second}${random}`;
}
