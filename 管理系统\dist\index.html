<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>三角洲任务平台 - 后台管理系统</title>
    <style>
      /* 防止白屏闪烁 */
      body {
        background-color: #0f1419;
        margin: 0;
        padding: 0;
      }
      
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(0, 212, 255, 0.3);
        border-top: 3px solid #00d4ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <script type="module" crossorigin src="/assets/index-_vY29Tf4.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-BTWMFwqw.js">
    <link rel="modulepreload" crossorigin href="/assets/antd-C4mrS3pD.js">
    <link rel="stylesheet" crossorigin href="/assets/index-BGgnewNh.css">
  </head>
  <body>
    <div id="root">
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    </div>
  </body>
</html>
