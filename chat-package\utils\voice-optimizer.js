/**
 * 语音性能优化工具
 * 优化语音录制、上传、发送的整体性能
 */

class VoiceOptimizer {
  constructor() {
    this.uploadQueue = []; // 上传队列
    this.isUploading = false; // 是否正在上传
    this.compressionConfig = {
      quality: 'high', // 音质：high, medium, low
      format: 'mp3',   // 格式：mp3, aac
      bitRate: 64000   // 比特率：64kbps 平衡质量和大小
    };
  }

  /**
   * 优化语音录制配置
   */
  getOptimizedRecordConfig() {
    return {
      duration: 60000,        // 最大录制时长60秒
      sampleRate: 16000,      // 采样率16kHz（降低文件大小）
      numberOfChannels: 1,    // 单声道（减少文件大小）
      encodeBitRate: 48000,   // 编码比特率48kbps（平衡质量和大小）
      format: 'mp3',          // 使用MP3格式（更好的压缩率）
      frameSize: 5            // 帧大小5（提高录制响应速度）
    };
  }

  /**
   * 预处理语音文件（压缩优化）
   */
  async preprocessVoiceFile(tempFilePath) {
    try {
      console.log('🎤 [性能优化] 开始预处理语音文件');
      const startTime = Date.now();

      // 获取文件信息
      const fileInfo = await this.getFileInfo(tempFilePath);
      console.log('🎤 [性能优化] 原始文件大小:', fileInfo.size, 'bytes');

      // 如果文件过大，进行压缩
      if (fileInfo.size > 500 * 1024) { // 大于500KB
        console.log('🎤 [性能优化] 文件较大，开始压缩');
        // 这里可以添加音频压缩逻辑
        // 由于小程序限制，我们主要通过录制参数优化
      }

      const processTime = Date.now() - startTime;
      console.log('🎤 [性能优化] 预处理完成，耗时:', processTime, 'ms');

      return {
        filePath: tempFilePath,
        size: fileInfo.size,
        processTime
      };
    } catch (error) {
      console.error('🎤 [性能优化] 预处理失败:', error);
      return {
        filePath: tempFilePath,
        size: 0,
        processTime: 0
      };
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath,
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 优化的语音上传方法
   */
  async optimizedUpload(filePath, progressCallback) {
    return new Promise((resolve, reject) => {
      console.log('🎤 [性能优化] 开始优化上传');
      const startTime = Date.now();

      // 生成优化的文件名（包含时间戳和随机数）
      const timestamp = Date.now();
      const random = Math.random().toString(36).substr(2, 9);
      const fileName = `voice_${timestamp}_${random}.mp3`;

      wx.cloud.uploadFile({
        cloudPath: `voice/${fileName}`,
        filePath: filePath,
        config: {
          timeout: 30000, // 30秒超时
        },
        success: (result) => {
          const uploadTime = Date.now() - startTime;
          console.log('🎤 [性能优化] 上传成功，耗时:', uploadTime, 'ms');
          console.log('🎤 [性能优化] 文件ID:', result.fileID);
          
          resolve({
            fileID: result.fileID,
            uploadTime,
            fileName
          });
        },
        fail: (error) => {
          const uploadTime = Date.now() - startTime;
          console.error('🎤 [性能优化] 上传失败，耗时:', uploadTime, 'ms', error);
          reject(error);
        }
      });
    });
  }

  /**
   * 批量上传优化（如果有多个文件）
   */
  async batchUpload(files) {
    const results = [];
    for (const file of files) {
      try {
        const result = await this.optimizedUpload(file.path);
        results.push({ ...result, originalFile: file });
      } catch (error) {
        console.error('🎤 [性能优化] 批量上传失败:', error);
        results.push({ error, originalFile: file });
      }
    }
    return results;
  }

  /**
   * 提取语音时长（优化版本）
   */
  extractDurationFromFileName(fileName) {
    try {
      // 从文件名中提取时长信息
      const match = fileName.match(/voice_(\d+)_/);
      if (match) {
        const timestamp = parseInt(match[1]);
        // 这里可以根据实际需求调整时长计算逻辑
        return Math.floor(Math.random() * 10) + 1; // 临时方案
      }
      return 1; // 默认1秒
    } catch (error) {
      console.error('🎤 [性能优化] 提取时长失败:', error);
      return 1;
    }
  }

  /**
   * 性能监控
   */
  startPerformanceMonitor() {
    this.performanceData = {
      recordStart: 0,
      recordEnd: 0,
      uploadStart: 0,
      uploadEnd: 0,
      sendStart: 0,
      sendEnd: 0
    };
  }

  /**
   * 记录性能数据
   */
  recordPerformance(stage, action) {
    const key = `${stage}${action}`;
    this.performanceData[key] = Date.now();
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const data = this.performanceData;
    return {
      recordTime: data.recordEnd - data.recordStart,
      uploadTime: data.uploadEnd - data.uploadStart,
      sendTime: data.sendEnd - data.sendStart,
      totalTime: data.sendEnd - data.recordStart
    };
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(filePaths) {
    for (const filePath of filePaths) {
      try {
        await wx.removeSavedFile({ filePath });
        console.log('🎤 [性能优化] 清理临时文件:', filePath);
      } catch (error) {
        console.warn('🎤 [性能优化] 清理文件失败:', error);
      }
    }
  }

  /**
   * 网络状态检测
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          const networkType = res.networkType;
          const isWifi = networkType === 'wifi';
          const isFast = ['wifi', '4g', '5g'].includes(networkType);
          
          resolve({
            type: networkType,
            isWifi,
            isFast,
            uploadStrategy: isFast ? 'normal' : 'compressed'
          });
        },
        fail: () => {
          resolve({
            type: 'unknown',
            isWifi: false,
            isFast: false,
            uploadStrategy: 'compressed'
          });
        }
      });
    });
  }

  /**
   * 根据网络状况调整上传策略
   */
  async getUploadStrategy() {
    const network = await this.checkNetworkStatus();
    
    if (network.isWifi) {
      return {
        timeout: 30000,
        retryCount: 3,
        chunkSize: 1024 * 1024, // 1MB chunks for WiFi
        quality: 'high'
      };
    } else if (network.isFast) {
      return {
        timeout: 20000,
        retryCount: 2,
        chunkSize: 512 * 1024, // 512KB chunks for 4G/5G
        quality: 'medium'
      };
    } else {
      return {
        timeout: 15000,
        retryCount: 1,
        chunkSize: 256 * 1024, // 256KB chunks for slow networks
        quality: 'low'
      };
    }
  }
}

// 创建全局实例
const voiceOptimizer = new VoiceOptimizer();

module.exports = voiceOptimizer;