/* 已完成订单页面样式 */
.completed-container {
  height: 100vh;
  background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  display: flex;
  flex-direction: column;
}

/* 为页面设置顶部间距 */
.completed-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 20rpx);
  box-sizing: border-box;
}

/* 订单列表 */
.order-list {
  flex: 1;
  height: 0; /* 确保 flex 子元素正确计算高度 */
  padding: var(--space-md) var(--space-md) var(--space-md) var(--space-md);
  /* 确保滚动行为正确 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

/* 加载状态 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-xl);
  color: var(--text-secondary);
  font-size: 24rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-xl);
  color: var(--text-tertiary);
  font-size: 24rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx var(--space-xl);
  text-align: center;
  min-height: 60vh;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-xl);
  opacity: 0.6;
  filter: drop-shadow(0 0 20rpx rgba(0, 212, 255, 0.3));
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
  letter-spacing: 0.5rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: var(--space-2xl);
  line-height: 1.5;
}

.create-order-btn {
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-lg) var(--space-2xl);
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.create-order-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.create-order-btn:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-md);
}

.create-order-btn:active::before {
  left: 100%;
}
