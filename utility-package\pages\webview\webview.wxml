<!--WebView页面-->
<navigation-bar title="{{pageTitle}}" back="{{true}}"></navigation-bar>

<!-- 返回按钮 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<view class="webview-container page-with-custom-nav">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- WebView -->
  <web-view
    src="{{url}}"
    bindload="onWebViewLoad"
    binderror="onWebViewError"
    wx:if="{{url}}"
  ></web-view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{error}}">
    <view class="error-icon">⚠️</view>
    <text class="error-title">页面加载失败</text>
    <text class="error-desc">{{errorMessage}}</text>
    <button class="retry-btn" bindtap="retryLoad">重新加载</button>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!url && !loading && !error}}">
    <view class="empty-icon">📄</view>
    <text class="empty-title">暂无内容</text>
    <text class="empty-desc">页面地址为空</text>
  </view>
</view>