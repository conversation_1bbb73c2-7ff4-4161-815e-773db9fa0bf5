import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Star } from 'lucide-react';

export default function EvaluationsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">评价管理</h1>
        <p className="text-gray-600">管理用户评价和信誉系统</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Star className="mr-2 h-5 w-5" />
            评价管理功能
          </CardTitle>
          <CardDescription>
            评价管理模块正在开发中...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-gray-500">
            <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>评价管理功能即将上线</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}