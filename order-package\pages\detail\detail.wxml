<!--订单详情页面 - 科技感优化版-->
<navigation-bar title="订单详情" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 返回按钮 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<!-- 遇到问题按钮 -->
<view class="problem-button-container" wx:if="{{showProblemButton}}">
  <view class="problem-button cyber-problem-btn" bindtap="handleProblem">
    <image class="problem-btn-icon" src="{{problemIcon}}" mode="aspectFit" wx:if="{{problemIcon}}"></image>
    <view class="problem-btn-glow"></view>
  </view>
</view>

<!-- 科技感背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3]}}" wx:key="*this"></view>
  </view>
</view>

<scroll-view class="scrollarea page-with-custom-nav" scroll-y type="list" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onPullDownRefresh">
  <view class="container" wx:if="{{!loading}}">

    <!-- 订单状态卡片 - 科技感设计 -->
    <view class="status-card cyber-card">
      <view class="card-glow"></view>
      <view class="status-header">
        <view class="status-icon-container">
          <view class="status-icon {{orderInfo.status || 'pending'}}">
            <text class="icon-text">{{orderInfo.statusIcon || '⏳'}}</text>
          </view>
          <view class="status-ring"></view>
        </view>
        <view class="status-info">
          <text class="status-text">{{orderInfo.statusText || '未知状态'}}</text>
          <text class="order-no" bindtap="copyOrderNo" wx:if="{{orderInfo.orderNo}}">
            <text class="order-label">订单号</text>
            <text class="order-value">{{orderInfo.orderNo}}</text>
          </text>
        </view>
      </view>

      <!-- 科技感状态流程 -->
      <view class="status-flow">
        <view class="flow-line"></view>
        <view class="flow-item {{index <= (orderInfo.currentStatusIndex || 0) ? 'active' : ''}}" wx:for="{{statusFlow}}" wx:key="key">
          <view class="flow-dot"></view>
          <view class="flow-icon">{{item.icon}}</view>
          <text class="flow-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 订单信息 - 科技感设计 -->
    <view class="info-card cyber-card">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">📋</text>
        <text class="title-text">订单信息</text>
      </view>
      <view class="info-list">
        <view class="info-item cyber-item" wx:if="{{orderInfo.title}}">
          <view class="info-label">
            <text class="label-icon">🎯</text>
            <text class="label-text">订单标题</text>
          </view>
          <text class="info-value">{{orderInfo.title}}</text>
        </view>
        <view class="info-item cyber-item info-item-vertical" wx:if="{{orderInfo.content}}">
          <view class="info-label">
            <text class="label-icon">📝</text>
            <text class="label-text">任务需求</text>
          </view>
          <view class="info-value-container">
            <text class="info-value info-value-content">{{orderInfo.content}}</text>
          </view>
        </view>
        <view class="info-item cyber-item">
          <view class="info-label">
            <text class="label-icon">⏱️</text>
            <text class="label-text" wx:if="{{orderInfo.serviceType === 'rounds' || (orderInfo.rounds && !orderInfo.serviceType)}}">服务局数</text>
            <text class="label-text" wx:else>服务时长</text>
          </view>
          <text class="info-value duration" wx:if="{{orderInfo.serviceType === 'rounds' || (orderInfo.rounds && !orderInfo.serviceType)}}">{{orderInfo.rounds}}局</text>
          <text class="info-value duration" wx:else>{{orderInfo.duration}}小时</text>
        </view>
        <view class="info-item cyber-item">
          <view class="info-label">
            <text class="label-icon">💰</text>
            <text class="label-text">悬赏金额</text>
          </view>
          <text class="info-value price">¥{{orderInfo.reward}}</text>
        </view>
        <view class="info-item cyber-item" wx:if="{{orderInfo.tags && orderInfo.tags.length > 0}}">
          <view class="info-label">
            <text class="label-icon">🏷️</text>
            <text class="label-text">订单标签</text>
          </view>
          <view class="tags-container">
            <view class="tag cyber-tag" wx:for="{{orderInfo.tags}}" wx:key="*this">
              <text class="tag-text">{{item}}</text>
              <view class="tag-glow"></view>
            </view>
          </view>
        </view>
        <view class="info-item cyber-item">
          <view class="info-label">
            <text class="label-icon">📅</text>
            <text class="label-text">下单时间</text>
          </view>
          <text class="info-value time-value">{{orderInfo.createTimeText}}</text>
        </view>
      </view>
    </view>

    <!-- 取消信息 - 科技感设计 -->
    <view class="info-card cyber-card" wx:if="{{orderInfo.status === 'cancelled'}}">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">❌</text>
        <text class="title-text">取消信息</text>
      </view>
      <view class="info-list">
        <view class="info-item cyber-item" wx:if="{{orderInfo.cancelReason}}">
          <view class="info-label">
            <text class="label-icon">📝</text>
            <text class="label-text">取消原因</text>
          </view>
          <text class="info-value">{{orderInfo.cancelReason}}</text>
        </view>
        <view class="info-item cyber-item" wx:if="{{orderInfo.cancelledAtText}}">
          <view class="info-label">
            <text class="label-icon">⏰</text>
            <text class="label-text">取消时间</text>
          </view>
          <text class="info-value time-value">{{orderInfo.cancelledAtText}}</text>
        </view>
      </view>
    </view>



    <!-- 发布者信息 - 科技感设计 -->
    <view class="info-card cyber-card" wx:if="{{orderInfo.customerInfo}}">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">👤</text>
        <text class="title-text">发布者信息</text>
      </view>
      <view class="user-info">
        <view class="user-header">
          <image class="user-avatar" src="{{orderInfo.customerInfo.avatarUrl}}" mode="aspectFill" />
          <view class="user-details">
            <text class="user-name">{{orderInfo.customerInfo.nickName}}</text>
            <view class="user-stats">
              <text class="user-role">订单发布者</text>
            </view>
          </view>
        </view>

        <!-- 游戏昵称显示区域 -->
        <view class="game-nickname-section" wx:if="{{orderInfo.customerInfo.gameNickName}}">
          <view class="game-nickname-item">
            <text class="game-nickname-label">游戏昵称：</text>
            <view class="game-nickname-content" wx:if="{{gameNicknameVisible}}">
              <text class="game-nickname-text" bindtap="copyGameNickname">{{orderInfo.customerInfo.gameNickName}}</text>
              <text class="copy-icon" bindtap="copyGameNickname">📋</text>
            </view>
            <view class="game-nickname-hidden" wx:else>
              <text class="hidden-text">接单后可见</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 接单者信息 - 科技感设计 -->
    <view class="info-card cyber-card" wx:if="{{orderInfo.acceptorInfo}}">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">🎮</text>
        <text class="title-text">接单者信息</text>
      </view>
      <view class="user-info">
        <view class="user-header">
          <image class="user-avatar" src="{{orderInfo.acceptorInfo.avatarUrl}}" mode="aspectFill" />
          <view class="user-details">
            <text class="user-name">{{orderInfo.acceptorInfo.nickName}}</text>
            <view class="user-stats">
              <text class="user-level">{{orderInfo.acceptorInfo.levelText}}</text>
              <text class="user-rating">⭐ {{orderInfo.acceptorInfo.rating}}</text>
              <text class="user-orders">{{orderInfo.acceptorInfo.completedOrders}}单</text>
            </view>
          </view>
        </view>

        <view class="user-intro" wx:if="{{orderInfo.acceptorInfo.introduction}}">
          <text class="intro-label">个人简介：</text>
          <text class="intro-text">{{orderInfo.acceptorInfo.introduction}}</text>
        </view>
      </view>
    </view>

    <!-- 订单评价 -->
    <view class="info-card" wx:if="{{orderInfo.status === 'completed' && orderInfo.evaluation && orderInfo.evaluation.customerRating}}">
      <view class="card-title">订单评价</view>
      <view class="evaluation-content">
        <view class="evaluation-rating">
          <text class="rating-label">评分：</text>
          <view class="rating-stars">
            <text class="star {{index < orderInfo.evaluation.customerRating ? 'active' : ''}}" wx:for="{{[1,2,3,4,5]}}" wx:key="*this">⭐</text>
          </view>
        </view>
        <view class="evaluation-comment" wx:if="{{orderInfo.evaluation.customerContent}}">
          <text class="comment-label">评价：</text>
          <text class="comment-text">{{orderInfo.evaluation.customerContent}}</text>
        </view>
        <view class="evaluation-tags" wx:if="{{orderInfo.evaluation.customerTags && orderInfo.evaluation.customerTags.length > 0}}">
          <text class="tags-label">标签：</text>
          <view class="tags-list">
            <text class="tag" wx:for="{{orderInfo.evaluation.customerTags}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
        <text class="evaluation-time">{{orderInfo.evaluation.createTimeText}}</text>
      </view>
    </view>

    <!-- 操作按钮 - 科技感设计 -->
    <view class="action-buttons" wx:if="{{actionButtons.length > 0}}">
      <button
        class="action-btn cyber-btn {{item.type}}"
        wx:for="{{actionButtons}}"
        wx:key="type"
        bindtap="handleAction"
        data-type="{{item.type}}"
        disabled="{{item.disabled}}"
      >
        <text class="btn-text">{{item.text}}</text>
        <view class="btn-glow"></view>
        <view class="btn-particles">
          <view class="btn-particle"></view>
        </view>
      </button>
    </view>

  </view>

  <!-- 科技感加载状态 -->
  <view class="loading-container cyber-loading" wx:if="{{loading}}">
    <view class="loading-ring">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
    <text class="loading-text">数据传输中...</text>
    <view class="loading-progress">
      <view class="progress-bar"></view>
    </view>
  </view>
</scroll-view>