import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// 权限定义
export type Permission = 
  | 'users.view' | 'users.edit' | 'users.delete' | 'users.export'
  | 'orders.view' | 'orders.edit' | 'orders.delete' | 'orders.export'
  | 'wallet.view' | 'wallet.edit' | 'wallet.export'
  | 'notifications.view' | 'notifications.send' | 'notifications.delete'
  | 'evaluations.view' | 'evaluations.delete'
  | 'chat.view' | 'chat.monitor'
  | 'dashboard.view'
  | 'system.settings' | 'system.logs';

// 角色定义
export type Role = 'super_admin' | 'admin' | 'operator' | 'viewer';

// 用户信息
export interface AuthUser {
  id: string;
  username: string;
  email: string;
  role: Role;
  permissions: Permission[];
  avatar?: string;
  lastLoginAt?: Date;
}

// 权限上下文
interface AuthContextType {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (permission: Permission) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasRole: (role: Role) => boolean;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 角色权限映射
const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  super_admin: [
    'users.view', 'users.edit', 'users.delete', 'users.export',
    'orders.view', 'orders.edit', 'orders.delete', 'orders.export',
    'wallet.view', 'wallet.edit', 'wallet.export',
    'notifications.view', 'notifications.send', 'notifications.delete',
    'evaluations.view', 'evaluations.delete',
    'chat.view', 'chat.monitor',
    'dashboard.view',
    'system.settings', 'system.logs'
  ],
  admin: [
    'users.view', 'users.edit', 'users.export',
    'orders.view', 'orders.edit', 'orders.export',
    'wallet.view', 'wallet.edit', 'wallet.export',
    'notifications.view', 'notifications.send',
    'evaluations.view',
    'chat.view', 'chat.monitor',
    'dashboard.view',
    'system.logs'
  ],
  operator: [
    'users.view', 'users.edit',
    'orders.view', 'orders.edit',
    'wallet.view',
    'notifications.view', 'notifications.send',
    'evaluations.view',
    'chat.view',
    'dashboard.view'
  ],
  viewer: [
    'users.view',
    'orders.view',
    'wallet.view',
    'notifications.view',
    'evaluations.view',
    'chat.view',
    'dashboard.view'
  ]
};

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化认证状态
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      if (token) {
        // 验证token并获取用户信息
        await refreshUser();
      }
    } catch (error) {
      console.error('初始化认证失败:', error);
      localStorage.removeItem('admin_token');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string) => {
    try {
      // 这里应该调用真实的登录API
      // const response = await authApi.login(username, password);
      
      // 模拟登录逻辑
      if (username === 'admin' && password === 'admin123') {
        const mockUser: AuthUser = {
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          role: 'super_admin',
          permissions: ROLE_PERMISSIONS.super_admin,
          avatar: '',
          lastLoginAt: new Date()
        };
        
        setUser(mockUser);
        localStorage.setItem('admin_token', 'mock_token_' + Date.now());
        
        // 记录登录日志
        logOperation('user.login', '用户登录', { username });
        
      } else if (username === 'operator' && password === 'op123') {
        const mockUser: AuthUser = {
          id: '2',
          username: 'operator',
          email: '<EMAIL>',
          role: 'operator',
          permissions: ROLE_PERMISSIONS.operator,
          avatar: '',
          lastLoginAt: new Date()
        };
        
        setUser(mockUser);
        localStorage.setItem('admin_token', 'mock_token_' + Date.now());
        
        // 记录登录日志
        logOperation('user.login', '用户登录', { username });
        
      } else {
        throw new Error('用户名或密码错误');
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  };

  const logout = () => {
    if (user) {
      // 记录登出日志
      logOperation('user.logout', '用户登出', { username: user.username });
    }
    
    setUser(null);
    localStorage.removeItem('admin_token');
  };

  const refreshUser = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error('No token found');
      }

      // 这里应该调用API验证token并获取用户信息
      // const response = await authApi.getCurrentUser();
      
      // 模拟用户信息获取
      const mockUser: AuthUser = {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'super_admin',
        permissions: ROLE_PERMISSIONS.super_admin,
        avatar: '',
        lastLoginAt: new Date()
      };
      
      setUser(mockUser);
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      logout();
      throw error;
    }
  };

  const hasPermission = (permission: Permission): boolean => {
    if (!user) return false;
    return user.permissions.includes(permission);
  };

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    if (!user) return false;
    return permissions.some(permission => user.permissions.includes(permission));
  };

  const hasRole = (role: Role): boolean => {
    if (!user) return false;
    return user.role === role;
  };

  // 操作日志记录
  const logOperation = (action: string, description: string, details?: any) => {
    const logEntry = {
      id: Date.now().toString(),
      userId: user?.id || 'anonymous',
      username: user?.username || 'anonymous',
      action,
      description,
      details,
      timestamp: new Date(),
      ip: 'unknown', // 在实际应用中应该获取真实IP
      userAgent: navigator.userAgent
    };

    // 存储到本地存储（实际应用中应该发送到服务器）
    const logs = JSON.parse(localStorage.getItem('operation_logs') || '[]');
    logs.unshift(logEntry);
    
    // 只保留最近1000条日志
    if (logs.length > 1000) {
      logs.splice(1000);
    }
    
    localStorage.setItem('operation_logs', JSON.stringify(logs));
    console.log('操作日志:', logEntry);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    hasPermission,
    hasAnyPermission,
    hasRole,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// 权限检查Hook
export function usePermission(permission: Permission) {
  const { hasPermission } = useAuth();
  return hasPermission(permission);
}

// 多权限检查Hook
export function useAnyPermission(permissions: Permission[]) {
  const { hasAnyPermission } = useAuth();
  return hasAnyPermission(permissions);
}

// 角色检查Hook
export function useRole(role: Role) {
  const { hasRole } = useAuth();
  return hasRole(role);
}

// 权限组件
interface PermissionGuardProps {
  permission?: Permission;
  permissions?: Permission[];
  role?: Role;
  fallback?: ReactNode;
  children: ReactNode;
}

export function PermissionGuard({ 
  permission, 
  permissions, 
  role, 
  fallback = null, 
  children 
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasRole } = useAuth();

  let hasAccess = true;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    hasAccess = hasAnyPermission(permissions);
  } else if (role) {
    hasAccess = hasRole(role);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}
