/* 订单详情页面样式 - 科技感优化版 */

/* 科技感背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  z-index: -1;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.05) 1px, transparent 1px);
  background-size: 50rpx 50rpx;
  animation: gridMove 30s linear infinite;
}

.tech-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 3rpx;
  height: 3rpx;
  background: rgba(0, 212, 255, 0.4);
  border-radius: 50%;
  animation: float 12s ease-in-out infinite;
}

.particle:nth-child(1) { left: 30%; animation-delay: 0s; }
.particle:nth-child(2) { left: 60%; animation-delay: 4s; }
.particle:nth-child(3) { left: 90%; animation-delay: 8s; }

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50rpx, 50rpx); }
}

@keyframes float {
  0% { transform: translateY(100vh) scale(0); opacity: 0; }
  20% { opacity: 0.4; transform: translateY(80vh) scale(1); }
  80% { opacity: 0.4; transform: translateY(-80rpx) scale(1); }
  100% { transform: translateY(-200rpx) scale(0); opacity: 0; }
}

.container {
  padding: 20rpx;
  padding-top: 10rpx; /* 减少顶部间距 */
  min-height: 100vh;
  position: relative;
}

/* 科技感卡片基础样式 */
.cyber-card {
  position: relative;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-glow {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg, #00d4ff, #0ea5e9, #00d4ff);
  border-radius: 16rpx;
  z-index: -1;
  opacity: 0.2;
  animation: borderGlow 6s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.3; }
}

/* 状态卡片 */
.status-card {
  background: rgba(15, 23, 42, 0.9);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-icon-container {
  position: relative;
  margin-right: 20rpx;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 212, 255, 0.1);
  border: 2rpx solid #00d4ff;
  position: relative;
  z-index: 2;
}

.status-ring {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  width: 96rpx;
  height: 96rpx;
  border: 2rpx solid rgba(0, 212, 255, 0.2);
  border-radius: 50%;
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
  100% { transform: scale(1); opacity: 0.3; }
}

.icon-text {
  font-size: 40rpx;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  display: block;
  margin-bottom: 8rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.order-no {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #00d4ff;
  cursor: pointer;
}

.order-label {
  margin-right: 8rpx;
  opacity: 0.8;
}

.order-value {
  font-family: 'Courier New', monospace;
  letter-spacing: 1rpx;
}

/* 科技感状态流程 */
.status-flow {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0 20rpx;
  border-top: 1rpx solid rgba(0, 212, 255, 0.2);
}

.flow-line {
  position: absolute;
  top: 50rpx;
  left: 15%;
  right: 15%;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.3), transparent);
}

.flow-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  opacity: 0.4;
  transition: all 0.3s ease;
}

.flow-item.active {
  opacity: 1;
  transform: scale(1.1);
}

.flow-dot {
  position: absolute;
  top: 20rpx;
  width: 12rpx;
  height: 12rpx;
  background: #00d4ff;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.8);
}

.flow-item:not(.active) .flow-dot {
  background: rgba(0, 212, 255, 0.3);
  box-shadow: none;
}

.flow-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  margin-top: 20rpx;
}

.flow-text {
  font-size: 22rpx;
  color: #00d4ff;
  text-align: center;
  font-weight: 500;
}

.flow-item:not(.active) .flow-text {
  color: rgba(255, 255, 255, 0.5);
}

/* 科技感信息卡片 */
.info-card {
  /* 使用 cyber-card 基础样式 */
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid rgba(0, 212, 255, 0.3);
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  filter: drop-shadow(0 0 8rpx rgba(0, 212, 255, 0.6));
}

.title-text {
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.info-list {
  /* 信息列表样式 */
}

.cyber-item {
  position: relative;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
}

.cyber-item:hover {
  background: rgba(0, 212, 255, 0.05);
  border-radius: 8rpx;
}

.cyber-item:last-child {
  border-bottom: none;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.info-item-vertical {
  flex-direction: column;
  align-items: flex-start;
}

.info-item-vertical .info-label {
  margin-bottom: 12rpx;
}

.info-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
  margin-right: 20rpx;
}

.label-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
  filter: drop-shadow(0 0 6rpx rgba(0, 212, 255, 0.6));
}

.label-text {
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.3);
}

.info-value {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  flex: 1;
  text-align: right;
  word-wrap: break-word;
  word-break: break-all;
}

.info-value-container {
  width: 100%;
  margin-top: 8rpx;
}

.info-value-content {
  text-align: left;
  line-height: 1.6;
  background: rgba(0, 212, 255, 0.1);
  padding: 16rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(0, 212, 255, 0.2);
}

.info-value.price {
  color: #00d4ff;
  font-weight: bold;
  font-size: 32rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.8);
}

.info-value.duration {
  color: #00d4ff;
  font-weight: 600;
}

.info-value.time-value {
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Courier New', monospace;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 12rpx;
}

.cyber-tag {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56rpx;
  padding: 0 20rpx;
  background: rgba(0, 212, 255, 0.1);
  border: 2rpx solid rgba(0, 212, 255, 0.5);
  border-radius: 28rpx;
  overflow: hidden;
}

.tag-text {
  font-size: 24rpx;
  color: #00d4ff;
  font-weight: 500;
  white-space: nowrap;
  position: relative;
  z-index: 2;
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.6);
}

.tag-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  animation: tagGlow 2s ease-in-out infinite;
}

@keyframes tagGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* 需求说明 */
.requirements-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
}

/* 用户信息（发布者和接单者）- 科技感设计 */
.user-info {
  padding: 20rpx;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  box-shadow: 0 0 15rpx rgba(0, 212, 255, 0.2);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  display: block;
  margin-bottom: 8rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.3);
}

.user-stats {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.user-role {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 212, 255, 0.3);
}

/* 游戏昵称显示区域 */
.game-nickname-section {
  margin-top: 20rpx;
  padding: 16rpx 20rpx;
  background: rgba(0, 212, 255, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 212, 255, 0.2);
}

.game-nickname-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.game-nickname-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 16rpx;
  flex-shrink: 0;
}

.game-nickname-content {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.game-nickname-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--cyber-blue);
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.4);
  margin-right: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-nickname-text:active {
  transform: scale(0.98);
  color: #ffffff;
}

.copy-icon {
  font-size: 24rpx;
  color: rgba(0, 212, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8rpx;
  border-radius: 6rpx;
  background: rgba(0, 212, 255, 0.1);
}

.copy-icon:active {
  background: rgba(0, 212, 255, 0.2);
  transform: scale(0.95);
}

.game-nickname-hidden {
  flex: 1;
  text-align: right;
}

.hidden-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.user-level {
  background: linear-gradient(135deg, #1890ff, #0066cc);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
}

.user-rating {
  font-size: 24rpx;
  color: #ffa940;
  text-shadow: 0 0 5rpx rgba(255, 169, 64, 0.3);
}

.user-orders {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.user-intro {
  background: rgba(0, 212, 255, 0.1);
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #00d4ff;
  border: 1rpx solid rgba(0, 212, 255, 0.2);
}

.intro-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8rpx;
  display: block;
}

.intro-text {
  font-size: 28rpx;
  color: #ffffff;
  line-height: 1.5;
}

/* 订单评价 */
.evaluation-content {
  /* 评价内容 */
}

.evaluation-rating {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.rating-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 12rpx;
}

.rating-stars {
  display: flex;
}

.star {
  font-size: 24rpx;
  color: #d9d9d9;
  margin-right: 4rpx;
}

.star.active {
  color: #fadb14;
}

.evaluation-comment {
  margin-bottom: 12rpx;
}

.comment-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.comment-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  background: #f8f8f8;
  padding: 16rpx;
  border-radius: 8rpx;
}

.evaluation-tags {
  margin-bottom: 12rpx;
}

.tags-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 12rpx;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 8rpx;
}

.tags-list .tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  background: rgba(0, 212, 255, 0.1);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #00d4ff;
}

.evaluation-time {
  font-size: 22rpx;
  color: #999;
}

/* 科技感操作按钮 */
.action-buttons {
  padding: 30rpx 0;
}

.cyber-btn {
  position: relative;
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  border: 2rpx solid rgba(0, 212, 255, 0.5);
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  background: rgba(0, 212, 255, 0.1);
  color: #00d4ff;
  overflow: hidden;
  transition: all 0.3s ease;
  /* 添加flex布局使文字居中 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.cyber-btn:last-child {
  margin-bottom: 0;
}

.cyber-btn:active {
  transform: scale(0.98);
}

.btn-text {
  position: relative;
  z-index: 3;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.8);
}

.btn-glow {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg, #00d4ff, #0ea5e9, #00d4ff);
  border-radius: 16rpx;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cyber-btn:active .btn-glow {
  opacity: 0.8;
}

.btn-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.btn-particle {
  position: absolute;
  width: 3rpx;
  height: 3rpx;
  background: rgba(0, 212, 255, 0.5);
  border-radius: 50%;
  opacity: 0;
  animation: particleFloat 6s ease-in-out infinite;
}

.btn-particle:nth-child(1) {
  left: 50%;
  top: 50%;
  animation-delay: 0s;
}

@keyframes particleFloat {
  0%, 100% { opacity: 0; transform: translateY(0); }
  50% { opacity: 0.6; transform: translateY(-15rpx); }
}

/* 按钮类型样式 */
.cyber-btn.primary {
  background: rgba(0, 212, 255, 0.2);
  border-color: #00d4ff;
}

.cyber-btn.danger {
  background: rgba(255, 77, 79, 0.2);
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.cyber-btn.danger .btn-text {
  text-shadow: 0 0 10rpx rgba(255, 77, 79, 0.8);
}

.cyber-btn.edit {
  background: rgba(24, 144, 255, 0.2);
  border-color: #1890ff;
  color: #1890ff;
}

.cyber-btn.edit .btn-text {
  text-shadow: 0 0 10rpx rgba(24, 144, 255, 0.8);
}

.cyber-btn.grab {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.3), rgba(14, 165, 233, 0.3));
  border-color: #00d4ff;
  box-shadow: 0 8rpx 24rpx rgba(0, 212, 255, 0.3);
}

.cyber-btn.grab:active {
  box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.5);
}

.cyber-btn::after {
  border: none;
}

/* 科技感加载状态 */
.cyber-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: rgba(15, 23, 42, 0.9);
  border-radius: 16rpx;
  margin: 20rpx;
}

.loading-ring {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 30rpx;
}

.loading-dot {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: #00d4ff;
  border-radius: 50%;
  animation: loadingRotate 1.5s linear infinite;
  box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.8);
}

.loading-dot:nth-child(1) {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  animation-delay: 0.5s;
}

.loading-dot:nth-child(3) {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 1s;
}

@keyframes loadingRotate {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.3; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1); }
}

.loading-text {
  font-size: 26rpx;
  color: #00d4ff;
  margin-bottom: 20rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.6);
}

.loading-progress {
  width: 200rpx;
  height: 4rpx;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 2rpx;
  overflow: hidden;
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: progressMove 2s ease-in-out infinite;
}

@keyframes progressMove {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 滚动区域 */
.scrollarea {
  height: 100vh;
}

/* 确保自定义导航栏页面的滚动区域有正确的顶部间距 */
.scrollarea.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 10rpx);
  box-sizing: border-box;
}

/* 遇到问题按钮容器 */
.problem-button-container {
  position: fixed;
  top: calc(var(--nav-bar-height, 94px) + 20rpx);
  right: 30rpx;
  z-index: 1000;
}

/* 遇到问题按钮样式 */
.cyber-problem-btn {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 77, 79, 0.15);
  border: 2rpx solid rgba(255, 77, 79, 0.6);
  border-radius: 50%;
  color: #ff4d4f;
  font-size: 24rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 15rpx rgba(255, 77, 79, 0.2);
  cursor: pointer;
}

.cyber-problem-btn:active {
  transform: scale(0.95);
  background: rgba(255, 77, 79, 0.25);
}

.problem-btn-icon {
  width: 32rpx;
  height: 32rpx;
  position: relative;
  z-index: 3;
  filter: brightness(0) saturate(100%) invert(35%) sepia(100%) saturate(7500%) hue-rotate(350deg) brightness(100%) contrast(100%);
}

.problem-btn-text {
  position: relative;
  z-index: 3;
  text-shadow: 0 0 8rpx rgba(255, 77, 79, 0.8);
}

.problem-btn-glow {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg, #ff4d4f, #ff7875, #ff4d4f);
  border-radius: 50%;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cyber-problem-btn:active .problem-btn-glow {
  opacity: 0.6;
}
