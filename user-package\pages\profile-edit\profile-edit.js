// profile-edit.js - 用户资料编辑页面
import API from '../../../utils/api.js';

const app = getApp();

Page({
  data: {
    userInfo: {},
    originalUserInfo: {},
    loading: false,
    saving: false,
    genderOptions: [
      { value: 0, label: '保密' },
      { value: 1, label: '男' },
      { value: 2, label: '女' }
    ],
    genderIndex: 0,
    formData: {
      nickName: '',
      gameNickName: '',
      gender: 0,
      age: '',
      bio: '',
      contactInfo: ''
    },
    errors: {}
  },

  onLoad() {
    this.loadUserData();
  },

  // 加载用户数据
  async loadUserData() {
    this.setData({ loading: true });

    try {
      // 从本地存储获取用户信息
      const localUserInfo = wx.getStorageSync('userInfo') || {};
      
      // 从服务器获取最新信息
      const result = await API.getUserInfo();
      let userInfo = localUserInfo;
      
      if (result.success) {
        userInfo = { ...localUserInfo, ...result.data.userInfo };
      }

      // 设置表单数据
      const formData = {
        nickName: userInfo.nickName || '',
        gameNickName: userInfo.gameNickName || '',
        gender: userInfo.gender || 0,
        age: userInfo.age ? userInfo.age.toString() : '',
        bio: userInfo.bio || '',
        contactInfo: userInfo.contactInfo || ''
      };

      // 设置性别选择器索引
      const genderIndex = this.data.genderOptions.findIndex(option => option.value === formData.gender);

      this.setData({
        userInfo,
        originalUserInfo: JSON.parse(JSON.stringify(userInfo)),
        formData,
        genderIndex: genderIndex >= 0 ? genderIndex : 0,
        loading: false
      });
    } catch (error) {
      console.error('加载用户数据失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 输入框变化处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除错误信息
    });
  },

  // 性别选择器变化
  onGenderChange(e) {
    const genderIndex = parseInt(e.detail.value);
    const gender = this.data.genderOptions[genderIndex].value;
    
    this.setData({
      genderIndex,
      'formData.gender': gender,
      'errors.gender': ''
    });
  },

  // 选择头像
  chooseAvatar() {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['album'] : ['camera'];
        
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType,
          success: (res) => {
            this.uploadAvatar(res.tempFilePaths[0]);
          }
        });
      }
    });
  },

  // 上传头像
  async uploadAvatar(tempFilePath) {
    wx.showLoading({ title: '上传中...' });

    try {
      // 生成唯一文件名
      const timestamp = Date.now();
      const random = Math.random().toString(36).substr(2, 9);
      const fileName = `avatar_${timestamp}_${random}.jpg`;
      
      // 上传到云存储
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: `images/avatars/${fileName}`,
        filePath: tempFilePath
      });

      if (uploadResult.fileID) {
        // 更新头像URL到userInfo和formData
        this.setData({
          'userInfo.avatarUrl': uploadResult.fileID,
          'formData.avatarUrl': uploadResult.fileID
        });

        wx.showToast({
          title: '头像上传成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      wx.showToast({
        title: '上传失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    const errors = {};

    // 昵称验证
    if (!formData.nickName.trim()) {
      errors.nickName = '请输入昵称';
    } else if (formData.nickName.length > 20) {
      errors.nickName = '昵称不能超过20个字符';
    }

    // 游戏昵称验证（选填）
    if (formData.gameNickName && formData.gameNickName.length > 30) {
      errors.gameNickName = '游戏昵称不能超过30个字符';
    }

    // 年龄验证
    if (formData.age) {
      const age = parseInt(formData.age);
      if (isNaN(age) || age < 16 || age > 100) {
        errors.age = '年龄必须在16-100岁之间';
      }
    }

    // 个人简介验证
    if (formData.bio && formData.bio.length > 200) {
      errors.bio = '个人简介不能超过200个字符';
    }

    // 联系方式验证
    if (formData.contactInfo && formData.contactInfo.length > 100) {
      errors.contactInfo = '联系方式不能超过100个字符';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  // 保存用户信息
  async saveUserInfo() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ saving: true });

    try {
      const { formData, userInfo } = this.data;
      
      // 准备更新数据
      const updateData = {
        nickName: formData.nickName.trim(),
        gameNickName: formData.gameNickName.trim(),
        gender: formData.gender,
        bio: formData.bio.trim(),
        contactInfo: formData.contactInfo.trim()
      };

      // 如果年龄有值，添加到更新数据中
      if (formData.age) {
        updateData.age = parseInt(formData.age);
      }

      // 如果头像有变化，添加到更新数据中
      if (userInfo.avatarUrl !== this.data.originalUserInfo.avatarUrl) {
        updateData.avatarUrl = userInfo.avatarUrl;
      }

      // 调用API更新用户信息
      const result = await API.updateUserInfo(updateData);

      if (result.success) {
        // 使用服务器返回的最新用户信息
        const updatedUserInfo = result.data;

        // 使用全局的用户信息更新方法
        app.updateUserInfo(updatedUserInfo);

        console.log('✅ [用户资料编辑] 用户信息更新成功:', updatedUserInfo);

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('保存用户信息失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  // 取消编辑
  cancelEdit() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消编辑吗？未保存的修改将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  }
});
