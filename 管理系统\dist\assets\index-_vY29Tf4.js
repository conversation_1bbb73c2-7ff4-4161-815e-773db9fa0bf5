import{r as b,c as fr,b as hr,R as pr}from"./vendor-BTWMFwqw.js";import{s as q,L as Be,I as wt,S as M,C as mr,t as St,M as xr,B as S,R as yr,a as gr,b as _,c as w,d as T,e as E,T as L,f as _e,F as ae,g as P,h as br,i as jr,j as vr,k as wr,l as Sr,m as kr,n as Cr,o as Tr,A as Z}from"./antd-C4mrS3pD.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&s(a)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var kt={exports:{}},ve={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rr=b,Er=Symbol.for("react.element"),Ir=Symbol.for("react.fragment"),Nr=Object.prototype.hasOwnProperty,Or=Rr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ar={key:!0,ref:!0,__self:!0,__source:!0};function Ct(e,t,n){var s,o={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(s in t)Nr.call(t,s)&&!Ar.hasOwnProperty(s)&&(o[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps,t)o[s]===void 0&&(o[s]=t[s]);return{$$typeof:Er,type:e,key:i,ref:a,props:o,_owner:Or.current}}ve.Fragment=Ir;ve.jsx=Ct;ve.jsxs=Ct;kt.exports=ve;var r=kt.exports,ze={},Qe=fr;ze.createRoot=Qe.createRoot,ze.hydrateRoot=Qe.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function se(){return se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(e[s]=n[s])}return e},se.apply(this,arguments)}var H;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(H||(H={}));const Ye="popstate";function Ur(e){e===void 0&&(e={});function t(s,o){let{pathname:i,search:a,hash:l}=s.location;return Le("",{pathname:i,search:a,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(s,o){return typeof o=="string"?o:Rt(o)}return Br(t,n,null,e)}function O(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Tt(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Pr(){return Math.random().toString(36).substr(2,8)}function et(e,t){return{usr:e.state,key:e.key,idx:t}}function Le(e,t,n,s){return n===void 0&&(n=null),se({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?ee(t):t,{state:n,key:t&&t.key||s||Pr()})}function Rt(e){let{pathname:t="/",search:n="",hash:s=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),s&&s!=="#"&&(t+=s.charAt(0)==="#"?s:"#"+s),t}function ee(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let s=e.indexOf("?");s>=0&&(t.search=e.substr(s),e=e.substr(0,s)),e&&(t.pathname=e)}return t}function Br(e,t,n,s){s===void 0&&(s={});let{window:o=document.defaultView,v5Compat:i=!1}=s,a=o.history,l=H.Pop,c=null,u=f();u==null&&(u=0,a.replaceState(se({},a.state,{idx:u}),""));function f(){return(a.state||{idx:null}).idx}function m(){l=H.Pop;let p=f(),y=p==null?null:p-u;u=p,c&&c({action:l,location:x.location,delta:y})}function v(p,y){l=H.Push;let j=Le(x.location,p,y);u=f()+1;let k=et(j,u),A=x.createHref(j);try{a.pushState(k,"",A)}catch(I){if(I instanceof DOMException&&I.name==="DataCloneError")throw I;o.location.assign(A)}i&&c&&c({action:l,location:x.location,delta:1})}function g(p,y){l=H.Replace;let j=Le(x.location,p,y);u=f();let k=et(j,u),A=x.createHref(j);a.replaceState(k,"",A),i&&c&&c({action:l,location:x.location,delta:0})}function d(p){let y=o.location.origin!=="null"?o.location.origin:o.location.href,j=typeof p=="string"?p:Rt(p);return j=j.replace(/ $/,"%20"),O(y,"No window.location.(origin|href) available to create URL for href: "+j),new URL(j,y)}let x={get action(){return l},get location(){return e(o,a)},listen(p){if(c)throw new Error("A history only accepts one active listener");return o.addEventListener(Ye,m),c=p,()=>{o.removeEventListener(Ye,m),c=null}},createHref(p){return t(o,p)},createURL:d,encodeLocation(p){let y=d(p);return{pathname:y.pathname,search:y.search,hash:y.hash}},push:v,replace:g,go(p){return a.go(p)}};return x}var tt;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(tt||(tt={}));function _r(e,t,n){return n===void 0&&(n="/"),zr(e,t,n)}function zr(e,t,n,s){let o=typeof t=="string"?ee(t):t,i=Nt(o.pathname||"/",n);if(i==null)return null;let a=Et(e);Lr(a);let l=null;for(let c=0;l==null&&c<a.length;++c){let u=Xr(i);l=Vr(a[c],u)}return l}function Et(e,t,n,s){t===void 0&&(t=[]),n===void 0&&(n=[]),s===void 0&&(s="");let o=(i,a,l)=>{let c={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:a,route:i};c.relativePath.startsWith("/")&&(O(c.relativePath.startsWith(s),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+s+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(s.length));let u=X([s,c.relativePath]),f=n.concat(c);i.children&&i.children.length>0&&(O(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Et(i.children,t,f,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:Jr(u,i.index),routesMeta:f})};return e.forEach((i,a)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))o(i,a);else for(let c of It(i.path))o(i,a,c)}),t}function It(e){let t=e.split("/");if(t.length===0)return[];let[n,...s]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(s.length===0)return o?[i,""]:[i];let a=It(s.join("/")),l=[];return l.push(...a.map(c=>c===""?i:[i,c].join("/"))),o&&l.push(...a),l.map(c=>e.startsWith("/")&&c===""?"/":c)}function Lr(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Hr(t.routesMeta.map(s=>s.childrenIndex),n.routesMeta.map(s=>s.childrenIndex)))}const Dr=/^:[\w-]+$/,Fr=3,Mr=2,$r=1,Wr=10,qr=-2,rt=e=>e==="*";function Jr(e,t){let n=e.split("/"),s=n.length;return n.some(rt)&&(s+=qr),t&&(s+=Mr),n.filter(o=>!rt(o)).reduce((o,i)=>o+(Dr.test(i)?Fr:i===""?$r:Wr),s)}function Hr(e,t){return e.length===t.length&&e.slice(0,-1).every((s,o)=>s===t[o])?e[e.length-1]-t[t.length-1]:0}function Vr(e,t,n){let{routesMeta:s}=e,o={},i="/",a=[];for(let l=0;l<s.length;++l){let c=s[l],u=l===s.length-1,f=i==="/"?t:t.slice(i.length)||"/",m=Kr({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},f),v=c.route;if(!m)return null;Object.assign(o,m.params),a.push({params:o,pathname:X([i,m.pathname]),pathnameBase:en(X([i,m.pathnameBase])),route:v}),m.pathnameBase!=="/"&&(i=X([i,m.pathnameBase]))}return a}function Kr(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,s]=Zr(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],a=i.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:s.reduce((u,f,m)=>{let{paramName:v,isOptional:g}=f;if(v==="*"){let x=l[m]||"";a=i.slice(0,i.length-x.length).replace(/(.)\/+$/,"$1")}const d=l[m];return g&&!d?u[v]=void 0:u[v]=(d||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:a,pattern:e}}function Zr(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Tt(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let s=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,l,c)=>(s.push({paramName:l,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(s.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),s]}function Xr(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Tt(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Nt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,s=e.charAt(n);return s&&s!=="/"?null:e.slice(n)||"/"}function Gr(e,t){t===void 0&&(t="/");let{pathname:n,search:s="",hash:o=""}=typeof e=="string"?ee(e):e;return{pathname:n?n.startsWith("/")?n:Qr(n,t):t,search:tn(s),hash:rn(o)}}function Qr(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Oe(e,t,n,s){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(s)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Yr(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Ot(e,t){let n=Yr(e);return t?n.map((s,o)=>o===n.length-1?s.pathname:s.pathnameBase):n.map(s=>s.pathnameBase)}function At(e,t,n,s){s===void 0&&(s=!1);let o;typeof e=="string"?o=ee(e):(o=se({},e),O(!o.pathname||!o.pathname.includes("?"),Oe("?","pathname","search",o)),O(!o.pathname||!o.pathname.includes("#"),Oe("#","pathname","hash",o)),O(!o.search||!o.search.includes("#"),Oe("#","search","hash",o)));let i=e===""||o.pathname==="",a=i?"/":o.pathname,l;if(a==null)l=n;else{let m=t.length-1;if(!s&&a.startsWith("..")){let v=a.split("/");for(;v[0]==="..";)v.shift(),m-=1;o.pathname=v.join("/")}l=m>=0?t[m]:"/"}let c=Gr(o,l),u=a&&a!=="/"&&a.endsWith("/"),f=(i||a===".")&&n.endsWith("/");return!c.pathname.endsWith("/")&&(u||f)&&(c.pathname+="/"),c}const X=e=>e.join("/").replace(/\/\/+/g,"/"),en=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),tn=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,rn=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function nn(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Ut=["post","put","patch","delete"];new Set(Ut);const sn=["get",...Ut];new Set(sn);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function oe(){return oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(e[s]=n[s])}return e},oe.apply(this,arguments)}const He=b.createContext(null),on=b.createContext(null),le=b.createContext(null),we=b.createContext(null),Y=b.createContext({outlet:null,matches:[],isDataRoute:!1}),Pt=b.createContext(null);function ce(){return b.useContext(we)!=null}function Se(){return ce()||O(!1),b.useContext(we).location}function Bt(e){b.useContext(le).static||b.useLayoutEffect(e)}function _t(){let{isDataRoute:e}=b.useContext(Y);return e?bn():an()}function an(){ce()||O(!1);let e=b.useContext(He),{basename:t,future:n,navigator:s}=b.useContext(le),{matches:o}=b.useContext(Y),{pathname:i}=Se(),a=JSON.stringify(Ot(o,n.v7_relativeSplatPath)),l=b.useRef(!1);return Bt(()=>{l.current=!0}),b.useCallback(function(u,f){if(f===void 0&&(f={}),!l.current)return;if(typeof u=="number"){s.go(u);return}let m=At(u,JSON.parse(a),i,f.relative==="path");e==null&&t!=="/"&&(m.pathname=m.pathname==="/"?t:X([t,m.pathname])),(f.replace?s.replace:s.push)(m,f.state,f)},[t,s,a,i,e])}function ln(e,t){return cn(e,t)}function cn(e,t,n,s){ce()||O(!1);let{navigator:o}=b.useContext(le),{matches:i}=b.useContext(Y),a=i[i.length-1],l=a?a.params:{};a&&a.pathname;let c=a?a.pathnameBase:"/";a&&a.route;let u=Se(),f;if(t){var m;let p=typeof t=="string"?ee(t):t;c==="/"||(m=p.pathname)!=null&&m.startsWith(c)||O(!1),f=p}else f=u;let v=f.pathname||"/",g=v;if(c!=="/"){let p=c.replace(/^\//,"").split("/");g="/"+v.replace(/^\//,"").split("/").slice(p.length).join("/")}let d=_r(e,{pathname:g}),x=pn(d&&d.map(p=>Object.assign({},p,{params:Object.assign({},l,p.params),pathname:X([c,o.encodeLocation?o.encodeLocation(p.pathname).pathname:p.pathname]),pathnameBase:p.pathnameBase==="/"?c:X([c,o.encodeLocation?o.encodeLocation(p.pathnameBase).pathname:p.pathnameBase])})),i,n,s);return t&&x?b.createElement(we.Provider,{value:{location:oe({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:H.Pop}},x):x}function dn(){let e=gn(),t=nn(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return b.createElement(b.Fragment,null,b.createElement("h2",null,"Unexpected Application Error!"),b.createElement("h3",{style:{fontStyle:"italic"}},t),n?b.createElement("pre",{style:o},n):null,null)}const un=b.createElement(dn,null);class fn extends b.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?b.createElement(Y.Provider,{value:this.props.routeContext},b.createElement(Pt.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function hn(e){let{routeContext:t,match:n,children:s}=e,o=b.useContext(He);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),b.createElement(Y.Provider,{value:t},s)}function pn(e,t,n,s){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),s===void 0&&(s=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=s)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let a=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let f=a.findIndex(m=>m.route.id&&(l==null?void 0:l[m.route.id])!==void 0);f>=0||O(!1),a=a.slice(0,Math.min(a.length,f+1))}let c=!1,u=-1;if(n&&s&&s.v7_partialHydration)for(let f=0;f<a.length;f++){let m=a[f];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(u=f),m.route.id){let{loaderData:v,errors:g}=n,d=m.route.loader&&v[m.route.id]===void 0&&(!g||g[m.route.id]===void 0);if(m.route.lazy||d){c=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((f,m,v)=>{let g,d=!1,x=null,p=null;n&&(g=l&&m.route.id?l[m.route.id]:void 0,x=m.route.errorElement||un,c&&(u<0&&v===0?(jn("route-fallback"),d=!0,p=null):u===v&&(d=!0,p=m.route.hydrateFallbackElement||null)));let y=t.concat(a.slice(0,v+1)),j=()=>{let k;return g?k=x:d?k=p:m.route.Component?k=b.createElement(m.route.Component,null):m.route.element?k=m.route.element:k=f,b.createElement(hn,{match:m,routeContext:{outlet:f,matches:y,isDataRoute:n!=null},children:k})};return n&&(m.route.ErrorBoundary||m.route.errorElement||v===0)?b.createElement(fn,{location:n.location,revalidation:n.revalidation,component:x,error:g,children:j(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):j()},null)}var zt=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(zt||{}),Lt=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Lt||{});function mn(e){let t=b.useContext(He);return t||O(!1),t}function xn(e){let t=b.useContext(on);return t||O(!1),t}function yn(e){let t=b.useContext(Y);return t||O(!1),t}function Dt(e){let t=yn(),n=t.matches[t.matches.length-1];return n.route.id||O(!1),n.route.id}function gn(){var e;let t=b.useContext(Pt),n=xn(),s=Dt();return t!==void 0?t:(e=n.errors)==null?void 0:e[s]}function bn(){let{router:e}=mn(zt.UseNavigateStable),t=Dt(Lt.UseNavigateStable),n=b.useRef(!1);return Bt(()=>{n.current=!0}),b.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,oe({fromRouteId:t},i)))},[e,t])}const nt={};function jn(e,t,n){nt[e]||(nt[e]=!0)}function vn(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function me(e){let{to:t,replace:n,state:s,relative:o}=e;ce()||O(!1);let{future:i,static:a}=b.useContext(le),{matches:l}=b.useContext(Y),{pathname:c}=Se(),u=_t(),f=At(t,Ot(l,i.v7_relativeSplatPath),c,o==="path"),m=JSON.stringify(f);return b.useEffect(()=>u(JSON.parse(m),{replace:n,state:s,relative:o}),[u,m,o,n,s]),null}function B(e){O(!1)}function wn(e){let{basename:t="/",children:n=null,location:s,navigationType:o=H.Pop,navigator:i,static:a=!1,future:l}=e;ce()&&O(!1);let c=t.replace(/^\/*/,"/"),u=b.useMemo(()=>({basename:c,navigator:i,static:a,future:oe({v7_relativeSplatPath:!1},l)}),[c,l,i,a]);typeof s=="string"&&(s=ee(s));let{pathname:f="/",search:m="",hash:v="",state:g=null,key:d="default"}=s,x=b.useMemo(()=>{let p=Nt(f,c);return p==null?null:{location:{pathname:p,search:m,hash:v,state:g,key:d},navigationType:o}},[c,f,m,v,g,d,o]);return x==null?null:b.createElement(le.Provider,{value:u},b.createElement(we.Provider,{children:n,value:x}))}function st(e){let{children:t,location:n}=e;return ln(De(t),n)}new Promise(()=>{});function De(e,t){t===void 0&&(t=[]);let n=[];return b.Children.forEach(e,(s,o)=>{if(!b.isValidElement(s))return;let i=[...t,o];if(s.type===b.Fragment){n.push.apply(n,De(s.props.children,i));return}s.type!==B&&O(!1),!s.props.index||!s.props.children||O(!1);let a={id:s.props.id||i.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(a.children=De(s.props.children,i)),n.push(a)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Sn="6";try{window.__reactRouterVersion=Sn}catch{}const kn="startTransition",ot=hr[kn];function Cn(e){let{basename:t,children:n,future:s,window:o}=e,i=b.useRef();i.current==null&&(i.current=Ur({window:o,v5Compat:!0}));let a=i.current,[l,c]=b.useState({action:a.action,location:a.location}),{v7_startTransition:u}=s||{},f=b.useCallback(m=>{u&&ot?ot(()=>c(m)):c(m)},[c,u]);return b.useLayoutEffect(()=>a.listen(f),[a,f]),b.useEffect(()=>vn(s),[s]),b.createElement(wn,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:a,future:s})}var it;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(it||(it={}));var at;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(at||(at={}));function Ft(e,t){return function(){return e.apply(t,arguments)}}const{toString:Tn}=Object.prototype,{getPrototypeOf:Ve}=Object,{iterator:ke,toStringTag:Mt}=Symbol,Ce=(e=>t=>{const n=Tn.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),$=e=>(e=e.toLowerCase(),t=>Ce(t)===e),Te=e=>t=>typeof t===e,{isArray:te}=Array,ie=Te("undefined");function de(e){return e!==null&&!ie(e)&&e.constructor!==null&&!ie(e.constructor)&&D(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const $t=$("ArrayBuffer");function Rn(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&$t(e.buffer),t}const En=Te("string"),D=Te("function"),Wt=Te("number"),ue=e=>e!==null&&typeof e=="object",In=e=>e===!0||e===!1,xe=e=>{if(Ce(e)!=="object")return!1;const t=Ve(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Mt in e)&&!(ke in e)},Nn=e=>{if(!ue(e)||de(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},On=$("Date"),An=$("File"),Un=$("Blob"),Pn=$("FileList"),Bn=e=>ue(e)&&D(e.pipe),_n=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||D(e.append)&&((t=Ce(e))==="formdata"||t==="object"&&D(e.toString)&&e.toString()==="[object FormData]"))},zn=$("URLSearchParams"),[Ln,Dn,Fn,Mn]=["ReadableStream","Request","Response","Headers"].map($),$n=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function fe(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,o;if(typeof e!="object"&&(e=[e]),te(e))for(s=0,o=e.length;s<o;s++)t.call(null,e[s],s,e);else{if(de(e))return;const i=n?Object.getOwnPropertyNames(e):Object.keys(e),a=i.length;let l;for(s=0;s<a;s++)l=i[s],t.call(null,e[l],l,e)}}function qt(e,t){if(de(e))return null;t=t.toLowerCase();const n=Object.keys(e);let s=n.length,o;for(;s-- >0;)if(o=n[s],t===o.toLowerCase())return o;return null}const K=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Jt=e=>!ie(e)&&e!==K;function Fe(){const{caseless:e}=Jt(this)&&this||{},t={},n=(s,o)=>{const i=e&&qt(t,o)||o;xe(t[i])&&xe(s)?t[i]=Fe(t[i],s):xe(s)?t[i]=Fe({},s):te(s)?t[i]=s.slice():t[i]=s};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&fe(arguments[s],n);return t}const Wn=(e,t,n,{allOwnKeys:s}={})=>(fe(t,(o,i)=>{n&&D(o)?e[i]=Ft(o,n):e[i]=o},{allOwnKeys:s}),e),qn=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Jn=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Hn=(e,t,n,s)=>{let o,i,a;const l={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),i=o.length;i-- >0;)a=o[i],(!s||s(a,e,t))&&!l[a]&&(t[a]=e[a],l[a]=!0);e=n!==!1&&Ve(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Vn=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},Kn=e=>{if(!e)return null;if(te(e))return e;let t=e.length;if(!Wt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Zn=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ve(Uint8Array)),Xn=(e,t)=>{const s=(e&&e[ke]).call(e);let o;for(;(o=s.next())&&!o.done;){const i=o.value;t.call(e,i[0],i[1])}},Gn=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},Qn=$("HTMLFormElement"),Yn=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,o){return s.toUpperCase()+o}),lt=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),es=$("RegExp"),Ht=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};fe(n,(o,i)=>{let a;(a=t(o,i,e))!==!1&&(s[i]=a||o)}),Object.defineProperties(e,s)},ts=e=>{Ht(e,(t,n)=>{if(D(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(D(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},rs=(e,t)=>{const n={},s=o=>{o.forEach(i=>{n[i]=!0})};return te(e)?s(e):s(String(e).split(t)),n},ns=()=>{},ss=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function os(e){return!!(e&&D(e.append)&&e[Mt]==="FormData"&&e[ke])}const is=e=>{const t=new Array(10),n=(s,o)=>{if(ue(s)){if(t.indexOf(s)>=0)return;if(de(s))return s;if(!("toJSON"in s)){t[o]=s;const i=te(s)?[]:{};return fe(s,(a,l)=>{const c=n(a,o+1);!ie(c)&&(i[l]=c)}),t[o]=void 0,i}}return s};return n(e,0)},as=$("AsyncFunction"),ls=e=>e&&(ue(e)||D(e))&&D(e.then)&&D(e.catch),Vt=((e,t)=>e?setImmediate:t?((n,s)=>(K.addEventListener("message",({source:o,data:i})=>{o===K&&i===n&&s.length&&s.shift()()},!1),o=>{s.push(o),K.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",D(K.postMessage)),cs=typeof queueMicrotask<"u"?queueMicrotask.bind(K):typeof process<"u"&&process.nextTick||Vt,ds=e=>e!=null&&D(e[ke]),h={isArray:te,isArrayBuffer:$t,isBuffer:de,isFormData:_n,isArrayBufferView:Rn,isString:En,isNumber:Wt,isBoolean:In,isObject:ue,isPlainObject:xe,isEmptyObject:Nn,isReadableStream:Ln,isRequest:Dn,isResponse:Fn,isHeaders:Mn,isUndefined:ie,isDate:On,isFile:An,isBlob:Un,isRegExp:es,isFunction:D,isStream:Bn,isURLSearchParams:zn,isTypedArray:Zn,isFileList:Pn,forEach:fe,merge:Fe,extend:Wn,trim:$n,stripBOM:qn,inherits:Jn,toFlatObject:Hn,kindOf:Ce,kindOfTest:$,endsWith:Vn,toArray:Kn,forEachEntry:Xn,matchAll:Gn,isHTMLForm:Qn,hasOwnProperty:lt,hasOwnProp:lt,reduceDescriptors:Ht,freezeMethods:ts,toObjectSet:rs,toCamelCase:Yn,noop:ns,toFiniteNumber:ss,findKey:qt,global:K,isContextDefined:Jt,isSpecCompliantForm:os,toJSONObject:is,isAsyncFn:as,isThenable:ls,setImmediate:Vt,asap:cs,isIterable:ds};function C(e,t,n,s,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),o&&(this.response=o,this.status=o.status?o.status:null)}h.inherits(C,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:h.toJSONObject(this.config),code:this.code,status:this.status}}});const Kt=C.prototype,Zt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Zt[e]={value:e}});Object.defineProperties(C,Zt);Object.defineProperty(Kt,"isAxiosError",{value:!0});C.from=(e,t,n,s,o,i)=>{const a=Object.create(Kt);return h.toFlatObject(e,a,function(c){return c!==Error.prototype},l=>l!=="isAxiosError"),C.call(a,e.message,t,n,s,o),a.cause=e,a.name=e.name,i&&Object.assign(a,i),a};const us=null;function Me(e){return h.isPlainObject(e)||h.isArray(e)}function Xt(e){return h.endsWith(e,"[]")?e.slice(0,-2):e}function ct(e,t,n){return e?e.concat(t).map(function(o,i){return o=Xt(o),!n&&i?"["+o+"]":o}).join(n?".":""):t}function fs(e){return h.isArray(e)&&!e.some(Me)}const hs=h.toFlatObject(h,{},null,function(t){return/^is[A-Z]/.test(t)});function Re(e,t,n){if(!h.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=h.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(x,p){return!h.isUndefined(p[x])});const s=n.metaTokens,o=n.visitor||f,i=n.dots,a=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&h.isSpecCompliantForm(t);if(!h.isFunction(o))throw new TypeError("visitor must be a function");function u(d){if(d===null)return"";if(h.isDate(d))return d.toISOString();if(h.isBoolean(d))return d.toString();if(!c&&h.isBlob(d))throw new C("Blob is not supported. Use a Buffer instead.");return h.isArrayBuffer(d)||h.isTypedArray(d)?c&&typeof Blob=="function"?new Blob([d]):Buffer.from(d):d}function f(d,x,p){let y=d;if(d&&!p&&typeof d=="object"){if(h.endsWith(x,"{}"))x=s?x:x.slice(0,-2),d=JSON.stringify(d);else if(h.isArray(d)&&fs(d)||(h.isFileList(d)||h.endsWith(x,"[]"))&&(y=h.toArray(d)))return x=Xt(x),y.forEach(function(k,A){!(h.isUndefined(k)||k===null)&&t.append(a===!0?ct([x],A,i):a===null?x:x+"[]",u(k))}),!1}return Me(d)?!0:(t.append(ct(p,x,i),u(d)),!1)}const m=[],v=Object.assign(hs,{defaultVisitor:f,convertValue:u,isVisitable:Me});function g(d,x){if(!h.isUndefined(d)){if(m.indexOf(d)!==-1)throw Error("Circular reference detected in "+x.join("."));m.push(d),h.forEach(d,function(y,j){(!(h.isUndefined(y)||y===null)&&o.call(t,y,h.isString(j)?j.trim():j,x,v))===!0&&g(y,x?x.concat(j):[j])}),m.pop()}}if(!h.isObject(e))throw new TypeError("data must be an object");return g(e),t}function dt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Ke(e,t){this._pairs=[],e&&Re(e,this,t)}const Gt=Ke.prototype;Gt.append=function(t,n){this._pairs.push([t,n])};Gt.toString=function(t){const n=t?function(s){return t.call(this,s,dt)}:dt;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function ps(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Qt(e,t,n){if(!t)return e;const s=n&&n.encode||ps;h.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let i;if(o?i=o(t,n):i=h.isURLSearchParams(t)?t.toString():new Ke(t,n).toString(s),i){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class ut{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){h.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Yt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ms=typeof URLSearchParams<"u"?URLSearchParams:Ke,xs=typeof FormData<"u"?FormData:null,ys=typeof Blob<"u"?Blob:null,gs={isBrowser:!0,classes:{URLSearchParams:ms,FormData:xs,Blob:ys},protocols:["http","https","file","blob","url","data"]},Ze=typeof window<"u"&&typeof document<"u",$e=typeof navigator=="object"&&navigator||void 0,bs=Ze&&(!$e||["ReactNative","NativeScript","NS"].indexOf($e.product)<0),js=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",vs=Ze&&window.location.href||"http://localhost",ws=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ze,hasStandardBrowserEnv:bs,hasStandardBrowserWebWorkerEnv:js,navigator:$e,origin:vs},Symbol.toStringTag,{value:"Module"})),U={...ws,...gs};function Ss(e,t){return Re(e,new U.classes.URLSearchParams,{visitor:function(n,s,o,i){return U.isNode&&h.isBuffer(n)?(this.append(s,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)},...t})}function ks(e){return h.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Cs(e){const t={},n=Object.keys(e);let s;const o=n.length;let i;for(s=0;s<o;s++)i=n[s],t[i]=e[i];return t}function er(e){function t(n,s,o,i){let a=n[i++];if(a==="__proto__")return!0;const l=Number.isFinite(+a),c=i>=n.length;return a=!a&&h.isArray(o)?o.length:a,c?(h.hasOwnProp(o,a)?o[a]=[o[a],s]:o[a]=s,!l):((!o[a]||!h.isObject(o[a]))&&(o[a]=[]),t(n,s,o[a],i)&&h.isArray(o[a])&&(o[a]=Cs(o[a])),!l)}if(h.isFormData(e)&&h.isFunction(e.entries)){const n={};return h.forEachEntry(e,(s,o)=>{t(ks(s),o,n,0)}),n}return null}function Ts(e,t,n){if(h.isString(e))try{return(t||JSON.parse)(e),h.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const he={transitional:Yt,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",o=s.indexOf("application/json")>-1,i=h.isObject(t);if(i&&h.isHTMLForm(t)&&(t=new FormData(t)),h.isFormData(t))return o?JSON.stringify(er(t)):t;if(h.isArrayBuffer(t)||h.isBuffer(t)||h.isStream(t)||h.isFile(t)||h.isBlob(t)||h.isReadableStream(t))return t;if(h.isArrayBufferView(t))return t.buffer;if(h.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Ss(t,this.formSerializer).toString();if((l=h.isFileList(t))||s.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Re(l?{"files[]":t}:t,c&&new c,this.formSerializer)}}return i||o?(n.setContentType("application/json",!1),Ts(t)):t}],transformResponse:[function(t){const n=this.transitional||he.transitional,s=n&&n.forcedJSONParsing,o=this.responseType==="json";if(h.isResponse(t)||h.isReadableStream(t))return t;if(t&&h.isString(t)&&(s&&!this.responseType||o)){const a=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(l){if(a)throw l.name==="SyntaxError"?C.from(l,C.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:U.classes.FormData,Blob:U.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};h.forEach(["delete","get","head","post","put","patch"],e=>{he.headers[e]={}});const Rs=h.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Es=e=>{const t={};let n,s,o;return e&&e.split(`
`).forEach(function(a){o=a.indexOf(":"),n=a.substring(0,o).trim().toLowerCase(),s=a.substring(o+1).trim(),!(!n||t[n]&&Rs[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},ft=Symbol("internals");function ne(e){return e&&String(e).trim().toLowerCase()}function ye(e){return e===!1||e==null?e:h.isArray(e)?e.map(ye):String(e)}function Is(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Ns=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ae(e,t,n,s,o){if(h.isFunction(s))return s.call(this,t,n);if(o&&(t=n),!!h.isString(t)){if(h.isString(s))return t.indexOf(s)!==-1;if(h.isRegExp(s))return s.test(t)}}function Os(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function As(e,t){const n=h.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(o,i,a){return this[s].call(this,t,o,i,a)},configurable:!0})})}let F=class{constructor(t){t&&this.set(t)}set(t,n,s){const o=this;function i(l,c,u){const f=ne(c);if(!f)throw new Error("header name must be a non-empty string");const m=h.findKey(o,f);(!m||o[m]===void 0||u===!0||u===void 0&&o[m]!==!1)&&(o[m||c]=ye(l))}const a=(l,c)=>h.forEach(l,(u,f)=>i(u,f,c));if(h.isPlainObject(t)||t instanceof this.constructor)a(t,n);else if(h.isString(t)&&(t=t.trim())&&!Ns(t))a(Es(t),n);else if(h.isObject(t)&&h.isIterable(t)){let l={},c,u;for(const f of t){if(!h.isArray(f))throw TypeError("Object iterator must return a key-value pair");l[u=f[0]]=(c=l[u])?h.isArray(c)?[...c,f[1]]:[c,f[1]]:f[1]}a(l,n)}else t!=null&&i(n,t,s);return this}get(t,n){if(t=ne(t),t){const s=h.findKey(this,t);if(s){const o=this[s];if(!n)return o;if(n===!0)return Is(o);if(h.isFunction(n))return n.call(this,o,s);if(h.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=ne(t),t){const s=h.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||Ae(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let o=!1;function i(a){if(a=ne(a),a){const l=h.findKey(s,a);l&&(!n||Ae(s,s[l],l,n))&&(delete s[l],o=!0)}}return h.isArray(t)?t.forEach(i):i(t),o}clear(t){const n=Object.keys(this);let s=n.length,o=!1;for(;s--;){const i=n[s];(!t||Ae(this,this[i],i,t,!0))&&(delete this[i],o=!0)}return o}normalize(t){const n=this,s={};return h.forEach(this,(o,i)=>{const a=h.findKey(s,i);if(a){n[a]=ye(o),delete n[i];return}const l=t?Os(i):String(i).trim();l!==i&&delete n[i],n[l]=ye(o),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return h.forEach(this,(s,o)=>{s!=null&&s!==!1&&(n[o]=t&&h.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(o=>s.set(o)),s}static accessor(t){const s=(this[ft]=this[ft]={accessors:{}}).accessors,o=this.prototype;function i(a){const l=ne(a);s[l]||(As(o,a),s[l]=!0)}return h.isArray(t)?t.forEach(i):i(t),this}};F.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);h.reduceDescriptors(F.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});h.freezeMethods(F);function Ue(e,t){const n=this||he,s=t||n,o=F.from(s.headers);let i=s.data;return h.forEach(e,function(l){i=l.call(n,i,o.normalize(),t?t.status:void 0)}),o.normalize(),i}function tr(e){return!!(e&&e.__CANCEL__)}function re(e,t,n){C.call(this,e??"canceled",C.ERR_CANCELED,t,n),this.name="CanceledError"}h.inherits(re,C,{__CANCEL__:!0});function rr(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new C("Request failed with status code "+n.status,[C.ERR_BAD_REQUEST,C.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Us(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ps(e,t){e=e||10;const n=new Array(e),s=new Array(e);let o=0,i=0,a;return t=t!==void 0?t:1e3,function(c){const u=Date.now(),f=s[i];a||(a=u),n[o]=c,s[o]=u;let m=i,v=0;for(;m!==o;)v+=n[m++],m=m%e;if(o=(o+1)%e,o===i&&(i=(i+1)%e),u-a<t)return;const g=f&&u-f;return g?Math.round(v*1e3/g):void 0}}function Bs(e,t){let n=0,s=1e3/t,o,i;const a=(u,f=Date.now())=>{n=f,o=null,i&&(clearTimeout(i),i=null),e(...u)};return[(...u)=>{const f=Date.now(),m=f-n;m>=s?a(u,f):(o=u,i||(i=setTimeout(()=>{i=null,a(o)},s-m)))},()=>o&&a(o)]}const be=(e,t,n=3)=>{let s=0;const o=Ps(50,250);return Bs(i=>{const a=i.loaded,l=i.lengthComputable?i.total:void 0,c=a-s,u=o(c),f=a<=l;s=a;const m={loaded:a,total:l,progress:l?a/l:void 0,bytes:c,rate:u||void 0,estimated:u&&l&&f?(l-a)/u:void 0,event:i,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(m)},n)},ht=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},pt=e=>(...t)=>h.asap(()=>e(...t)),_s=U.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,U.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(U.origin),U.navigator&&/(msie|trident)/i.test(U.navigator.userAgent)):()=>!0,zs=U.hasStandardBrowserEnv?{write(e,t,n,s,o,i){const a=[e+"="+encodeURIComponent(t)];h.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),h.isString(s)&&a.push("path="+s),h.isString(o)&&a.push("domain="+o),i===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ls(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ds(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function nr(e,t,n){let s=!Ls(t);return e&&(s||n==!1)?Ds(e,t):t}const mt=e=>e instanceof F?{...e}:e;function Q(e,t){t=t||{};const n={};function s(u,f,m,v){return h.isPlainObject(u)&&h.isPlainObject(f)?h.merge.call({caseless:v},u,f):h.isPlainObject(f)?h.merge({},f):h.isArray(f)?f.slice():f}function o(u,f,m,v){if(h.isUndefined(f)){if(!h.isUndefined(u))return s(void 0,u,m,v)}else return s(u,f,m,v)}function i(u,f){if(!h.isUndefined(f))return s(void 0,f)}function a(u,f){if(h.isUndefined(f)){if(!h.isUndefined(u))return s(void 0,u)}else return s(void 0,f)}function l(u,f,m){if(m in t)return s(u,f);if(m in e)return s(void 0,u)}const c={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:l,headers:(u,f,m)=>o(mt(u),mt(f),m,!0)};return h.forEach(Object.keys({...e,...t}),function(f){const m=c[f]||o,v=m(e[f],t[f],f);h.isUndefined(v)&&m!==l||(n[f]=v)}),n}const sr=e=>{const t=Q({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:i,headers:a,auth:l}=t;t.headers=a=F.from(a),t.url=Qt(nr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let c;if(h.isFormData(n)){if(U.hasStandardBrowserEnv||U.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((c=a.getContentType())!==!1){const[u,...f]=c?c.split(";").map(m=>m.trim()).filter(Boolean):[];a.setContentType([u||"multipart/form-data",...f].join("; "))}}if(U.hasStandardBrowserEnv&&(s&&h.isFunction(s)&&(s=s(t)),s||s!==!1&&_s(t.url))){const u=o&&i&&zs.read(i);u&&a.set(o,u)}return t},Fs=typeof XMLHttpRequest<"u",Ms=Fs&&function(e){return new Promise(function(n,s){const o=sr(e);let i=o.data;const a=F.from(o.headers).normalize();let{responseType:l,onUploadProgress:c,onDownloadProgress:u}=o,f,m,v,g,d;function x(){g&&g(),d&&d(),o.cancelToken&&o.cancelToken.unsubscribe(f),o.signal&&o.signal.removeEventListener("abort",f)}let p=new XMLHttpRequest;p.open(o.method.toUpperCase(),o.url,!0),p.timeout=o.timeout;function y(){if(!p)return;const k=F.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),I={data:!l||l==="text"||l==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:k,config:e,request:p};rr(function(V){n(V),x()},function(V){s(V),x()},I),p=null}"onloadend"in p?p.onloadend=y:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(y)},p.onabort=function(){p&&(s(new C("Request aborted",C.ECONNABORTED,e,p)),p=null)},p.onerror=function(){s(new C("Network Error",C.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let A=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const I=o.transitional||Yt;o.timeoutErrorMessage&&(A=o.timeoutErrorMessage),s(new C(A,I.clarifyTimeoutError?C.ETIMEDOUT:C.ECONNABORTED,e,p)),p=null},i===void 0&&a.setContentType(null),"setRequestHeader"in p&&h.forEach(a.toJSON(),function(A,I){p.setRequestHeader(I,A)}),h.isUndefined(o.withCredentials)||(p.withCredentials=!!o.withCredentials),l&&l!=="json"&&(p.responseType=o.responseType),u&&([v,d]=be(u,!0),p.addEventListener("progress",v)),c&&p.upload&&([m,g]=be(c),p.upload.addEventListener("progress",m),p.upload.addEventListener("loadend",g)),(o.cancelToken||o.signal)&&(f=k=>{p&&(s(!k||k.type?new re(null,e,p):k),p.abort(),p=null)},o.cancelToken&&o.cancelToken.subscribe(f),o.signal&&(o.signal.aborted?f():o.signal.addEventListener("abort",f)));const j=Us(o.url);if(j&&U.protocols.indexOf(j)===-1){s(new C("Unsupported protocol "+j+":",C.ERR_BAD_REQUEST,e));return}p.send(i||null)})},$s=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,o;const i=function(u){if(!o){o=!0,l();const f=u instanceof Error?u:this.reason;s.abort(f instanceof C?f:new re(f instanceof Error?f.message:f))}};let a=t&&setTimeout(()=>{a=null,i(new C(`timeout ${t} of ms exceeded`,C.ETIMEDOUT))},t);const l=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),e=null)};e.forEach(u=>u.addEventListener("abort",i));const{signal:c}=s;return c.unsubscribe=()=>h.asap(l),c}},Ws=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,o;for(;s<n;)o=s+t,yield e.slice(s,o),s=o},qs=async function*(e,t){for await(const n of Js(e))yield*Ws(n,t)},Js=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},xt=(e,t,n,s)=>{const o=qs(e,t);let i=0,a,l=c=>{a||(a=!0,s&&s(c))};return new ReadableStream({async pull(c){try{const{done:u,value:f}=await o.next();if(u){l(),c.close();return}let m=f.byteLength;if(n){let v=i+=m;n(v)}c.enqueue(new Uint8Array(f))}catch(u){throw l(u),u}},cancel(c){return l(c),o.return()}},{highWaterMark:2})},Ee=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",or=Ee&&typeof ReadableStream=="function",Hs=Ee&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ir=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Vs=or&&ir(()=>{let e=!1;const t=new Request(U.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),yt=64*1024,We=or&&ir(()=>h.isReadableStream(new Response("").body)),je={stream:We&&(e=>e.body)};Ee&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!je[t]&&(je[t]=h.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new C(`Response type '${t}' is not supported`,C.ERR_NOT_SUPPORT,s)})})})(new Response);const Ks=async e=>{if(e==null)return 0;if(h.isBlob(e))return e.size;if(h.isSpecCompliantForm(e))return(await new Request(U.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(h.isArrayBufferView(e)||h.isArrayBuffer(e))return e.byteLength;if(h.isURLSearchParams(e)&&(e=e+""),h.isString(e))return(await Hs(e)).byteLength},Zs=async(e,t)=>{const n=h.toFiniteNumber(e.getContentLength());return n??Ks(t)},Xs=Ee&&(async e=>{let{url:t,method:n,data:s,signal:o,cancelToken:i,timeout:a,onDownloadProgress:l,onUploadProgress:c,responseType:u,headers:f,withCredentials:m="same-origin",fetchOptions:v}=sr(e);u=u?(u+"").toLowerCase():"text";let g=$s([o,i&&i.toAbortSignal()],a),d;const x=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let p;try{if(c&&Vs&&n!=="get"&&n!=="head"&&(p=await Zs(f,s))!==0){let I=new Request(t,{method:"POST",body:s,duplex:"half"}),J;if(h.isFormData(s)&&(J=I.headers.get("content-type"))&&f.setContentType(J),I.body){const[V,pe]=ht(p,be(pt(c)));s=xt(I.body,yt,V,pe)}}h.isString(m)||(m=m?"include":"omit");const y="credentials"in Request.prototype;d=new Request(t,{...v,signal:g,method:n.toUpperCase(),headers:f.normalize().toJSON(),body:s,duplex:"half",credentials:y?m:void 0});let j=await fetch(d,v);const k=We&&(u==="stream"||u==="response");if(We&&(l||k&&x)){const I={};["status","statusText","headers"].forEach(Ge=>{I[Ge]=j[Ge]});const J=h.toFiniteNumber(j.headers.get("content-length")),[V,pe]=l&&ht(J,be(pt(l),!0))||[];j=new Response(xt(j.body,yt,V,()=>{pe&&pe(),x&&x()}),I)}u=u||"text";let A=await je[h.findKey(je,u)||"text"](j,e);return!k&&x&&x(),await new Promise((I,J)=>{rr(I,J,{data:A,headers:F.from(j.headers),status:j.status,statusText:j.statusText,config:e,request:d})})}catch(y){throw x&&x(),y&&y.name==="TypeError"&&/Load failed|fetch/i.test(y.message)?Object.assign(new C("Network Error",C.ERR_NETWORK,e,d),{cause:y.cause||y}):C.from(y,y&&y.code,e,d)}}),qe={http:us,xhr:Ms,fetch:Xs};h.forEach(qe,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const gt=e=>`- ${e}`,Gs=e=>h.isFunction(e)||e===null||e===!1,ar={getAdapter:e=>{e=h.isArray(e)?e:[e];const{length:t}=e;let n,s;const o={};for(let i=0;i<t;i++){n=e[i];let a;if(s=n,!Gs(n)&&(s=qe[(a=String(n)).toLowerCase()],s===void 0))throw new C(`Unknown adapter '${a}'`);if(s)break;o[a||"#"+i]=s}if(!s){const i=Object.entries(o).map(([l,c])=>`adapter ${l} `+(c===!1?"is not supported by the environment":"is not available in the build"));let a=t?i.length>1?`since :
`+i.map(gt).join(`
`):" "+gt(i[0]):"as no adapter specified";throw new C("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return s},adapters:qe};function Pe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new re(null,e)}function bt(e){return Pe(e),e.headers=F.from(e.headers),e.data=Ue.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ar.getAdapter(e.adapter||he.adapter)(e).then(function(s){return Pe(e),s.data=Ue.call(e,e.transformResponse,s),s.headers=F.from(s.headers),s},function(s){return tr(s)||(Pe(e),s&&s.response&&(s.response.data=Ue.call(e,e.transformResponse,s.response),s.response.headers=F.from(s.response.headers))),Promise.reject(s)})}const lr="1.11.0",Ie={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ie[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const jt={};Ie.transitional=function(t,n,s){function o(i,a){return"[Axios v"+lr+"] Transitional option '"+i+"'"+a+(s?". "+s:"")}return(i,a,l)=>{if(t===!1)throw new C(o(a," has been removed"+(n?" in "+n:"")),C.ERR_DEPRECATED);return n&&!jt[a]&&(jt[a]=!0,console.warn(o(a," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,a,l):!0}};Ie.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Qs(e,t,n){if(typeof e!="object")throw new C("options must be an object",C.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let o=s.length;for(;o-- >0;){const i=s[o],a=t[i];if(a){const l=e[i],c=l===void 0||a(l,i,e);if(c!==!0)throw new C("option "+i+" must be "+c,C.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new C("Unknown option "+i,C.ERR_BAD_OPTION)}}const ge={assertOptions:Qs,validators:Ie},W=ge.validators;let G=class{constructor(t){this.defaults=t||{},this.interceptors={request:new ut,response:new ut}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const i=o.stack?o.stack.replace(/^.+\n/,""):"";try{s.stack?i&&!String(s.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+i):s.stack=i}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Q(this.defaults,n);const{transitional:s,paramsSerializer:o,headers:i}=n;s!==void 0&&ge.assertOptions(s,{silentJSONParsing:W.transitional(W.boolean),forcedJSONParsing:W.transitional(W.boolean),clarifyTimeoutError:W.transitional(W.boolean)},!1),o!=null&&(h.isFunction(o)?n.paramsSerializer={serialize:o}:ge.assertOptions(o,{encode:W.function,serialize:W.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),ge.assertOptions(n,{baseUrl:W.spelling("baseURL"),withXsrfToken:W.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let a=i&&h.merge(i.common,i[n.method]);i&&h.forEach(["delete","get","head","post","put","patch","common"],d=>{delete i[d]}),n.headers=F.concat(a,i);const l=[];let c=!0;this.interceptors.request.forEach(function(x){typeof x.runWhen=="function"&&x.runWhen(n)===!1||(c=c&&x.synchronous,l.unshift(x.fulfilled,x.rejected))});const u=[];this.interceptors.response.forEach(function(x){u.push(x.fulfilled,x.rejected)});let f,m=0,v;if(!c){const d=[bt.bind(this),void 0];for(d.unshift(...l),d.push(...u),v=d.length,f=Promise.resolve(n);m<v;)f=f.then(d[m++],d[m++]);return f}v=l.length;let g=n;for(m=0;m<v;){const d=l[m++],x=l[m++];try{g=d(g)}catch(p){x.call(this,p);break}}try{f=bt.call(this,g)}catch(d){return Promise.reject(d)}for(m=0,v=u.length;m<v;)f=f.then(u[m++],u[m++]);return f}getUri(t){t=Q(this.defaults,t);const n=nr(t.baseURL,t.url,t.allowAbsoluteUrls);return Qt(n,t.params,t.paramsSerializer)}};h.forEach(["delete","get","head","options"],function(t){G.prototype[t]=function(n,s){return this.request(Q(s||{},{method:t,url:n,data:(s||{}).data}))}});h.forEach(["post","put","patch"],function(t){function n(s){return function(i,a,l){return this.request(Q(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:i,data:a}))}}G.prototype[t]=n(),G.prototype[t+"Form"]=n(!0)});let Ys=class cr{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const s=this;this.promise.then(o=>{if(!s._listeners)return;let i=s._listeners.length;for(;i-- >0;)s._listeners[i](o);s._listeners=null}),this.promise.then=o=>{let i;const a=new Promise(l=>{s.subscribe(l),i=l}).then(o);return a.cancel=function(){s.unsubscribe(i)},a},t(function(i,a,l){s.reason||(s.reason=new re(i,a,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new cr(function(o){t=o}),cancel:t}}};function eo(e){return function(n){return e.apply(null,n)}}function to(e){return h.isObject(e)&&e.isAxiosError===!0}const Je={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Je).forEach(([e,t])=>{Je[t]=e});function dr(e){const t=new G(e),n=Ft(G.prototype.request,t);return h.extend(n,G.prototype,t,{allOwnKeys:!0}),h.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return dr(Q(e,o))},n}const N=dr(he);N.Axios=G;N.CanceledError=re;N.CancelToken=Ys;N.isCancel=tr;N.VERSION=lr;N.toFormData=Re;N.AxiosError=C;N.Cancel=N.CanceledError;N.all=function(t){return Promise.all(t)};N.spread=eo;N.isAxiosError=to;N.mergeConfig=Q;N.AxiosHeaders=F;N.formToJSON=e=>er(h.isHTMLForm(e)?new FormData(e):e);N.getAdapter=ar.getAdapter;N.HttpStatusCode=Je;N.default=N;const{Axios:ko,AxiosError:Co,CanceledError:To,isCancel:Ro,CancelToken:Eo,VERSION:Io,all:No,Cancel:Oo,isAxiosError:Ao,spread:Uo,toFormData:Po,AxiosHeaders:Bo,HttpStatusCode:_o,formToJSON:zo,getAdapter:Lo,mergeConfig:Do}=N,ro="cloud1-9gsj7t48183e5a9f",no=`https://${ro}.tcb-api.tencentcloudapi.com/web`,Xe=N.create({baseURL:no,timeout:3e4,headers:{"Content-Type":"application/json"}});Xe.interceptors.request.use(e=>{var n;const t=localStorage.getItem("admin_token");return t&&e.headers&&(e.headers.Authorization=`Bearer ${t}`),console.log("🚀 API Request:",(n=e.method)==null?void 0:n.toUpperCase(),e.url,e.data),e},e=>(console.error("❌ Request Error:",e),Promise.reject(e)));Xe.interceptors.response.use(e=>{console.log("✅ API Response:",e.config.url,e.data);const{data:t}=e;return t&&!t.success?(q.error(t.error||t.message||"请求失败"),Promise.reject(new Error(t.error||t.message||"请求失败"))):e},e=>{if(console.error("❌ Response Error:",e),!e.response)return q.error("网络连接失败，请检查网络设置"),Promise.reject(e);const{status:t,data:n}=e.response;switch(t){case 401:q.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:q.error("权限不足，无法访问");break;case 404:q.error("请求的资源不存在");break;case 500:q.error("服务器内部错误");break;default:q.error((n==null?void 0:n.error)||(n==null?void 0:n.message)||`请求失败 (${t})`)}return Promise.reject(e)});const z=async(e,t)=>{try{console.log(`🚀 调用云函数 [${e}]:`,t);const n=await Xe.post("/adminApi",{action:e,data:t});return console.log(`✅ 云函数响应 [${e}]:`,n.data),n.data}catch(n){throw console.error(`❌ 云函数错误 [${e}]:`,n),n}},so={getStats:()=>z("getDashboardStats"),getChartData:(e,t)=>z("getChartData",{type:e,timeRange:t}),getRecentActivities:()=>z("getRecentActivities")},ur={getUserList:e=>z("getUserList",e),getUserDetail:e=>z("getUserDetail",{userId:e}),updateUserStatus:(e,t)=>z("updateUserStatus",{userId:e,status:t}),getUserStats:()=>z("getUserStats")},oo={getOrderList:e=>z("getOrderList",e),getOrderDetail:e=>z("getOrderDetail",{orderId:e}),updateOrderStatus:(e,t)=>z("updateOrderStatus",{orderId:e,status:t}),getOrderStats:()=>z("getOrderStats")},io={getTransactionList:e=>z("getTransactionList",e),getWithdrawList:e=>z("getWithdrawList",e),approveWithdraw:(e,t,n)=>z("approveWithdraw",{withdrawId:e,status:t,remark:n}),getWalletStats:()=>z("getWalletStats")},{Header:ao,Sider:lo,Content:co}=Be,{Search:Ne}=wt,{Option:R}=M,uo=[{key:"dashboard",icon:r.jsx(jr,{}),label:"仪表盘"},{key:"users",icon:r.jsx(_e,{}),label:"用户管理"},{key:"orders",icon:r.jsx(vr,{}),label:"订单管理"},{key:"chat",icon:r.jsx(wr,{}),label:"聊天监控"},{key:"wallet",icon:r.jsx(Sr,{}),label:"钱包管理"},{key:"notifications",icon:r.jsx(kr,{}),label:"通知管理"},{key:"evaluations",icon:r.jsx(Cr,{}),label:"评价管理"},{key:"settings",icon:r.jsx(Tr,{}),label:"系统设置"}],fo=({children:e})=>{const[t,n]=b.useState(!1),s=_t(),o=Se();St.useToken();const i=()=>{const l=o.pathname;return l.includes("/admin/dashboard")?"dashboard":l.includes("/admin/users")?"users":l.includes("/admin/orders")?"orders":l.includes("/admin/chat")?"chat":l.includes("/admin/wallet")?"wallet":l.includes("/admin/notifications")?"notifications":l.includes("/admin/evaluations")?"evaluations":l.includes("/admin/settings")?"settings":"dashboard"},a=l=>{s(`/admin/${l}`)};return r.jsxs(Be,{style:{minHeight:"100vh"},children:[r.jsxs(lo,{trigger:null,collapsible:!0,collapsed:t,style:{background:"#1e293b"},children:[r.jsx("div",{style:{height:"64px",margin:"16px",background:"rgba(0, 212, 255, 0.1)",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",color:"#00d4ff",fontWeight:"bold",fontSize:t?"16px":"14px"},children:t?"🔺":"🔺 三角洲平台"}),r.jsx(xr,{theme:"dark",mode:"inline",selectedKeys:[i()],onClick:({key:l})=>a(l),items:uo,style:{background:"transparent"}})]}),r.jsxs(Be,{children:[r.jsxs(ao,{style:{padding:0,background:"#334155",display:"flex",alignItems:"center"},children:[r.jsx(S,{type:"text",icon:t?r.jsx(yr,{}):r.jsx(gr,{}),onClick:()=>n(!t),style:{fontSize:"16px",width:64,height:64,color:"#00d4ff"}}),r.jsx("div",{style:{color:"#f8fafc",fontSize:"18px",fontWeight:"bold"},children:"三角洲任务平台管理系统"})]}),r.jsx(co,{style:{margin:"24px 16px",padding:24,minHeight:280,background:"#0f172a",borderRadius:"8px"},children:e})]})]})},ho=()=>r.jsxs("div",{style:{color:"#00d4ff",padding:"20px",fontSize:"24px",background:"#0f172a",minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"},children:[r.jsx("div",{children:"🎉 三角洲任务平台管理系统"}),r.jsx("div",{style:{fontSize:"18px",marginTop:"20px"},children:"系统运行正常！"}),r.jsx("div",{style:{fontSize:"14px",marginTop:"10px",color:"#cbd5e1"},children:"路由测试页面 - 访问 /dashboard 查看仪表盘"})]}),po=()=>{const[e,t]=b.useState("today"),[n,s]=b.useState(!0),[o,i]=b.useState({onlineUsers:0,activeOrders:0,todayRevenue:0,systemLoad:0}),[a,l]=b.useState({totalUsers:0,totalOrders:0,totalRevenue:0,newUsersToday:0}),c={users:[{period:"00:00",value:120},{period:"04:00",value:89},{period:"08:00",value:156},{period:"12:00",value:234},{period:"16:00",value:189},{period:"20:00",value:167},{period:"24:00",value:145}],orders:[{period:"周一",value:45},{period:"周二",value:67},{period:"周三",value:89},{period:"周四",value:78},{period:"周五",value:95},{period:"周六",value:123},{period:"周日",value:89}],revenue:[{period:"1月",value:45600},{period:"2月",value:52300},{period:"3月",value:48900},{period:"4月",value:61200},{period:"5月",value:58700},{period:"6月",value:67800}]},u=async()=>{var g,d,x;try{s(!0);const[p,y,j,k]=await Promise.all([so.getStats().catch(()=>({success:!1,data:null})),ur.getUserStats().catch(()=>({success:!1,data:null})),oo.getOrderStats().catch(()=>({success:!1,data:null})),io.getWalletStats().catch(()=>({success:!1,data:null}))]);p.success&&p.data&&l({totalUsers:p.data.totalUsers||1234,totalOrders:p.data.totalOrders||567,totalRevenue:p.data.totalRevenue||89012,newUsersToday:p.data.newUsersToday||23}),i({onlineUsers:((g=y.data)==null?void 0:g.onlineUsers)||156,activeOrders:((d=j.data)==null?void 0:d.activeOrders)||89,todayRevenue:((x=k.data)==null?void 0:x.todayRevenue)||12580.5,systemLoad:Math.floor(Math.random()*40)+50})}catch(p){console.error("获取仪表盘数据失败:",p),q.error("获取数据失败，显示模拟数据"),l({totalUsers:1234,totalOrders:567,totalRevenue:89012,newUsersToday:23}),i({onlineUsers:156,activeOrders:89,todayRevenue:12580.5,systemLoad:68})}finally{s(!1)}};b.useEffect(()=>{u()},[]),b.useEffect(()=>{const g=setInterval(()=>{u()},3e4);return()=>clearInterval(g)},[]);const f=({data:g,color:d,height:x=60})=>r.jsx("div",{style:{display:"flex",alignItems:"end",height:`${x}px`,gap:"4px"},children:g.map((p,y)=>r.jsxs("div",{style:{flex:1,display:"flex",flexDirection:"column",alignItems:"center"},children:[r.jsx("div",{style:{width:"100%",backgroundColor:d,height:`${p.value/Math.max(...g.map(j=>j.value))*(x-20)}px`,borderRadius:"2px 2px 0 0",marginBottom:"4px",opacity:.8,transition:"all 0.3s ease"}}),r.jsx("div",{style:{fontSize:"10px",color:"#94a3b8",textAlign:"center"},children:p.period})]},y))}),m=({data:g,color:d,height:x=60})=>{const p=Math.max(...g.map(j=>j.value)),y=g.map((j,k)=>{const A=k/(g.length-1)*100,I=100-j.value/p*80;return`${A},${I}`}).join(" ");return r.jsxs("div",{style:{height:`${x}px`,position:"relative"},children:[r.jsxs("svg",{width:"100%",height:"100%",style:{position:"absolute"},children:[r.jsx("polyline",{fill:"none",stroke:d,strokeWidth:"2",points:y,style:{filter:"drop-shadow(0 0 4px rgba(0, 212, 255, 0.3))"}}),g.map((j,k)=>{const A=k/(g.length-1)*100,I=100-j.value/p*80;return r.jsx("circle",{cx:`${A}%`,cy:`${I}%`,r:"3",fill:d,style:{filter:"drop-shadow(0 0 2px rgba(0, 212, 255, 0.5))"}},k)})]}),r.jsx("div",{style:{display:"flex",justifyContent:"space-between",position:"absolute",bottom:0,width:"100%"},children:g.map((j,k)=>r.jsx("div",{style:{fontSize:"10px",color:"#94a3b8",textAlign:"center"},children:j.period},k))})]})},v=({percentage:g,color:d,size:x=80})=>{const p=(x-8)/2,y=2*Math.PI*p,j=y,k=y-g/100*y;return r.jsxs("div",{style:{position:"relative",width:x,height:x},children:[r.jsxs("svg",{width:x,height:x,style:{transform:"rotate(-90deg)"},children:[r.jsx("circle",{cx:x/2,cy:x/2,r:p,stroke:"#334155",strokeWidth:"4",fill:"transparent"}),r.jsx("circle",{cx:x/2,cy:x/2,r:p,stroke:d,strokeWidth:"4",fill:"transparent",strokeDasharray:j,strokeDashoffset:k,style:{transition:"stroke-dashoffset 0.5s ease-in-out",filter:`drop-shadow(0 0 6px ${d}40)`}})]}),r.jsxs("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:"#e2e8f0",fontSize:"14px",fontWeight:"bold"},children:[g,"%"]})]})};return r.jsxs("div",{style:{padding:"24px"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[r.jsx("h2",{style:{color:"#e2e8f0",margin:0},children:"📊 管理仪表盘"}),r.jsxs("div",{style:{display:"flex",gap:"8px"},children:[r.jsx(S,{type:e==="today"?"primary":"default",size:"small",onClick:()=>t("today"),style:e==="today"?{background:"#00d4ff",borderColor:"#00d4ff"}:{},children:"今日"}),r.jsx(S,{type:e==="week"?"primary":"default",size:"small",onClick:()=>t("week"),style:e==="week"?{background:"#00d4ff",borderColor:"#00d4ff"}:{},children:"本周"}),r.jsx(S,{type:e==="month"?"primary":"default",size:"small",onClick:()=>t("month"),style:e==="month"?{background:"#00d4ff",borderColor:"#00d4ff"}:{},children:"本月"})]})]}),r.jsxs(_,{gutter:[16,16],style:{marginBottom:"24px"},children:[r.jsx(w,{xs:24,sm:12,md:6,children:r.jsxs(T,{style:{background:"linear-gradient(135deg, #1e293b 0%, #334155 100%)",borderColor:"#334155",color:"#e2e8f0",position:"relative",overflow:"hidden"},children:[r.jsx("div",{style:{position:"absolute",top:0,right:0,width:"60px",height:"60px",background:"rgba(0, 212, 255, 0.1)",borderRadius:"50%",transform:"translate(20px, -20px)"}}),r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"在线用户"}),value:n?0:o.onlineUsers,valueStyle:{color:"#00d4ff"},prefix:"👥",suffix:r.jsx("span",{style:{fontSize:"12px",color:"#52c41a"},children:"↗ 实时"}),loading:n}),r.jsxs("div",{style:{marginTop:"8px",fontSize:"12px",color:"#94a3b8"},children:["实时更新 • 总用户：",a.totalUsers]})]})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsxs(T,{style:{background:"linear-gradient(135deg, #1e293b 0%, #334155 100%)",borderColor:"#334155",color:"#e2e8f0",position:"relative",overflow:"hidden"},children:[r.jsx("div",{style:{position:"absolute",top:0,right:0,width:"60px",height:"60px",background:"rgba(82, 196, 26, 0.1)",borderRadius:"50%",transform:"translate(20px, -20px)"}}),r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"活跃订单"}),value:n?0:o.activeOrders,valueStyle:{color:"#52c41a"},prefix:"📦",suffix:r.jsx("span",{style:{fontSize:"12px",color:"#52c41a"},children:"↗ 进行中"}),loading:n}),r.jsxs("div",{style:{marginTop:"8px",fontSize:"12px",color:"#94a3b8"},children:["总订单：",a.totalOrders," • 完成率 94.2%"]})]})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsxs(T,{style:{background:"linear-gradient(135deg, #1e293b 0%, #334155 100%)",borderColor:"#334155",color:"#e2e8f0",position:"relative",overflow:"hidden"},children:[r.jsx("div",{style:{position:"absolute",top:0,right:0,width:"60px",height:"60px",background:"rgba(250, 173, 20, 0.1)",borderRadius:"50%",transform:"translate(20px, -20px)"}}),r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"今日收入"}),value:n?0:o.todayRevenue,precision:2,valueStyle:{color:"#faad14"},prefix:"💰",suffix:r.jsx("span",{style:{fontSize:"12px"},children:"元"}),loading:n}),r.jsxs("div",{style:{marginTop:"8px",fontSize:"12px",color:"#94a3b8"},children:["总收入：",a.totalRevenue.toLocaleString(),"元"]})]})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsxs(T,{style:{background:"linear-gradient(135deg, #1e293b 0%, #334155 100%)",borderColor:"#334155",color:"#e2e8f0",position:"relative",overflow:"hidden"},children:[r.jsx("div",{style:{position:"absolute",top:0,right:0,width:"60px",height:"60px",background:"rgba(247, 89, 171, 0.1)",borderRadius:"50%",transform:"translate(20px, -20px)"}}),r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"新增用户"}),value:n?0:a.newUsersToday,valueStyle:{color:"#f759ab"},prefix:"🆕",suffix:"人",loading:n}),r.jsxs("div",{style:{marginTop:"8px",fontSize:"12px",color:"#94a3b8"},children:["今日新增 • 系统负载：",o.systemLoad,"%"]})]})})]}),r.jsxs(_,{gutter:[16,16],style:{marginBottom:"24px"},children:[r.jsx(w,{xs:24,lg:8,children:r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[r.jsx("h3",{style:{color:"#e2e8f0",margin:0},children:"📈 用户活跃度"}),r.jsx(L,{color:"#00d4ff",children:"24小时"})]}),r.jsx(m,{data:c.users,color:"#00d4ff",height:120}),r.jsx("div",{style:{marginTop:"12px",fontSize:"12px",color:"#94a3b8"},children:"峰值时段：12:00-16:00 • 平均在线：156人"})]})}),r.jsx(w,{xs:24,lg:8,children:r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[r.jsx("h3",{style:{color:"#e2e8f0",margin:0},children:"📊 订单趋势"}),r.jsx(L,{color:"#52c41a",children:"本周"})]}),r.jsx(f,{data:c.orders,color:"#52c41a",height:120}),r.jsx("div",{style:{marginTop:"12px",fontSize:"12px",color:"#94a3b8"},children:"周末订单量最高 • 平均每日：78单"})]})}),r.jsx(w,{xs:24,lg:8,children:r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[r.jsx("h3",{style:{color:"#e2e8f0",margin:0},children:"💰 收入分析"}),r.jsx(L,{color:"#faad14",children:"近6月"})]}),r.jsx(f,{data:c.revenue,color:"#faad14",height:120}),r.jsx("div",{style:{marginTop:"12px",fontSize:"12px",color:"#94a3b8"},children:"持续增长趋势 • 月均收入：55.8K"})]})})]}),r.jsxs(_,{gutter:[16,16],children:[r.jsx(w,{xs:24,lg:12,children:r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsx("h3",{style:{color:"#e2e8f0",marginBottom:"20px"},children:"🖥️ 系统监控"}),r.jsxs(_,{gutter:[16,16],children:[r.jsx(w,{span:8,children:r.jsxs("div",{style:{textAlign:"center"},children:[r.jsx(v,{percentage:o.systemLoad,color:"#00d4ff"}),r.jsx("div",{style:{marginTop:"8px",fontSize:"12px",color:"#94a3b8"},children:"CPU使用率"})]})}),r.jsx(w,{span:8,children:r.jsxs("div",{style:{textAlign:"center"},children:[r.jsx(v,{percentage:76,color:"#52c41a"}),r.jsx("div",{style:{marginTop:"8px",fontSize:"12px",color:"#94a3b8"},children:"内存使用率"})]})}),r.jsx(w,{span:8,children:r.jsxs("div",{style:{textAlign:"center"},children:[r.jsx(v,{percentage:45,color:"#faad14"}),r.jsx("div",{style:{marginTop:"8px",fontSize:"12px",color:"#94a3b8"},children:"磁盘使用率"})]})})]}),r.jsxs("div",{style:{marginTop:"16px",padding:"12px",background:"#334155",borderRadius:"6px"},children:[r.jsx("div",{style:{fontSize:"12px",color:"#94a3b8",marginBottom:"8px"},children:"系统状态"}),r.jsx("div",{style:{color:"#52c41a",fontSize:"14px"},children:"✅ 所有服务运行正常"}),r.jsx("div",{style:{color:"#52c41a",fontSize:"14px"},children:"✅ 数据库连接正常"}),r.jsx("div",{style:{color:"#52c41a",fontSize:"14px"},children:"✅ API响应正常"})]})]})}),r.jsx(w,{xs:24,lg:12,children:r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsx("h3",{style:{color:"#e2e8f0",marginBottom:"20px"},children:"⚡ 快速操作"}),r.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(2, 1fr)",gap:"12px"},children:[r.jsx(S,{style:{height:"60px",background:"linear-gradient(135deg, #00d4ff20 0%, #00d4ff10 100%)",borderColor:"#00d4ff",color:"#00d4ff"},children:r.jsxs("div",{children:[r.jsx("div",{children:"👥 用户管理"}),r.jsx("div",{style:{fontSize:"12px",opacity:.7},children:"查看用户列表"})]})}),r.jsx(S,{style:{height:"60px",background:"linear-gradient(135deg, #52c41a20 0%, #52c41a10 100%)",borderColor:"#52c41a",color:"#52c41a"},children:r.jsxs("div",{children:[r.jsx("div",{children:"📦 订单管理"}),r.jsx("div",{style:{fontSize:"12px",opacity:.7},children:"处理订单"})]})}),r.jsx(S,{style:{height:"60px",background:"linear-gradient(135deg, #faad1420 0%, #faad1410 100%)",borderColor:"#faad14",color:"#faad14"},children:r.jsxs("div",{children:[r.jsx("div",{children:"💰 财务管理"}),r.jsx("div",{style:{fontSize:"12px",opacity:.7},children:"查看收支"})]})}),r.jsx(S,{style:{height:"60px",background:"linear-gradient(135deg, #f759ab20 0%, #f759ab10 100%)",borderColor:"#f759ab",color:"#f759ab"},children:r.jsxs("div",{children:[r.jsx("div",{children:"📊 数据分析"}),r.jsx("div",{style:{fontSize:"12px",opacity:.7},children:"查看报表"})]})})]}),r.jsxs("div",{style:{marginTop:"16px",padding:"12px",background:"#334155",borderRadius:"6px"},children:[r.jsx("div",{style:{fontSize:"12px",color:"#94a3b8",marginBottom:"8px"},children:"今日待办"}),r.jsx("div",{style:{color:"#e2e8f0",fontSize:"14px",marginBottom:"4px"},children:"🔔 3个提现申请待审核"}),r.jsx("div",{style:{color:"#e2e8f0",fontSize:"14px",marginBottom:"4px"},children:"📝 5个用户反馈待处理"}),r.jsx("div",{style:{color:"#e2e8f0",fontSize:"14px"},children:"⚠️ 2个异常订单需关注"})]})]})})]})]})},mo=()=>{const[e,t]=b.useState(!0),[n,s]=b.useState([]),[o,i]=b.useState({current:1,pageSize:10,total:0}),[a,l]=b.useState(""),[c,u]=b.useState("all"),f=async(g=1,d=10)=>{try{t(!0);const x=await ur.getUserList({page:g,limit:d,search:a||void 0,status:c!=="all"?c:void 0});if(x.success&&x.data)s(x.data.users||[]),i({current:g,pageSize:d,total:x.data.total||0});else{const p=[{key:"1",id:"U001",openid:"ox1234567890abcdef",nickName:"游戏达人",gameNickName:"三角洲战士",avatarUrl:"/placeholder.svg?height=40&width=40",phone:"13800138001",realName:"张三",isVerified:!0,status:"active",balance:156.8,creditScore:85,orderCount:12,createTime:"2024-01-15",updateTime:"2024-01-20 14:30"},{key:"2",id:"U002",openid:"ox2234567890abcdef",nickName:"陪玩小助手",gameNickName:"专业陪练",avatarUrl:"/placeholder.svg?height=40&width=40",phone:"13800138002",realName:"李四",isVerified:!1,status:"active",balance:89.5,creditScore:92,orderCount:8,createTime:"2024-01-10",updateTime:"2024-01-18 09:15"},{key:"3",id:"U003",openid:"ox3234567890abcdef",nickName:"游戏新手",gameNickName:"学习中",avatarUrl:"/placeholder.svg?height=40&width=40",phone:"",realName:"",isVerified:!1,status:"banned",balance:0,creditScore:45,orderCount:2,createTime:"2024-01-05",updateTime:"2024-01-20 16:45"}];s(p),i({current:1,pageSize:10,total:p.length}),q.warning("API连接失败，显示模拟数据")}}catch(x){console.error("获取用户数据失败:",x),q.error("获取用户数据失败")}finally{t(!1)}};b.useEffect(()=>{f()},[a,c]);const m=g=>{f(g.current,g.pageSize)},v=[{title:"用户信息",key:"userInfo",render:g=>{var d;return r.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[r.jsx(Z,{src:g.avatarUrl,style:{backgroundColor:"#00d4ff",marginRight:12},children:((d=g.nickName)==null?void 0:d.charAt(0))||"用"}),r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0",fontWeight:"bold"},children:g.nickName||"未设置昵称"}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["游戏昵称：",g.gameNickName||"未设置"]}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["ID: ",g.id]})]})]})}},{title:"联系方式",key:"contact",render:g=>r.jsxs("div",{children:[r.jsxs("div",{style:{color:"#e2e8f0"},children:["📱 ",g.phone||"未绑定手机"]}),g.realName&&r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["实名：",g.realName]})]})},{title:"认证状态",key:"verification",render:g=>r.jsx("div",{children:r.jsx(L,{color:g.isVerified?"#52c41a":"#faad14",children:g.isVerified?"✅ 已实名":"⏳ 未实名"})})},{title:"账户状态",dataIndex:"status",key:"status",render:g=>{const d={active:{color:"#52c41a",text:"🟢 正常"},banned:{color:"#ff4d4f",text:"🔴 已封禁"},inactive:{color:"#faad14",text:"🟡 非活跃"}},x=d[g]||d.active;return r.jsx(L,{color:x.color,children:x.text})}},{title:"账户信息",key:"accountInfo",render:g=>r.jsxs("div",{children:[r.jsxs("div",{style:{color:"#e2e8f0"},children:["💰 余额：¥",g.balance.toFixed(2)]}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["📊 信用分：",g.creditScore,"/100"]}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["📋 订单数：",g.orderCount]})]})},{title:"注册时间",dataIndex:"createTime",key:"createTime"},{title:"最后活跃",dataIndex:"updateTime",key:"updateTime"},{title:"操作",key:"action",render:g=>r.jsxs(P,{size:"middle",children:[r.jsx(S,{type:"link",size:"small",style:{color:"#00d4ff"},children:"查看详情"}),r.jsx(S,{type:"link",size:"small",style:{color:"#faad14"},children:"编辑信息"}),g.status==="active"?r.jsx(S,{type:"link",size:"small",style:{color:"#ff4d4f"},children:"封禁用户"}):r.jsx(S,{type:"link",size:"small",style:{color:"#52c41a"},children:"解除封禁"})]})}];return r.jsxs("div",{children:[r.jsxs("h1",{style:{color:"#00d4ff",marginBottom:"24px",fontSize:"28px"},children:[r.jsx(_e,{style:{marginRight:"12px"}}),"👥 用户管理"]}),r.jsxs(_,{gutter:[16,16],style:{marginBottom:"24px"},children:[r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"总用户数"}),value:1234,valueStyle:{color:"#00d4ff"},prefix:"👥"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"活跃用户"}),value:987,valueStyle:{color:"#52c41a"},prefix:"🟢"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"实名用户"}),value:856,valueStyle:{color:"#faad14"},prefix:"✅"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"今日新增"}),value:23,valueStyle:{color:"#f759ab"},prefix:"🆕"})})})]}),r.jsxs(T,{title:r.jsx("span",{style:{color:"#00d4ff"},children:"📋 用户列表"}),style:{background:"#1e293b",borderColor:"#334155"},extra:r.jsxs(P,{children:[r.jsx(S,{type:"primary",icon:r.jsx(_e,{}),children:"添加用户"}),r.jsx(S,{style:{background:"#334155",borderColor:"#475569",color:"#e2e8f0"},children:"导出数据"})]}),children:[r.jsxs("div",{style:{marginBottom:"16px",display:"flex",gap:"12px",alignItems:"center"},children:[r.jsx(wt.Search,{placeholder:"搜索用户昵称...",value:a,onChange:g=>l(g.target.value),style:{width:300},allowClear:!0}),r.jsxs(M,{value:c,onChange:u,style:{width:120},children:[r.jsx(M.Option,{value:"all",children:"全部状态"}),r.jsx(M.Option,{value:"active",children:"正常"}),r.jsx(M.Option,{value:"inactive",children:"停用"}),r.jsx(M.Option,{value:"banned",children:"封禁"})]})]}),r.jsx(ae,{columns:v,dataSource:n,loading:e,pagination:{...o,showSizeChanger:!0,showQuickJumper:!0,showTotal:(g,d)=>`第 ${d[0]}-${d[1]} 条，共 ${g} 条记录`},onChange:m,style:{background:"transparent"}})]})]})},xo=()=>{const e=[{key:"1",_id:"ORD001",orderNo:"TJZ20240120001",title:"三角洲行动陪玩",content:"需要一位技术好的玩家带我上分，要求KD比1.5以上",reward:50,customerId:"U001",customerName:"游戏达人",accepterId:"U002",accepterName:"专业陪练",platformType:"pc",serviceType:"duration",duration:2,rounds:null,tags:["上分","技术流","PC端"],orderType:"immediate",status:"in_progress",createTime:"2024-01-20 10:30:00",updateTime:"2024-01-20 14:30:00",startTime:"2024-01-20 14:00:00",endTime:null,evaluation:null},{key:"2",_id:"ORD002",orderNo:"TJZ20240120002",title:"休闲娱乐陪玩",content:"想找个人一起玩游戏聊天，轻松愉快就好",reward:30,customerId:"U003",customerName:"游戏新手",accepterId:null,accepterName:null,platformType:"mobile",serviceType:"rounds",duration:null,rounds:5,tags:["休闲","聊天","手机端"],orderType:"scheduled",status:"pending",createTime:"2024-01-20 09:15:00",updateTime:"2024-01-20 09:15:00",startTime:null,endTime:null,evaluation:null},{key:"3",_id:"ORD003",orderNo:"TJZ20240119001",title:"竞技模式陪练",content:"需要高手带我练习竞技模式，提升技术水平",reward:80,customerId:"U001",customerName:"游戏达人",accepterId:"U002",accepterName:"专业陪练",platformType:"pc",serviceType:"duration",duration:3,rounds:null,tags:["竞技","练习","高手"],orderType:"immediate",status:"completed",createTime:"2024-01-19 15:20:00",updateTime:"2024-01-19 20:30:00",startTime:"2024-01-19 16:00:00",endTime:"2024-01-19 19:00:00",evaluation:{customerRating:5,accepterRating:5,customerComment:"非常专业，技术很好！",accepterComment:"客户很配合，愉快的合作！"}}],t=[{title:"订单信息",key:"orderInfo",render:n=>r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0",fontWeight:"bold",marginBottom:"4px"},children:n.title}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px",marginBottom:"2px"},children:["订单号：",n.orderNo]}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["ID：",n._id]})]})},{title:"服务详情",key:"serviceInfo",render:n=>r.jsxs("div",{children:[r.jsxs("div",{style:{color:"#e2e8f0",marginBottom:"4px"},children:["💰 ¥",n.reward.toFixed(2)]}),r.jsx("div",{style:{color:"#94a3b8",fontSize:"12px",marginBottom:"2px"},children:n.platformType==="pc"?"🖥️ PC端":"📱 手机端"}),r.jsx("div",{style:{color:"#94a3b8",fontSize:"12px"},children:n.serviceType==="duration"?`⏰ ${n.duration}小时`:`🎮 ${n.rounds}局`})]})},{title:"发布者",key:"customer",render:n=>r.jsxs("div",{style:{color:"#e2e8f0"},children:["👤 ",n.customerName]})},{title:"接单者",key:"accepter",render:n=>r.jsx("div",{style:{color:"#e2e8f0"},children:n.accepterName?`🎯 ${n.accepterName}`:"⏳ 待接单"})},{title:"订单状态",dataIndex:"status",key:"status",render:n=>{const s={pending:{color:"#faad14",text:"⏳ 待接单"},accepted:{color:"#52c41a",text:"✅ 已接单"},in_progress:{color:"#1890ff",text:"🔄 进行中"},completed:{color:"#52c41a",text:"✅ 已完成"},cancelled:{color:"#ff4d4f",text:"❌ 已取消"}},o=s[n]||s.pending;return r.jsx(L,{color:o.color,children:o.text})}},{title:"标签",key:"tags",render:n=>r.jsxs("div",{children:[n.tags.slice(0,2).map((s,o)=>r.jsx(L,{style:{marginBottom:"2px",fontSize:"12px"},children:s},o)),n.tags.length>2&&r.jsxs(L,{style:{color:"#94a3b8",fontSize:"12px"},children:["+",n.tags.length-2]})]})},{title:"创建时间",dataIndex:"createTime",key:"createTime"},{title:"操作",key:"action",render:n=>r.jsxs(P,{size:"middle",children:[r.jsx(S,{type:"link",size:"small",style:{color:"#00d4ff"},children:"查看详情"}),r.jsx(S,{type:"link",size:"small",style:{color:"#faad14"},children:"编辑订单"}),n.status==="pending"&&r.jsx(S,{type:"link",size:"small",style:{color:"#ff4d4f"},children:"取消订单"})]})}];return r.jsxs("div",{style:{padding:"24px"},children:[r.jsx("h2",{style:{color:"#e2e8f0",marginBottom:"24px"},children:"📋 订单管理"}),r.jsxs(_,{gutter:[16,16],style:{marginBottom:"24px"},children:[r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"总订单数"}),value:2456,valueStyle:{color:"#00d4ff"},prefix:"📋"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"进行中"}),value:156,valueStyle:{color:"#1890ff"},prefix:"🔄"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"已完成"}),value:1987,valueStyle:{color:"#52c41a"},prefix:"✅"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"今日订单"}),value:45,valueStyle:{color:"#f759ab"},prefix:"🆕"})})})]}),r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",marginBottom:"16px"},children:r.jsxs(_,{gutter:[16,16],align:"middle",children:[r.jsx(w,{xs:24,sm:8,children:r.jsx(Ne,{placeholder:"搜索订单号、标题、用户名",style:{width:"100%"}})}),r.jsx(w,{xs:12,sm:4,children:r.jsxs(M,{defaultValue:"all",style:{width:"100%"},children:[r.jsx(R,{value:"all",children:"全部状态"}),r.jsx(R,{value:"pending",children:"待接单"}),r.jsx(R,{value:"accepted",children:"已接单"}),r.jsx(R,{value:"in_progress",children:"进行中"}),r.jsx(R,{value:"completed",children:"已完成"}),r.jsx(R,{value:"cancelled",children:"已取消"})]})}),r.jsx(w,{xs:12,sm:4,children:r.jsxs(M,{defaultValue:"all",style:{width:"100%"},children:[r.jsx(R,{value:"all",children:"全部平台"}),r.jsx(R,{value:"pc",children:"PC端"}),r.jsx(R,{value:"mobile",children:"手机端"})]})}),r.jsx(w,{xs:24,sm:8,children:r.jsxs(P,{children:[r.jsx(S,{type:"primary",style:{background:"#00d4ff",borderColor:"#00d4ff"},children:"🔍 搜索"}),r.jsx(S,{children:"🔄 重置"}),r.jsx(S,{type:"primary",style:{background:"#52c41a",borderColor:"#52c41a"},children:"📊 导出数据"})]})})]})}),r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[r.jsx("h3",{style:{color:"#e2e8f0",margin:0},children:"📋 订单列表"}),r.jsx(P,{children:r.jsx(S,{type:"primary",style:{background:"#00d4ff",borderColor:"#00d4ff"},children:"🔄 刷新数据"})})]}),r.jsx(ae,{columns:t,dataSource:e,pagination:{total:2456,pageSize:10,current:1,showSizeChanger:!0,showQuickJumper:!0,showTotal:(n,s)=>`第 ${s[0]}-${s[1]} 条，共 ${n} 条`},style:{background:"transparent"},className:"dark-table"})]})]})},yo=()=>{const[e,t]=b.useState(null),[n,s]=b.useState(""),o=[{key:"1",_id:"CHAT001",orderNo:"TJZ20240120001",orderDbId:"ORD001",customerId:"U001",accepterId:"U002",customerInfo:{nickName:"游戏达人",avatarUrl:"/placeholder.svg?height=40&width=40"},accepterInfo:{nickName:"专业陪练",avatarUrl:"/placeholder.svg?height=40&width=40"},orderInfo:{title:"三角洲行动陪玩",reward:50,status:"in_progress"},lastMessage:{content:"好的，我们开始游戏吧！",createTime:"2024-01-20 14:30:00",senderInfo:{nickName:"专业陪练"}},status:"active",createTime:"2024-01-20 14:00:00",updateTime:"2024-01-20 14:30:00",messageCount:25,unreadCount:0},{key:"2",_id:"CHAT002",orderNo:"TJZ20240120002",orderDbId:"ORD002",customerId:"U003",accepterId:"U004",customerInfo:{nickName:"游戏新手",avatarUrl:"/placeholder.svg?height=40&width=40"},accepterInfo:{nickName:"温柔陪玩",avatarUrl:"/placeholder.svg?height=40&width=40"},orderInfo:{title:"休闲娱乐陪玩",reward:30,status:"completed"},lastMessage:{content:"谢谢你的陪伴，很开心！",createTime:"2024-01-20 12:45:00",senderInfo:{nickName:"游戏新手"}},status:"closed",createTime:"2024-01-20 10:00:00",updateTime:"2024-01-20 12:45:00",messageCount:18,unreadCount:0},{key:"3",_id:"CHAT003",orderNo:"TJZ20240120003",orderDbId:"ORD003",customerId:"U005",accepterId:"U006",customerInfo:{nickName:"竞技玩家",avatarUrl:"/placeholder.svg?height=40&width=40"},accepterInfo:{nickName:"高手带飞",avatarUrl:"/placeholder.svg?height=40&width=40"},orderInfo:{title:"竞技模式陪练",reward:80,status:"in_progress"},lastMessage:{content:"这局打得不错，继续保持！",createTime:"2024-01-20 15:20:00",senderInfo:{nickName:"高手带飞"}},status:"active",createTime:"2024-01-20 15:00:00",updateTime:"2024-01-20 15:20:00",messageCount:12,unreadCount:2}],i=[{_id:"MSG001",chatRoomId:"CHAT001",senderId:"U001",senderInfo:{nickName:"游戏达人",avatarUrl:"/placeholder.svg?height=40&width=40"},type:"text",content:"你好，我想找个人陪我玩三角洲行动",createTime:"2024-01-20 14:00:00"},{_id:"MSG002",chatRoomId:"CHAT001",senderId:"U002",senderInfo:{nickName:"专业陪练",avatarUrl:"/placeholder.svg?height=40&width=40"},type:"text",content:"没问题！我KD比1.8，可以带你上分",createTime:"2024-01-20 14:02:00"},{_id:"MSG003",chatRoomId:"CHAT001",senderId:"U001",senderInfo:{nickName:"游戏达人",avatarUrl:"/placeholder.svg?height=40&width=40"},type:"text",content:"太好了！我们什么时候开始？",createTime:"2024-01-20 14:05:00"},{_id:"MSG004",chatRoomId:"CHAT001",senderId:"U002",senderInfo:{nickName:"专业陪练",avatarUrl:"/placeholder.svg?height=40&width=40"},type:"text",content:"现在就可以，我已经在线了",createTime:"2024-01-20 14:10:00"},{_id:"MSG005",chatRoomId:"CHAT001",senderId:"U002",senderInfo:{nickName:"专业陪练",avatarUrl:"/placeholder.svg?height=40&width=40"},type:"system",content:"订单已开始，祝您游戏愉快！",createTime:"2024-01-20 14:15:00"}],a=[{title:"聊天室信息",key:"roomInfo",render:c=>{var u;return r.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[r.jsxs("div",{style:{position:"relative",marginRight:"12px"},children:[r.jsx(Z,{src:c.customerInfo.avatarUrl,style:{backgroundColor:"#00d4ff"},children:((u=c.customerInfo.nickName)==null?void 0:u.charAt(0))||"用"}),c.status==="active"&&r.jsx("div",{style:{position:"absolute",bottom:0,right:0,width:"8px",height:"8px",backgroundColor:"#52c41a",borderRadius:"50%",border:"2px solid #1e293b"}})]}),r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0",fontWeight:"bold",marginBottom:"2px"},children:c.orderInfo.title}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["订单号：",c.orderNo]}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["💰 ¥",c.orderInfo.reward.toFixed(2)]})]})]})}},{title:"参与用户",key:"participants",render:c=>r.jsxs("div",{children:[r.jsxs("div",{style:{color:"#e2e8f0",marginBottom:"4px"},children:["👤 发布者：",c.customerInfo.nickName]}),r.jsxs("div",{style:{color:"#e2e8f0"},children:["🎯 接单者：",c.accepterInfo.nickName]})]})},{title:"最后消息",key:"lastMessage",render:c=>r.jsx("div",{children:c.lastMessage?r.jsxs(r.Fragment,{children:[r.jsx("div",{style:{color:"#e2e8f0",marginBottom:"2px"},children:c.lastMessage.content.length>20?c.lastMessage.content.substring(0,20)+"...":c.lastMessage.content}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:[c.lastMessage.senderInfo.nickName," · ",c.lastMessage.createTime]})]}):r.jsx("div",{style:{color:"#94a3b8"},children:"暂无消息"})})},{title:"状态",key:"status",render:c=>r.jsxs("div",{children:[r.jsx(L,{color:c.status==="active"?"#52c41a":"#94a3b8",children:c.status==="active"?"🟢 活跃":"⚪ 已关闭"}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px",marginTop:"4px"},children:["💬 ",c.messageCount," 条消息"]}),c.unreadCount>0&&r.jsxs("div",{style:{color:"#ff4d4f",fontSize:"12px"},children:["🔴 ",c.unreadCount," 条未读"]})]})},{title:"创建时间",dataIndex:"createTime",key:"createTime"},{title:"操作",key:"action",render:c=>r.jsxs(P,{size:"middle",children:[r.jsx(S,{type:"link",size:"small",style:{color:"#00d4ff"},onClick:()=>t(c),children:"查看消息"}),r.jsx(S,{type:"link",size:"small",style:{color:"#faad14"},children:"导出记录"}),c.status==="active"&&r.jsx(S,{type:"link",size:"small",style:{color:"#ff4d4f"},children:"关闭聊天室"})]})}],l=()=>{if(!e)return r.jsx("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"400px",color:"#94a3b8"},children:"请选择一个聊天室查看消息记录"});const c=i.filter(u=>u.chatRoomId===e._id);return r.jsx("div",{style:{height:"400px",overflowY:"auto",padding:"16px"},children:c.map(u=>{var f;return r.jsx("div",{style:{marginBottom:"16px"},children:r.jsxs("div",{style:{display:"flex",alignItems:"flex-start"},children:[r.jsx(Z,{src:u.senderInfo.avatarUrl,size:"small",style:{backgroundColor:"#00d4ff",marginRight:"8px"},children:((f=u.senderInfo.nickName)==null?void 0:f.charAt(0))||"用"}),r.jsxs("div",{style:{flex:1},children:[r.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"4px"},children:[r.jsx("span",{style:{color:"#e2e8f0",fontSize:"14px",fontWeight:"bold"},children:u.senderInfo.nickName}),r.jsx("span",{style:{color:"#94a3b8",fontSize:"12px",marginLeft:"8px"},children:u.createTime}),r.jsx(L,{color:u.type==="system"?"#faad14":"#52c41a",style:{marginLeft:"8px",fontSize:"12px"},children:u.type==="system"?"系统":"文本"})]}),r.jsx("div",{style:{color:u.type==="system"?"#faad14":"#e2e8f0",backgroundColor:u.type==="system"?"#2d1b0e":"#334155",padding:"8px 12px",borderRadius:"8px",fontSize:"14px"},children:u.content})]})]})},u._id)})})};return r.jsxs("div",{style:{padding:"24px"},children:[r.jsx("h2",{style:{color:"#e2e8f0",marginBottom:"24px"},children:"💬 聊天监控"}),r.jsxs(_,{gutter:[16,16],style:{marginBottom:"24px"},children:[r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"总聊天室"}),value:156,valueStyle:{color:"#00d4ff"},prefix:"💬"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"活跃聊天室"}),value:89,valueStyle:{color:"#52c41a"},prefix:"🟢"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"今日消息"}),value:2456,valueStyle:{color:"#1890ff"},prefix:"📨"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"异常消息"}),value:3,valueStyle:{color:"#ff4d4f"},prefix:"⚠️"})})})]}),r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",marginBottom:"16px"},children:r.jsxs(_,{gutter:[16,16],align:"middle",children:[r.jsx(w,{xs:24,sm:8,children:r.jsx(Ne,{placeholder:"搜索聊天室、用户名、订单号",value:n,onChange:c=>s(c.target.value),style:{width:"100%"}})}),r.jsx(w,{xs:12,sm:4,children:r.jsxs(M,{defaultValue:"all",style:{width:"100%"},children:[r.jsx(R,{value:"all",children:"全部状态"}),r.jsx(R,{value:"active",children:"活跃中"}),r.jsx(R,{value:"closed",children:"已关闭"})]})}),r.jsx(w,{xs:12,sm:4,children:r.jsxs(M,{defaultValue:"all",style:{width:"100%"},children:[r.jsx(R,{value:"all",children:"全部类型"}),r.jsx(R,{value:"text",children:"文本消息"}),r.jsx(R,{value:"image",children:"图片消息"}),r.jsx(R,{value:"voice",children:"语音消息"})]})}),r.jsx(w,{xs:24,sm:8,children:r.jsxs(P,{children:[r.jsx(S,{type:"primary",style:{background:"#00d4ff",borderColor:"#00d4ff"},children:"🔍 搜索"}),r.jsx(S,{children:"🔄 重置"}),r.jsx(S,{type:"primary",style:{background:"#52c41a",borderColor:"#52c41a"},children:"📊 导出数据"})]})})]})}),r.jsxs(_,{gutter:[16,16],children:[r.jsx(w,{xs:24,lg:14,children:r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[r.jsx("h3",{style:{color:"#e2e8f0",margin:0},children:"💬 聊天室列表"}),r.jsx(P,{children:r.jsx(S,{type:"primary",style:{background:"#00d4ff",borderColor:"#00d4ff"},children:"🔄 刷新数据"})})]}),r.jsx(ae,{columns:a,dataSource:o,pagination:{total:156,pageSize:10,current:1,showSizeChanger:!0,showQuickJumper:!0,showTotal:(c,u)=>`第 ${u[0]}-${u[1]} 条，共 ${c} 条`},style:{background:"transparent"},className:"dark-table",size:"small"})]})}),r.jsx(w,{xs:24,lg:10,children:r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[r.jsxs("h3",{style:{color:"#e2e8f0",margin:0},children:["📨 消息记录",e&&r.jsxs("span",{style:{fontSize:"14px",fontWeight:"normal",color:"#94a3b8",marginLeft:"8px"},children:["- ",e.orderInfo.title]})]}),e&&r.jsx(P,{children:r.jsx(S,{size:"small",type:"primary",style:{background:"#faad14",borderColor:"#faad14"},children:"📤 导出记录"})})]}),l()]})})]})]})},go=()=>{const[e,t]=b.useState(null),[n,s]=b.useState(""),[o,i]=b.useState("all"),a=[{key:"1",_id:"TXN001",userId:"U001",userName:"游戏达人",userAvatar:"/placeholder.svg?height=40&width=40",type:"recharge",amount:100,status:"completed",description:"余额充值 - 微信支付",paymentMethod:"wechat",createTime:"2024-01-20 10:30:00",completedAt:"2024-01-20 10:31:00",transactionNo:"RCH20240120001"},{key:"2",_id:"TXN002",userId:"U002",userName:"专业陪练",userAvatar:"/placeholder.svg?height=40&width=40",type:"withdraw",amount:80,status:"pending",description:"提现申请 - 支付宝",paymentMethod:"alipay",createTime:"2024-01-20 14:15:00",completedAt:null,transactionNo:"WTH20240120001",withdrawInfo:{account:"138****8888",realName:"李四",bankName:"支付宝"}},{key:"3",_id:"TXN003",userId:"U001",userName:"游戏达人",userAvatar:"/placeholder.svg?height=40&width=40",type:"payment",amount:50,status:"completed",description:"订单支付 - TJZ20240120001",relatedOrderNo:"TJZ20240120001",createTime:"2024-01-20 14:30:00",completedAt:"2024-01-20 14:30:00",transactionNo:"PAY20240120001"},{key:"4",_id:"TXN004",userId:"U002",userName:"专业陪练",userAvatar:"/placeholder.svg?height=40&width=40",type:"income",amount:40,status:"completed",description:"订单收入 - TJZ20240120001",relatedOrderNo:"TJZ20240120001",createTime:"2024-01-20 17:30:00",completedAt:"2024-01-20 17:30:00",transactionNo:"INC20240120001"},{key:"5",_id:"TXN005",userId:"U003",userName:"游戏新手",userAvatar:"/placeholder.svg?height=40&width=40",type:"refund",amount:30,status:"completed",description:"订单退款 - TJZ20240120002",relatedOrderNo:"TJZ20240120002",createTime:"2024-01-20 16:00:00",completedAt:"2024-01-20 16:05:00",transactionNo:"REF20240120001"}],l=[{title:"用户信息",key:"userInfo",render:y=>{var j;return r.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[r.jsx(Z,{src:y.userAvatar,style:{backgroundColor:"#00d4ff",marginRight:"12px"},children:((j=y.userName)==null?void 0:j.charAt(0))||"用"}),r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0",fontWeight:"bold"},children:y.userName}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["ID: ",y.userId]})]})]})}},{title:"交易信息",key:"transactionInfo",render:y=>r.jsxs("div",{children:[r.jsxs("div",{style:{color:"#e2e8f0",fontWeight:"bold",marginBottom:"4px"},children:[c(y.type)," ",u(y.type)]}),r.jsx("div",{style:{color:"#94a3b8",fontSize:"12px",marginBottom:"2px"},children:y.description}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["交易号：",y.transactionNo]})]})},{title:"金额",key:"amount",render:y=>r.jsxs("div",{children:[r.jsxs("div",{style:{color:f(y.type),fontWeight:"bold",fontSize:"16px"},children:[m(y.type),"¥",y.amount.toFixed(2)]}),y.paymentMethod&&r.jsx("div",{style:{color:"#94a3b8",fontSize:"12px"},children:v(y.paymentMethod)})]})},{title:"状态",dataIndex:"status",key:"status",render:y=>{const j={pending:{color:"#faad14",text:"⏳ 待处理"},completed:{color:"#52c41a",text:"✅ 已完成"},failed:{color:"#ff4d4f",text:"❌ 失败"},cancelled:{color:"#94a3b8",text:"⚪ 已取消"}},k=j[y]||j.pending;return r.jsx(L,{color:k.color,children:k.text})}},{title:"时间",key:"time",render:y=>r.jsxs("div",{children:[r.jsxs("div",{style:{color:"#e2e8f0",fontSize:"12px"},children:["创建：",y.createTime]}),y.completedAt&&r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["完成：",y.completedAt]})]})},{title:"操作",key:"action",render:y=>r.jsxs(P,{size:"middle",children:[r.jsx(S,{type:"link",size:"small",style:{color:"#00d4ff"},onClick:()=>t(y),children:"查看详情"}),y.type==="withdraw"&&y.status==="pending"&&r.jsxs(r.Fragment,{children:[r.jsx(S,{type:"link",size:"small",style:{color:"#52c41a"},children:"批准提现"}),r.jsx(S,{type:"link",size:"small",style:{color:"#ff4d4f"},children:"拒绝提现"})]})]})}],c=y=>({recharge:"💰",withdraw:"💸",payment:"💳",income:"💵",refund:"🔄"})[y]||"💼",u=y=>({recharge:"充值",withdraw:"提现",payment:"支付",income:"收入",refund:"退款"})[y]||"其他",f=y=>["recharge","income","refund"].includes(y)?"#52c41a":"#ff4d4f",m=y=>["recharge","income","refund"].includes(y)?"+":"-",v=y=>({wechat:"微信支付",alipay:"支付宝",bank:"银行卡"})[y]||y,g=a.filter(y=>y.type==="recharge"&&y.status==="completed").reduce((y,j)=>y+j.amount,0),d=a.filter(y=>y.type==="withdraw"&&y.status==="completed").reduce((y,j)=>y+j.amount,0),x=a.filter(y=>y.type==="withdraw"&&y.status==="pending").reduce((y,j)=>y+j.amount,0),p=a.filter(y=>y.createTime.includes("2024-01-20")).length;return r.jsxs("div",{style:{padding:"24px"},children:[r.jsx("h2",{style:{color:"#e2e8f0",marginBottom:"24px"},children:"💰 钱包管理"}),r.jsxs(_,{gutter:[16,16],style:{marginBottom:"24px"},children:[r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"总充值金额"}),value:g,precision:2,valueStyle:{color:"#52c41a"},prefix:"💰",suffix:"元"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"总提现金额"}),value:d,precision:2,valueStyle:{color:"#1890ff"},prefix:"💸",suffix:"元"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"待审核提现"}),value:x,precision:2,valueStyle:{color:"#faad14"},prefix:"⏳",suffix:"元"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"今日交易"}),value:p,valueStyle:{color:"#f759ab"},prefix:"📊",suffix:"笔"})})})]}),r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",marginBottom:"16px"},children:r.jsxs(_,{gutter:[16,16],align:"middle",children:[r.jsx(w,{xs:24,sm:8,children:r.jsx(Ne,{placeholder:"搜索用户名、交易号、描述",value:n,onChange:y=>s(y.target.value),style:{width:"100%"}})}),r.jsx(w,{xs:12,sm:4,children:r.jsxs(M,{value:o,onChange:i,style:{width:"100%"},children:[r.jsx(R,{value:"all",children:"全部状态"}),r.jsx(R,{value:"pending",children:"待处理"}),r.jsx(R,{value:"completed",children:"已完成"}),r.jsx(R,{value:"failed",children:"失败"}),r.jsx(R,{value:"cancelled",children:"已取消"})]})}),r.jsx(w,{xs:12,sm:4,children:r.jsxs(M,{defaultValue:"all",style:{width:"100%"},children:[r.jsx(R,{value:"all",children:"全部类型"}),r.jsx(R,{value:"recharge",children:"充值"}),r.jsx(R,{value:"withdraw",children:"提现"}),r.jsx(R,{value:"payment",children:"支付"}),r.jsx(R,{value:"income",children:"收入"}),r.jsx(R,{value:"refund",children:"退款"})]})}),r.jsx(w,{xs:24,sm:8,children:r.jsxs(P,{children:[r.jsx(S,{type:"primary",style:{background:"#00d4ff",borderColor:"#00d4ff"},children:"🔍 搜索"}),r.jsx(S,{children:"🔄 重置"}),r.jsx(S,{type:"primary",style:{background:"#52c41a",borderColor:"#52c41a"},children:"📊 导出数据"})]})})]})}),r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[r.jsx("h3",{style:{color:"#e2e8f0",margin:0},children:"💳 交易记录"}),r.jsxs(P,{children:[r.jsx(S,{type:"primary",style:{background:"#00d4ff",borderColor:"#00d4ff"},children:"🔄 刷新数据"}),r.jsx(S,{type:"primary",style:{background:"#faad14",borderColor:"#faad14"},children:"📋 批量审核"})]})]}),r.jsx(ae,{columns:l,dataSource:a,pagination:{total:1256,pageSize:10,current:1,showSizeChanger:!0,showQuickJumper:!0,showTotal:(y,j)=>`第 ${j[0]}-${j[1]} 条，共 ${y} 条`},style:{background:"transparent"},className:"dark-table"})]}),r.jsx(br,{title:"交易详情",open:!!e,onCancel:()=>t(null),footer:[r.jsx(S,{onClick:()=>t(null),children:"关闭"},"close"),(e==null?void 0:e.type)==="withdraw"&&(e==null?void 0:e.status)==="pending"&&r.jsx(S,{type:"primary",style:{background:"#52c41a",borderColor:"#52c41a"},children:"批准提现"},"approve")],styles:{mask:{backgroundColor:"rgba(0, 0, 0, 0.7)"},content:{backgroundColor:"#1e293b",color:"#e2e8f0"}},children:e&&r.jsx("div",{style:{color:"#e2e8f0"},children:r.jsxs(_,{gutter:[16,16],children:[r.jsx(w,{span:12,children:r.jsxs("div",{style:{marginBottom:"16px"},children:[r.jsx("div",{style:{color:"#94a3b8",marginBottom:"4px"},children:"交易类型"}),r.jsxs("div",{children:[c(e.type)," ",u(e.type)]})]})}),r.jsx(w,{span:12,children:r.jsxs("div",{style:{marginBottom:"16px"},children:[r.jsx("div",{style:{color:"#94a3b8",marginBottom:"4px"},children:"交易金额"}),r.jsxs("div",{style:{color:f(e.type),fontWeight:"bold"},children:[m(e.type),"¥",e.amount.toFixed(2)]})]})}),r.jsx(w,{span:12,children:r.jsxs("div",{style:{marginBottom:"16px"},children:[r.jsx("div",{style:{color:"#94a3b8",marginBottom:"4px"},children:"用户信息"}),r.jsxs("div",{children:[e.userName," (",e.userId,")"]})]})}),r.jsx(w,{span:12,children:r.jsxs("div",{style:{marginBottom:"16px"},children:[r.jsx("div",{style:{color:"#94a3b8",marginBottom:"4px"},children:"交易状态"}),r.jsx(L,{color:e.status==="completed"?"#52c41a":"#faad14",children:e.status==="completed"?"✅ 已完成":"⏳ 待处理"})]})}),r.jsx(w,{span:24,children:r.jsxs("div",{style:{marginBottom:"16px"},children:[r.jsx("div",{style:{color:"#94a3b8",marginBottom:"4px"},children:"交易描述"}),r.jsx("div",{children:e.description})]})}),e.withdrawInfo&&r.jsx(w,{span:24,children:r.jsxs("div",{style:{marginBottom:"16px"},children:[r.jsx("div",{style:{color:"#94a3b8",marginBottom:"4px"},children:"提现信息"}),r.jsxs("div",{children:[r.jsxs("div",{children:["账户：",e.withdrawInfo.account]}),r.jsxs("div",{children:["姓名：",e.withdrawInfo.realName]}),r.jsxs("div",{children:["银行：",e.withdrawInfo.bankName]})]})]})})]})})})]})},vt=()=>{const[e,t]=b.useState("notifications"),[n,s]=b.useState(""),o=[{key:"1",_id:"NOT001",type:"order",title:"订单状态更新",content:"您的订单TJZ20240120001已完成",receiverId:"U001",receiverInfo:{nickName:"游戏达人",avatarUrl:"/placeholder.svg?height=40&width=40"},senderId:"SYSTEM",senderName:"系统",status:"read",orderId:"ORD001",orderNo:"TJZ20240120001",createTime:"2024-01-20 17:30:00",readTime:"2024-01-20 17:35:00",priority:"normal"},{key:"2",_id:"NOT002",type:"chat",title:"新消息",content:"专业陪练：好的，我们开始游戏吧！",receiverId:"U001",receiverInfo:{nickName:"游戏达人",avatarUrl:"/placeholder.svg?height=40&width=40"},senderId:"U002",senderName:"专业陪练",status:"unread",createTime:"2024-01-20 14:30:00",priority:"normal"},{key:"3",_id:"NOT003",type:"system",title:"系统维护通知",content:"系统将于今晚23:00-01:00进行维护，请提前做好准备",receiverId:null,receiverInfo:null,senderId:"ADMIN",senderName:"管理员",status:"unread",createTime:"2024-01-20 16:00:00",priority:"high"}],i=[{key:"1",_id:"EVA001",orderId:"ORD001",orderNo:"TJZ20240120001",evaluatorId:"U001",evaluatedId:"U002",evaluatorInfo:{nickName:"游戏达人",avatarUrl:"/placeholder.svg?height=40&width=40"},evaluatedInfo:{nickName:"专业陪练",avatarUrl:"/placeholder.svg?height=40&width=40"},rating:5,comment:"服务很好，技术过硬，下次还会找他！",tags:["技术好","态度好","准时"],isAnonymous:!1,createTime:"2024-01-20 18:00:00",orderInfo:{title:"三角洲行动陪玩",reward:50}},{key:"2",_id:"EVA002",orderId:"ORD002",orderNo:"TJZ20240120002",evaluatorId:"U003",evaluatedId:"U004",evaluatorInfo:{nickName:"游戏新手",avatarUrl:"/placeholder.svg?height=40&width=40"},evaluatedInfo:{nickName:"温柔陪练",avatarUrl:"/placeholder.svg?height=40&width=40"},rating:4,comment:"很有耐心，教得很好，就是声音有点小",tags:["有耐心","教学好"],isAnonymous:!1,createTime:"2024-01-20 13:00:00",orderInfo:{title:"休闲娱乐陪玩",reward:30}},{key:"3",_id:"EVA003",orderId:"ORD003",orderNo:"TJZ20240120003",evaluatorId:"U006",evaluatedId:"U005",evaluatorInfo:{nickName:"匿名用户",avatarUrl:"/placeholder.svg?height=40&width=40"},evaluatedInfo:{nickName:"竞技玩家",avatarUrl:"/placeholder.svg?height=40&width=40"},rating:2,comment:"技术一般，态度也不太好",tags:["态度差"],isAnonymous:!0,createTime:"2024-01-20 16:30:00",orderInfo:{title:"竞技模式陪练",reward:80}}],a=[{title:"通知信息",key:"notificationInfo",render:d=>r.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[r.jsx("div",{style:{marginRight:"12px"},children:c(d.type)}),r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0",fontWeight:"bold",marginBottom:"4px"},children:d.title}),r.jsx("div",{style:{color:"#94a3b8",fontSize:"12px",marginBottom:"2px"},children:d.content.length>30?d.content.substring(0,30)+"...":d.content}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["类型：",u(d.type)]})]})]})},{title:"接收者",key:"receiver",render:d=>{var x;return r.jsx("div",{children:d.receiverInfo?r.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[r.jsx(Z,{src:d.receiverInfo.avatarUrl,size:"small",style:{backgroundColor:"#00d4ff",marginRight:"8px"},children:((x=d.receiverInfo.nickName)==null?void 0:x.charAt(0))||"用"}),r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0"},children:d.receiverInfo.nickName}),r.jsx("div",{style:{color:"#94a3b8",fontSize:"12px"},children:d.receiverId})]})]}):r.jsx("div",{style:{color:"#faad14"},children:"📢 系统广播"})})}},{title:"发送者",key:"sender",render:d=>r.jsx("div",{style:{color:"#e2e8f0"},children:d.senderName})},{title:"状态",dataIndex:"status",key:"status",render:d=>r.jsx(L,{color:d==="read"?"#52c41a":"#faad14",children:d==="read"?"✅ 已读":"📬 未读"})},{title:"优先级",dataIndex:"priority",key:"priority",render:d=>{const x={low:{color:"#94a3b8",text:"🔵 低"},normal:{color:"#1890ff",text:"🟢 普通"},high:{color:"#faad14",text:"🟡 高"},urgent:{color:"#ff4d4f",text:"🔴 紧急"}},p=x[d]||x.normal;return r.jsx(L,{color:p.color,children:p.text})}},{title:"创建时间",dataIndex:"createTime",key:"createTime"},{title:"操作",key:"action",render:d=>r.jsxs(P,{size:"middle",children:[r.jsx(S,{type:"link",size:"small",style:{color:"#00d4ff"},onClick:()=>console.log("查看通知详情:",d),children:"查看详情"}),d.status==="unread"&&r.jsx(S,{type:"link",size:"small",style:{color:"#52c41a"},children:"标记已读"}),r.jsx(S,{type:"link",size:"small",style:{color:"#ff4d4f"},children:"删除"})]})}],l=[{title:"评价信息",key:"evaluationInfo",render:d=>r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0",fontWeight:"bold",marginBottom:"4px"},children:d.orderInfo.title}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px",marginBottom:"2px"},children:["订单号：",d.orderNo]}),r.jsxs("div",{style:{color:"#94a3b8",fontSize:"12px"},children:["💰 ¥",d.orderInfo.reward.toFixed(2)]})]})},{title:"评价者",key:"evaluator",render:d=>{var x;return r.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[r.jsx(Z,{src:d.evaluatorInfo.avatarUrl,size:"small",style:{backgroundColor:"#00d4ff",marginRight:"8px"},children:((x=d.evaluatorInfo.nickName)==null?void 0:x.charAt(0))||"用"}),r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0"},children:d.isAnonymous?"匿名用户":d.evaluatorInfo.nickName}),r.jsx("div",{style:{color:"#94a3b8",fontSize:"12px"},children:d.evaluatorId})]})]})}},{title:"被评价者",key:"evaluated",render:d=>{var x;return r.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[r.jsx(Z,{src:d.evaluatedInfo.avatarUrl,size:"small",style:{backgroundColor:"#52c41a",marginRight:"8px"},children:((x=d.evaluatedInfo.nickName)==null?void 0:x.charAt(0))||"用"}),r.jsxs("div",{children:[r.jsx("div",{style:{color:"#e2e8f0"},children:d.evaluatedInfo.nickName}),r.jsx("div",{style:{color:"#94a3b8",fontSize:"12px"},children:d.evaluatedId})]})]})}},{title:"评分",key:"rating",render:d=>r.jsxs("div",{children:[r.jsxs("div",{style:{color:"#faad14",fontSize:"16px",marginBottom:"4px"},children:["⭐".repeat(d.rating),r.jsxs("span",{style:{color:"#94a3b8",marginLeft:"8px"},children:[d.rating,"/5"]})]}),d.tags.length>0&&r.jsx("div",{children:d.tags.map((x,p)=>r.jsx(L,{color:"#1890ff",style:{marginBottom:"2px",fontSize:"12px"},children:x},p))})]})},{title:"评价内容",key:"comment",render:d=>r.jsx("div",{style:{color:"#e2e8f0",maxWidth:"200px"},children:d.comment.length>50?d.comment.substring(0,50)+"...":d.comment})},{title:"创建时间",dataIndex:"createTime",key:"createTime"},{title:"操作",key:"action",render:d=>r.jsxs(P,{size:"middle",children:[r.jsx(S,{type:"link",size:"small",style:{color:"#00d4ff"},onClick:()=>console.log("查看评价详情:",d),children:"查看详情"}),d.rating<=2&&r.jsx(S,{type:"link",size:"small",style:{color:"#faad14"},children:"处理投诉"}),r.jsx(S,{type:"link",size:"small",style:{color:"#ff4d4f"},children:"删除"})]})}],c=d=>({order:"📦",chat:"💬",system:"⚙️",payment:"💰",evaluation:"⭐"})[d]||"📢",u=d=>({order:"订单通知",chat:"聊天消息",system:"系统通知",payment:"支付通知",evaluation:"评价通知"})[d]||"其他",f=o.length,m=o.filter(d=>d.status==="unread").length,v=i.length,g=i.reduce((d,x)=>d+x.rating,0)/i.length;return r.jsxs("div",{style:{padding:"24px"},children:[r.jsx("h2",{style:{color:"#e2e8f0",marginBottom:"24px"},children:"📢 通知和评价管理"}),r.jsxs(_,{gutter:[16,16],style:{marginBottom:"24px"},children:[r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"总通知数"}),value:f,valueStyle:{color:"#1890ff"},prefix:"📢"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"未读通知"}),value:m,valueStyle:{color:"#faad14"},prefix:"📬"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"总评价数"}),value:v,valueStyle:{color:"#52c41a"},prefix:"⭐"})})}),r.jsx(w,{xs:24,sm:12,md:6,children:r.jsx(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:r.jsx(E,{title:r.jsx("span",{style:{color:"#94a3b8"},children:"平均评分"}),value:g,precision:1,valueStyle:{color:"#f759ab"},prefix:"⭐",suffix:"/5"})})})]}),r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",marginBottom:"16px"},children:[r.jsx("div",{style:{marginBottom:"16px"},children:r.jsxs(P,{size:"large",children:[r.jsx(S,{type:e==="notifications"?"primary":"default",onClick:()=>t("notifications"),style:e==="notifications"?{background:"#00d4ff",borderColor:"#00d4ff"}:{},children:"📢 通知管理"}),r.jsx(S,{type:e==="evaluations"?"primary":"default",onClick:()=>t("evaluations"),style:e==="evaluations"?{background:"#52c41a",borderColor:"#52c41a"}:{},children:"⭐ 评价管理"})]})}),r.jsxs(_,{gutter:[16,16],align:"middle",children:[r.jsx(w,{xs:24,sm:8,children:r.jsx(Ne,{placeholder:e==="notifications"?"搜索通知标题、内容":"搜索评价内容、用户名",value:n,onChange:d=>s(d.target.value),style:{width:"100%"}})}),r.jsx(w,{xs:24,sm:16,children:r.jsxs(P,{children:[r.jsx(S,{type:"primary",style:{background:"#00d4ff",borderColor:"#00d4ff"},children:"🔍 搜索"}),r.jsx(S,{children:"🔄 重置"}),e==="notifications"&&r.jsxs(r.Fragment,{children:[r.jsx(S,{type:"primary",style:{background:"#52c41a",borderColor:"#52c41a"},children:"📤 发送通知"}),r.jsx(S,{type:"primary",style:{background:"#faad14",borderColor:"#faad14"},children:"📋 批量操作"})]}),r.jsx(S,{type:"primary",style:{background:"#1890ff",borderColor:"#1890ff"},children:"📊 导出数据"})]})})]})]}),r.jsxs(T,{style:{background:"#1e293b",borderColor:"#334155",color:"#e2e8f0"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[r.jsx("h3",{style:{color:"#e2e8f0",margin:0},children:e==="notifications"?"📢 通知列表":"⭐ 评价列表"}),r.jsx(S,{type:"primary",style:{background:"#00d4ff",borderColor:"#00d4ff"},children:"🔄 刷新数据"})]}),r.jsx(ae,{columns:e==="notifications"?a:l,dataSource:e==="notifications"?o:i,pagination:{total:e==="notifications"?156:89,pageSize:10,current:1,showSizeChanger:!0,showQuickJumper:!0,showTotal:(d,x)=>`第 ${x[0]}-${x[1]} 条，共 ${d} 条`},style:{background:"transparent"},className:"dark-table"})]})]})},bo=()=>r.jsx(mr,{theme:{algorithm:St.darkAlgorithm,token:{colorPrimary:"#00d4ff",colorBgContainer:"#1e293b",colorBgElevated:"#1e293b",colorBorder:"#334155",colorText:"#e2e8f0",colorTextSecondary:"#94a3b8"}},children:r.jsx(Cn,{children:r.jsxs(st,{children:[r.jsx(B,{path:"/",element:r.jsx(me,{to:"/admin/dashboard",replace:!0})}),r.jsx(B,{path:"/test",element:r.jsx(ho,{})}),r.jsx(B,{path:"/admin/*",element:r.jsx(fo,{children:r.jsxs(st,{children:[r.jsx(B,{path:"dashboard",element:r.jsx(po,{})}),r.jsx(B,{path:"users",element:r.jsx(mo,{})}),r.jsx(B,{path:"orders",element:r.jsx(xo,{})}),r.jsx(B,{path:"chat",element:r.jsx(yo,{})}),r.jsx(B,{path:"wallet",element:r.jsx(go,{})}),r.jsx(B,{path:"notifications",element:r.jsx(vt,{})}),r.jsx(B,{path:"evaluations",element:r.jsx(vt,{})}),r.jsx(B,{path:"settings",element:r.jsx("div",{style:{color:"#cbd5e1"},children:"⚙️ 系统设置页面开发中..."})}),r.jsx(B,{path:"",element:r.jsx(me,{to:"dashboard",replace:!0})})]})})}),r.jsx(B,{path:"/dashboard",element:r.jsx(me,{to:"/admin/dashboard",replace:!0})}),r.jsx(B,{path:"*",element:r.jsx(me,{to:"/admin/dashboard",replace:!0})})]})})});ze.createRoot(document.getElementById("root")).render(r.jsx(pr.StrictMode,{children:r.jsx(bo,{})}));
