/* 语音性能监控组件样式 */
.performance-monitor {
  position: fixed;
  top: 100rpx;
  right: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 16rpx;
  padding: 24rpx;
  color: #fff;
  font-size: 24rpx;
  z-index: 9999;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10rpx);
}

/* 监控头部 */
.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.monitor-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #00d4ff;
}

.monitor-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  border: 1rpx solid #00d4ff;
  border-radius: 8rpx;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
}

.action-btn:active {
  background: rgba(0, 212, 255, 0.4);
}

/* 数据区域 */
.current-data,
.stats-data,
.history-data {
  margin-bottom: 24rpx;
}

.data-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #00d4ff;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

/* 数据网格 */
.data-grid,
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.data-item,
.stat-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8rpx;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.data-label,
.stat-label {
  font-size: 20rpx;
  color: #ccc;
  margin-bottom: 8rpx;
}

.data-value,
.stat-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #fff;
}

.data-value.highlight {
  color: #00ff88;
  font-size: 28rpx;
}

.stat-value.success {
  color: #00ff88;
}

/* 历史记录 */
.history-list {
  max-height: 300rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8rpx;
  padding: 16rpx;
}

.history-item {
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.history-item:last-child {
  border-bottom: none;
}

.history-time {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.history-metrics {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.metric {
  font-size: 20rpx;
  color: #ccc;
  background: rgba(255, 255, 255, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

/* 性能提示 */
.performance-tips {
  background: rgba(0, 212, 255, 0.1);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 8rpx;
  padding: 16rpx;
}

.tip-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #00d4ff;
  margin-bottom: 12rpx;
}

.tip-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 20rpx;
  color: #ccc;
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .performance-monitor {
    width: calc(100vw - 40rpx);
    right: 20rpx;
    left: 20rpx;
  }
  
  .data-grid,
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.performance-monitor {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 4rpx;
}

.history-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2rpx;
}

.history-list::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.5);
  border-radius: 2rpx;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 255, 0.8);
}