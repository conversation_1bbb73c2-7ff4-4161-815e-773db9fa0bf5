<!-- 应用内通知组件 -->
<view wx:if="{{visible}}"
      class="notification-toast {{animationClass}}"
      bindtap="onNotificationTap">
  <view class="notification-content">
    <!-- 头像 -->
    <view class="notification-avatar" wx:if="{{avatar}}">
      <image src="{{avatar}}" class="avatar-image" mode="aspectFill" />
    </view>

    <!-- 通知图标 -->
    <view class="notification-icon" wx:else>
      <text class="icon-text">
        {{type === 'message' ? '💬' : type === 'order' ? '📋' : '🔔'}}
      </text>
    </view>

    <!-- 通知文本 -->
    <view class="notification-text">
      <view class="notification-title" wx:if="{{title}}">{{title}}</view>
      <view class="notification-body">{{content}}</view>
    </view>

    <!-- 关闭按钮 -->
    <view class="notification-close" bindtap="onCloseTap" catchtap="true">
      <text class="close-icon">✕</text>
    </view>
  </view>

  <!-- 进度条 -->
  <view class="notification-progress" wx:if="{{duration > 0}}">
    <view class="progress-bar {{progressPaused ? 'paused' : ''}}"
          style="animation-duration: {{progressDuration}}ms;"></view>
  </view>
</view>
