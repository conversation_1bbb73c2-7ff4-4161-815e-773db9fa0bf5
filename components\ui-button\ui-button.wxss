/* 统一按钮组件样式 */
.ui-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 0;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  border: 2rpx solid transparent;
  border-radius: 8rpx;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.ui-button::after {
  border: none;
}

/* 按钮尺寸 */
.ui-button--mini {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  min-height: 48rpx;
}

.ui-button--small {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  min-height: 56rpx;
}

.ui-button--normal {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  min-height: 64rpx;
}

.ui-button--large {
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  min-height: 80rpx;
}

/* 按钮类型 - 主要按钮 */
.ui-button--primary {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: #ffffff;
  border-color: #ff6b35;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

.ui-button--primary.ui-button--active {
  background: linear-gradient(135deg, #e55a2b 0%, #de831a 100%);
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.4);
}

/* 按钮类型 - 次要按钮 */
.ui-button--secondary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-color: #667eea;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.ui-button--secondary.ui-button--active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(2rpx);
}

/* 按钮类型 - 成功按钮 */
.ui-button--success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  color: #ffffff;
  border-color: #11998e;
  box-shadow: 0 4rpx 12rpx rgba(17, 153, 142, 0.3);
}

.ui-button--success.ui-button--active {
  background: linear-gradient(135deg, #0e8078 0%, #32d470 100%);
  transform: translateY(2rpx);
}

/* 按钮类型 - 警告按钮 */
.ui-button--warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: #ffffff;
  border-color: #f093fb;
  box-shadow: 0 4rpx 12rpx rgba(240, 147, 251, 0.3);
}

.ui-button--warning.ui-button--active {
  background: linear-gradient(135deg, #e082e8 0%, #e04e5f 100%);
  transform: translateY(2rpx);
}

/* 按钮类型 - 危险按钮 */
.ui-button--danger {
  background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
  color: #ffffff;
  border-color: #fc466b;
  box-shadow: 0 4rpx 12rpx rgba(252, 70, 107, 0.3);
}

.ui-button--danger.ui-button--active {
  background: linear-gradient(135deg, #e93d5e 0%, #3851e8 100%);
  transform: translateY(2rpx);
}

/* 按钮类型 - 信息按钮 */
.ui-button--info {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333333;
  border-color: #a8edea;
  box-shadow: 0 4rpx 12rpx rgba(168, 237, 234, 0.3);
}

.ui-button--info.ui-button--active {
  background: linear-gradient(135deg, #96d9d6 0%, #f4c2d7 100%);
  transform: translateY(2rpx);
}

/* 按钮类型 - 文本按钮 */
.ui-button--text {
  background: transparent;
  color: #ff6b35;
  border-color: transparent;
  box-shadow: none;
}

.ui-button--text.ui-button--active {
  background: rgba(255, 107, 53, 0.1);
  color: #e55a2b;
}

/* 朴素按钮 */
.ui-button--plain.ui-button--primary {
  background: transparent;
  color: #ff6b35;
  border-color: #ff6b35;
  box-shadow: none;
}

.ui-button--plain.ui-button--primary.ui-button--active {
  background: rgba(255, 107, 53, 0.1);
}

/* 块级按钮 */
.ui-button--block {
  display: flex;
  width: 100%;
}

/* 圆形按钮 */
.ui-button--round {
  border-radius: 50rpx;
}

/* 禁用状态 */
.ui-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 加载状态 */
.ui-button--loading {
  cursor: not-allowed;
}

.ui-button__loading {
  margin-right: 8rpx;
}

.ui-button__loading-icon {
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: ui-button-spin 1s linear infinite;
}

@keyframes ui-button-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图标 */
.ui-button__icon {
  margin-right: 8rpx;
  display: flex;
  align-items: center;
}

.ui-button__icon-text {
  font-size: 1.2em;
}

/* 按钮内容 */
.ui-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.ui-button__text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 按下状态 */
.ui-button--pressed {
  transform: scale(0.98);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .ui-button--info {
    color: #ffffff;
  }
  
  .ui-button--text {
    color: #ff8a65;
  }
  
  .ui-button--text.ui-button--active {
    background: rgba(255, 138, 101, 0.1);
    color: #ff8a65;
  }
}
