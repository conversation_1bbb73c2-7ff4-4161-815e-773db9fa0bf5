// 提现页面
import API from '../../../utils/api.js';

const app = getApp();

Page({
  data: {
    userInfo: null,
    currentBalance: 0,
    frozenBalance: 0,

    // 提现金额
    withdrawAmount: '',

    // 提现方式
    withdrawMethods: [
      {
        id: 'wechat',
        name: '微信零钱',
        icon: '/images/icons/wechat-pay.png',
        desc: '实时到账',
        selected: true
      },
      {
        id: 'bank',
        name: '银行卡',
        icon: '/images/icons/bank-card.png',
        desc: '1-3个工作日',
        selected: false
      }
    ],
    selectedWithdrawMethod: 'wechat',

    // 银行卡信息
    bankInfo: {
      bankName: '',
      cardNumber: '',
      cardHolder: ''
    },

    // 页面状态
    loading: false,
    submitting: false,

    // 提现规则
    minWithdraw: 10,
    maxWithdraw: 50000,
    withdrawFee: 0 // 提现手续费
  },

  onLoad() {
    this.loadUserInfo();
  },

  onShow() {
    this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const result = await API.getUserInfo();
      if (result.success) {
        this.setData({
          userInfo: result.data,
          currentBalance: result.data.balance || 0,
          frozenBalance: result.data.frozenBalance || 0
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  // 提现金额输入
  onWithdrawAmountInput(e) {
    const value = e.detail.value;
    this.setData({
      withdrawAmount: value
    });
  },

  // 选择提现方式
  selectWithdrawMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedWithdrawMethod: method
    });
  },

  // 银行卡信息输入
  onBankInfoInput(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [`bankInfo.${field}`]: value
    });
  },

  // 全部提现
  withdrawAll() {
    this.setData({
      withdrawAmount: this.data.currentBalance.toString()
    });
  },

  // 提交提现申请
  async submitWithdraw() {
    if (this.data.submitting) return;

    // 验证提现金额
    const amount = parseFloat(this.data.withdrawAmount);
    if (!amount || amount <= 0) {
      app.utils.showError('请输入提现金额');
      return;
    }

    if (amount < this.data.minWithdraw) {
      app.utils.showError(`提现金额不能少于${this.data.minWithdraw}元`);
      return;
    }

    if (amount > this.data.maxWithdraw) {
      app.utils.showError(`提现金额不能超过${this.data.maxWithdraw}元`);
      return;
    }

    if (amount > this.data.currentBalance) {
      app.utils.showError('提现金额不能超过可用余额');
      return;
    }

    // 验证银行卡信息（如果选择银行卡提现）
    if (this.data.selectedWithdrawMethod === 'bank') {
      const { bankName, cardNumber, cardHolder } = this.data.bankInfo;
      if (!bankName || !cardNumber || !cardHolder) {
        app.utils.showError('请完善银行卡信息');
        return;
      }
    }

    // 确认提现
    wx.showModal({
      title: '确认提现',
      content: `确定要提现 ¥${amount} 吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.processWithdraw(amount);
        }
      }
    });
  },

  // 处理提现
  async processWithdraw(amount) {
    this.setData({ submitting: true });

    try {
      app.utils.showLoading('提交申请中...');

      const params = {
        amount: amount,
        withdrawMethod: this.data.selectedWithdrawMethod
      };

      // 如果是银行卡提现，添加银行卡信息
      if (this.data.selectedWithdrawMethod === 'bank') {
        params.bankInfo = this.data.bankInfo;
      }

      const result = await API.createWithdraw(params);

      if (result.success) {
        app.utils.showSuccess('提现申请提交成功');

        // 清空表单
        this.setData({
          withdrawAmount: '',
          bankInfo: {
            bankName: '',
            cardNumber: '',
            cardHolder: ''
          }
        });

        // 刷新用户信息
        setTimeout(() => {
          this.loadUserInfo();
        }, 1000);

        // 跳转到提现记录页面
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/payment/records/records?type=withdraw'
          });
        }, 2000);
      } else {
        app.utils.showError(result.error || '提现申请失败');
      }
    } catch (error) {
      console.error('提现申请失败:', error);
      app.utils.showError('提现申请失败，请重试');
    } finally {
      app.utils.hideLoading();
      this.setData({ submitting: false });
    }
  },

  // 查看提现记录
  viewWithdrawHistory() {
    wx.navigateTo({
      url: '/pages/payment/records/records?type=withdraw'
    });
  }
});