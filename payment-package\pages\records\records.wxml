<!-- 交易记录页面 -->
<navigation-bar title="交易记录" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)" loading="{{navLoading}}"></navigation-bar>

<!-- 返回按钮 - 与其他页面保持一致 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
  </view>
</view>

<!-- G.T.I. SECURITY 科技感加载框 -->
<view wx:if="{{loading}}" class="gti-loading-container">
  <view class="gti-loading-backdrop"></view>
  <view class="gti-loading-content">
    <view class="gti-logo-container">
      <image class="gti-logo" src="cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/logos/gti-security-logo.png" mode="aspectFit"></image>
    </view>
    <view class="gti-loading-spinner">
      <view class="spinner-ring"></view>
      <view class="spinner-ring"></view>
      <view class="spinner-ring"></view>
    </view>
    <text class="gti-loading-text">正在加载交易记录...</text>
    <view class="gti-loading-dots">
      <view class="dot dot-1"></view>
      <view class="dot dot-2"></view>
      <view class="dot dot-3"></view>
    </view>
  </view>
</view>

<view class="records-container page-with-custom-nav">
  <!-- 统计卡片 -->
  <view class="stats-card">
    <view class="stats-header">
      <text class="stats-title">交易统计</text>
      <view class="date-filter" bindtap="showDatePicker">
        <text class="filter-text">
          {{dateRange.startDate && dateRange.endDate ? dateRange.startDate + ' 至 ' + dateRange.endDate : '选择时间'}}
        </text>
        <text class="filter-icon">📅</text>
      </view>
    </view>

    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-value income">+¥{{stats.totalIncome}}</text>
        <text class="stats-label">总收入</text>
      </view>
      <view class="stats-item">
        <text class="stats-value expense">-¥{{stats.totalExpense}}</text>
        <text class="stats-label">总支出</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{stats.transactionCount}}</text>
        <text class="stats-label">交易笔数</text>
      </view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <scroll-view class="tabs-scroll" scroll-x>
      <view class="tab-item {{activeTab === item.id ? 'active' : ''}}"
            wx:for="{{filterTabs}}"
            wx:key="id"
            bindtap="switchTab"
            data-tab="{{item.id}}">
        <text class="tab-text">{{item.name}}</text>
        <view class="tab-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
      </view>
    </scroll-view>
  </view>

  <!-- 交易记录列表 -->
  <scroll-view class="records-list"
               scroll-y
               bindscrolltolower="onReachBottom"
               refresher-enabled="{{true}}"
               refresher-triggered="{{refreshing}}"
               bindrefresherrefresh="onPullDownRefresh">

    <view class="record-item"
          wx:for="{{recordList}}"
          wx:key="_id"
          bindtap="viewTransactionDetail"
          data-id="{{item._id}}">

      <!-- 交易图标和信息 -->
      <view class="record-info">
        <view class="record-icon {{item.type}}">
          <text class="icon-text">
            {{item.type === 'recharge' ? '💰' :
              item.type === 'withdraw' ? '💸' :
              item.type === 'income' ? '💵' :
              item.type === 'payment' ? '💳' :
              item.type === 'refund' ? '🔄' : '💼'}}
          </text>
        </view>

        <view class="record-details">
          <view class="record-header">
            <text class="record-title">{{item.typeText}}</text>
            <text class="record-status {{item.status}}">{{item.statusText}}</text>
          </view>

          <text class="record-desc">{{item.description}}</text>

          <view class="record-meta">
            <text class="record-time">{{item.createTimeText}}</text>
            <view class="related-info" wx:if="{{item.relatedInfo}}">
              <text class="related-text">
                {{item.relatedInfo.type === 'order' ? item.relatedInfo.orderNo :
                  item.relatedInfo.type === 'recharge' ? item.relatedInfo.rechargeNo :
                  item.relatedInfo.type === 'withdraw' ? item.relatedInfo.withdrawNo : ''}}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 交易金额 -->
      <view class="record-amount">
        <text class="amount-value {{item.amount > 0 ? 'income' : 'expense'}}">
          {{item.amount > 0 ? '+' : ''}}¥{{item.amount}}
        </text>
      </view>
    </view>

    <!-- 分页加载状态 -->
    <view class="load-more" wx:if="{{loadingMore}}">
      <text>加载中...</text>
    </view>

    <view class="no-more" wx:if="{{!hasMore && recordList.length > 0}}">
      <text>没有更多了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && recordList.length === 0}}">
      <view class="empty-icon">📊</view>
      <text class="empty-text">暂无交易记录</text>
      <text class="empty-desc">完成订单或充值后即可查看交易记录</text>
    </view>
  </scroll-view>

  <!-- 日期选择弹窗 -->
  <view class="date-picker-modal {{dateRange.showDatePicker ? 'show' : ''}}" bindtap="clearDateFilter">
    <view class="date-picker-content" catch:tap="true">
      <view class="picker-header">
        <text class="picker-title">选择时间范围</text>
        <text class="picker-close" bindtap="clearDateFilter">✕</text>
      </view>

      <view class="picker-body">
        <view class="date-input-group">
          <text class="date-label">开始日期</text>
          <picker mode="date"
                  value="{{dateRange.startDate}}"
                  bindchange="onStartDateChange">
            <view class="date-input">
              {{dateRange.startDate || '请选择开始日期'}}
            </view>
          </picker>
        </view>

        <view class="date-input-group">
          <text class="date-label">结束日期</text>
          <picker mode="date"
                  value="{{dateRange.endDate}}"
                  bindchange="onEndDateChange">
            <view class="date-input">
              {{dateRange.endDate || '请选择结束日期'}}
            </view>
          </picker>
        </view>
      </view>

      <view class="picker-footer">
        <button class="picker-btn cancel" bindtap="clearDateFilter">清除</button>
        <button class="picker-btn confirm" bindtap="applyDateFilter">确定</button>
      </view>
    </view>
  </view>
</view>