<!--语音性能监控组件-->
<view class="performance-monitor" wx:if="{{show}}">
  <view class="monitor-header">
    <text class="monitor-title">🚀 语音性能监控</text>
    <view class="monitor-actions">
      <button class="action-btn" bindtap="clearHistory" size="mini">清空</button>
      <button class="action-btn" bindtap="exportData" size="mini">导出</button>
    </view>
  </view>

  <!-- 当前性能数据 -->
  <view class="current-data">
    <view class="data-title">📊 当前性能</view>
    <view class="data-grid">
      <view class="data-item">
        <text class="data-label">用户感知延迟</text>
        <text class="data-value highlight">{{performanceData.userPerceived}}ms</text>
      </view>
      <view class="data-item">
        <text class="data-label">上传耗时</text>
        <text class="data-value">{{performanceData.uploadTime}}ms</text>
      </view>
      <view class="data-item">
        <text class="data-label">发送耗时</text>
        <text class="data-value">{{performanceData.sendTime}}ms</text>
      </view>
      <view class="data-item">
        <text class="data-label">总耗时</text>
        <text class="data-value">{{performanceData.totalTime}}ms</text>
      </view>
      <view class="data-item">
        <text class="data-label">文件大小</text>
        <text class="data-value">{{performanceData.fileSize}}KB</text>
      </view>
      <view class="data-item">
        <text class="data-label">压缩比例</text>
        <text class="data-value">{{performanceData.compressionRatio}}%</text>
      </view>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="stats-data">
    <view class="data-title">📈 统计数据 (最近{{history.length}}次)</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-label">平均感知延迟</text>
        <text class="stat-value">{{stats.avgUserPerceived}}ms</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">平均上传时间</text>
        <text class="stat-value">{{stats.avgUploadTime}}ms</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">平均总时间</text>
        <text class="stat-value">{{stats.avgTotalTime}}ms</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">成功率</text>
        <text class="stat-value success">{{stats.successRate}}%</text>
      </view>
    </view>
  </view>

  <!-- 性能历史 -->
  <view class="history-data" wx:if="{{history.length > 0}}">
    <view class="data-title">📋 历史记录</view>
    <scroll-view class="history-list" scroll-y="true">
      <view class="history-item" wx:for="{{history}}" wx:key="timestamp">
        <view class="history-time">{{item.date}}</view>
        <view class="history-metrics">
          <text class="metric">感知: {{item.userPerceived}}ms</text>
          <text class="metric">上传: {{item.uploadTime}}ms</text>
          <text class="metric">总计: {{item.totalTime}}ms</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 性能提示 -->
  <view class="performance-tips">
    <view class="tip-title">💡 性能优化提示</view>
    <view class="tip-content">
      <text class="tip-item">• 用户感知延迟 < 300ms = 极佳体验</text>
      <text class="tip-item">• 文件压缩比例 > 50% = 优秀优化</text>
      <text class="tip-item">• 成功率 > 95% = 稳定可靠</text>
    </view>
  </view>
</view>