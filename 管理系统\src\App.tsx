import React, { useState, useEffect } from 'react';
import { BrowserRouter, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { Button, Card, Row, Col, Statistic, Space, Layout, Menu, theme, ConfigProvider, Table, Tag, Avatar, Select, Input, Modal, message } from 'antd';
import { dashboardApi, userApi, orderApi, walletApi } from './services/api';
import {
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  // DollarOutlined,
  MessageOutlined,
  WalletOutlined,
  BellOutlined,
  StarOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons';

const { Header, Sider, Content } = Layout;
const { Search } = Input;
const { Option } = Select;

// 菜单项配置
const menuItems = [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: '仪表盘',
  },
  {
    key: 'users',
    icon: <UserOutlined />,
    label: '用户管理',
  },
  {
    key: 'orders',
    icon: <ShoppingOutlined />,
    label: '订单管理',
  },
  {
    key: 'chat',
    icon: <MessageOutlined />,
    label: '聊天监控',
  },
  {
    key: 'wallet',
    icon: <WalletOutlined />,
    label: '钱包管理',
  },
  {
    key: 'notifications',
    icon: <BellOutlined />,
    label: '通知管理',
  },
  {
    key: 'evaluations',
    icon: <StarOutlined />,
    label: '评价管理',
  },
  {
    key: 'settings',
    icon: <SettingOutlined />,
    label: '系统设置',
  },
];

// 管理布局组件
const AdminLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const {
    // token: { colorBgContainer },
  } = theme.useToken();

  // 获取当前选中的菜单项
  const getCurrentMenuKey = () => {
    const path = location.pathname;
    if (path.includes('/admin/dashboard')) return 'dashboard';
    if (path.includes('/admin/users')) return 'users';
    if (path.includes('/admin/orders')) return 'orders';
    if (path.includes('/admin/chat')) return 'chat';
    if (path.includes('/admin/wallet')) return 'wallet';
    if (path.includes('/admin/notifications')) return 'notifications';
    if (path.includes('/admin/evaluations')) return 'evaluations';
    if (path.includes('/admin/settings')) return 'settings';
    return 'dashboard';
  };

  // 处理菜单点击
  const handleMenuClick = (key: string) => {
    navigate(`/admin/${key}`);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          background: '#1e293b',
        }}
      >
        <div style={{
          height: '64px',
          margin: '16px',
          background: 'rgba(0, 212, 255, 0.1)',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#00d4ff',
          fontWeight: 'bold',
          fontSize: collapsed ? '16px' : '14px',
        }}>
          {collapsed ? '🔺' : '🔺 三角洲平台'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[getCurrentMenuKey()]}
          onClick={({ key }) => handleMenuClick(key)}
          items={menuItems}
          style={{
            background: 'transparent',
          }}
        />
      </Sider>
      <Layout>
        <Header style={{
          padding: 0,
          background: '#334155',
          display: 'flex',
          alignItems: 'center',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
              color: '#00d4ff',
            }}
          />
          <div style={{
            color: '#f8fafc',
            fontSize: '18px',
            fontWeight: 'bold',
          }}>
            三角洲任务平台管理系统
          </div>
        </Header>
        <Content style={{
          margin: '24px 16px',
          padding: 24,
          minHeight: 280,
          background: '#0f172a',
          borderRadius: '8px',
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

// 简单的测试页面组件
const TestPage: React.FC = () => (
  <div style={{
    color: '#00d4ff',
    padding: '20px',
    fontSize: '24px',
    background: '#0f172a',
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column'
  }}>
    <div>🎉 三角洲任务平台管理系统</div>
    <div style={{fontSize: '18px', marginTop: '20px'}}>系统运行正常！</div>
    <div style={{fontSize: '14px', marginTop: '10px', color: '#cbd5e1'}}>
      路由测试页面 - 访问 /dashboard 查看仪表盘
    </div>
  </div>
);

// 增强的仪表盘页面
const SimpleDashboard: React.FC = () => {
  const [timeRange, setTimeRange] = useState('today');
  const [loading, setLoading] = useState(true);

  // 实时数据状态
  const [realTimeData, setRealTimeData] = useState({
    onlineUsers: 0,
    activeOrders: 0,
    todayRevenue: 0,
    systemLoad: 0
  });

  // 统计数据状态
  const [statsData, setStatsData] = useState({
    totalUsers: 0,
    totalOrders: 0,
    totalRevenue: 0,
    newUsersToday: 0
  });

  // 模拟趋势数据
  const trendData = {
    users: [
      { period: '00:00', value: 120 },
      { period: '04:00', value: 89 },
      { period: '08:00', value: 156 },
      { period: '12:00', value: 234 },
      { period: '16:00', value: 189 },
      { period: '20:00', value: 167 },
      { period: '24:00', value: 145 }
    ],
    orders: [
      { period: '周一', value: 45 },
      { period: '周二', value: 67 },
      { period: '周三', value: 89 },
      { period: '周四', value: 78 },
      { period: '周五', value: 95 },
      { period: '周六', value: 123 },
      { period: '周日', value: 89 }
    ],
    revenue: [
      { period: '1月', value: 45600 },
      { period: '2月', value: 52300 },
      { period: '3月', value: 48900 },
      { period: '4月', value: 61200 },
      { period: '5月', value: 58700 },
      { period: '6月', value: 67800 }
    ]
  };

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // 并行获取各种统计数据
      const [dashboardStats, userStats, orderStats, walletStats] = await Promise.all([
        dashboardApi.getStats().catch((e) => {
          console.error('dashboardApi.getStats 失败:', e);
          return { success: false, data: null };
        }),
        userApi.getUserStats().catch((e) => {
          console.error('userApi.getUserStats 失败:', e);
          return { success: false, data: null };
        }),
        orderApi.getOrderStats().catch((e) => {
          console.error('orderApi.getOrderStats 失败:', e);
          return { success: false, data: null };
        }),
        walletApi.getWalletStats().catch((e) => {
          console.error('walletApi.getWalletStats 失败:', e);
          return { success: false, data: null };
        })
      ]);

      console.log('📊 API调用结果:', {
        dashboardStats,
        userStats,
        orderStats,
        walletStats
      });

      // 更新统计数据
      if (dashboardStats.success && dashboardStats.data) {
        console.log('🎯 更新仪表盘统计数据:', dashboardStats.data);
        setStatsData({
          totalUsers: dashboardStats.data.totalUsers ?? 0,
          totalOrders: dashboardStats.data.totalOrders ?? 0,
          totalRevenue: dashboardStats.data.totalRevenue ?? 0,
          newUsersToday: dashboardStats.data.newUsersToday ?? 0
        });
      }

      // 更新实时数据
      console.log('🎯 更新实时数据:', {
        userStats: userStats.data,
        orderStats: orderStats.data,
        walletStats: walletStats.data
      });
      setRealTimeData({
        onlineUsers: dashboardStats.data?.onlineUsers ?? 0,
        activeOrders: dashboardStats.data?.activeOrders ?? 0,
        todayRevenue: dashboardStats.data?.todayRevenue ?? 0,
        systemLoad: dashboardStats.data?.systemLoad ?? 50
      });

    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      message.error('获取数据失败，显示模拟数据');

      // 使用模拟数据作为后备
      setStatsData({
        totalUsers: 1234,
        totalOrders: 567,
        totalRevenue: 89012,
        newUsersToday: 23
      });

      setRealTimeData({
        onlineUsers: 156,
        activeOrders: 89,
        todayRevenue: 12580.50,
        systemLoad: 68
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据获取
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // 定时刷新数据
  // 调试用户数据
  const handleDebugUsers = async () => {
    try {
      message.loading('正在调试用户数据...', 0);

      const debugResult = await userApi.debugUsers();
      message.destroy();

      console.log('🔍 调试结果:', debugResult);

      if (debugResult.success) {
        // 检查是否是模拟数据
        if (debugResult.message?.includes('模拟数据')) {
          // 显示临时调试信息
          Modal.info({
            title: '⚠️ 云函数未部署',
            width: 600,
            content: (
              <div>
                <p>检测到云函数还未部署最新代码。</p>
                <p><strong>请按照以下步骤部署云函数：</strong></p>
                <ol>
                  <li>打开微信开发者工具</li>
                  <li>将 <code>管理系统\云函数代码\admin-api\index-simple.js</code> 的内容复制到 <code>cloudfunctions/admin-api/index.js</code></li>
                  <li>右键点击 <code>cloudfunctions/admin-api/</code> 文件夹</li>
                  <li>选择"上传并部署：云端安装依赖"</li>
                  <li>等待部署完成后重试</li>
                </ol>
                <p style={{ marginTop: '16px', padding: '8px', background: '#f0f0f0', borderRadius: '4px' }}>
                  <strong>当前状态：</strong>在线用户数为0可能是因为用户数据缺少 lastActiveTime 字段
                </p>
              </div>
            ),
          });
          return;
        }

        const { userAnalysis, queryResult, recommendations } = debugResult.data;

        Modal.info({
          title: '🔍 用户数据调试报告',
          width: 800,
          content: (
            <div style={{ maxHeight: '500px', overflow: 'auto' }}>
              <div style={{ marginBottom: '16px' }}>
                <h4>📊 用户数据分析</h4>
                <p>总用户数: {userAnalysis.totalUsers}</p>
                <p>有 lastActiveTime 字段: {userAnalysis.usersWithLastActiveTime}</p>
                <p>缺少 lastActiveTime 字段: {userAnalysis.usersWithoutLastActiveTime}</p>
                <p>最近30分钟活跃用户: {userAnalysis.recentActiveUsers}</p>
              </div>

              <div style={{ marginBottom: '16px' }}>
                <h4>🔍 查询结果</h4>
                {queryResult.success ? (
                  <p>查询成功，找到 {queryResult.count} 个在线用户</p>
                ) : (
                  <p style={{ color: 'red' }}>查询失败: {queryResult.error}</p>
                )}
              </div>

              {recommendations.length > 0 && (
                <div>
                  <h4>💡 修复建议</h4>
                  {recommendations.map((rec, index) => (
                    <div key={index} style={{ marginBottom: '8px', padding: '8px', background: '#f6f8fa', borderRadius: '4px' }}>
                      <p><strong>问题:</strong> {rec.issue}</p>
                      <p><strong>解决方案:</strong> {rec.solution}</p>
                      <Button
                        size="small"
                        type="primary"
                        onClick={() => handleFixUserData(rec.action)}
                        style={{ marginTop: '4px' }}
                      >
                        执行修复
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ),
        });
      } else {
        message.error('调试失败: ' + debugResult.error);
      }
    } catch (error) {
      message.destroy();
      message.error('调试用户数据失败');
      console.error('调试用户数据失败:', error);
    }
  };

  // 修复用户数据
  const handleFixUserData = async (action: string) => {
    try {
      message.loading('正在修复用户数据...', 0);

      const fixResult = await userApi.fixUserData(action);
      message.destroy();

      if (fixResult.success) {
        message.success(fixResult.message);
        // 修复完成后重新获取数据
        setTimeout(() => {
          fetchDashboardData();
        }, 1000);
      } else {
        message.error('修复失败: ' + fixResult.error);
      }
    } catch (error) {
      message.destroy();
      message.error('修复用户数据失败');
      console.error('修复用户数据失败:', error);
    }
  };

  // 快速修复功能
  const handleQuickFix = () => {
    Modal.confirm({
      title: '🔧 快速修复在线用户数',
      width: 700,
      content: (
        <div>
          <p><strong>问题诊断：</strong></p>
          <p>根据你提供的用户数据，所有用户都缺少 <code>lastActiveTime</code> 字段，这导致在线用户数显示为0。</p>

          <p style={{ marginTop: '16px' }}><strong>修复方案：</strong></p>
          <p>需要为所有用户添加 <code>lastActiveTime</code> 字段。由于云函数部署问题，请手动执行以下步骤：</p>

          <div style={{ background: '#f6f8fa', padding: '12px', borderRadius: '6px', marginTop: '12px' }}>
            <p><strong>步骤1：在微信开发者工具中</strong></p>
            <ol>
              <li>打开云开发控制台</li>
              <li>进入数据库管理</li>
              <li>选择 <code>users</code> 集合</li>
              <li>点击"导入数据"旁边的"更多操作"</li>
              <li>选择"批量更新"</li>
            </ol>
          </div>

          <div style={{ background: '#f0f9ff', padding: '12px', borderRadius: '6px', marginTop: '12px' }}>
            <p><strong>步骤2：执行批量更新</strong></p>
            <p>使用以下更新语句：</p>
            <pre style={{ background: '#1f2937', color: '#f9fafb', padding: '8px', borderRadius: '4px', fontSize: '12px' }}>
{`// 查询条件（更新所有用户）
{}

// 更新内容
{
  "$set": {
    "lastActiveTime": "$updateTime"
  }
}`}
            </pre>
          </div>

          <div style={{ background: '#fef3c7', padding: '12px', borderRadius: '6px', marginTop: '12px' }}>
            <p><strong>步骤3：验证修复</strong></p>
            <p>更新完成后，刷新管理系统页面，在线用户数应该会显示正确的数值。</p>
          </div>
        </div>
      ),
      onOk() {
        message.info('请按照步骤手动修复数据库，完成后刷新页面查看效果');
      },
      okText: '我知道了',
      cancelText: '取消'
    });
  };

  // 刷新数据功能
  const handleRefreshData = async () => {
    try {
      message.loading('正在刷新数据...', 1);
      await fetchDashboardData();
      message.success('数据刷新成功！');
    } catch (error) {
      message.error('数据刷新失败');
      console.error('刷新数据失败:', error);
    }
  };

  // 时间检查功能
  const handleTimeCheck = () => {
    const now = new Date();
    const last30Minutes = new Date(now.getTime() - 30 * 60 * 1000);
    const userTime = new Date('2025-01-21T10:05:00.000Z');

    Modal.info({
      title: '⏰ 时间检查报告',
      width: 700,
      content: (
        <div>
          <p><strong>当前时间分析：</strong></p>
          <div style={{ background: '#f6f8fa', padding: '12px', borderRadius: '6px', fontFamily: 'monospace', fontSize: '12px' }}>
            <p>🕐 <strong>当前时间：</strong> {now.toISOString()}</p>
            <p>⏰ <strong>30分钟前：</strong> {last30Minutes.toISOString()}</p>
            <p>👤 <strong>用户时间：</strong> {userTime.toISOString()}</p>
            <p>✅ <strong>是否在线：</strong> {userTime >= last30Minutes ? '是' : '否'}</p>
            <p>📊 <strong>时间差：</strong> {Math.round((now.getTime() - userTime.getTime()) / (1000 * 60))} 分钟</p>
          </div>

          <p style={{ marginTop: '16px' }}><strong>问题分析：</strong></p>
          {userTime < last30Minutes ? (
            <div style={{ background: '#fef2f2', padding: '12px', borderRadius: '6px', color: '#dc2626' }}>
              <p>❌ <strong>问题发现：</strong>用户的 lastActiveTime 时间太早了！</p>
              <p>需要将时间更新为最近30分钟内的时间。</p>
            </div>
          ) : (
            <div style={{ background: '#f0fdf4', padding: '12px', borderRadius: '6px', color: '#16a34a' }}>
              <p>✅ <strong>时间正确：</strong>用户时间在最近30分钟内。</p>
              <p>可能是云函数缓存问题，请等待几分钟。</p>
            </div>
          )}

          <div style={{ background: '#fef3c7', padding: '12px', borderRadius: '6px', marginTop: '12px' }}>
            <p><strong>🔧 修复建议：</strong></p>
            <p>将用户的 lastActiveTime 更新为：<code>{now.toISOString()}</code></p>
          </div>
        </div>
      ),
    });
  };

  useEffect(() => {
    const interval = setInterval(() => {
      fetchDashboardData();
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(interval);
  }, []);

  // 创建简单的条形图组件
  const SimpleBarChart = ({ data, color, height = 60 }: { data: any[], color: string, height?: number }) => (
    <div style={{ display: 'flex', alignItems: 'end', height: `${height}px`, gap: '4px' }}>
      {data.map((item, index) => (
        <div key={index} style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <div
            style={{
              width: '100%',
              backgroundColor: color,
              height: `${(item.value / Math.max(...data.map(d => d.value))) * (height - 20)}px`,
              borderRadius: '2px 2px 0 0',
              marginBottom: '4px',
              opacity: 0.8,
              transition: 'all 0.3s ease'
            }}
          />
          <div style={{ fontSize: '10px', color: '#94a3b8', textAlign: 'center' }}>
            {item.period}
          </div>
        </div>
      ))}
    </div>
  );

  // 创建简单的线形图组件
  const SimpleLineChart = ({ data, color, height = 60 }: { data: any[], color: string, height?: number }) => {
    const maxValue = Math.max(...data.map(d => d.value));
    const points = data.map((item, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - ((item.value / maxValue) * 80);
      return `${x},${y}`;
    }).join(' ');

    return (
      <div style={{ height: `${height}px`, position: 'relative' }}>
        <svg width="100%" height="100%" style={{ position: 'absolute' }}>
          <polyline
            fill="none"
            stroke={color}
            strokeWidth="2"
            points={points}
            style={{ filter: 'drop-shadow(0 0 4px rgba(0, 212, 255, 0.3))' }}
          />
          {data.map((item, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = 100 - ((item.value / maxValue) * 80);
            return (
              <circle
                key={index}
                cx={`${x}%`}
                cy={`${y}%`}
                r="3"
                fill={color}
                style={{ filter: 'drop-shadow(0 0 2px rgba(0, 212, 255, 0.5))' }}
              />
            );
          })}
        </svg>
        <div style={{ display: 'flex', justifyContent: 'space-between', position: 'absolute', bottom: 0, width: '100%' }}>
          {data.map((item, index) => (
            <div key={index} style={{ fontSize: '10px', color: '#94a3b8', textAlign: 'center' }}>
              {item.period}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 创建环形进度条组件
  const CircularProgress = ({ percentage, color, size = 80 }: { percentage: number, color: string, size?: number }) => {
    const radius = (size - 8) / 2;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    return (
      <div style={{ position: 'relative', width: size, height: size }}>
        <svg width={size} height={size} style={{ transform: 'rotate(-90deg)' }}>
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#334155"
            strokeWidth="4"
            fill="transparent"
          />
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth="4"
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            style={{
              transition: 'stroke-dashoffset 0.5s ease-in-out',
              filter: `drop-shadow(0 0 6px ${color}40)`
            }}
          />
        </svg>
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: '#e2e8f0',
          fontSize: '14px',
          fontWeight: 'bold'
        }}>
          {percentage}%
        </div>
      </div>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <h2 style={{ color: '#e2e8f0', margin: 0 }}>📊 管理仪表盘</h2>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            type={timeRange === 'today' ? 'primary' : 'default'}
            size="small"
            onClick={() => setTimeRange('today')}
            style={timeRange === 'today' ? { background: '#00d4ff', borderColor: '#00d4ff' } : {}}
          >
            今日
          </Button>
          <Button
            type={timeRange === 'week' ? 'primary' : 'default'}
            size="small"
            onClick={() => setTimeRange('week')}
            style={timeRange === 'week' ? { background: '#00d4ff', borderColor: '#00d4ff' } : {}}
          >
            本周
          </Button>
          <Button
            type={timeRange === 'month' ? 'primary' : 'default'}
            size="small"
            onClick={() => setTimeRange('month')}
            style={timeRange === 'month' ? { background: '#00d4ff', borderColor: '#00d4ff' } : {}}
          >
            本月
          </Button>
        </div>
      </div>

      {/* 实时统计卡片 */}
      <Row gutter={[16, 16]} style={{marginBottom: '24px'}}>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
            borderColor: '#334155',
            color: '#e2e8f0',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'absolute', top: 0, right: 0, width: '60px', height: '60px', background: 'rgba(0, 212, 255, 0.1)', borderRadius: '50%', transform: 'translate(20px, -20px)' }} />
            <Statistic
              title={<span style={{color: '#94a3b8'}}>在线用户</span>}
              value={loading ? 0 : realTimeData.onlineUsers}
              valueStyle={{ color: '#00d4ff' }}
              prefix="👥"
              suffix={<span style={{ fontSize: '12px', color: '#52c41a' }}>↗ 实时</span>}
              loading={loading}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#94a3b8' }}>
              实时更新 • 总用户：{statsData.totalUsers}
            </div>
            <div style={{ marginTop: '8px', display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
              <Button
                size="small"
                type="link"
                style={{ padding: 0, height: 'auto', fontSize: '11px', color: '#00d4ff' }}
                onClick={handleDebugUsers}
              >
                🔍 调试用户数据
              </Button>
              <Button
                size="small"
                type="link"
                style={{ padding: 0, height: 'auto', fontSize: '11px', color: '#52c41a' }}
                onClick={handleQuickFix}
              >
                🔧 快速修复
              </Button>
              <Button
                size="small"
                type="link"
                style={{ padding: 0, height: 'auto', fontSize: '11px', color: '#f59e0b' }}
                onClick={handleRefreshData}
              >
                🔄 刷新数据
              </Button>
              <Button
                size="small"
                type="link"
                style={{ padding: 0, height: 'auto', fontSize: '11px', color: '#8b5cf6' }}
                onClick={handleTimeCheck}
              >
                ⏰ 时间检查
              </Button>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
            borderColor: '#334155',
            color: '#e2e8f0',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'absolute', top: 0, right: 0, width: '60px', height: '60px', background: 'rgba(82, 196, 26, 0.1)', borderRadius: '50%', transform: 'translate(20px, -20px)' }} />
            <Statistic
              title={<span style={{color: '#94a3b8'}}>活跃订单</span>}
              value={loading ? 0 : realTimeData.activeOrders}
              valueStyle={{ color: '#52c41a' }}
              prefix="📦"
              suffix={<span style={{ fontSize: '12px', color: '#52c41a' }}>↗ 进行中</span>}
              loading={loading}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#94a3b8' }}>
              总订单：{statsData.totalOrders} • 完成率 94.2%
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
            borderColor: '#334155',
            color: '#e2e8f0',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'absolute', top: 0, right: 0, width: '60px', height: '60px', background: 'rgba(250, 173, 20, 0.1)', borderRadius: '50%', transform: 'translate(20px, -20px)' }} />
            <Statistic
              title={<span style={{color: '#94a3b8'}}>今日收入</span>}
              value={loading ? 0 : realTimeData.todayRevenue}
              precision={2}
              valueStyle={{ color: '#faad14' }}
              prefix="💰"
              suffix={<span style={{ fontSize: '12px' }}>元</span>}
              loading={loading}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#94a3b8' }}>
              总收入：{statsData.totalRevenue.toLocaleString()}元
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
            borderColor: '#334155',
            color: '#e2e8f0',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'absolute', top: 0, right: 0, width: '60px', height: '60px', background: 'rgba(247, 89, 171, 0.1)', borderRadius: '50%', transform: 'translate(20px, -20px)' }} />
            <Statistic
              title={<span style={{color: '#94a3b8'}}>新增用户</span>}
              value={loading ? 0 : statsData.newUsersToday}
              valueStyle={{ color: '#f759ab' }}
              prefix="🆕"
              suffix="人"
              loading={loading}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#94a3b8' }}>
              今日新增 • 系统负载：{realTimeData.systemLoad}%
            </div>
          </Card>
        </Col>
      </Row>

      {/* 数据可视化图表 */}
      <Row gutter={[16, 16]} style={{marginBottom: '24px'}}>
        <Col xs={24} lg={8}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <h3 style={{ color: '#e2e8f0', margin: 0 }}>📈 用户活跃度</h3>
              <Tag color="#00d4ff">24小时</Tag>
            </div>
            <SimpleLineChart data={trendData.users} color="#00d4ff" height={120} />
            <div style={{ marginTop: '12px', fontSize: '12px', color: '#94a3b8' }}>
              峰值时段：12:00-16:00 • 平均在线：156人
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <h3 style={{ color: '#e2e8f0', margin: 0 }}>📊 订单趋势</h3>
              <Tag color="#52c41a">本周</Tag>
            </div>
            <SimpleBarChart data={trendData.orders} color="#52c41a" height={120} />
            <div style={{ marginTop: '12px', fontSize: '12px', color: '#94a3b8' }}>
              周末订单量最高 • 平均每日：78单
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <h3 style={{ color: '#e2e8f0', margin: 0 }}>💰 收入分析</h3>
              <Tag color="#faad14">近6月</Tag>
            </div>
            <SimpleBarChart data={trendData.revenue} color="#faad14" height={120} />
            <div style={{ marginTop: '12px', fontSize: '12px', color: '#94a3b8' }}>
              持续增长趋势 • 月均收入：55.8K
            </div>
          </Card>
        </Col>
      </Row>

      {/* 系统监控和快速操作 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <h3 style={{ color: '#e2e8f0', marginBottom: '20px' }}>🖥️ 系统监控</h3>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <CircularProgress percentage={realTimeData.systemLoad} color="#00d4ff" />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: '#94a3b8' }}>CPU使用率</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <CircularProgress percentage={76} color="#52c41a" />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: '#94a3b8' }}>内存使用率</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <CircularProgress percentage={45} color="#faad14" />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: '#94a3b8' }}>磁盘使用率</div>
                </div>
              </Col>
            </Row>
            <div style={{ marginTop: '16px', padding: '12px', background: '#334155', borderRadius: '6px' }}>
              <div style={{ fontSize: '12px', color: '#94a3b8', marginBottom: '8px' }}>系统状态</div>
              <div style={{ color: '#52c41a', fontSize: '14px' }}>✅ 所有服务运行正常</div>
              <div style={{ color: '#52c41a', fontSize: '14px' }}>✅ 数据库连接正常</div>
              <div style={{ color: '#52c41a', fontSize: '14px' }}>✅ API响应正常</div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <h3 style={{ color: '#e2e8f0', marginBottom: '20px' }}>⚡ 快速操作</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
              <Button
                style={{
                  height: '60px',
                  background: 'linear-gradient(135deg, #00d4ff20 0%, #00d4ff10 100%)',
                  borderColor: '#00d4ff',
                  color: '#00d4ff'
                }}
              >
                <div>
                  <div>👥 用户管理</div>
                  <div style={{ fontSize: '12px', opacity: 0.7 }}>查看用户列表</div>
                </div>
              </Button>
              <Button
                style={{
                  height: '60px',
                  background: 'linear-gradient(135deg, #52c41a20 0%, #52c41a10 100%)',
                  borderColor: '#52c41a',
                  color: '#52c41a'
                }}
              >
                <div>
                  <div>📦 订单管理</div>
                  <div style={{ fontSize: '12px', opacity: 0.7 }}>处理订单</div>
                </div>
              </Button>
              <Button
                style={{
                  height: '60px',
                  background: 'linear-gradient(135deg, #faad1420 0%, #faad1410 100%)',
                  borderColor: '#faad14',
                  color: '#faad14'
                }}
              >
                <div>
                  <div>💰 财务管理</div>
                  <div style={{ fontSize: '12px', opacity: 0.7 }}>查看收支</div>
                </div>
              </Button>
              <Button
                style={{
                  height: '60px',
                  background: 'linear-gradient(135deg, #f759ab20 0%, #f759ab10 100%)',
                  borderColor: '#f759ab',
                  color: '#f759ab'
                }}
              >
                <div>
                  <div>📊 数据分析</div>
                  <div style={{ fontSize: '12px', opacity: 0.7 }}>查看报表</div>
                </div>
              </Button>
            </div>

            <div style={{ marginTop: '16px', padding: '12px', background: '#334155', borderRadius: '6px' }}>
              <div style={{ fontSize: '12px', color: '#94a3b8', marginBottom: '8px' }}>今日待办</div>
              <div style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '4px' }}>🔔 3个提现申请待审核</div>
              <div style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '4px' }}>📝 5个用户反馈待处理</div>
              <div style={{ color: '#e2e8f0', fontSize: '14px' }}>⚠️ 2个异常订单需关注</div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// 用户管理页面
const UserManagement: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // 获取用户列表数据
  const fetchUserData = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const response = await userApi.getUserList({
        page,
        limit: pageSize,
        search: searchText || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined
      });

      if (response.success && response.data) {
        setUserData(response.data.users || []);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0
        });
      } else {
        // 使用模拟数据作为后备
        const mockData = [
    {
      key: '1',
      id: 'U001',
      openid: 'ox1234567890abcdef',
      nickName: '游戏达人',
      gameNickName: '三角洲战士',
      avatarUrl: '/placeholder.svg?height=40&width=40',
      phone: '13800138001',
      realName: '张三',
      isVerified: true,
      status: 'active',
      balance: 156.80,
      creditScore: 85,
      orderCount: 12,
      createTime: '2024-01-15',
      updateTime: '2024-01-20 14:30',
    },
    {
      key: '2',
      id: 'U002',
      openid: 'ox2234567890abcdef',
      nickName: '陪玩小助手',
      gameNickName: '专业陪练',
      avatarUrl: '/placeholder.svg?height=40&width=40',
      phone: '13800138002',
      realName: '李四',
      isVerified: false,
      status: 'active',
      balance: 89.50,
      creditScore: 92,
      orderCount: 8,
      createTime: '2024-01-10',
      updateTime: '2024-01-18 09:15',
    },
    {
      key: '3',
      id: 'U003',
      openid: 'ox3234567890abcdef',
      nickName: '游戏新手',
      gameNickName: '学习中',
      avatarUrl: '/placeholder.svg?height=40&width=40',
      phone: '',
      realName: '',
      isVerified: false,
      status: 'banned',
      balance: 0,
      creditScore: 45,
      orderCount: 2,
      createTime: '2024-01-05',
      updateTime: '2024-01-20 16:45',
    },
        ];
        setUserData(mockData);
        setPagination({
          current: 1,
          pageSize: 10,
          total: mockData.length
        });
        message.warning('API连接失败，显示模拟数据');
      }
    } catch (error) {
      console.error('获取用户数据失败:', error);
      message.error('获取用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchUserData();
  }, [searchText, statusFilter]);

  // 处理分页变化
  const handleTableChange = (paginationInfo: any) => {
    fetchUserData(paginationInfo.current, paginationInfo.pageSize);
  };

  // 处理用户状态更新
  // const handleStatusUpdate = async (userId: string, newStatus: string) => {
  //   try {
  //     const response = await userApi.updateUserStatus(userId, newStatus as any);
  //     if (response.success) {
  //       message.success('用户状态更新成功');
  //       fetchUserData(pagination.current, pagination.pageSize);
  //     } else {
  //       message.error('用户状态更新失败');
  //     }
  //   } catch (error) {
  //     console.error('更新用户状态失败:', error);
  //     message.error('更新用户状态失败');
  //   }
  // };

  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            src={record.avatarUrl}
            style={{ backgroundColor: '#00d4ff', marginRight: 12 }}
          >
            {record.nickName?.charAt(0) || '用'}
          </Avatar>
          <div>
            <div style={{ color: '#e2e8f0', fontWeight: 'bold' }}>
              {record.nickName || '未设置昵称'}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              游戏昵称：{record.gameNickName || '未设置'}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              ID: {record.id}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (record: any) => (
        <div>
          <div style={{ color: '#e2e8f0' }}>
            📱 {record.phone || '未绑定手机'}
          </div>
          {record.realName && (
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              实名：{record.realName}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '认证状态',
      key: 'verification',
      render: (record: any) => (
        <div>
          <Tag color={record.isVerified ? '#52c41a' : '#faad14'}>
            {record.isVerified ? '✅ 已实名' : '⏳ 未实名'}
          </Tag>
        </div>
      ),
    },
    {
      title: '账户状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          active: { color: '#52c41a', text: '🟢 正常' },
          banned: { color: '#ff4d4f', text: '🔴 已封禁' },
          inactive: { color: '#faad14', text: '🟡 非活跃' },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '账户信息',
      key: 'accountInfo',
      render: (record: any) => (
        <div>
          <div style={{ color: '#e2e8f0' }}>
            💰 余额：¥{record.balance.toFixed(2)}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px' }}>
            📊 信用分：{record.creditScore}/100
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px' }}>
            📋 订单数：{record.orderCount}
          </div>
        </div>
      ),
    },
    {
      title: '注册时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '最后活跃',
      dataIndex: 'updateTime',
      key: 'updateTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space size="middle">
          <Button type="link" size="small" style={{ color: '#00d4ff' }}>
            查看详情
          </Button>
          <Button type="link" size="small" style={{ color: '#faad14' }}>
            编辑信息
          </Button>
          {record.status === 'active' ? (
            <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
              封禁用户
            </Button>
          ) : (
            <Button type="link" size="small" style={{ color: '#52c41a' }}>
              解除封禁
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <h1 style={{color: '#00d4ff', marginBottom: '24px', fontSize: '28px'}}>
        <UserOutlined style={{marginRight: '12px'}} />
        👥 用户管理
      </h1>

      {/* 用户统计 */}
      <Row gutter={[16, 16]} style={{marginBottom: '24px'}}>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>总用户数</span>}
              value={1234}
              valueStyle={{ color: '#00d4ff' }}
              prefix="👥"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>活跃用户</span>}
              value={987}
              valueStyle={{ color: '#52c41a' }}
              prefix="🟢"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>实名用户</span>}
              value={856}
              valueStyle={{ color: '#faad14' }}
              prefix="✅"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>今日新增</span>}
              value={23}
              valueStyle={{ color: '#f759ab' }}
              prefix="🆕"
            />
          </Card>
        </Col>
      </Row>

      {/* 用户列表 */}
      <Card
        title={<span style={{color: '#00d4ff'}}>📋 用户列表</span>}
        style={{
          background: '#1e293b',
          borderColor: '#334155'
        }}
        extra={
          <Space>
            <Button type="primary" icon={<UserOutlined />}>
              添加用户
            </Button>
            <Button style={{
              background: '#334155',
              borderColor: '#475569',
              color: '#e2e8f0'
            }}>
              导出数据
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: '16px', display: 'flex', gap: '12px', alignItems: 'center' }}>
          <Input.Search
            placeholder="搜索用户昵称..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            allowClear
          />
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            <Select.Option value="all">全部状态</Select.Option>
            <Select.Option value="active">正常</Select.Option>
            <Select.Option value="inactive">停用</Select.Option>
            <Select.Option value="banned">封禁</Select.Option>
          </Select>
        </div>

        <Table
          columns={columns}
          dataSource={userData as any}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          style={{
            background: 'transparent',
          }}
        />
      </Card>
    </div>
  );
};

// 订单管理页面
const OrderManagement: React.FC = () => {
  // 模拟订单数据 - 基于实际小程序订单结构
  const orderData = [
    {
      key: '1',
      _id: 'ORD001',
      orderNo: 'TJZ20240120001',
      title: '三角洲行动陪玩',
      content: '需要一位技术好的玩家带我上分，要求KD比1.5以上',
      reward: 50.00,
      customerId: 'U001',
      customerName: '游戏达人',
      accepterId: 'U002',
      accepterName: '专业陪练',
      platformType: 'pc',
      serviceType: 'duration',
      duration: 2,
      rounds: null,
      tags: ['上分', '技术流', 'PC端'],
      orderType: 'immediate',
      status: 'in_progress',
      createTime: '2024-01-20 10:30:00',
      updateTime: '2024-01-20 14:30:00',
      startTime: '2024-01-20 14:00:00',
      endTime: null,
      evaluation: null,
    },
    {
      key: '2',
      _id: 'ORD002',
      orderNo: 'TJZ20240120002',
      title: '休闲娱乐陪玩',
      content: '想找个人一起玩游戏聊天，轻松愉快就好',
      reward: 30.00,
      customerId: 'U003',
      customerName: '游戏新手',
      accepterId: null,
      accepterName: null,
      platformType: 'mobile',
      serviceType: 'rounds',
      duration: null,
      rounds: 5,
      tags: ['休闲', '聊天', '手机端'],
      orderType: 'scheduled',
      status: 'pending',
      createTime: '2024-01-20 09:15:00',
      updateTime: '2024-01-20 09:15:00',
      startTime: null,
      endTime: null,
      evaluation: null,
    },
    {
      key: '3',
      _id: 'ORD003',
      orderNo: 'TJZ20240119001',
      title: '竞技模式陪练',
      content: '需要高手带我练习竞技模式，提升技术水平',
      reward: 80.00,
      customerId: 'U001',
      customerName: '游戏达人',
      accepterId: 'U002',
      accepterName: '专业陪练',
      platformType: 'pc',
      serviceType: 'duration',
      duration: 3,
      rounds: null,
      tags: ['竞技', '练习', '高手'],
      orderType: 'immediate',
      status: 'completed',
      createTime: '2024-01-19 15:20:00',
      updateTime: '2024-01-19 20:30:00',
      startTime: '2024-01-19 16:00:00',
      endTime: '2024-01-19 19:00:00',
      evaluation: {
        customerRating: 5,
        accepterRating: 5,
        customerComment: '非常专业，技术很好！',
        accepterComment: '客户很配合，愉快的合作！'
      },
    },
  ];

  const orderColumns = [
    {
      title: '订单信息',
      key: 'orderInfo',
      render: (record: any) => (
        <div>
          <div style={{ color: '#e2e8f0', fontWeight: 'bold', marginBottom: '4px' }}>
            {record.title}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px', marginBottom: '2px' }}>
            订单号：{record.orderNo}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px' }}>
            ID：{record._id}
          </div>
        </div>
      ),
    },
    {
      title: '服务详情',
      key: 'serviceInfo',
      render: (record: any) => (
        <div>
          <div style={{ color: '#e2e8f0', marginBottom: '4px' }}>
            💰 ¥{record.reward.toFixed(2)}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px', marginBottom: '2px' }}>
            {record.platformType === 'pc' ? '🖥️ PC端' : '📱 手机端'}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px' }}>
            {record.serviceType === 'duration'
              ? `⏰ ${record.duration}小时`
              : `🎮 ${record.rounds}局`}
          </div>
        </div>
      ),
    },
    {
      title: '发布者',
      key: 'customer',
      render: (record: any) => (
        <div style={{ color: '#e2e8f0' }}>
          👤 {record.customerName}
        </div>
      ),
    },
    {
      title: '接单者',
      key: 'accepter',
      render: (record: any) => (
        <div style={{ color: '#e2e8f0' }}>
          {record.accepterName ? `🎯 ${record.accepterName}` : '⏳ 待接单'}
        </div>
      ),
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          pending: { color: '#faad14', text: '⏳ 待接单' },
          accepted: { color: '#52c41a', text: '✅ 已接单' },
          in_progress: { color: '#1890ff', text: '🔄 进行中' },
          completed: { color: '#52c41a', text: '✅ 已完成' },
          cancelled: { color: '#ff4d4f', text: '❌ 已取消' },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '标签',
      key: 'tags',
      render: (record: any) => (
        <div>
          {record.tags.slice(0, 2).map((tag: string, index: number) => (
            <Tag key={index} style={{ marginBottom: '2px', fontSize: '12px' }}>
              {tag}
            </Tag>
          ))}
          {record.tags.length > 2 && (
            <Tag style={{ color: '#94a3b8', fontSize: '12px' }}>
              +{record.tags.length - 2}
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space size="middle">
          <Button type="link" size="small" style={{ color: '#00d4ff' }}>
            查看详情
          </Button>
          <Button type="link" size="small" style={{ color: '#faad14' }}>
            编辑订单
          </Button>
          {record.status === 'pending' && (
            <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
              取消订单
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <h2 style={{ color: '#e2e8f0', marginBottom: '24px' }}>📋 订单管理</h2>

      {/* 订单统计 */}
      <Row gutter={[16, 16]} style={{marginBottom: '24px'}}>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>总订单数</span>}
              value={2456}
              valueStyle={{ color: '#00d4ff' }}
              prefix="📋"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>进行中</span>}
              value={156}
              valueStyle={{ color: '#1890ff' }}
              prefix="🔄"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>已完成</span>}
              value={1987}
              valueStyle={{ color: '#52c41a' }}
              prefix="✅"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>今日订单</span>}
              value={45}
              valueStyle={{ color: '#f759ab' }}
              prefix="🆕"
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{
        background: '#1e293b',
        borderColor: '#334155',
        marginBottom: '16px'
      }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder="搜索订单号、标题、用户名"
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={4}>
            <Select defaultValue="all" style={{ width: '100%' }}>
              <Option value="all">全部状态</Option>
              <Option value="pending">待接单</Option>
              <Option value="accepted">已接单</Option>
              <Option value="in_progress">进行中</Option>
              <Option value="completed">已完成</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Col>
          <Col xs={12} sm={4}>
            <Select defaultValue="all" style={{ width: '100%' }}>
              <Option value="all">全部平台</Option>
              <Option value="pc">PC端</Option>
              <Option value="mobile">手机端</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8}>
            <Space>
              <Button type="primary" style={{ background: '#00d4ff', borderColor: '#00d4ff' }}>
                🔍 搜索
              </Button>
              <Button>
                🔄 重置
              </Button>
              <Button type="primary" style={{ background: '#52c41a', borderColor: '#52c41a' }}>
                📊 导出数据
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 订单列表 */}
      <Card style={{
        background: '#1e293b',
        borderColor: '#334155',
        color: '#e2e8f0'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <h3 style={{ color: '#e2e8f0', margin: 0 }}>📋 订单列表</h3>
          <Space>
            <Button type="primary" style={{ background: '#00d4ff', borderColor: '#00d4ff' }}>
              🔄 刷新数据
            </Button>
          </Space>
        </div>

        <Table
          columns={orderColumns}
          dataSource={orderData as any}
          pagination={{
            total: 2456,
            pageSize: 10,
            current: 1,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          style={{
            background: 'transparent',
          }}
          className="dark-table"
        />
      </Card>
    </div>
  );
};

// 聊天监控页面
const ChatMonitoring: React.FC = () => {
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  // const [messages, setMessages] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // 模拟聊天室数据 - 基于实际小程序聊天结构
  const chatRoomData = [
    {
      key: '1',
      _id: 'CHAT001',
      orderNo: 'TJZ20240120001',
      orderDbId: 'ORD001',
      customerId: 'U001',
      accepterId: 'U002',
      customerInfo: {
        nickName: '游戏达人',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      accepterInfo: {
        nickName: '专业陪练',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      orderInfo: {
        title: '三角洲行动陪玩',
        reward: 50.00,
        status: 'in_progress'
      },
      lastMessage: {
        content: '好的，我们开始游戏吧！',
        createTime: '2024-01-20 14:30:00',
        senderInfo: {
          nickName: '专业陪练'
        }
      },
      status: 'active',
      createTime: '2024-01-20 14:00:00',
      updateTime: '2024-01-20 14:30:00',
      messageCount: 25,
      unreadCount: 0
    },
    {
      key: '2',
      _id: 'CHAT002',
      orderNo: 'TJZ20240120002',
      orderDbId: 'ORD002',
      customerId: 'U003',
      accepterId: 'U004',
      customerInfo: {
        nickName: '游戏新手',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      accepterInfo: {
        nickName: '温柔陪玩',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      orderInfo: {
        title: '休闲娱乐陪玩',
        reward: 30.00,
        status: 'completed'
      },
      lastMessage: {
        content: '谢谢你的陪伴，很开心！',
        createTime: '2024-01-20 12:45:00',
        senderInfo: {
          nickName: '游戏新手'
        }
      },
      status: 'closed',
      createTime: '2024-01-20 10:00:00',
      updateTime: '2024-01-20 12:45:00',
      messageCount: 18,
      unreadCount: 0
    },
    {
      key: '3',
      _id: 'CHAT003',
      orderNo: 'TJZ20240120003',
      orderDbId: 'ORD003',
      customerId: 'U005',
      accepterId: 'U006',
      customerInfo: {
        nickName: '竞技玩家',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      accepterInfo: {
        nickName: '高手带飞',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      orderInfo: {
        title: '竞技模式陪练',
        reward: 80.00,
        status: 'in_progress'
      },
      lastMessage: {
        content: '这局打得不错，继续保持！',
        createTime: '2024-01-20 15:20:00',
        senderInfo: {
          nickName: '高手带飞'
        }
      },
      status: 'active',
      createTime: '2024-01-20 15:00:00',
      updateTime: '2024-01-20 15:20:00',
      messageCount: 12,
      unreadCount: 2
    },
  ];

  // 模拟聊天消息数据
  const mockMessages = [
    {
      _id: 'MSG001',
      chatRoomId: 'CHAT001',
      senderId: 'U001',
      senderInfo: {
        nickName: '游戏达人',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      type: 'text',
      content: '你好，我想找个人陪我玩三角洲行动',
      createTime: '2024-01-20 14:00:00'
    },
    {
      _id: 'MSG002',
      chatRoomId: 'CHAT001',
      senderId: 'U002',
      senderInfo: {
        nickName: '专业陪练',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      type: 'text',
      content: '没问题！我KD比1.8，可以带你上分',
      createTime: '2024-01-20 14:02:00'
    },
    {
      _id: 'MSG003',
      chatRoomId: 'CHAT001',
      senderId: 'U001',
      senderInfo: {
        nickName: '游戏达人',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      type: 'text',
      content: '太好了！我们什么时候开始？',
      createTime: '2024-01-20 14:05:00'
    },
    {
      _id: 'MSG004',
      chatRoomId: 'CHAT001',
      senderId: 'U002',
      senderInfo: {
        nickName: '专业陪练',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      type: 'text',
      content: '现在就可以，我已经在线了',
      createTime: '2024-01-20 14:10:00'
    },
    {
      _id: 'MSG005',
      chatRoomId: 'CHAT001',
      senderId: 'U002',
      senderInfo: {
        nickName: '专业陪练',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      type: 'system',
      content: '订单已开始，祝您游戏愉快！',
      createTime: '2024-01-20 14:15:00'
    }
  ];

  const chatRoomColumns = [
    {
      title: '聊天室信息',
      key: 'roomInfo',
      render: (record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ position: 'relative', marginRight: '12px' }}>
            <Avatar
              src={record.customerInfo.avatarUrl}
              style={{ backgroundColor: '#00d4ff' }}
            >
              {record.customerInfo.nickName?.charAt(0) || '用'}
            </Avatar>
            {record.status === 'active' && (
              <div style={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                width: '8px',
                height: '8px',
                backgroundColor: '#52c41a',
                borderRadius: '50%',
                border: '2px solid #1e293b'
              }} />
            )}
          </div>
          <div>
            <div style={{ color: '#e2e8f0', fontWeight: 'bold', marginBottom: '2px' }}>
              {record.orderInfo.title}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              订单号：{record.orderNo}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              💰 ¥{record.orderInfo.reward.toFixed(2)}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '参与用户',
      key: 'participants',
      render: (record: any) => (
        <div>
          <div style={{ color: '#e2e8f0', marginBottom: '4px' }}>
            👤 发布者：{record.customerInfo.nickName}
          </div>
          <div style={{ color: '#e2e8f0' }}>
            🎯 接单者：{record.accepterInfo.nickName}
          </div>
        </div>
      ),
    },
    {
      title: '最后消息',
      key: 'lastMessage',
      render: (record: any) => (
        <div>
          {record.lastMessage ? (
            <>
              <div style={{ color: '#e2e8f0', marginBottom: '2px' }}>
                {record.lastMessage.content.length > 20
                  ? record.lastMessage.content.substring(0, 20) + '...'
                  : record.lastMessage.content}
              </div>
              <div style={{ color: '#94a3b8', fontSize: '12px' }}>
                {record.lastMessage.senderInfo.nickName} · {record.lastMessage.createTime}
              </div>
            </>
          ) : (
            <div style={{ color: '#94a3b8' }}>暂无消息</div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: any) => (
        <div>
          <Tag color={record.status === 'active' ? '#52c41a' : '#94a3b8'}>
            {record.status === 'active' ? '🟢 活跃' : '⚪ 已关闭'}
          </Tag>
          <div style={{ color: '#94a3b8', fontSize: '12px', marginTop: '4px' }}>
            💬 {record.messageCount} 条消息
          </div>
          {record.unreadCount > 0 && (
            <div style={{ color: '#ff4d4f', fontSize: '12px' }}>
              🔴 {record.unreadCount} 条未读
            </div>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space size="middle">
          <Button
            type="link"
            size="small"
            style={{ color: '#00d4ff' }}
            onClick={() => setSelectedRoom(record)}
          >
            查看消息
          </Button>
          <Button type="link" size="small" style={{ color: '#faad14' }}>
            导出记录
          </Button>
          {record.status === 'active' && (
            <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
              关闭聊天室
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const renderMessageList = () => {
    if (!selectedRoom) {
      return (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '400px',
          color: '#94a3b8'
        }}>
          请选择一个聊天室查看消息记录
        </div>
      );
    }

    const roomMessages = mockMessages.filter(msg => msg.chatRoomId === selectedRoom._id);

    return (
      <div style={{ height: '400px', overflowY: 'auto', padding: '16px' }}>
        {roomMessages.map((message) => (
          <div key={message._id} style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'flex-start' }}>
              <Avatar
                src={message.senderInfo.avatarUrl}
                size="small"
                style={{ backgroundColor: '#00d4ff', marginRight: '8px' }}
              >
                {message.senderInfo.nickName?.charAt(0) || '用'}
              </Avatar>
              <div style={{ flex: 1 }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '4px'
                }}>
                  <span style={{ color: '#e2e8f0', fontSize: '14px', fontWeight: 'bold' }}>
                    {message.senderInfo.nickName}
                  </span>
                  <span style={{ color: '#94a3b8', fontSize: '12px', marginLeft: '8px' }}>
                    {message.createTime}
                  </span>
                  <Tag
                    color={message.type === 'system' ? '#faad14' : '#52c41a'}
                    style={{ marginLeft: '8px', fontSize: '12px' }}
                  >
                    {message.type === 'system' ? '系统' : '文本'}
                  </Tag>
                </div>
                <div style={{
                  color: message.type === 'system' ? '#faad14' : '#e2e8f0',
                  backgroundColor: message.type === 'system' ? '#2d1b0e' : '#334155',
                  padding: '8px 12px',
                  borderRadius: '8px',
                  fontSize: '14px'
                }}>
                  {message.content}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <h2 style={{ color: '#e2e8f0', marginBottom: '24px' }}>💬 聊天监控</h2>

      {/* 聊天统计 */}
      <Row gutter={[16, 16]} style={{marginBottom: '24px'}}>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>总聊天室</span>}
              value={156}
              valueStyle={{ color: '#00d4ff' }}
              prefix="💬"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>活跃聊天室</span>}
              value={89}
              valueStyle={{ color: '#52c41a' }}
              prefix="🟢"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>今日消息</span>}
              value={2456}
              valueStyle={{ color: '#1890ff' }}
              prefix="📨"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>异常消息</span>}
              value={3}
              valueStyle={{ color: '#ff4d4f' }}
              prefix="⚠️"
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{
        background: '#1e293b',
        borderColor: '#334155',
        marginBottom: '16px'
      }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder="搜索聊天室、用户名、订单号"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={4}>
            <Select defaultValue="all" style={{ width: '100%' }}>
              <Option value="all">全部状态</Option>
              <Option value="active">活跃中</Option>
              <Option value="closed">已关闭</Option>
            </Select>
          </Col>
          <Col xs={12} sm={4}>
            <Select defaultValue="all" style={{ width: '100%' }}>
              <Option value="all">全部类型</Option>
              <Option value="text">文本消息</Option>
              <Option value="image">图片消息</Option>
              <Option value="voice">语音消息</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8}>
            <Space>
              <Button type="primary" style={{ background: '#00d4ff', borderColor: '#00d4ff' }}>
                🔍 搜索
              </Button>
              <Button>
                🔄 重置
              </Button>
              <Button type="primary" style={{ background: '#52c41a', borderColor: '#52c41a' }}>
                📊 导出数据
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 聊天室列表和消息查看 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={14}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '16px'
            }}>
              <h3 style={{ color: '#e2e8f0', margin: 0 }}>💬 聊天室列表</h3>
              <Space>
                <Button type="primary" style={{ background: '#00d4ff', borderColor: '#00d4ff' }}>
                  🔄 刷新数据
                </Button>
              </Space>
            </div>

            <Table
              columns={chatRoomColumns}
              dataSource={chatRoomData as any}
              pagination={{
                total: 156,
                pageSize: 10,
                current: 1,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              style={{
                background: 'transparent',
              }}
              className="dark-table"
              size="small"
            />
          </Card>
        </Col>

        <Col xs={24} lg={10}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '16px'
            }}>
              <h3 style={{ color: '#e2e8f0', margin: 0 }}>
                📨 消息记录
                {selectedRoom && (
                  <span style={{ fontSize: '14px', fontWeight: 'normal', color: '#94a3b8', marginLeft: '8px' }}>
                    - {selectedRoom.orderInfo.title}
                  </span>
                )}
              </h3>
              {selectedRoom && (
                <Space>
                  <Button size="small" type="primary" style={{ background: '#faad14', borderColor: '#faad14' }}>
                    📤 导出记录
                  </Button>
                </Space>
              )}
            </div>

            {renderMessageList()}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// 钱包管理页面
const WalletManagement: React.FC = () => {
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // 模拟钱包交易数据 - 基于实际小程序钱包结构
  const transactionData = [
    {
      key: '1',
      _id: 'TXN001',
      userId: 'U001',
      userName: '游戏达人',
      userAvatar: '/placeholder.svg?height=40&width=40',
      type: 'recharge',
      amount: 100.00,
      status: 'completed',
      description: '余额充值 - 微信支付',
      paymentMethod: 'wechat',
      createTime: '2024-01-20 10:30:00',
      completedAt: '2024-01-20 10:31:00',
      transactionNo: 'RCH20240120001',
    },
    {
      key: '2',
      _id: 'TXN002',
      userId: 'U002',
      userName: '专业陪练',
      userAvatar: '/placeholder.svg?height=40&width=40',
      type: 'withdraw',
      amount: 80.00,
      status: 'pending',
      description: '提现申请 - 支付宝',
      paymentMethod: 'alipay',
      createTime: '2024-01-20 14:15:00',
      completedAt: null,
      transactionNo: 'WTH20240120001',
      withdrawInfo: {
        account: '138****8888',
        realName: '李四',
        bankName: '支付宝'
      }
    },
    {
      key: '3',
      _id: 'TXN003',
      userId: 'U001',
      userName: '游戏达人',
      userAvatar: '/placeholder.svg?height=40&width=40',
      type: 'payment',
      amount: 50.00,
      status: 'completed',
      description: '订单支付 - TJZ20240120001',
      relatedOrderNo: 'TJZ20240120001',
      createTime: '2024-01-20 14:30:00',
      completedAt: '2024-01-20 14:30:00',
      transactionNo: 'PAY20240120001',
    },
    {
      key: '4',
      _id: 'TXN004',
      userId: 'U002',
      userName: '专业陪练',
      userAvatar: '/placeholder.svg?height=40&width=40',
      type: 'income',
      amount: 40.00,
      status: 'completed',
      description: '订单收入 - TJZ20240120001',
      relatedOrderNo: 'TJZ20240120001',
      createTime: '2024-01-20 17:30:00',
      completedAt: '2024-01-20 17:30:00',
      transactionNo: 'INC20240120001',
    },
    {
      key: '5',
      _id: 'TXN005',
      userId: 'U003',
      userName: '游戏新手',
      userAvatar: '/placeholder.svg?height=40&width=40',
      type: 'refund',
      amount: 30.00,
      status: 'completed',
      description: '订单退款 - TJZ20240120002',
      relatedOrderNo: 'TJZ20240120002',
      createTime: '2024-01-20 16:00:00',
      completedAt: '2024-01-20 16:05:00',
      transactionNo: 'REF20240120001',
    },
  ];

  const transactionColumns = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            src={record.userAvatar}
            style={{ backgroundColor: '#00d4ff', marginRight: '12px' }}
          >
            {record.userName?.charAt(0) || '用'}
          </Avatar>
          <div>
            <div style={{ color: '#e2e8f0', fontWeight: 'bold' }}>
              {record.userName}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              ID: {record.userId}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '交易信息',
      key: 'transactionInfo',
      render: (record: any) => (
        <div>
          <div style={{ color: '#e2e8f0', fontWeight: 'bold', marginBottom: '4px' }}>
            {getTransactionTypeIcon(record.type)} {getTransactionTypeName(record.type)}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px', marginBottom: '2px' }}>
            {record.description}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px' }}>
            交易号：{record.transactionNo}
          </div>
        </div>
      ),
    },
    {
      title: '金额',
      key: 'amount',
      render: (record: any) => (
        <div>
          <div style={{
            color: getAmountColor(record.type),
            fontWeight: 'bold',
            fontSize: '16px'
          }}>
            {getAmountPrefix(record.type)}¥{record.amount.toFixed(2)}
          </div>
          {record.paymentMethod && (
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              {getPaymentMethodName(record.paymentMethod)}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          pending: { color: '#faad14', text: '⏳ 待处理' },
          completed: { color: '#52c41a', text: '✅ 已完成' },
          failed: { color: '#ff4d4f', text: '❌ 失败' },
          cancelled: { color: '#94a3b8', text: '⚪ 已取消' },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '时间',
      key: 'time',
      render: (record: any) => (
        <div>
          <div style={{ color: '#e2e8f0', fontSize: '12px' }}>
            创建：{record.createTime}
          </div>
          {record.completedAt && (
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              完成：{record.completedAt}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space size="middle">
          <Button
            type="link"
            size="small"
            style={{ color: '#00d4ff' }}
            onClick={() => setSelectedTransaction(record)}
          >
            查看详情
          </Button>
          {record.type === 'withdraw' && record.status === 'pending' && (
            <>
              <Button type="link" size="small" style={{ color: '#52c41a' }}>
                批准提现
              </Button>
              <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
                拒绝提现
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  // 辅助函数
  const getTransactionTypeIcon = (type: string) => {
    const icons = {
      recharge: '💰',
      withdraw: '💸',
      payment: '💳',
      income: '💵',
      refund: '🔄',
    };
    return icons[type as keyof typeof icons] || '💼';
  };

  const getTransactionTypeName = (type: string) => {
    const names = {
      recharge: '充值',
      withdraw: '提现',
      payment: '支付',
      income: '收入',
      refund: '退款',
    };
    return names[type as keyof typeof names] || '其他';
  };

  const getAmountColor = (type: string) => {
    return ['recharge', 'income', 'refund'].includes(type) ? '#52c41a' : '#ff4d4f';
  };

  const getAmountPrefix = (type: string) => {
    return ['recharge', 'income', 'refund'].includes(type) ? '+' : '-';
  };

  const getPaymentMethodName = (method: string) => {
    const methods = {
      wechat: '微信支付',
      alipay: '支付宝',
      bank: '银行卡',
    };
    return methods[method as keyof typeof methods] || method;
  };

  // 统计数据
  const totalRecharge = transactionData
    .filter(t => t.type === 'recharge' && t.status === 'completed')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalWithdraw = transactionData
    .filter(t => t.type === 'withdraw' && t.status === 'completed')
    .reduce((sum, t) => sum + t.amount, 0);

  const pendingWithdraw = transactionData
    .filter(t => t.type === 'withdraw' && t.status === 'pending')
    .reduce((sum, t) => sum + t.amount, 0);

  const todayTransactions = transactionData
    .filter(t => t.createTime.includes('2024-01-20'))
    .length;

  return (
    <div style={{ padding: '24px' }}>
      <h2 style={{ color: '#e2e8f0', marginBottom: '24px' }}>💰 钱包管理</h2>

      {/* 钱包统计 */}
      <Row gutter={[16, 16]} style={{marginBottom: '24px'}}>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>总充值金额</span>}
              value={totalRecharge}
              precision={2}
              valueStyle={{ color: '#52c41a' }}
              prefix="💰"
              suffix="元"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>总提现金额</span>}
              value={totalWithdraw}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              prefix="💸"
              suffix="元"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>待审核提现</span>}
              value={pendingWithdraw}
              precision={2}
              valueStyle={{ color: '#faad14' }}
              prefix="⏳"
              suffix="元"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>今日交易</span>}
              value={todayTransactions}
              valueStyle={{ color: '#f759ab' }}
              prefix="📊"
              suffix="笔"
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{
        background: '#1e293b',
        borderColor: '#334155',
        marginBottom: '16px'
      }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder="搜索用户名、交易号、描述"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={4}>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '100%' }}
            >
              <Option value="all">全部状态</Option>
              <Option value="pending">待处理</Option>
              <Option value="completed">已完成</Option>
              <Option value="failed">失败</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Col>
          <Col xs={12} sm={4}>
            <Select defaultValue="all" style={{ width: '100%' }}>
              <Option value="all">全部类型</Option>
              <Option value="recharge">充值</Option>
              <Option value="withdraw">提现</Option>
              <Option value="payment">支付</Option>
              <Option value="income">收入</Option>
              <Option value="refund">退款</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8}>
            <Space>
              <Button type="primary" style={{ background: '#00d4ff', borderColor: '#00d4ff' }}>
                🔍 搜索
              </Button>
              <Button>
                🔄 重置
              </Button>
              <Button type="primary" style={{ background: '#52c41a', borderColor: '#52c41a' }}>
                📊 导出数据
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 交易记录列表 */}
      <Card style={{
        background: '#1e293b',
        borderColor: '#334155',
        color: '#e2e8f0'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <h3 style={{ color: '#e2e8f0', margin: 0 }}>💳 交易记录</h3>
          <Space>
            <Button type="primary" style={{ background: '#00d4ff', borderColor: '#00d4ff' }}>
              🔄 刷新数据
            </Button>
            <Button type="primary" style={{ background: '#faad14', borderColor: '#faad14' }}>
              📋 批量审核
            </Button>
          </Space>
        </div>

        <Table
          columns={transactionColumns}
          dataSource={transactionData as any}
          pagination={{
            total: 1256,
            pageSize: 10,
            current: 1,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          style={{
            background: 'transparent',
          }}
          className="dark-table"
        />
      </Card>

      {/* 交易详情弹窗 */}
      <Modal
        title="交易详情"
        open={!!selectedTransaction}
        onCancel={() => setSelectedTransaction(null)}
        footer={[
          <Button key="close" onClick={() => setSelectedTransaction(null)}>
            关闭
          </Button>,
          selectedTransaction?.type === 'withdraw' && selectedTransaction?.status === 'pending' && (
            <Button key="approve" type="primary" style={{ background: '#52c41a', borderColor: '#52c41a' }}>
              批准提现
            </Button>
          ),
        ]}
        styles={{
          mask: { backgroundColor: 'rgba(0, 0, 0, 0.7)' },
          content: { backgroundColor: '#1e293b', color: '#e2e8f0' }
        }}
      >
        {selectedTransaction && (
          <div style={{ color: '#e2e8f0' }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ color: '#94a3b8', marginBottom: '4px' }}>交易类型</div>
                  <div>{getTransactionTypeIcon(selectedTransaction.type)} {getTransactionTypeName(selectedTransaction.type)}</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ color: '#94a3b8', marginBottom: '4px' }}>交易金额</div>
                  <div style={{ color: getAmountColor(selectedTransaction.type), fontWeight: 'bold' }}>
                    {getAmountPrefix(selectedTransaction.type)}¥{selectedTransaction.amount.toFixed(2)}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ color: '#94a3b8', marginBottom: '4px' }}>用户信息</div>
                  <div>{selectedTransaction.userName} ({selectedTransaction.userId})</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ color: '#94a3b8', marginBottom: '4px' }}>交易状态</div>
                  <Tag color={selectedTransaction.status === 'completed' ? '#52c41a' : '#faad14'}>
                    {selectedTransaction.status === 'completed' ? '✅ 已完成' : '⏳ 待处理'}
                  </Tag>
                </div>
              </Col>
              <Col span={24}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ color: '#94a3b8', marginBottom: '4px' }}>交易描述</div>
                  <div>{selectedTransaction.description}</div>
                </div>
              </Col>
              {selectedTransaction.withdrawInfo && (
                <Col span={24}>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ color: '#94a3b8', marginBottom: '4px' }}>提现信息</div>
                    <div>
                      <div>账户：{selectedTransaction.withdrawInfo.account}</div>
                      <div>姓名：{selectedTransaction.withdrawInfo.realName}</div>
                      <div>银行：{selectedTransaction.withdrawInfo.bankName}</div>
                    </div>
                  </div>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

// 通知和评价管理页面
const NotificationEvaluationManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('notifications');
  // const [selectedNotification, setSelectedNotification] = useState<any>(null);
  // const [selectedEvaluation, setSelectedEvaluation] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // 模拟通知数据 - 基于实际小程序通知结构
  const notificationData = [
    {
      key: '1',
      _id: 'NOT001',
      type: 'order',
      title: '订单状态更新',
      content: '您的订单TJZ20240120001已完成',
      receiverId: 'U001',
      receiverInfo: {
        nickName: '游戏达人',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      senderId: 'SYSTEM',
      senderName: '系统',
      status: 'read',
      orderId: 'ORD001',
      orderNo: 'TJZ20240120001',
      createTime: '2024-01-20 17:30:00',
      readTime: '2024-01-20 17:35:00',
      priority: 'normal'
    },
    {
      key: '2',
      _id: 'NOT002',
      type: 'chat',
      title: '新消息',
      content: '专业陪练：好的，我们开始游戏吧！',
      receiverId: 'U001',
      receiverInfo: {
        nickName: '游戏达人',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      senderId: 'U002',
      senderName: '专业陪练',
      status: 'unread',
      createTime: '2024-01-20 14:30:00',
      priority: 'normal'
    },
    {
      key: '3',
      _id: 'NOT003',
      type: 'system',
      title: '系统维护通知',
      content: '系统将于今晚23:00-01:00进行维护，请提前做好准备',
      receiverId: null, // 系统广播
      receiverInfo: null,
      senderId: 'ADMIN',
      senderName: '管理员',
      status: 'unread',
      createTime: '2024-01-20 16:00:00',
      priority: 'high'
    },
  ];

  // 模拟评价数据 - 基于实际小程序评价结构
  const evaluationData = [
    {
      key: '1',
      _id: 'EVA001',
      orderId: 'ORD001',
      orderNo: 'TJZ20240120001',
      evaluatorId: 'U001',
      evaluatedId: 'U002',
      evaluatorInfo: {
        nickName: '游戏达人',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      evaluatedInfo: {
        nickName: '专业陪练',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      rating: 5,
      comment: '服务很好，技术过硬，下次还会找他！',
      tags: ['技术好', '态度好', '准时'],
      isAnonymous: false,
      createTime: '2024-01-20 18:00:00',
      orderInfo: {
        title: '三角洲行动陪玩',
        reward: 50.00
      }
    },
    {
      key: '2',
      _id: 'EVA002',
      orderId: 'ORD002',
      orderNo: 'TJZ20240120002',
      evaluatorId: 'U003',
      evaluatedId: 'U004',
      evaluatorInfo: {
        nickName: '游戏新手',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      evaluatedInfo: {
        nickName: '温柔陪练',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      rating: 4,
      comment: '很有耐心，教得很好，就是声音有点小',
      tags: ['有耐心', '教学好'],
      isAnonymous: false,
      createTime: '2024-01-20 13:00:00',
      orderInfo: {
        title: '休闲娱乐陪玩',
        reward: 30.00
      }
    },
    {
      key: '3',
      _id: 'EVA003',
      orderId: 'ORD003',
      orderNo: 'TJZ20240120003',
      evaluatorId: 'U006',
      evaluatedId: 'U005',
      evaluatorInfo: {
        nickName: '匿名用户',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      evaluatedInfo: {
        nickName: '竞技玩家',
        avatarUrl: '/placeholder.svg?height=40&width=40'
      },
      rating: 2,
      comment: '技术一般，态度也不太好',
      tags: ['态度差'],
      isAnonymous: true,
      createTime: '2024-01-20 16:30:00',
      orderInfo: {
        title: '竞技模式陪练',
        reward: 80.00
      }
    },
  ];

  const notificationColumns = [
    {
      title: '通知信息',
      key: 'notificationInfo',
      render: (record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ marginRight: '12px' }}>
            {getNotificationIcon(record.type)}
          </div>
          <div>
            <div style={{ color: '#e2e8f0', fontWeight: 'bold', marginBottom: '4px' }}>
              {record.title}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px', marginBottom: '2px' }}>
              {record.content.length > 30
                ? record.content.substring(0, 30) + '...'
                : record.content}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              类型：{getNotificationTypeName(record.type)}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '接收者',
      key: 'receiver',
      render: (record: any) => (
        <div>
          {record.receiverInfo ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                src={record.receiverInfo.avatarUrl}
                size="small"
                style={{ backgroundColor: '#00d4ff', marginRight: '8px' }}
              >
                {record.receiverInfo.nickName?.charAt(0) || '用'}
              </Avatar>
              <div>
                <div style={{ color: '#e2e8f0' }}>
                  {record.receiverInfo.nickName}
                </div>
                <div style={{ color: '#94a3b8', fontSize: '12px' }}>
                  {record.receiverId}
                </div>
              </div>
            </div>
          ) : (
            <div style={{ color: '#faad14' }}>
              📢 系统广播
            </div>
          )}
        </div>
      ),
    },
    {
      title: '发送者',
      key: 'sender',
      render: (record: any) => (
        <div style={{ color: '#e2e8f0' }}>
          {record.senderName}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'read' ? '#52c41a' : '#faad14'}>
          {status === 'read' ? '✅ 已读' : '📬 未读'}
        </Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => {
        const priorityConfig = {
          low: { color: '#94a3b8', text: '🔵 低' },
          normal: { color: '#1890ff', text: '🟢 普通' },
          high: { color: '#faad14', text: '🟡 高' },
          urgent: { color: '#ff4d4f', text: '🔴 紧急' },
        };
        const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.normal;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space size="middle">
          <Button
            type="link"
            size="small"
            style={{ color: '#00d4ff' }}
            onClick={() => console.log('查看通知详情:', record)}
          >
            查看详情
          </Button>
          {record.status === 'unread' && (
            <Button type="link" size="small" style={{ color: '#52c41a' }}>
              标记已读
            </Button>
          )}
          <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const evaluationColumns = [
    {
      title: '评价信息',
      key: 'evaluationInfo',
      render: (record: any) => (
        <div>
          <div style={{ color: '#e2e8f0', fontWeight: 'bold', marginBottom: '4px' }}>
            {record.orderInfo.title}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px', marginBottom: '2px' }}>
            订单号：{record.orderNo}
          </div>
          <div style={{ color: '#94a3b8', fontSize: '12px' }}>
            💰 ¥{record.orderInfo.reward.toFixed(2)}
          </div>
        </div>
      ),
    },
    {
      title: '评价者',
      key: 'evaluator',
      render: (record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            src={record.evaluatorInfo.avatarUrl}
            size="small"
            style={{ backgroundColor: '#00d4ff', marginRight: '8px' }}
          >
            {record.evaluatorInfo.nickName?.charAt(0) || '用'}
          </Avatar>
          <div>
            <div style={{ color: '#e2e8f0' }}>
              {record.isAnonymous ? '匿名用户' : record.evaluatorInfo.nickName}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              {record.evaluatorId}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '被评价者',
      key: 'evaluated',
      render: (record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            src={record.evaluatedInfo.avatarUrl}
            size="small"
            style={{ backgroundColor: '#52c41a', marginRight: '8px' }}
          >
            {record.evaluatedInfo.nickName?.charAt(0) || '用'}
          </Avatar>
          <div>
            <div style={{ color: '#e2e8f0' }}>
              {record.evaluatedInfo.nickName}
            </div>
            <div style={{ color: '#94a3b8', fontSize: '12px' }}>
              {record.evaluatedId}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '评分',
      key: 'rating',
      render: (record: any) => (
        <div>
          <div style={{ color: '#faad14', fontSize: '16px', marginBottom: '4px' }}>
            {'⭐'.repeat(record.rating)}
            <span style={{ color: '#94a3b8', marginLeft: '8px' }}>
              {record.rating}/5
            </span>
          </div>
          {record.tags.length > 0 && (
            <div>
              {record.tags.map((tag: string, index: number) => (
                <Tag key={index} color="#1890ff" style={{ marginBottom: '2px', fontSize: '12px' }}>
                  {tag}
                </Tag>
              ))}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '评价内容',
      key: 'comment',
      render: (record: any) => (
        <div style={{ color: '#e2e8f0', maxWidth: '200px' }}>
          {record.comment.length > 50
            ? record.comment.substring(0, 50) + '...'
            : record.comment}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space size="middle">
          <Button
            type="link"
            size="small"
            style={{ color: '#00d4ff' }}
            onClick={() => console.log('查看评价详情:', record)}
          >
            查看详情
          </Button>
          {record.rating <= 2 && (
            <Button type="link" size="small" style={{ color: '#faad14' }}>
              处理投诉
            </Button>
          )}
          <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 辅助函数
  const getNotificationIcon = (type: string) => {
    const icons = {
      order: '📦',
      chat: '💬',
      system: '⚙️',
      payment: '💰',
      evaluation: '⭐',
    };
    return icons[type as keyof typeof icons] || '📢';
  };

  const getNotificationTypeName = (type: string) => {
    const names = {
      order: '订单通知',
      chat: '聊天消息',
      system: '系统通知',
      payment: '支付通知',
      evaluation: '评价通知',
    };
    return names[type as keyof typeof names] || '其他';
  };

  // 统计数据
  const totalNotifications = notificationData.length;
  const unreadNotifications = notificationData.filter(n => n.status === 'unread').length;
  const totalEvaluations = evaluationData.length;
  const averageRating = evaluationData.reduce((sum, e) => sum + e.rating, 0) / evaluationData.length;

  return (
    <div style={{ padding: '24px' }}>
      <h2 style={{ color: '#e2e8f0', marginBottom: '24px' }}>📢 通知和评价管理</h2>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{marginBottom: '24px'}}>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>总通知数</span>}
              value={totalNotifications}
              valueStyle={{ color: '#1890ff' }}
              prefix="📢"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>未读通知</span>}
              value={unreadNotifications}
              valueStyle={{ color: '#faad14' }}
              prefix="📬"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>总评价数</span>}
              value={totalEvaluations}
              valueStyle={{ color: '#52c41a' }}
              prefix="⭐"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: '#1e293b',
            borderColor: '#334155',
            color: '#e2e8f0'
          }}>
            <Statistic
              title={<span style={{color: '#94a3b8'}}>平均评分</span>}
              value={averageRating}
              precision={1}
              valueStyle={{ color: '#f759ab' }}
              prefix="⭐"
              suffix="/5"
            />
          </Card>
        </Col>
      </Row>

      {/* 标签页切换 */}
      <Card style={{
        background: '#1e293b',
        borderColor: '#334155',
        marginBottom: '16px'
      }}>
        <div style={{ marginBottom: '16px' }}>
          <Space size="large">
            <Button
              type={activeTab === 'notifications' ? 'primary' : 'default'}
              onClick={() => setActiveTab('notifications')}
              style={activeTab === 'notifications' ? { background: '#00d4ff', borderColor: '#00d4ff' } : {}}
            >
              📢 通知管理
            </Button>
            <Button
              type={activeTab === 'evaluations' ? 'primary' : 'default'}
              onClick={() => setActiveTab('evaluations')}
              style={activeTab === 'evaluations' ? { background: '#52c41a', borderColor: '#52c41a' } : {}}
            >
              ⭐ 评价管理
            </Button>
          </Space>
        </div>

        {/* 搜索和操作 */}
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder={activeTab === 'notifications' ? '搜索通知标题、内容' : '搜索评价内容、用户名'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={16}>
            <Space>
              <Button type="primary" style={{ background: '#00d4ff', borderColor: '#00d4ff' }}>
                🔍 搜索
              </Button>
              <Button>
                🔄 重置
              </Button>
              {activeTab === 'notifications' && (
                <>
                  <Button type="primary" style={{ background: '#52c41a', borderColor: '#52c41a' }}>
                    📤 发送通知
                  </Button>
                  <Button type="primary" style={{ background: '#faad14', borderColor: '#faad14' }}>
                    📋 批量操作
                  </Button>
                </>
              )}
              <Button type="primary" style={{ background: '#1890ff', borderColor: '#1890ff' }}>
                📊 导出数据
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card style={{
        background: '#1e293b',
        borderColor: '#334155',
        color: '#e2e8f0'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <h3 style={{ color: '#e2e8f0', margin: 0 }}>
            {activeTab === 'notifications' ? '📢 通知列表' : '⭐ 评价列表'}
          </h3>
          <Button type="primary" style={{ background: '#00d4ff', borderColor: '#00d4ff' }}>
            🔄 刷新数据
          </Button>
        </div>

        <Table
          columns={activeTab === 'notifications' ? notificationColumns : evaluationColumns}
          dataSource={activeTab === 'notifications' ? notificationData as any : evaluationData as any}
          pagination={{
            total: activeTab === 'notifications' ? 156 : 89,
            pageSize: 10,
            current: 1,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          style={{
            background: 'transparent',
          }}
          className="dark-table"
        />
      </Card>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#00d4ff',
          colorBgContainer: '#1e293b',
          colorBgElevated: '#1e293b',
          colorBorder: '#334155',
          colorText: '#e2e8f0',
          colorTextSecondary: '#94a3b8',
        },
      }}
    >
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="/test" element={<TestPage />} />

          {/* 管理系统路由 */}
          <Route path="/admin/*" element={
            <AdminLayout>
              <Routes>
                <Route path="dashboard" element={<SimpleDashboard />} />
                <Route path="users" element={<UserManagement />} />
                <Route path="orders" element={<OrderManagement />} />
                <Route path="chat" element={<ChatMonitoring />} />
                <Route path="wallet" element={<WalletManagement />} />
                <Route path="notifications" element={<NotificationEvaluationManagement />} />
                <Route path="evaluations" element={<NotificationEvaluationManagement />} />
                <Route path="settings" element={<div style={{color: '#cbd5e1'}}>⚙️ 系统设置页面开发中...</div>} />
                <Route path="" element={<Navigate to="dashboard" replace />} />
              </Routes>
            </AdminLayout>
          } />

          {/* 兼容旧路由 */}
          <Route path="/dashboard" element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
        </Routes>
      </BrowserRouter>
    </ConfigProvider>
  );
};

export default App;
