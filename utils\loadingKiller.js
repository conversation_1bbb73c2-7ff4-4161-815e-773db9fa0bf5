/**
 * 加载状态消除器
 * 彻底消除所有烦人的"加载中"提示，提升用户体验
 */

class LoadingKiller {
  constructor() {
    this.originalMethods = {};
    this.isActive = false;
    this.allowedPages = []; // 允许显示加载的页面
    this.blockedCount = 0;
    
    console.log('🚫 [加载消除器] 初始化完成');
  }

  /**
   * 激活加载消除器
   */
  activate(options = {}) {
    if (this.isActive) {
      console.log('🚫 [加载消除器] 已经激活，跳过');
      return;
    }

    const { 
      allowPages = [], 
      allowVoiceUpload = false,
      allowChatRoomCreation = false 
    } = options;

    this.allowedPages = allowPages;
    this.isActive = true;

    // 保存原始方法
    this.originalMethods = {
      wxShowLoading: wx.showLoading,
      wxHideLoading: wx.hideLoading
    };

    // 替换 wx.showLoading
    wx.showLoading = (options = {}) => {
      const currentPage = this.getCurrentPagePath();
      
      // 检查是否允许显示加载
      if (this.shouldAllowLoading(options, currentPage)) {
        console.log('✅ [加载消除器] 允许显示加载:', options.title);
        return this.originalMethods.wxShowLoading(options);
      }

      // 阻止显示加载
      this.blockedCount++;
      console.log(`🚫 [加载消除器] 已阻止加载提示 #${this.blockedCount}:`, options.title || '加载中...');
      
      // 立即调用 hideLoading 确保没有残留
      this.originalMethods.wxHideLoading();
    };

    // 增强 wx.hideLoading
    wx.hideLoading = () => {
      this.originalMethods.wxHideLoading();
      
      // 多次调用确保彻底隐藏
      setTimeout(() => this.originalMethods.wxHideLoading(), 1);
      setTimeout(() => this.originalMethods.wxHideLoading(), 10);
      setTimeout(() => this.originalMethods.wxHideLoading(), 50);
    };

    console.log('🚫 [加载消除器] 已激活，开始拦截所有加载提示');
  }

  /**
   * 停用加载消除器
   */
  deactivate() {
    if (!this.isActive) {
      return;
    }

    // 恢复原始方法
    wx.showLoading = this.originalMethods.wxShowLoading;
    wx.hideLoading = this.originalMethods.wxHideLoading;

    this.isActive = false;
    console.log(`🚫 [加载消除器] 已停用，共阻止了 ${this.blockedCount} 次加载提示`);
  }

  /**
   * 判断是否应该允许显示加载
   */
  shouldAllowLoading(options, currentPage) {
    const title = options.title || '';
    
    // 特殊情况：允许某些关键操作显示加载
    const criticalOperations = [
      '查找聊天室',
      '创建聊天室',
      '撤回中',
      '删除中',
      '彻底删除中',
      '清空中'
    ];

    // 检查是否是关键操作
    if (criticalOperations.some(op => title.includes(op))) {
      return true;
    }

    // 检查是否在允许的页面
    if (this.allowedPages.some(page => currentPage.includes(page))) {
      return true;
    }

    return false;
  }

  /**
   * 获取当前页面路径
   */
  getCurrentPagePath() {
    try {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        return pages[pages.length - 1].route || '';
      }
    } catch (error) {
      console.error('🚫 [加载消除器] 获取页面路径失败:', error);
    }
    return '';
  }

  /**
   * 强制隐藏所有加载状态
   */
  forceHideAll() {
    console.log('🚫 [加载消除器] 强制隐藏所有加载状态');
    
    // 多次调用原始hideLoading方法
    for (let i = 0; i < 10; i++) {
      setTimeout(() => {
        this.originalMethods.wxHideLoading();
      }, i * 10);
    }
  }

  /**
   * 临时允许加载（用于特定操作）
   */
  temporaryAllow(duration = 3000) {
    console.log(`🚫 [加载消除器] 临时允许加载 ${duration}ms`);
    
    const originalShowLoading = wx.showLoading;
    wx.showLoading = this.originalMethods.wxShowLoading;
    
    setTimeout(() => {
      wx.showLoading = originalShowLoading;
      console.log('🚫 [加载消除器] 恢复加载拦截');
    }, duration);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      isActive: this.isActive,
      blockedCount: this.blockedCount,
      allowedPages: this.allowedPages
    };
  }

  /**
   * 重置统计
   */
  resetStats() {
    this.blockedCount = 0;
    console.log('🚫 [加载消除器] 统计已重置');
  }
}

// 创建全局实例
const loadingKiller = new LoadingKiller();

// 聊天页面专用配置
const chatPageConfig = {
  allowPages: [], // 不允许任何页面显示加载
  allowVoiceUpload: false, // 不允许语音上传显示加载
  allowChatRoomCreation: true // 允许聊天室创建显示加载（这是必要的）
};

// 导出方法
module.exports = {
  loadingKiller,
  
  // 快速激活聊天页面加载消除
  activateForChat() {
    loadingKiller.activate(chatPageConfig);
  },
  
  // 快速停用
  deactivate() {
    loadingKiller.deactivate();
  },
  
  // 强制隐藏所有加载
  forceHideAll() {
    loadingKiller.forceHideAll();
  },
  
  // 临时允许加载
  temporaryAllow(duration) {
    loadingKiller.temporaryAllow(duration);
  },
  
  // 获取统计
  getStats() {
    return loadingKiller.getStats();
  }
};
