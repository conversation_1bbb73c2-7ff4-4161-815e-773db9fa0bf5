// 云函数API对接服务
import axios from 'axios';

// 配置云开发环境
const CLOUD_ENV_ID = 'cloud1-9gsj7t48183e5a9f'; // 您的实际云环境ID

// 重要提示：请将下面的URL替换为您从云开发控制台获得的实际HTTP触发器URL
// 格式示例：https://cloud1-9gsj7t48183e5a9f-xxxxx.service.tcloudbase.com/
const CLOUD_BASE_URL = 'https://cloud1-9gsj7t48183e5a9f-1366958750.ap-shanghai.app.tcloudbase.com';

// 创建axios实例
const cloudApi = axios.create({
  baseURL: CLOUD_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
cloudApi.interceptors.request.use(
  (config) => {
    // 添加管理员认证token
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      
      // 如果是临时管理员token，添加特殊标识
      if (token.startsWith('admin-')) {
        config.headers['X-Admin-Temp-Token'] = 'true';
        config.headers['X-Admin-Username'] = 'admin';
      }
    }
    
    // 添加管理员标识
    config.headers['X-Admin-Request'] = 'true';
    
    console.log('发送云开发API请求:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      data: config.data,
      token: token ? (token.startsWith('admin-') ? '临时管理员token' : '真实token') : '无token'
    });
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
cloudApi.interceptors.response.use(
  (response) => {
    console.log('云开发API响应:', response);
    // 直接返回完整响应，让调用方处理
    return response;
  },
  (error) => {
    console.error('云开发API请求错误:', error);
    console.error('错误详情:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    
    if (error.response?.status === 401) {
      // 清除token并跳转到登录页
      localStorage.removeItem('admin_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 用户管理API
export const userApi = {
  // 获取用户列表
  getUserList: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) => {
    return cloudApi.post('/', { action: 'getUserList', data: params });
  },

  // 获取用户详情
  getUserDetail: async (userId: string) => {
    return cloudApi.post('/', { action: 'getUserDetail', data: { userId } });
  },

  // 更新用户状态
  updateUserStatus: async (userId: string, status: 'active' | 'inactive') => {
    return cloudApi.post('/', { action: 'updateUserStatus', data: { userId, status } });
  },

  // 获取用户统计
  getUserStats: async () => {
    return cloudApi.post('/', { action: 'getUserStats' });
  },
};

// 订单管理API
export const orderApi = {
  // 获取订单列表
  getOrderList: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }) => {
    return cloudApi.post('/', { action: 'getOrderList', data: params });
  },

  // 获取订单详情
  getOrderDetail: async (orderId: string) => {
    return cloudApi.post('/', { action: 'getOrderDetail', data: { orderId } });
  },

  // 更新订单状态
  updateOrderStatus: async (orderId: string, status: string) => {
    return cloudApi.post('/', { action: 'updateOrderStatus', data: { orderId, status } });
  },

  // 获取订单统计
  getOrderStats: async () => {
    return cloudApi.post('/', { action: 'getOrderStats' });
  },
};

// 聊天管理API
export const chatApi = {
  // 获取聊天室列表
  getChatRoomList: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }) => {
    return cloudApi.post('/', { action: 'getChatRoomList', data: params });
  },

  // 获取聊天消息
  getChatMessages: async (roomId: string, params?: {
    page?: number;
    limit?: number;
  }) => {
    return cloudApi.post('/', { action: 'getChatMessages', data: { roomId, ...params } });
  },

  // 禁言用户
  banUser: async (userId: string) => {
    return cloudApi.post('/', { action: 'banUser', data: { userId } });
  },

  // 获取聊天统计
  getChatStats: async () => {
    return cloudApi.post('/', { action: 'getChatStats' });
  },
};

// 钱包管理API
export const walletApi = {
  // 获取交易记录
  getTransactionList: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
  }) => {
    return cloudApi.post('/', { action: 'getTransactionList', data: params });
  },

  // 审核提现申请
  approveWithdraw: async (transactionId: string) => {
    return cloudApi.post('/', { action: 'approveWithdraw', data: { transactionId } });
  },

  // 拒绝提现申请
  rejectWithdraw: async (transactionId: string, reason?: string) => {
    return cloudApi.post('/', { action: 'rejectWithdraw', data: { transactionId, reason } });
  },

  // 获取钱包统计
  getWalletStats: async () => {
    return cloudApi.post('/', { action: 'getWalletStats' });
  },
};

// 通知管理API
export const notificationApi = {
  // 获取通知列表
  getNotificationList: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    search?: string;
  }) => {
    return cloudApi.post('/', { action: 'getNotificationList', data: params });
  },

  // 创建通知
  createNotification: async (notification: {
    title: string;
    content: string;
    type: string;
    isBroadcast?: boolean;
    userId?: string;
  }) => {
    return cloudApi.post('/', { action: 'createNotification', data: notification });
  },

  // 删除通知
  deleteNotification: async (notificationId: string) => {
    return cloudApi.post('/', { action: 'deleteNotification', data: { notificationId } });
  },
};

// 评价管理API
export const evaluationApi = {
  // 获取评价列表
  getEvaluationList: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    score?: number;
  }) => {
    return cloudApi.post('/', { action: 'getEvaluationList', data: params });
  },

  // 审核评价
  approveEvaluation: async (evaluationId: string) => {
    return cloudApi.post('/', { action: 'approveEvaluation', data: { evaluationId } });
  },

  // 拒绝评价
  rejectEvaluation: async (evaluationId: string, reason: string) => {
    return cloudApi.post('/', { action: 'rejectEvaluation', data: { evaluationId, reason } });
  },

  // 获取评价统计
  getEvaluationStats: async () => {
    return cloudApi.post('/', { action: 'getEvaluationStats' });
  },
};

// 仪表盘API
export const dashboardApi = {
  // 获取仪表盘统计数据
  getDashboardStats: async () => {
    try {
      console.log('调用仪表盘统计API...');
      const response = await cloudApi.post('/', { action: 'getDashboardStats' });
      console.log('仪表盘统计API响应:', response);
      return response;
    } catch (error) {
      console.error('仪表盘统计API调用失败:', error);
      throw error;
    }
  },

  // 获取图表数据
  getChartData: async (type: 'order' | 'user' | 'revenue') => {
    try {
      console.log('调用图表数据API...', type);
      const response = await cloudApi.post('/', { action: 'getChartData', data: { type } });
      console.log('图表数据API响应:', response);
      return response;
    } catch (error) {
      console.error('图表数据API调用失败:', error);
      throw error;
    }
  },

  // 获取最近活动
  getRecentActivities: async () => {
    try {
      console.log('调用最近活动API...');
      const response = await cloudApi.post('/', { action: 'getRecentActivities' });
      console.log('最近活动API响应:', response);
      return response;
    } catch (error) {
      console.error('最近活动API调用失败:', error);
      throw error;
    }
  },
};

// 认证API
export const authApi = {
  // 管理员登录
  login: async (username: string, password: string) => {
    return cloudApi.post('/', { action: 'adminLogin', data: { username, password } });
  },

  // 验证token
  verifyToken: async () => {
    return cloudApi.post('/', { action: 'verifyToken' });
  },

  // 退出登录
  logout: async () => {
    return cloudApi.post('/', { action: 'adminLogout' });
  },
};

export default cloudApi;