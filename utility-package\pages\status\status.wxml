<!--功能状态页面-->
<navigation-bar title="功能状态" back="{{true}}"></navigation-bar>

<scroll-view class="scrollarea page-with-custom-nav" scroll-y type="list">
  <view class="container">
    
    <!-- 系统状态 -->
    <view class="status-card">
      <view class="card-header">
        <text class="card-title">系统状态</text>
        <view class="status-indicator {{systemStatus.online ? 'online' : 'offline'}}">
          <text class="status-dot"></text>
          <text class="status-text">{{systemStatus.online ? '在线' : '离线'}}</text>
        </view>
      </view>
      <view class="card-content">
        <view class="info-item">
          <text class="info-label">运行模式：</text>
          <text class="info-value {{systemStatus.isDemo ? 'demo' : 'normal'}}">
            {{systemStatus.isDemo ? '演示模式' : '正常模式'}}
          </text>
        </view>
        <view class="info-item">
          <text class="info-label">云开发：</text>
          <text class="info-value {{systemStatus.cloudReady ? 'ready' : 'error'}}">
            {{systemStatus.cloudReady ? '已连接' : '未连接'}}
          </text>
        </view>
        <view class="info-item">
          <text class="info-label">版本号：</text>
          <text class="info-value">{{systemStatus.version}}</text>
        </view>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="feature-list">
      <view class="list-header">
        <text class="list-title">功能状态</text>
        <text class="list-desc">绿色表示正常，橙色表示演示模式，红色表示不可用</text>
      </view>
      
      <view class="feature-item" wx:for="{{features}}" wx:key="name">
        <view class="feature-info">
          <text class="feature-icon">{{item.icon}}</text>
          <view class="feature-details">
            <text class="feature-name">{{item.name}}</text>
            <text class="feature-desc">{{item.description}}</text>
          </view>
        </view>
        <view class="feature-status">
          <view class="status-badge {{item.status}}">
            <text class="badge-text">{{item.statusText}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="refreshStatus">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">刷新状态</text>
      </button>
      
      <button class="action-btn secondary" bindtap="clearCache">
        <text class="btn-icon">🗑️</text>
        <text class="btn-text">清除缓存</text>
      </button>
      
      <button class="action-btn secondary" bindtap="contactSupport">
        <text class="btn-icon">📞</text>
        <text class="btn-text">联系客服</text>
      </button>
    </view>

    <!-- 说明信息 -->
    <view class="info-section">
      <text class="info-title">说明</text>
      <text class="info-text">
        • 演示模式下，部分功能使用模拟数据
        • 如需完整功能，请配置云开发环境
        • 遇到问题可联系客服获得帮助
      </text>
    </view>

  </view>
</scroll-view>
