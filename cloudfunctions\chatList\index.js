/**
 * 聊天列表查询云函数
 * 功能：获取用户的聊天列表
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 工具函数：判断用户在聊天室中的角色
function getUserRole(chatRoom, userId) {
  if (chatRoom.customerId === userId) return 'customer';
  if (chatRoom.accepterId === userId) return 'accepter';
  return null;
}

// 工具函数：获取对方用户信息
function getOtherUserInfo(chatRoom, currentUserId) {
  const userRole = getUserRole(chatRoom, currentUserId);
  
  if (userRole === 'customer') {
    // 当前用户是客户，对方是接单者
    return {
      role: 'accepter',
      userId: chatRoom.accepterId,
      userInfo: chatRoom.accepterInfo
    };
  } else if (userRole === 'accepter') {
    // 当前用户是接单者，对方是客户
    return {
      role: 'customer',
      userId: chatRoom.customerId,
      userInfo: chatRoom.customerInfo
    };
  }
  
  return null;
}

// 工具函数：格式化时间显示
function formatTimeDisplay(dateTime) {
  if (!dateTime) return '';
  
  const now = new Date();
  const time = new Date(dateTime);
  const timeDiff = now - time;
  const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  
  if (daysDiff === 0) {
    // 今天，显示时间
    return time.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  } else if (daysDiff === 1) {
    // 昨天
    return '昨天';
  } else if (daysDiff < 7) {
    // 一周内，显示天数
    return `${daysDiff}天前`;
  } else {
    // 超过一周，显示日期
    return time.toLocaleDateString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit' 
    });
  }
}

// 工具函数：格式化聊天室数据
async function formatChatRoomData(chatRoom, currentUserId) {
  const userRole = getUserRole(chatRoom, currentUserId);
  const otherUser = getOtherUserInfo(chatRoom, currentUserId);

  // 格式化最后消息
  let lastMessageDisplay = '💬 开始聊天吧'; // 默认显示友好提示而不是"暂无消息"

  console.log('📋 [消息格式化] 聊天室:', chatRoom._id, '最后消息:', chatRoom.lastMessage);

  if (chatRoom.lastMessage) {
    const lastMessage = chatRoom.lastMessage;

    // 检查消息内容是否存在
    if (lastMessage.content && lastMessage.content.trim()) {
      let content = lastMessage.content.trim();

      // 根据消息类型进行特殊处理
      if (lastMessage.type === 'system') {
        // 系统消息的特殊显示处理
        if (content.includes('🎉 接单成功！')) {
          lastMessageDisplay = '🎉 接单成功！';
        } else if (content.includes('🔧 系统自动修复')) {
          lastMessageDisplay = '💬 开始聊天吧';
        } else {
          // 其他系统消息，截取前20个字符
          lastMessageDisplay = content.length > 20 ? content.substring(0, 20) + '...' : content;
        }
      } else if (lastMessage.type === 'image') {
        lastMessageDisplay = '[图片]';
      } else if (lastMessage.type === 'voice') {
        lastMessageDisplay = '[语音]';
      } else if (lastMessage.type === 'recalled') {
        lastMessageDisplay = '[消息已撤回]';
      } else {
        // 普通文本消息，截取前30个字符
        lastMessageDisplay = content.length > 30 ? content.substring(0, 30) + '...' : content;
      }

      console.log('✅ [消息格式化] 处理后的显示内容:', lastMessageDisplay);
    } else {
      console.log('⚠️ [消息格式化] lastMessage存在但content为空');
      lastMessageDisplay = '💬 开始聊天吧';
    }
  } else {
    console.log('⚠️ [消息格式化] lastMessage不存在，尝试获取最新消息');

    // 如果lastMessage不存在，尝试从消息集合中获取最新消息
    try {
      // 查询最新的未撤回消息
      let messagesResult;
      try {
        messagesResult = await db.collection('messages')
          .where({
            chatRoomId: chatRoom._id,
            isRecalled: _.neq(true) // 排除已撤回的消息
          })
          .orderBy('createTime', 'desc')
          .limit(1)
          .get();
      } catch (orderError) {
        console.log('❌ [消息查询] 排序查询失败，尝试简单查询:', orderError.message);

        // 备用方案：查询所有消息，手动过滤
        const allMessagesResult = await db.collection('messages')
          .where({ chatRoomId: chatRoom._id })
          .get();

        const unrecalledMessages = allMessagesResult.data
          .filter(msg => !msg.isRecalled)
          .sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

        messagesResult = {
          data: unrecalledMessages.slice(0, 1)
        };
      }

      if (messagesResult.data.length > 0) {
        const latestMessage = messagesResult.data[0];
        console.log('✅ [消息格式化] 找到最新消息:', latestMessage.content);

        // 更新聊天室的lastMessage字段
        await db.collection('chatRooms').doc(chatRoom._id).update({
          data: {
            lastMessage: {
              content: latestMessage.content,
              type: latestMessage.type,
              senderId: latestMessage.senderId,
              createTime: latestMessage.createTime
            },
            updateTime: new Date()
          }
        });

        // 格式化显示内容
        if (latestMessage.type === 'image') {
          lastMessageDisplay = '[图片]';
        } else if (latestMessage.type === 'voice') {
          lastMessageDisplay = '[语音]';
        } else {
          lastMessageDisplay = latestMessage.content.length > 30 ?
            latestMessage.content.substring(0, 30) + '...' : latestMessage.content;
        }
      } else {
        console.log('⚠️ [消息格式化] 聊天室中没有消息');
        lastMessageDisplay = '💬 开始聊天吧';
      }
    } catch (error) {
      console.error('❌ [消息格式化] 获取最新消息失败:', error);
      lastMessageDisplay = '💬 开始聊天吧';
    }
  }
  
  return {
    _id: chatRoom._id,
    orderNo: chatRoom.orderNo,
    orderDbId: chatRoom.orderDbId,
    
    // 用户角色信息
    userRole: userRole,
    customerId: chatRoom.customerId,
    accepterId: chatRoom.accepterId,
    
    // 对方用户信息
    otherUser: otherUser ? {
      userId: otherUser.userId,
      nickName: otherUser.userInfo?.nickName || '未知用户',
      avatarUrl: otherUser.userInfo?.avatarUrl || '',
      role: otherUser.role
    } : null,
    
    // 订单信息
    orderInfo: chatRoom.orderInfo || {},
    
    // 最后消息
    lastMessage: chatRoom.lastMessage,
    lastMessageDisplay: lastMessageDisplay,
    lastMessageTime: chatRoom.lastMessage?.createTime || chatRoom.updateTime,
    timeDisplay: formatTimeDisplay(chatRoom.lastMessage?.createTime || chatRoom.updateTime),
    
    // 聊天室状态
    status: chatRoom.status || 'active',
    
    // 时间信息
    createTime: chatRoom.createTime,
    updateTime: chatRoom.updateTime
  };
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { page = 1, pageSize = 20, status = 'active' } = event;

  console.log('=== 聊天列表查询云函数开始 ===');
  console.log('📋 [请求参数] 页码:', page, '页大小:', pageSize, '状态:', status);
  console.log('📋 [用户信息] OpenID:', wxContext.OPENID);

  try {
    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在，请先登录'
      };
    }

    const user = userResult.data[0];

    // 构建查询条件 - 兼容新旧字段名
    const whereCondition = _.and([
      _.or([
        { customerId: user._id },
        { accepterId: user._id },
        { companionId: user._id } // 兼容旧字段名
      ]),
      { status: status },
      // 过滤掉当前用户已删除的聊天室
      _.or([
        { [`deletedBy.${user._id}`]: _.neq(true) },
        { [`deletedBy.${user._id}`]: _.exists(false) }
      ])
    ]);

    // 查询聊天室列表
    let chatRoomsResult;
    try {
      // 尝试带排序的查询
      chatRoomsResult = await db.collection('chatRooms')
        .where(whereCondition)
        .orderBy('updateTime', 'desc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get();
    } catch (orderError) {
      
      // 如果排序查询失败，尝试不排序的查询
      chatRoomsResult = await db.collection('chatRooms')
        .where(whereCondition)
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get();
    }

    console.log(`✅ [聊天室查询] 找到聊天室数量: ${chatRoomsResult.data.length}`);

    // 格式化聊天室数据
    const formattedChatRooms = await Promise.all(chatRoomsResult.data.map(async chatRoom => {
      const formatted = await formatChatRoomData(chatRoom, user._id);

      console.log(`📋 [数据格式化] 聊天室 ${chatRoom._id}:`, {
        orderNo: formatted.orderNo,
        userRole: formatted.userRole,
        otherUser: formatted.otherUser?.nickName,
        lastMessage: formatted.lastMessageDisplay
      });

      return formatted;
    }));

    // 如果数据库查询没有排序，在内存中排序
    if (chatRoomsResult.data.length > 0 && !chatRoomsResult.data[0].updateTime) {
      console.log('📋 [数据排序] 在内存中按更新时间排序');
      formattedChatRooms.sort((a, b) => {
        const timeA = new Date(a.lastMessageTime || a.updateTime || a.createTime);
        const timeB = new Date(b.lastMessageTime || b.updateTime || b.createTime);
        return timeB - timeA;
      });
    }

    // 统计信息
    const stats = {
      total: formattedChatRooms.length,
      asCustomer: formattedChatRooms.filter(room => room.userRole === 'customer').length,
      asAccepter: formattedChatRooms.filter(room => room.userRole === 'accepter').length,
      page: page,
      pageSize: pageSize,
      hasMore: formattedChatRooms.length === pageSize
    };

    return {
      success: true,
      data: {
        list: formattedChatRooms,
        stats: stats
      }
    };

  } catch (error) {
    console.error('❌ [云函数错误]:', error);
    return {
      success: false,
      error: error.message || '获取聊天列表失败'
    };
  }
};
