Component({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    extClass: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    background: {
      type: String,
      value: ''
    },
    color: {
      type: String,
      value: ''
    },
    back: {
      type: Boolean,
      value: true
    },
    loading: {
      type: Boolean,
      value: false
    },
    homeButton: {
      type: Boolean,
      value: false,
    },
    animated: {
      // 显示隐藏的时候opacity动画效果
      type: Boolean,
      value: true
    },
    show: {
      // 显示隐藏导航，隐藏的时候navigation-bar的高度占位还在
      type: Boolean,
      value: true,
      observer: '_showChange'
    },
    // back为true的时候，返回的页面深度
    delta: {
      type: Number,
      value: 1
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    displayStyle: ''
  },
  lifetimes: {
    attached() {
      const rect = wx.getMenuButtonBoundingClientRect()

      // 使用 wx.getSystemInfo 获取系统信息
      wx.getSystemInfo({
        success: (res) => {
          const { platform, windowWidth, statusBarHeight, safeArea } = res
          const isAndroid = platform === 'android'
          const isDevtools = platform === 'devtools'

          // 优先使用 statusBarHeight，如果没有则使用 safeArea.top
          let top = statusBarHeight || (safeArea && safeArea.top) || 0

          // 如果获取不到状态栏高度，尝试从胶囊按钮位置推算
          if (top === 0 && rect && rect.top > 0) {
            top = rect.top
          }

          // 如果是开发者工具，使用固定值
          if (isDevtools) {
            top = 44
          }

          // 如果状态栏高度仍为0，使用默认值
          if (top === 0) {
            top = isAndroid ? 25 : 44
          }

          console.log('导航栏状态栏高度:', top, 'px')
          console.log('设备平台:', platform)
          console.log('窗口宽度:', windowWidth)
          console.log('胶囊按钮位置:', rect)

          // 计算胶囊按钮的宽度和右边距
          const capsuleWidth = rect.width || 87  // 胶囊按钮宽度，默认87px
          const capsuleRight = windowWidth - rect.left  // 胶囊按钮右侧到屏幕右边的距离
          const rightPadding = capsuleWidth + 20  // 给胶囊按钮预留空间，增加20px安全距离

          this.setData({
            ios: !isAndroid,
            innerPaddingRight: `padding-right: ${rightPadding}px`,
            leftWidth: '', // 移除左侧宽度限制，让返回按钮从最左边开始
            safeAreaTop: `height: calc(var(--height) + ${top}px); padding-top: ${top}px`,
            statusBarHeight: top
          })
        },
        fail: () => {
          console.log('获取系统信息失败，使用降级处理')
          this.setFallbackData(rect)
        }
      })
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 降级处理方法
    setFallbackData(rect) {
      console.log('使用降级处理，默认状态栏高度: 44px')
      // 使用默认胶囊按钮尺寸
      const defaultCapsuleWidth = 87
      const rightPadding = defaultCapsuleWidth + 20

      this.setData({
        ios: true, // 默认iOS样式
        innerPaddingRight: `padding-right: ${rightPadding}px`,
        leftWidth: '', // 移除左侧宽度限制，让返回按钮从最左边开始
        safeAreaTop: `height: calc(var(--height) + 44px); padding-top: 44px`, // 默认状态栏高度
        statusBarHeight: 44
      })
    },

    _showChange(show) {
      const animated = this.data.animated
      let displayStyle = ''
      if (animated) {
        displayStyle = `opacity: ${show ? '1' : '0'
          };transition:opacity 0.5s;`
      } else {
        displayStyle = `display: ${show ? '' : 'none'}`
      }
      this.setData({
        displayStyle
      })
    },
    back() {
      const data = this.data
      if (data.delta) {
        wx.navigateBack({
          delta: data.delta
        })
      }
      this.triggerEvent('back', { delta: data.delta }, {})
    }
  },
})
