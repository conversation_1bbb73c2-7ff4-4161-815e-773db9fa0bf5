/* 现代化订单列表页面样式 */
.list-container {
  height: 100vh;
  background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  display: flex;
  flex-direction: column;
}

/* 为所有模式设置顶部间距 - 减少间距让订单更靠近顶部 */
.list-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 5rpx);
  box-sizing: border-box;
}

/* 状态筛选区域 */
.status-section {
  display: flex;
  align-items: center;
  margin: var(--space-xs) var(--space-xs) var(--space-sm) var(--space-xs);
  gap: var(--space-xs);
  position: relative;
  z-index: 1;
}

/* 现代化状态筛选标签 */
.status-tabs {
  width: 100%;
  background: var(--bg-primary);
  backdrop-filter: blur(20rpx);
  padding: var(--space-md) 8rpx;
  border-radius: var(--radius-md);
  border-bottom: 1rpx solid var(--border-light);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 4rpx;
}



.status-tab {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: var(--space-sm) 6rpx;
  margin: 0;
  font-size: 26rpx;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  letter-spacing: 0.2rpx;
  text-align: center;
  white-space: nowrap;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-tab.active {
  color: var(--primary-color);
  background: var(--primary-light);
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.tab-text {
  position: relative;
  z-index: 2;
}

.tab-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
  line-height: 1;
}

/* 小屏幕适配 */
@media (max-width: 375px) {
  .status-tab {
    font-size: 24rpx;
    padding: var(--space-sm) 4rpx;
    letter-spacing: 0.1rpx;
  }

  .status-tabs {
    padding: var(--space-md) 6rpx;
    gap: 2rpx;
  }
}

/* 超小屏幕适配 */
@media (max-width: 320px) {
  .status-tab {
    font-size: 22rpx;
    padding: var(--space-sm) 2rpx;
    letter-spacing: 0;
  }

  .status-tabs {
    padding: var(--space-md) 4rpx;
    gap: 1rpx;
  }
}

/* 抢单大厅模式样式调整 */
.list-container.grab-mode .order-list {
  padding-top: var(--space-lg); /* 增加顶部间距，补偿移除的筛选组件 */
}

/* 现代化订单列表 - 减少顶部间距 */
.order-list {
  flex: 1;
  height: 0; /* 确保 flex 子元素正确计算高度 */
  padding: var(--space-xs) var(--space-md) var(--space-md) var(--space-md);
  /* 确保滚动行为正确 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

.order-item {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-2xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.order-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.order-item:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-lg);
}

.order-item:active::before {
  opacity: 1;
}

/* 订单头部 - 现代化设计 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}

.order-title {
  font-size: 28rpx;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
  flex: 1;
  margin-right: 20rpx;
  line-height: 1.4;
}

.order-price {
  font-size: 36rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
  letter-spacing: 0.5rpx;
  white-space: nowrap;
}

/* 订单详情 */
.order-details {
  margin-bottom: 20rpx;
}

.order-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  padding: 8rpx 0;
}

.detail-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 80rpx;
}

.detail-value {
  font-size: 26rpx;
  color: var(--text-primary);
  font-weight: 600;
  text-align: right;
  flex: 1;
}

/* 状态样式 */
.detail-value.status-pending {
  color: var(--warning-color);
}

.detail-value.status-accepted {
  color: var(--info-color);
}

.detail-value.status-in_progress {
  color: var(--success-color);
}

.detail-value.status-completed {
  color: #722ed1;
}

.detail-value.status-waiting_match {
  color: #eb2f96;
}

/* 订单元信息 */
.order-meta {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid var(--border-light);
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-item-vertical {
  flex-direction: column;
  align-items: flex-start;
}

.meta-item-vertical .meta-label {
  margin-bottom: 6rpx;
}

.meta-label {
  font-size: 22rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.meta-value {
  font-size: 22rpx;
  color: var(--text-primary);
  font-weight: 600;
}

.meta-value-content {
  line-height: 1.5;
  text-align: left;
}

/* 订单元信息 */
.order-meta {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid var(--border-light);
}



/* 陪玩师信息 */
.companion-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.companion-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
}

.companion-details {
  flex: 1;
}

.companion-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.companion-level {
  font-size: 24rpx;
  color: #666;
}



/* 操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  min-width: 120rpx;
}

.cancel-btn {
  background: #fff2f0;
  color: #ff4d4f;
}

.edit-btn {
  background: #e6f7ff;
  color: #1890ff;
}

.chat-btn {
  background: #e6f7ff;
  color: #1890ff;
}

.complete-btn {
  background: #f6ffed;
  color: #52c41a;
}

.evaluate-btn {
  background: #f0f5ff;
  color: #722ed1;
}

.reorder-btn {
  background: #fff7e6;
  color: #fa8c16;
}

.grab-btn {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  color: white;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  border-radius: 12rpx;
  font-size: 28rpx;
}

.grab-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.4);
}

.detail-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-light);
  border-radius: 12rpx;
}

.waiting-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.waiting-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
}

/* 加载状态 */
.load-more, .no-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 现代化空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-3xl) var(--space-xl);
  text-align: center;
  background: transparent;
  border-radius: var(--radius-lg);
  margin: 0;
  box-shadow: none;
  border: none;
  position: absolute;
  top: 30%;
  left: 47%;
  transform: translate(-50%, -50%);
  width: 100vw;
  height: 100vh;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-lg);
  opacity: 0.9;
  color: var(--cyber-blue);
  text-shadow: 0 0 20rpx rgba(0, 212, 255, 0.5);
  animation: pulse 2s ease-in-out infinite;
}

.empty-text {
  font-size: 36rpx;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
  font-weight: 700;
  letter-spacing: 1rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: var(--space-xl);
  line-height: 1.5;
  opacity: 0.8;
}

.create-order-btn {
  background: linear-gradient(135deg, var(--cyber-blue) 0%, #4ab37e 100%);
  color: white;
  padding: var(--space-md) var(--space-3xl);
  border-radius: var(--radius-xl);
  font-size: 32rpx;
  font-weight: 700;
  border: none;
  box-shadow: 0 8rpx 32rpx rgba(0, 212, 255, 0.4);
  transition: all 0.3s ease;
  letter-spacing: 1rpx;
  position: relative;
  overflow: hidden;
}

.create-order-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.create-order-btn:active::before {
  left: 100%;
}

.create-order-btn:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}





.filter-toggle-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  margin-left: var(--space-sm);
}

.filter-toggle-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.filter-icon {
  font-size: 28rpx;
  color: white;
}



.grab-hall-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  animation: float 6s ease-in-out infinite;
}



/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 8rpx 32rpx rgba(0, 212, 255, 0.1);
  }
  50% {
    box-shadow: 0 8rpx 32rpx rgba(0, 212, 255, 0.3);
  }
}



@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
}


