<!--登录页面-->
<view class="login-container page-with-custom-nav">
  <!-- 登录头部 -->
  <view class="login-header">
    <view class="logo-container">
      <image class="logo" src="cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/logos/gti-security-logo.png" mode="aspectFit" />
    </view>
    <text class="app-name">三角洲陪玩</text>
    <text class="app-desc">专业的《三角洲行动》陪玩服务平台</text>
    <text class="welcome-text">欢迎加入我们的游戏世界</text>
  </view>

  <!-- 登录内容 -->
  <view class="login-content">
    <!-- 特色功能展示 -->
    <view class="feature-list">
      <view class="feature-item">
        <text class="feature-emoji">🎮</text>
        <text class="feature-text">专业陪玩师认证</text>
      </view>
      <view class="feature-item">
        <text class="feature-emoji">🛡️</text>
        <text class="feature-text">安全可靠的交易</text>
      </view>
      <view class="feature-item">
        <text class="feature-emoji">⚡</text>
        <text class="feature-text">快速匹配响应</text>
      </view>
    </view>

    <!-- 登录按钮区域 -->
    <view class="login-actions">
      <!-- 微信登录按钮 -->
      <button
        class="login-btn {{loading ? 'loading' : ''}}"
        bindtap="handleLogin"
        disabled="{{loading}}"
      >
        <view class="btn-content">
          <text class="btn-icon" wx:if="{{!loading}}">💬</text>
          <view class="loading-spinner" wx:if="{{loading}}"></view>
          <text class="btn-text">{{loading ? '登录中...' : '微信一键登录'}}</text>
        </view>
      </button>

      <!-- 游客模式 -->
      <view class="guest-mode" bindtap="guestLogin" wx:if="{{!loading}}">
        <text class="guest-text">游客模式体验</text>
        <text class="guest-desc">（功能受限）</text>
      </view>
    </view>

    <!-- 登录提示 -->
    <view class="login-tips">
      <view class="tips-row">
        <text class="tip-text">登录即表示同意</text>
        <text class="link-text" bindtap="showUserAgreement">《用户协议》</text>
        <text class="tip-text">和</text>
        <text class="link-text" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </view>
      <view class="security-tip">
        <text class="security-icon">🔒</text>
        <text class="security-text">我们承诺保护您的隐私安全</text>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="login-footer">
    <text class="footer-text">遇到问题？</text>
    <text class="footer-link" bindtap="contactSupport">联系客服</text>
  </view>
</view>
