/* 通知中心页面样式 */

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: var(--cyber-blue);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
  box-shadow: 0 0 10rpx var(--cyber-blue);
}

.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 80%; animation-delay: 1s; }
.particle:nth-child(3) { top: 80%; left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { top: 40%; left: 70%; animation-delay: 0.5s; }
.particle:nth-child(5) { top: 10%; left: 90%; animation-delay: 1.5s; }

/* 主容器 */
.notification-container {
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
  min-height: 100vh;
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.notification-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + var(--space-lg) + 20rpx);
}

/* 科技感卡片 */
.cyber-card {
  position: relative;
  background: rgba(15, 23, 42, 0.8);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  backdrop-filter: blur(10rpx);
  overflow: hidden;
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.1) 0%,
    transparent 50%,
    rgba(0, 212, 255, 0.05) 100%);
  border-radius: var(--radius-lg);
  pointer-events: none;
}

/* 头部区域 */
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.stats-info {
  display: flex;
  align-items: baseline;
  gap: var(--space-sm);
}

.unread-count {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--cyber-blue);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.stats-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

.header-actions {
  display: flex;
  gap: var(--space-md);
}

.action-btn {
  padding: var(--space-sm) var(--space-md);
  background: rgba(0, 212, 255, 0.2);
  border: 1rpx solid var(--cyber-blue);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.action-btn:active {
  background: rgba(0, 212, 255, 0.3);
  transform: scale(0.95);
}

.action-text {
  font-size: 24rpx;
  color: var(--cyber-blue);
  font-weight: 500;
}

/* 筛选区域 */
.filter-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  position: relative;
  z-index: 1;
}

.filter-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.filter-picker {
  flex: 1;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  height: 60rpx;
}

.picker-text {
  color: #ffffff;
  font-size: 26rpx;
}

.picker-arrow {
  color: var(--cyber-blue);
  font-size: 20rpx;
}

/* 通知列表 */
.notification-list {
  margin-top: var(--space-md);
}

.notification-item {
  margin-bottom: var(--space-md);
  transition: all 0.3s ease;
}

.notification-item.unread {
  border-color: var(--cyber-blue);
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.2);
}

.notification-item:active {
  transform: scale(0.98);
}

.notification-content {
  position: relative;
  z-index: 1;
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-sm);
}

.type-info {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.type-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 0 5rpx rgba(0, 212, 255, 0.5));
}

.type-text {
  font-size: 24rpx;
  color: var(--cyber-blue);
  font-weight: 500;
}

.time-info {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.time-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.unread-dot {
  width: 12rpx;
  height: 12rpx;
  background: #ff4757;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(255, 71, 87, 0.5);
}

.notification-body {
  margin-bottom: var(--space-md);
}

.notification-title {
  display: block;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: var(--space-xs);
  line-height: 1.4;
}

.notification-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.notification-actions {
  display: flex;
  justify-content: flex-end;
}

.delete-btn {
  padding: var(--space-xs) var(--space-sm);
  background: rgba(255, 71, 87, 0.2);
  border: 1rpx solid rgba(255, 71, 87, 0.5);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.delete-btn:active {
  background: rgba(255, 71, 87, 0.3);
  transform: scale(0.95);
}

.delete-text {
  font-size: 22rpx;
  color: #ff4757;
}

/* 空状态 */
.empty-content {
  text-align: center;
  padding: var(--space-xl) 0;
  position: relative;
  z-index: 1;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: var(--space-md);
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: var(--space-sm);
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 加载状态 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg);
  gap: var(--space-md);
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(0, 212, 255, 0.3);
  border-top: 3rpx solid var(--cyber-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.no-more {
  text-align: center;
  padding: var(--space-lg);
}

.no-more-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 动画 */
@keyframes dataFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(100rpx, 100rpx); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
