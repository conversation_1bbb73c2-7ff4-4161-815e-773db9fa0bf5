// 交易记录页面
import API from '../../../utils/api.js';

const app = getApp();

Page({
  // 页面创建时立即执行loading控制
  onLoad(options) {
    // 立即执行loading控制，在任何其他操作之前
    console.log('🔄 交易记录页面 - 页面创建');

    // 立即强制隐藏所有loading
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 1);
    setTimeout(() => wx.hideLoading(), 5);
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 20);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);

    // 保存原始方法
    this.originalShowLoading = app.utils?.showLoading;
    this.originalWxShowLoading = wx.showLoading;

    // 立即禁用所有loading显示
    if (app.utils) {
      app.utils.showLoading = () => {
        console.log('🚫 [交易记录页面] 阻止app.utils.showLoading调用');
      };
    }

    wx.showLoading = () => {
      console.log('🚫 [交易记录页面] 阻止wx.showLoading调用');
    };

    // 调用原来的onLoad逻辑
    this.originalOnLoad(options);
  },

  onReady() {
    // 页面初次渲染完成时继续禁用loading
    console.log('🔄 交易记录页面 - 页面渲染完成');
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
  },

  data: {
    // 页面类型：all-全部, recharge-充值, withdraw-提现, order-订单
    recordType: 'all',

    // 筛选标签
    filterTabs: [
      { id: 'all', name: '全部', count: 0 },
      { id: 'recharge', name: '充值', count: 0 },
      { id: 'withdraw', name: '提现', count: 0 },
      { id: 'income', name: '收入', count: 0 },
      { id: 'payment', name: '支出', count: 0 }
    ],
    activeTab: 'all',

    // 交易记录列表
    recordList: [],
    page: 1,
    pageSize: 20,
    loading: false,
    loadingMore: false, // 分页加载状态
    refreshing: false,
    hasMore: true,
    navLoading: false, // 导航栏加载状态

    // 统计信息
    stats: {
      totalIncome: 0,
      totalExpense: 0,
      rechargeAmount: 0,
      withdrawAmount: 0,
      orderIncome: 0,
      orderExpense: 0,
      transactionCount: 0
    },

    // 时间筛选
    dateRange: {
      startDate: '',
      endDate: '',
      showDatePicker: false
    },

    // 已查看的标签（用于控制红点数字显示）
    viewedTabs: []
  },

  originalOnLoad(options) {
    console.log('🔄 交易记录页面 - 页面加载');

    // 立即强制隐藏所有loading
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);

    // 保存原始方法
    this.originalShowLoading = app.utils?.showLoading;
    this.originalWxShowLoading = wx.showLoading;

    // 临时禁用所有loading显示
    if (app.utils) {
      app.utils.showLoading = () => {
        console.log('🚫 [交易记录页面] 阻止app.utils.showLoading调用');
      };
    }

    wx.showLoading = () => {
      console.log('🚫 [交易记录页面] 阻止wx.showLoading调用');
    };

    // 获取页面类型参数
    if (options.type) {
      this.setData({
        recordType: options.type,
        activeTab: options.type === 'all' ? 'all' : options.type
      });
    }

    // 记录当前活跃标签为已查看
    if (!this.data.viewedTabs.includes(this.data.activeTab)) {
      this.data.viewedTabs.push(this.data.activeTab);
    }

    // 延迟加载数据，确保loading控制生效
    setTimeout(() => {
      this.loadTransactionList(true);
    }, 100);

    // 页面加载完成后恢复原始方法
    setTimeout(() => {
      if (app.utils && this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      wx.showLoading = this.originalWxShowLoading;
    }, 2000); // 延长恢复时间
  },

  onShow() {
    console.log('🔄 交易记录页面 - 页面显示');

    // 立即强制隐藏所有loading
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);
    setTimeout(() => wx.hideLoading(), 200);
    setTimeout(() => wx.hideLoading(), 500);

    // 临时禁用所有loading显示
    if (app.utils) {
      app.utils.showLoading = () => {
        console.log('🚫 [交易记录页面显示] 阻止app.utils.showLoading调用');
      };
    }

    wx.showLoading = () => {
      console.log('🚫 [交易记录页面显示] 阻止wx.showLoading调用');
    };

    // 延迟刷新数据，确保loading控制生效
    setTimeout(() => {
      this.loadTransactionList(true);
    }, 200);

    // 页面显示完成后恢复原始方法
    setTimeout(() => {
      if (app.utils && this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      if (this.originalWxShowLoading) {
        wx.showLoading = this.originalWxShowLoading;
      }
    }, 1500); // 延长恢复时间
  },

  onUnload() {
    // 确保页面卸载时恢复原始方法
    if (app.utils && this.originalShowLoading) {
      app.utils.showLoading = this.originalShowLoading;
    }
    if (this.originalWxShowLoading) {
      wx.showLoading = this.originalWxShowLoading;
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadTransactionList(true).finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    if (!this.data.loading && !this.data.loadingMore && this.data.hasMore) {
      this.loadTransactionList(false);
    }
  },

  // 加载交易记录
  async loadTransactionList(reset = false) {
    if (this.data.loading || this.data.loadingMore) return;

    const page = reset ? 1 : this.data.page + 1;

    // 区分初始加载和分页加载
    if (reset || this.data.recordList.length === 0) {
      this.setData({ loading: true }); // 初始加载使用全屏加载框
    } else {
      this.setData({ loadingMore: true }); // 分页加载使用列表内加载提示
    }

    try {
      const params = {
        page,
        pageSize: this.data.pageSize,
        type: this.data.activeTab === 'all' ? null : this.data.activeTab
      };

      // 添加时间筛选
      if (this.data.dateRange.startDate && this.data.dateRange.endDate) {
        params.startDate = this.data.dateRange.startDate;
        params.endDate = this.data.dateRange.endDate;
      }

      const result = await API.getTransactionList({
        ...params,
        showLoading: false
      });

      if (result.success) {
        const newList = result.data.list;

        this.setData({
          recordList: reset ? newList : [...this.data.recordList, ...newList],
          page,
          hasMore: result.data.hasMore,
          stats: result.data.stats || this.data.stats
        });

        // 更新标签计数
        this.updateTabCounts();
      } else {
        app.utils.showError(result.error || '加载失败');
      }
    } catch (error) {
      console.error('加载交易记录失败:', error);
      app.utils.showError('加载失败');
    } finally {
      this.setData({
        loading: false,
        loadingMore: false
      });
    }
  },

  // 切换筛选标签
  switchTab(e) {
    const tabId = e.currentTarget.dataset.tab;
    if (tabId === this.data.activeTab) return;

    // 记录已查看的标签
    if (!this.data.viewedTabs.includes(tabId)) {
      this.data.viewedTabs.push(tabId);
    }

    this.setData({
      activeTab: tabId,
      recordList: [],
      page: 1,
      hasMore: true
    });

    this.loadTransactionList(true);
  },

  // 更新标签计数
  updateTabCounts() {
    // 基于统计数据更新计数，而不是当前页面的记录列表
    const { stats } = this.data;
    const counts = {
      all: stats.transactionCount || 0,
      recharge: stats.rechargeCount || 0,
      withdraw: stats.withdrawCount || 0,
      income: stats.incomeCount || 0,
      payment: stats.paymentCount || 0
    };

    const updatedTabs = this.data.filterTabs.map(tab => ({
      ...tab,
      // 如果标签已被查看过，则不显示红点数字
      count: this.data.viewedTabs.includes(tab.id) ? 0 : (counts[tab.id] || 0)
    }));

    this.setData({ filterTabs: updatedTabs });
  },



  // 显示日期选择器
  showDatePicker() {
    this.setData({
      'dateRange.showDatePicker': true
    });
  },

  // 开始日期选择
  onStartDateChange(e) {
    this.setData({
      'dateRange.startDate': e.detail.value
    });
  },

  // 结束日期选择
  onEndDateChange(e) {
    this.setData({
      'dateRange.endDate': e.detail.value
    });
  },

  // 应用日期筛选
  applyDateFilter() {
    this.setData({
      'dateRange.showDatePicker': false,
      recordList: [],
      page: 1,
      hasMore: true
    });

    this.loadTransactionList(true);
  },

  // 清除日期筛选
  clearDateFilter() {
    this.setData({
      'dateRange.startDate': '',
      'dateRange.endDate': '',
      'dateRange.showDatePicker': false,
      recordList: [],
      page: 1,
      hasMore: true
    });

    this.loadTransactionList(true);
  },

  // 查看交易详情
  viewTransactionDetail(e) {
    const { id } = e.currentTarget.dataset;
    const transaction = this.data.recordList.find(item => item._id === id);

    if (transaction) {
      // 根据交易类型跳转到不同的详情页面
      if (transaction.type === 'recharge') {
        // 跳转到充值详情
        wx.showModal({
          title: '充值详情',
          content: `充值金额：¥${transaction.amount}\n状态：${transaction.statusText}\n时间：${transaction.createTimeText}`,
          showCancel: false
        });
      } else if (transaction.type === 'withdraw') {
        // 跳转到提现详情
        wx.showModal({
          title: '提现详情',
          content: `提现金额：¥${Math.abs(transaction.amount)}\n状态：${transaction.statusText}\n时间：${transaction.createTimeText}`,
          showCancel: false
        });
      } else if (transaction.orderId) {
        // 跳转到订单详情
        wx.navigateTo({
          url: `/order-package/pages/detail/detail?id=${transaction.orderId}`
        });
      }
    }
  }
});