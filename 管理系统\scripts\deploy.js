#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { getConfig, validateConfig } = require('../deploy.config.js');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 部署类
class Deployer {
  constructor(environment = 'development') {
    this.env = environment;
    this.config = getConfig(environment);
    this.startTime = Date.now();
    
    log(`🚀 开始部署到 ${this.config.current.name}`, 'cyan');
  }

  // 验证环境
  validateEnvironment() {
    log('🔍 验证部署环境...', 'blue');
    
    try {
      validateConfig(this.config.current);
      
      // 检查必需的工具
      const requiredTools = ['node', 'npm'];
      requiredTools.forEach(tool => {
        try {
          execSync(`${tool} --version`, { stdio: 'ignore' });
        } catch (error) {
          throw new Error(`缺少必需工具: ${tool}`);
        }
      });
      
      // 检查 package.json
      if (!fs.existsSync('package.json')) {
        throw new Error('未找到 package.json 文件');
      }
      
      log('✅ 环境验证通过', 'green');
      return true;
    } catch (error) {
      log(`❌ 环境验证失败: ${error.message}`, 'red');
      process.exit(1);
    }
  }

  // 安装依赖
  installDependencies() {
    log('📦 安装项目依赖...', 'blue');
    
    try {
      execSync('npm ci', { stdio: 'inherit' });
      log('✅ 依赖安装完成', 'green');
    } catch (error) {
      log('❌ 依赖安装失败', 'red');
      process.exit(1);
    }
  }

  // 运行构建前脚本
  runPreBuildScripts() {
    log('🔧 运行构建前脚本...', 'blue');
    
    const scripts = this.config.deployScripts.preBuild;
    scripts.forEach(script => {
      try {
        log(`执行: ${script}`, 'yellow');
        execSync(script, { stdio: 'inherit' });
      } catch (error) {
        log(`❌ 脚本执行失败: ${script}`, 'red');
        // 某些脚本失败不应该阻止部署
        if (script.includes('lint') || script.includes('test')) {
          log('⚠️ 跳过非关键脚本错误', 'yellow');
        } else {
          process.exit(1);
        }
      }
    });
    
    log('✅ 构建前脚本执行完成', 'green');
  }

  // 构建项目
  buildProject() {
    log('🏗️ 构建项目...', 'blue');
    
    try {
      const buildCommand = this.config.current.buildCommand;
      log(`执行构建命令: ${buildCommand}`, 'yellow');
      
      // 设置环境变量
      const env = {
        ...process.env,
        NODE_ENV: this.env === 'development' ? 'development' : 'production',
        VITE_API_URL: this.config.current.apiUrl,
        VITE_CLOUD_ENV: this.config.current.cloudEnv
      };
      
      execSync(buildCommand, { stdio: 'inherit', env });
      log('✅ 项目构建完成', 'green');
    } catch (error) {
      log('❌ 项目构建失败', 'red');
      process.exit(1);
    }
  }

  // 运行构建后脚本
  runPostBuildScripts() {
    log('🔧 运行构建后脚本...', 'blue');
    
    const scripts = this.config.deployScripts.postBuild;
    scripts.forEach(script => {
      try {
        log(`执行: ${script}`, 'yellow');
        execSync(script, { stdio: 'inherit' });
      } catch (error) {
        log(`⚠️ 构建后脚本执行失败: ${script}`, 'yellow');
        // 构建后脚本失败不应该阻止部署
      }
    });
    
    log('✅ 构建后脚本执行完成', 'green');
  }

  // 验证构建结果
  validateBuild() {
    log('🔍 验证构建结果...', 'blue');
    
    const distDir = this.config.build.outDir;
    
    if (!fs.existsSync(distDir)) {
      log(`❌ 构建目录不存在: ${distDir}`, 'red');
      process.exit(1);
    }
    
    const indexFile = path.join(distDir, 'index.html');
    if (!fs.existsSync(indexFile)) {
      log(`❌ 入口文件不存在: ${indexFile}`, 'red');
      process.exit(1);
    }
    
    // 检查文件大小
    const stats = fs.statSync(indexFile);
    if (stats.size === 0) {
      log('❌ 入口文件为空', 'red');
      process.exit(1);
    }
    
    log('✅ 构建结果验证通过', 'green');
  }

  // 生成部署报告
  generateDeployReport() {
    const endTime = Date.now();
    const duration = ((endTime - this.startTime) / 1000).toFixed(2);
    
    const report = {
      environment: this.config.current.name,
      timestamp: new Date().toISOString(),
      duration: `${duration}s`,
      buildSize: this.getBuildSize(),
      version: this.getVersion(),
      commit: this.getCommitHash()
    };
    
    // 保存报告
    const reportPath = path.join(this.config.build.outDir, 'deploy-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    log('\n📊 部署报告:', 'cyan');
    log(`环境: ${report.environment}`, 'blue');
    log(`时间: ${report.timestamp}`, 'blue');
    log(`耗时: ${report.duration}`, 'blue');
    log(`大小: ${report.buildSize}`, 'blue');
    log(`版本: ${report.version}`, 'blue');
    log(`提交: ${report.commit}`, 'blue');
    
    return report;
  }

  // 获取构建大小
  getBuildSize() {
    try {
      const distDir = this.config.build.outDir;
      const result = execSync(`du -sh ${distDir}`, { encoding: 'utf8' });
      return result.split('\t')[0];
    } catch (error) {
      return 'Unknown';
    }
  }

  // 获取版本号
  getVersion() {
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      return packageJson.version || '1.0.0';
    } catch (error) {
      return '1.0.0';
    }
  }

  // 获取提交哈希
  getCommitHash() {
    try {
      return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      return 'Unknown';
    }
  }

  // 执行完整部署流程
  async deploy() {
    try {
      this.validateEnvironment();
      this.installDependencies();
      this.runPreBuildScripts();
      this.buildProject();
      this.runPostBuildScripts();
      this.validateBuild();
      
      const report = this.generateDeployReport();
      
      log('\n🎉 部署成功完成！', 'green');
      log(`🕒 总耗时: ${report.duration}`, 'cyan');
      
      return true;
    } catch (error) {
      log(`\n💥 部署失败: ${error.message}`, 'red');
      process.exit(1);
    }
  }
}

// 命令行接口
function main() {
  const args = process.argv.slice(2);
  const environment = args[0] || 'development';
  
  const validEnvs = ['development', 'staging', 'production'];
  if (!validEnvs.includes(environment)) {
    log(`❌ 无效的环境: ${environment}`, 'red');
    log(`可用环境: ${validEnvs.join(', ')}`, 'yellow');
    process.exit(1);
  }
  
  const deployer = new Deployer(environment);
  deployer.deploy();
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { Deployer };
