/* 聊天室页面样式 - 科技主题版 */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0f1419 !important; /* 强制深色背景，避免白色闪烁 */
  background-color: #0f1419 !important;
}

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 0 10rpx var(--primary-color);
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.particle:nth-child(3) {
  top: 80%;
  left: 40%;
  animation-delay: 4s;
}

@keyframes dataFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-100rpx, -100rpx); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) scale(1); opacity: 0.7; }
  50% { transform: translateY(-20rpx) scale(1.2); opacity: 1; }
}

.container {
  height: calc(100vh - 160rpx); /* 增加高度预留，适应多行输入框 */
  background: transparent;
  display: flex;
  flex-direction: column;
}

/* 确保自定义导航栏页面有正确的顶部间距 - 减少间距 */
.container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 10rpx); /* 减少顶部间距 */
}

/* 消息列表 */
.message-list {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 0; /* 移除底部间距，使用底部占位元素代替 */
  overflow-y: auto;
  box-sizing: border-box;
}

/* 底部占位元素 */
.bottom-anchor {
  height: 300rpx; /* 增加高度，确保有足够空间 */
  width: 100%;
  background: transparent;
  flex-shrink: 0; /* 防止被压缩 */
  min-height: 300rpx; /* 最小高度保证 */
}

/* 当表情包面板打开时，增加底部占位元素高度 */
.emoji-panel-open .bottom-anchor {
  height: 800rpx; /* 恢复原来的高度 */
}

/* 当附件面板打开时，增加底部占位元素高度 */
.attachment-panel-open .bottom-anchor {
  height: 560rpx; /* 恢复原来的高度 */
}

/* 历史消息加载 */
.load-more {
  text-align: center;
  padding: 20rpx;
}

.load-more-text {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 消息项 */
.message-item {
  margin-bottom: 30rpx;
}

.message-content {
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
}

/* 当有昵称时，调整对齐方式 */
.message-content:has(.avatar-container) {
  align-items: flex-start;
}

/* 为了兼容性，使用类选择器 */
.message-content.with-nickname {
  align-items: flex-start;
}

/* 确保消息气泡与头像顶部对齐 */
.message-content.with-nickname .message-bubble {
  margin-top: 30rpx; /* 与昵称高度对齐 */
  align-self: flex-start;
}

/* 对方消息 */
.other-message {
  justify-content: flex-start;
}

.other-message .message-bubble {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-radius: 20rpx 20rpx 20rpx 8rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(0, 212, 255, 0.2);
}

/* 自己的消息 */
.self-message {
  justify-content: flex-end;
}

.self-message .message-bubble {
  background: var(--primary-gradient);
  border-radius: 20rpx 20rpx 8rpx 20rpx;
  color: var(--text-primary);
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
}

/* 头像容器 */
.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  justify-content: flex-start; /* 让头像在顶部对齐 */
}

/* 昵称 */
.nickname {
  font-size: 22rpx;
  color: var(--text-tertiary);
  opacity: 0.8;
  text-align: center; /* 居中对齐 */
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transform: translateX(-10rpx); /* 向左偏移更多一点 */
}

/* 头像 */
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: 2rpx solid var(--primary-color);
}

.avatar.default-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
}

.avatar .avatar-text {
  font-size: 32rpx;
  color: var(--primary-color);
}

/* 消息气泡 */
.message-bubble {
  max-width: 500rpx;
  padding: 24rpx 30rpx;
  position: relative;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 图片消息样式 */
.message-image-container {
  border-radius: 12rpx;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.1);
}

.message-image {
  width: 400rpx;
  max-width: 400rpx;
  min-height: 200rpx;
  max-height: 600rpx;
  border-radius: 12rpx;
  display: block;
  transition: all 0.3s ease;
}

.message-image:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 图片消息的气泡样式调整 */
.message-bubble:has(.message-image-container) {
  padding: 8rpx;
  background: transparent;
  border: 2rpx solid rgba(0, 212, 255, 0.3);
}

.self-message .message-bubble:has(.message-image-container) {
  background: transparent;
  border: 2rpx solid rgba(0, 212, 255, 0.5);
}

/* 语音消息的气泡样式调整 - 使用类名方式 */
.voice-message-bubble {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
  max-width: none !important;
}

.self-message .voice-message-bubble {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
  max-width: none !important;
}

.other-message .voice-message-bubble {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
  max-width: none !important;
}

/* 语音消息样式 */
.message-voice-container {
  min-width: 200rpx;
  max-width: 400rpx;
}

/* 强制移除语音消息外围背景 */
.message-item:has(.message-voice-container) .message-bubble {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
}

.self-message:has(.message-voice-container) .message-bubble {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
}

.other-message:has(.message-voice-container) .message-bubble {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
}

.voice-bubble {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(0, 212, 255, 0.1);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 50rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 80rpx;
  justify-content: space-between;
}

.voice-bubble:active {
  transform: scale(0.95);
  background: rgba(0, 212, 255, 0.2);
}

.voice-bubble.playing {
  background: rgba(0, 212, 255, 0.3);
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.4);
}

.voice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.voice-duration {
  font-size: 28rpx;
  color: var(--text-primary);
  margin-right: 16rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.voice-waves {
  display: flex;
  align-items: center;
  gap: 4rpx;
  flex: 1;
  justify-content: center;
}

.wave {
  width: 6rpx;
  height: 20rpx;
  background: var(--primary-color);
  border-radius: 3rpx;
  animation: waveAnimation 1.2s ease-in-out infinite;
}

.wave:nth-child(1) {
  animation-delay: 0s;
}

.wave:nth-child(2) {
  animation-delay: 0.2s;
}

.wave:nth-child(3) {
  animation-delay: 0.4s;
}

.wave:nth-child(4) {
  animation-delay: 0.6s;
}

.wave:nth-child(5) {
  animation-delay: 0.8s;
}

.voice-status {
  flex: 1;
  text-align: center;
}

.voice-status-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  opacity: 0.8;
}

@keyframes waveAnimation {
  0%, 100% {
    height: 20rpx;
    opacity: 0.6;
  }
  50% {
    height: 40rpx;
    opacity: 1;
  }
}

/* 自己发送的语音消息样式调整 */
.self-message .voice-bubble {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.self-message .voice-bubble.playing {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 20rpx rgba(255, 255, 255, 0.3);
}

.self-message .voice-duration {
  color: rgba(255, 255, 255, 0.9);
}

.self-message .voice-status-text {
  color: rgba(255, 255, 255, 0.7);
}

.self-message .wave {
  background: rgba(255, 255, 255, 0.8);
}

/* 撤回消息样式 */
.message-bubble.recalled {
  background: rgba(128, 128, 128, 0.3) !important;
  border: 1rpx solid rgba(128, 128, 128, 0.5);
  opacity: 0.7;
}

.recalled-text {
  color: rgba(255, 255, 255, 0.6) !important;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.recalled-icon {
  font-size: 24rpx;
  opacity: 0.8;
}

.message-time {
  font-size: 20rpx;
  opacity: 0.7;
  margin-top: 8rpx;
  text-align: right;
}

.other-message .message-time {
  text-align: left;
  color: var(--text-tertiary);
}

.self-message .message-time {
  color: rgba(255, 255, 255, 0.7);
}

/* 空状态 */
.empty-messages {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.3);
}



/* 输入区域 */
.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  border-top: 2rpx solid var(--border-color);
  padding: 16rpx 20rpx; /* 恢复原来的内边距 */
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  z-index: 1000;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.3);
  min-height: 96rpx; /* 恢复原来的最小高度 */
  max-height: 320rpx; /* 恢复原来的最大高度限制 */
}

.input-container {
  display: flex;
  align-items: center; /* 改为居中对齐，使所有按钮统一整齐 */
  gap: 12rpx; /* 统一间距 */
  position: relative;
  min-height: 64rpx;
  width: 100%;
  box-sizing: border-box;
}

.message-input {
  flex: 1;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border: 1rpx solid var(--border-color);
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  outline: none;
  /* 移除transition，防止切换时的动画冲突 */
  margin: 0;
  min-height: 64rpx;
  max-height: 240rpx;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  resize: none;
  box-sizing: border-box;
  /* 添加稳定的显示属性 */
  width: 100%;
  display: block;
}

.message-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
}

.message-input::placeholder {
  color: var(--text-tertiary);
}

/* ==================== 语音模式样式 ==================== */

/* 语音/键盘切换按钮 */
.voice-toggle-button {
  width: 64rpx; /* 统一按钮尺寸 */
  height: 64rpx;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border: 1rpx solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;
}

.voice-toggle-button:active {
  transform: scale(0.95);
  background: var(--primary-color);
}

.voice-toggle-icon {
  font-size: 32rpx;
  color: var(--text-primary);
}

/* 统一的输入容器样式 */
.text-input-container,
.voice-input-container {
  flex: 1;
  margin: 0;
  min-width: 0;
  overflow: hidden;
  /* 添加固定高度，防止切换时的高度变化 */
  min-height: 64rpx;
  display: flex;
  align-items: center;
}

/* 语音输入按钮 */
.voice-input-button {
  width: 100%;
  height: 64rpx; /* 固定高度，与输入框一致 */
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border: 1rpx solid var(--border-color);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 移除transition，防止切换时的动画冲突 */
  cursor: pointer;
  user-select: none;
  box-sizing: border-box;
}

.voice-input-button.recording {
  background: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.5);
}

.voice-input-text {
  font-size: 28rpx; /* 恢复原来的字体大小 */
  color: var(--text-primary);
  font-weight: 500; /* 恢复原来的字体粗细 */
}

.voice-input-button.recording .voice-input-text {
  color: #ffffff;
}

/* 语音录制状态提示 */
.voice-recording-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
  /* 调整弹窗位置，向上偏移 */
  padding-bottom: 200rpx; /* 增加底部内边距，让弹窗向上移动 */
}

.voice-recording-container {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1rpx solid var(--border-color);
  border-radius: 32rpx;
  padding: 60rpx 80rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  min-width: 400rpx;
  transition: all 0.3s ease;
}

.voice-recording-container.cancel-mode {
  border-color: #ff4757;
  box-shadow: 0 20rpx 60rpx rgba(255, 71, 87, 0.3);
}

.voice-recording-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.voice-recording-circle {
  width: 120rpx;
  height: 120rpx;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  animation: pulse 1.5s ease-in-out infinite;
  transition: all 0.3s ease;
}

.voice-recording-circle.cancel-circle {
  background: #ff4757;
  animation: shake 0.5s ease-in-out infinite;
}

.voice-recording-icon {
  font-size: 48rpx;
  color: #ffffff;
}

.voice-recording-waves {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.recording-wave {
  width: 6rpx;
  height: 20rpx;
  background: var(--primary-color);
  border-radius: 3rpx;
  animation: wave 1.2s ease-in-out infinite;
}

.recording-wave:nth-child(1) {
  animation-delay: 0s;
}

.recording-wave:nth-child(2) {
  animation-delay: 0.1s;
}

.recording-wave:nth-child(3) {
  animation-delay: 0.2s;
}

.recording-wave:nth-child(4) {
  animation-delay: 0.3s;
}

.recording-wave:nth-child(5) {
  animation-delay: 0.4s;
}

.voice-recording-time {
  margin-bottom: 20rpx;
}

.recording-time-text {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
}

.voice-recording-hint {
  margin-bottom: 20rpx;
}

.recording-hint-text {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.recording-hint-text.cancel {
  color: #ff4757;
  font-weight: 600;
}

.cancel-hint {
  margin-top: 10rpx;
}

.cancel-hint-text {
  font-size: 24rpx;
  color: #ff4757;
  opacity: 0.8;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 20rpx rgba(0, 212, 255, 0);
  }
}

@keyframes wave {
  0%, 100% {
    height: 20rpx;
  }
  50% {
    height: 60rpx;
  }
}

@keyframes shake {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.05) rotate(-2deg);
  }
  75% {
    transform: scale(1.05) rotate(2deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

/* ==================== 输入区域按钮样式 ==================== */

/* 语音按钮 */
.voice-button {
  width: 72rpx;
  height: 72rpx;
  background: var(--bg-glass);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.voice-button:active {
  background: var(--primary-light);
  transform: scale(0.95);
}

.voice-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

/* 表情包按钮 */
.emoji-button {
  width: 64rpx; /* 统一按钮尺寸 */
  height: 64rpx;
  background: var(--bg-glass);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.emoji-button:active {
  background: var(--primary-light);
  transform: scale(0.95);
}

.emoji-icon {
  font-size: 36rpx;
  color: var(--text-primary);
}

/* 动作按钮容器 */
.action-button-container {
  position: relative;
  width: 64rpx; /* 统一按钮尺寸 */
  height: 64rpx;
  flex-shrink: 0;
}

/* 附件按钮 */
.attachment-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 64rpx; /* 统一按钮尺寸 */
  height: 64rpx;
  background: var(--bg-glass);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

.attachment-button.hidden {
  opacity: 0;
  transform: scale(0.8) rotate(45deg);
  pointer-events: none;
}

.attachment-button.visible {
  opacity: 1;
  transform: scale(1) rotate(0deg);
  pointer-events: auto;
}

.attachment-button:active {
  background: var(--primary-light);
  transform: scale(0.95) rotate(0deg);
}

.attachment-icon {
  font-size: 36rpx;
  color: var(--text-primary);
  font-weight: 300;
}

/* 发送按钮 */
.send-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 64rpx; /* 统一按钮尺寸 */
  height: 64rpx;
  background: var(--primary-gradient);
  color: var(--text-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx; /* 调整字体大小 */
  font-weight: 600;
  border: 1rpx solid var(--primary-color);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.send-button.visible {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

.send-button.hidden {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.send-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(0, 212, 255, 0.2);
}

/* ==================== 附件面板样式 ==================== */

.attachment-panel {
  background: var(--bg-secondary);
  border-top: 1rpx solid var(--border-color);
  padding: 30rpx 20rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}

.attachment-panel.show {
  max-height: 240rpx;
  opacity: 1;
  transform: translateY(0);
}

.attachment-panel.hide {
  max-height: 0;
  opacity: 0;
  transform: translateY(-20rpx);
  padding: 0 20rpx;
}

.attachment-options {
  display: flex;
  justify-content: center;
  gap: 80rpx;
  flex-wrap: wrap;
}

.attachment-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 120rpx;
}

.attachment-option:active {
  background: var(--primary-light);
  transform: scale(0.95);
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
}

.option-icon-text {
  font-size: 40rpx;
  color: var(--text-primary);
}

.option-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  text-align: center;
}

/* ==================== 表情包面板样式 ==================== */

.emoji-panel {
  background: var(--bg-secondary);
  border-top: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
  overflow: hidden;
}

.emoji-panel.show {
  height: 500rpx;
  opacity: 1;
  transform: translateY(0);
}

.emoji-panel.hide {
  height: 0;
  opacity: 0;
  transform: translateY(-20rpx);
}

.emoji-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx 10rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  background: var(--bg-tertiary);
}

.emoji-title {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
}

.emoji-close-hint {
  opacity: 0.7;
}

.close-hint-text {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

.emoji-scroll {
  height: calc(100% - 80rpx);
  padding: 20rpx;
}

.emoji-categories {
  padding-bottom: 20rpx;
}

.emoji-category {
  margin-bottom: 40rpx;
}

.category-title {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  font-weight: 600;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 20rpx;
  padding: 0 10rpx;
}

.emoji-item {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  cursor: pointer;
}

.emoji-item:active {
  background: var(--primary-light);
  transform: scale(1.2);
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
}

/* 顶部工具栏 */
.toolbar {
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  border-bottom: 2rpx solid var(--border-color);
  padding: 20rpx;
  display: flex;
  justify-content: center;
  flex-shrink: 0; /* 防止被压缩 */
}

.toolbar-button {
  background: var(--bg-glass);
  color: var(--primary-color);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border: 2rpx solid var(--primary-color);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.2);
}

.toolbar-button:active {
  background: var(--primary-light);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 212, 255, 0.3);
}

/* 响应式调整 */
@media (max-width: 375px) {
  .message-bubble {
    max-width: 400rpx;
  }

  .message-text {
    font-size: 28rpx;
  }
}

/* 动画效果 */
.message-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 科技感装饰效果 */
.message-bubble::before {
  content: '';
  position: absolute;
  top: -1rpx;
  left: -1rpx;
  right: -1rpx;
  bottom: -1rpx;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.self-message .message-bubble::before {
  opacity: 1;
}


