/**index.wxss - 科技感首页样式**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0f1419; /* 确保始终有深色背景，避免白色闪烁 */
}

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

/* 自定义背景图片样式 */
.tech-bg.has-custom-bg {
  background-blend-mode: overlay;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

/* 背景图片覆盖层 */
.tech-grid.with-overlay {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(1px);
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: var(--cyber-blue);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
  box-shadow: 0 0 10rpx var(--cyber-blue);
}

.particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 1s;
}

.particle:nth-child(3) {
  top: 40%;
  left: 30%;
  animation-delay: 2s;
}

.particle:nth-child(4) {
  top: 80%;
  left: 60%;
  animation-delay: 0.5s;
}

.particle:nth-child(5) {
  top: 10%;
  left: 70%;
  animation-delay: 1.5s;
}



.scrollarea {
  flex: 1;
  overflow-y: auto;
  height: 100%;
}

/* 确保自定义导航栏页面的滚动区域有正确的顶部间距 */
.scrollarea.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 10rpx);
  box-sizing: border-box;
}

.container {
  padding: 0 0 calc(env(safe-area-inset-bottom) + 120rpx) 0;
}

/* 科技感轮播图样式 */
.banner-swiper {
  height: 360rpx;
  margin: var(--space-xs) var(--space-lg) var(--space-lg) var(--space-lg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-glow);
  border: 1rpx solid var(--border-glow);
  position: relative;
}

.banner-swiper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
  z-index: 1;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.banner-item:active .banner-image {
  transform: scale(1.05);
}

.banner-title {
  position: absolute;
  bottom: var(--space-lg);
  left: var(--space-lg);
  right: var(--space-lg);
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20rpx);
  color: var(--text-primary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  border: 1rpx solid var(--border-glow);
  font-size: 30rpx;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  letter-spacing: 0.5rpx;
}

/* 每日密码卡片样式 */
.daily-password-card {
  background: linear-gradient(135deg, rgba(30, 45, 61, 0.9), rgba(21, 32, 43, 0.9));
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 0 24rpx 20rpx;
  border: 1rpx solid rgba(74, 179, 126, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.4);
  position: relative;
  overflow: hidden;
}

.daily-password-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #4ab37e, transparent);
  animation: scanLine 3s ease-in-out infinite;
}

@keyframes scanLine {
  0%, 100% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; transform: translateX(100%); }
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.password-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.password-icon {
  font-size: 28rpx;
  color: #4ab37e;
}

.password-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2rpx 8rpx rgba(74, 179, 126, 0.3);
}

.password-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: rgba(0, 212, 255, 0.2);
  border: 1rpx solid rgba(0, 212, 255, 0.4);
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.refresh-btn:active {
  background: rgba(0, 212, 255, 0.3);
  transform: scale(0.95);
}

.refresh-icon {
  font-size: 18rpx;
  color: #00d4ff;
}

.refresh-text {
  font-size: 18rpx;
  color: #00d4ff;
  font-weight: 600;
}

.copy-all-btn {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: rgba(74, 179, 126, 0.2);
  border: 1rpx solid rgba(74, 179, 126, 0.4);
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.copy-all-btn:active {
  background: rgba(74, 179, 126, 0.3);
  transform: scale(0.95);
}

.copy-icon {
  font-size: 18rpx;
  color: #4ab37e;
}

.copy-text {
  font-size: 18rpx;
  color: #4ab37e;
  font-weight: 600;
}

.password-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #52c41a;
  box-shadow: 0 0 12rpx rgba(82, 196, 26, 0.6);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.status-text {
  font-size: 22rpx;
  color: #52c41a;
  font-weight: 500;
}

.password-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12rpx;
}

.password-item {
  background: rgba(74, 179, 126, 0.1);
  border: 1rpx solid rgba(74, 179, 126, 0.3);
  border-radius: 12rpx;
  padding: 7rpx 6rpx;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 60rpx;
}

.password-item:active {
  background: rgba(74, 179, 126, 0.2);
  border-color: rgba(74, 179, 126, 0.5);
  transform: scale(0.95);
}

.password-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(74, 179, 126, 0.2), transparent);
  transition: left 0.5s ease;
}

.password-item:hover::before {
  left: 100%;
}

.location-name {
  font-size: 20rpx;
  color: #4ab37e;
  font-weight: 600;
  line-height: 1.0;
  margin: 0;
}

.location-code {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  text-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.2);
  letter-spacing: 1rpx;
  line-height: 1.0;
  margin: 0;
}

/* 破译失败状态样式 */
.password-item[data-code="----"] {
  background: rgba(255, 77, 79, 0.1);
  border-color: rgba(255, 77, 79, 0.3);
}

.password-item[data-code="----"] .location-code {
  color: #ff4d4f;
  text-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

.password-item[data-code="----"] .location-name {
  color: #ff4d4f;
}

.password-item[data-code="----"] .copy-hint {
  color: rgba(255, 77, 79, 0.8);
}

.copy-hint {
  font-size: 16rpx;
  color: rgba(74, 179, 126, 0.8);
  opacity: 0;
  transition: opacity 0.3s ease;
  line-height: 1.0;
  margin: 0;
  position: absolute;
  bottom: 2rpx;
  left: 50%;
  transform: translateX(-50%);
}

.password-item:active .copy-hint {
  opacity: 1;
}

/* 破译状态样式 - 复用密码卡片布局 */
.daily-password-card.decrypting {
  border-color: rgba(0, 212, 255, 0.4);
  box-shadow: 0 8rpx 32rpx rgba(0, 212, 255, 0.2);
}

.daily-password-card.decrypting::before {
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: scanLine 2s ease-in-out infinite;
}

/* 破译状态特定样式 */
.decryption-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot.decrypting {
  background: #00d4ff;
  box-shadow: 0 0 12rpx rgba(0, 212, 255, 0.6);
  animation: pulse 1s ease-in-out infinite;
}

.decryption-status .status-text {
  color: #00d4ff;
  font-size: 18rpx;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  letter-spacing: 1rpx;
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* 破译状态下的密码项样式 */
.password-item.decrypting-item {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
}

.password-item.decrypting-item::before {
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  animation: scanEffect 2s linear infinite;
}

@keyframes scanEffect {
  0% { left: -100%; }
  100% { left: 100%; }
}

.decrypting-item .location-name {
  color: #00d4ff;
}

.location-code.decrypting-code {
  display: flex;
  justify-content: center;
  gap: 2rpx;
}

.decrypting-code .matrix-char {
  font-size: 28rpx;
  color: #00d4ff;
  font-family: 'Courier New', monospace;
  font-weight: 700;
  animation: matrixFlicker 0.5s linear infinite;
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.5);
  line-height: 1.0;
}

.decrypting-code .matrix-char:nth-child(1) {
  animation-delay: 0s;
}

.decrypting-code .matrix-char:nth-child(2) {
  animation-delay: 0.1s;
}

.decrypting-code .matrix-char:nth-child(3) {
  animation-delay: 0.2s;
}

.decrypting-code .matrix-char:nth-child(4) {
  animation-delay: 0.3s;
}

@keyframes matrixFlicker {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* 通用卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 0 24rpx 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.card-title {
  font-size: 32rpx;
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
  letter-spacing: 0.5rpx;
}

/* 现代化区块头部样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding: 0 var(--space-lg);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-link {
  color: var(--primary-color);
  font-size: 26rpx;
  font-weight: 400;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(0, 212, 255, 0.08);
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.action-link:active {
  background: rgba(0, 212, 255, 0.15);
  transform: scale(0.95);
}

.more-link {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 500;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.more-link:active {
  background: var(--primary-light);
  transform: scale(0.95);
}



.price {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff6b35;
  margin-bottom: 8rpx;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.status-indicator.online {
  background-color: #52c41a;
}

.status-indicator.offline {
  background-color: #999;
}

.status-indicator.busy {
  background-color: #faad14;
}

/* 现代化订单列表样式 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0;
}

/* 新版订单卡片样式 */
.order-item-new {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.order-item-new:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.3);
}

/* 新版订单头部 */
.order-header-new {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.order-title-new {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
  flex: 1;
  margin-right: 20rpx;
}

.order-price-new {
  font-size: 36rpx;
  font-weight: 700;
  color: #FFD700;
  text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
  white-space: nowrap;
}

/* 新版信息网格 */
.order-info-grid {
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  min-height: 40rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 80rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: var(--text-primary);
  font-weight: 600;
  text-align: right;
  flex: 1;
  line-height: 1.4;
}

.info-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.tag-new {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 56rpx;
  padding: 0 20rpx 8rpx 20rpx;
  background: rgba(0, 212, 255, 0.15);
  border: 2rpx solid #00d4ff;
  border-radius: 25rpx;
  font-size: 24rpx;
  color: #00d4ff;
  font-weight: 500;
  box-sizing: border-box;
  white-space: nowrap;
}

/* 新版订单底部 */
.order-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

.user-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.user-avatar-new {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 212, 255, 0.3);
}

.avatar-text {
  font-size: 20rpx;
  color: white;
  font-weight: 600;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-name-new {
  font-size: 26rpx;
  color: var(--text-primary);
  font-weight: 600;
}

.user-status-new {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #52c41a;
  box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.6);
}

.status-text {
  font-size: 22rpx;
  color: var(--text-secondary);
}

.grab-btn-new {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.4);
  transition: all 0.3s ease;
}

.grab-btn-new:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.6);
}

/* 旧版订单卡片样式已移除，使用新版 order-item-new */



/* 旧版订单样式已移除，使用新版样式 */

/* 旧版标签样式已移除 */

.order-status {
  font-size: 22rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.order-status::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.8;
  z-index: -1;
}

.order-status.pending {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.9), rgba(255, 142, 142, 0.9));
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
  animation: pendingGlow 2s ease-in-out infinite;
}

.order-status.accepted {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.9), rgba(0, 153, 204, 0.9));
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.3);
}

.order-status.in_progress {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.9), rgba(135, 208, 104, 0.9));
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.3);
}

.order-status.completed {
  background: linear-gradient(135deg, rgba(140, 140, 140, 0.9), rgba(180, 180, 180, 0.9));
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(140, 140, 140, 0.3);
}



.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
  padding-top: 16rpx;
  border-top: 1rpx solid var(--border-glow);
  width: 100%;
  min-height: 60rpx;
  margin-top: auto;
}

.order-user {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.avatar-icon {
  font-size: 18rpx;
  font-weight: 700;
  color: #ffffff;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-name {
  font-size: 24rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.user-status {
  font-size: 20rpx;
  color: #52c41a;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.user-status::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #52c41a;
  box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.5);
}

.order-action-wrapper {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}







.order-price {
  font-size: 36rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.5rpx;
  text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

/* 现代化空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-3xl) var(--space-lg);
  text-align: center;
  background: transparent;
  border-radius: var(--radius-lg);
  margin: var(--space-lg);
  box-shadow: none;
  border: none;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-xl);
  opacity: 0.7;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  letter-spacing: 0.5rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-xl);
}

.empty-action {
  background: var(--primary-gradient);
  color: #ffffff;
  padding: var(--space-sm) var(--space-xl);
  border-radius: var(--radius-xl);
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.empty-action:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

/* G.T.I. SECURITY 品牌加载状态样式 */
.gti-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx var(--space-lg);
  min-height: 400rpx;
  background: rgba(15, 23, 42, 0.8);
  border-radius: var(--radius-xl);
  margin: var(--space-lg);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(0, 212, 255, 0.2);
}

.gti-logo-container {
  margin-bottom: 40rpx;
  position: relative;
}

.gti-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.1);
  padding: 20rpx;
  animation: logoGlow 2s ease-in-out infinite;
}

@keyframes logoGlow {
  0%, 100% {
    box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40rpx rgba(0, 212, 255, 0.6);
    transform: scale(1.05);
  }
}

.gti-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 212, 255, 0.2);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
}

.gti-loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 30rpx;
  font-weight: 500;
}

.gti-loading-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background: var(--primary-color);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
  box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.dot-1 { animation-delay: -0.32s; }
.dot-2 { animation-delay: -0.16s; }
.dot-3 { animation-delay: 0s; }

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 保留原有的加载状态样式作为备用 */
.loading-state {
  padding: var(--space-lg);
}

.loading-state .loading-skeleton {
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-md);
}

/* 抢单大厅样式 */
.grab-order-item {
  position: relative;
  transition: all 0.3s ease;
}

.grab-order-item:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-lg);
}

.grab-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  border-radius: 24rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 100rpx;
  flex-shrink: 0;
}

.grab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.grab-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.6);
}

.grab-btn:active::before {
  opacity: 1;
}

.grab-btn-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5rpx;
  white-space: nowrap;
}

/* 订单卡片悬停效果 */
.order-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.2);
}

/* 抢单按钮发光动画 */
@keyframes grabBtnGlow {
  0%, 100% {
    box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.4);
  }
  50% {
    box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.6);
  }
}

.grab-btn {
  animation: grabBtnGlow 3s ease-in-out infinite;
}

/* 抢单大厅待接单动画 */
@keyframes pendingGlow {
  0%, 100% {
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.5);
    transform: scale(1.05);
  }
}

@keyframes grabHintPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6rpx 20rpx rgba(255, 215, 0, 0.6);
  }
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 212, 255, 0.2);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  color: var(--primary-color);
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 浮动按钮 */
.btn-floating {
  position: fixed;
  bottom: 200rpx; /* 设置为导航栏高度(120rpx) + 安全距离(80rpx) */
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 212, 255, 0.4);
  z-index: 1001; /* 提高层级，确保在导航栏之上 */
  transition: all 0.3s ease;
}

.btn-floating:active {
  transform: scale(0.95);
}

.floating-icon {
  width: 60rpx;
  height: 60rpx;
}

.floating-text {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
}

/* 快速操作按钮样式 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  gap: var(--space-md);
  margin-top: var(--space-md);
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-lg);
  background: rgba(0, 212, 255, 0.05);
  border: 1rpx solid rgba(0, 212, 255, 0.2);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-item:active {
  transform: scale(0.95);
  background: rgba(0, 212, 255, 0.1);
}

.action-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.action-item:active::before {
  left: 100%;
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: var(--space-sm);
  display: block;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
}


