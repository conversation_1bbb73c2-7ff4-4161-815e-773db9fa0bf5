// 登录页面
import API from '../../../utils/api.js';
const errorHandler = require('../../../utils/errorHandler.js');

const app = getApp();

Page({
  data: {
    userInfo: null,
    loading: false
  },

  onLoad() {
    // 检查登录状态
    this.checkLoginStatus();
    // 预加载用户信息
    this.preloadUserInfo();
    // 测试云开发
    this.testCloudConnection();
  },

  onShow() {
    // 页面显示时检查登录状态
    this.checkLoginStatus();
  },

  // 测试云开发连接
  async testCloudConnection() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: { test: true }
      });
    } catch (error) {
      // 静默处理连接失败
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.openid) {
      // 已登录，直接跳转到首页
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;

      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 预加载用户信息
  preloadUserInfo() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo
        });
      },
      fail: () => {
        // 获取失败，使用默认信息
        console.log('预加载用户信息失败');
      }
    });
  },

  // 处理登录
  handleLogin() {
    if (this.data.loading) return;

    // 获取用户信息并登录
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo
        });
        this.performLogin();
      },
      fail: () => {
        wx.showModal({
          title: '授权提示',
          content: '需要获取您的基本信息才能正常使用小程序功能',
          confirmText: '重新授权',
          success: (modalRes) => {
            if (modalRes.confirm) {
              this.handleLogin();
            }
          }
        });
      }
    });
  },

  // 执行登录
  async performLogin() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 获取微信登录凭证
      const loginRes = await this.getWxLoginCode();

      if (!loginRes.code) {
        throw new Error('获取登录凭证失败');
      }

      let userInfo;

      try {
        // 尝试调用云函数登录
        const result = await API.login({
          code: loginRes.code,
          nickName: this.data.userInfo?.nickName || '微信用户',
          avatarUrl: this.data.userInfo?.avatarUrl || '',
          gender: this.data.userInfo?.gender || 0
        });

        if (result.success) {
          userInfo = result.data;
        } else {
          throw new Error(result.error || '云函数登录失败');
        }
      } catch (cloudError) {

        // 使用模拟登录
        userInfo = {
          openid: 'mock_openid_' + Date.now(),
          nickName: this.data.userInfo?.nickName || '微信用户',
          avatarUrl: this.data.userInfo?.avatarUrl || '',
          gender: this.data.userInfo?.gender || 0,
          isDemo: true // 标记为演示模式
        };
      }

      // 保存用户信息到本地存储
      wx.setStorageSync('userInfo', userInfo);

      // 更新全局用户信息
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;

      // 显示登录成功
      wx.showToast({
        title: userInfo.isDemo ? '演示模式登录成功' : '登录成功',
        icon: 'success',
        duration: 1500
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
    } catch (error) {
      // 使用统一错误处理
      errorHandler.handle(error, '用户登录');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取微信登录凭证
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  // 游客登录
  guestLogin() {
    wx.showModal({
      title: '游客模式',
      content: '游客模式下部分功能将受限，建议使用微信登录获得完整体验。确定要继续吗？',
      confirmText: '继续',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 设置游客模式
          const guestInfo = {
            isGuest: true,
            nickName: '游客用户',
            avatarUrl: '',
            openid: 'guest_' + Date.now()
          };

          wx.setStorageSync('userInfo', guestInfo);
          app.globalData.userInfo = guestInfo;
          app.globalData.isLogin = true;

          wx.showToast({
            title: '进入游客模式',
            icon: 'success'
          });

          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1000);
        }
      }
    });
  },

  // 显示用户协议
  showUserAgreement() {
    const agreementContent = `用户协议

欢迎使用三角洲陪玩小程序！

1. 服务说明
本小程序为用户提供游戏陪玩服务，包括但不限于技能指导、游戏陪伴等。

2. 用户责任
- 遵守游戏规则和平台规定
- 不得发布违法违规内容
- 保持良好的游戏环境

3. 服务条款
- 平台有权对违规行为进行处理
- 用户应对自己的行为负责
- 平台保留最终解释权

更多详细条款请访问官网查看。`;

    wx.showModal({
      title: '用户协议',
      content: agreementContent,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    const privacyContent = `隐私政策

我们重视您的隐私保护！

1. 信息收集
- 基本用户信息（昵称、头像）
- 使用行为数据
- 设备信息

2. 信息使用
- 提供更好的服务体验
- 进行数据分析和优化
- 客服支持

3. 信息保护
- 采用加密技术保护数据
- 严格控制访问权限
- 不会向第三方泄露

4. 用户权利
- 可以查看、修改个人信息
- 可以删除账户数据
- 可以联系客服处理隐私问题

我们承诺严格保护您的隐私安全。`;

    wx.showModal({
      title: '隐私政策',
      content: privacyContent,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 联系客服
  contactSupport() {
    wx.showActionSheet({
      itemList: ['在线客服', '客服电话', '问题反馈'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.showToast({
              title: '客服功能开发中',
              icon: 'none'
            });
            break;
          case 1:
            wx.showModal({
              title: '客服电话',
              content: '************\n工作时间：9:00-21:00',
              confirmText: '拨打',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.makePhoneCall({
                    phoneNumber: '************',
                    fail: () => {
                      wx.showToast({
                        title: '拨打失败',
                        icon: 'none'
                      });
                    }
                  });
                }
              }
            });
            break;
          case 2:
            wx.showModal({
              title: '问题反馈',
              content: '请描述您遇到的问题',
              editable: true,
              placeholderText: '请输入问题描述...',
              success: (modalRes) => {
                if (modalRes.confirm && modalRes.content) {
                  wx.showToast({
                    title: '反馈已提交',
                    icon: 'success'
                  });
                }
              }
            });
            break;
        }
      }
    });
  }
});
