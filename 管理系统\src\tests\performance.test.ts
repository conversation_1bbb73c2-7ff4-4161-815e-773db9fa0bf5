// 性能测试工具
export class PerformanceTestSuite {
  private metrics: {
    [key: string]: {
      loadTime: number;
      renderTime: number;
      memoryUsage?: number;
      timestamp: number;
    }[];
  } = {};

  // 测量页面加载性能
  measurePageLoad(pageName: string): void {
    const startTime = performance.now();
    
    // 监听页面加载完成
    window.addEventListener('load', () => {
      const loadTime = performance.now() - startTime;
      this.recordMetric(pageName, 'load', loadTime);
    });

    // 监听 DOM 内容加载完成
    document.addEventListener('DOMContentLoaded', () => {
      const renderTime = performance.now() - startTime;
      this.recordMetric(pageName, 'render', renderTime);
    });
  }

  // 测量组件渲染性能
  measureComponentRender(componentName: string, renderFn: () => void): number {
    const startTime = performance.now();
    renderFn();
    const renderTime = performance.now() - startTime;
    
    this.recordMetric(componentName, 'render', renderTime);
    return renderTime;
  }

  // 测量 API 调用性能
  async measureApiCall<T>(
    apiName: string, 
    apiCall: () => Promise<T>
  ): Promise<{ result: T; duration: number }> {
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const duration = performance.now() - startTime;
      
      this.recordMetric(apiName, 'api', duration);
      return { result, duration };
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordMetric(apiName, 'api_error', duration);
      throw error;
    }
  }

  // 记录性能指标
  private recordMetric(name: string, type: string, value: number): void {
    const key = `${name}_${type}`;
    
    if (!this.metrics[key]) {
      this.metrics[key] = [];
    }
    
    this.metrics[key].push({
      loadTime: type === 'load' ? value : 0,
      renderTime: type === 'render' ? value : 0,
      timestamp: Date.now()
    });
  }

  // 获取性能报告
  getPerformanceReport(): {
    summary: {
      avgLoadTime: number;
      avgRenderTime: number;
      slowestPage: string;
      fastestPage: string;
    };
    details: { [key: string]: any };
  } {
    const loadTimes: number[] = [];
    const renderTimes: number[] = [];
    const pagePerformance: { [key: string]: number } = {};

    Object.entries(this.metrics).forEach(([key, values]) => {
      const avgValue = values.reduce((sum, v) => sum + v.loadTime + v.renderTime, 0) / values.length;
      pagePerformance[key] = avgValue;

      values.forEach(v => {
        if (v.loadTime > 0) loadTimes.push(v.loadTime);
        if (v.renderTime > 0) renderTimes.push(v.renderTime);
      });
    });

    const avgLoadTime = loadTimes.length > 0 ? loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length : 0;
    const avgRenderTime = renderTimes.length > 0 ? renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length : 0;

    const sortedPages = Object.entries(pagePerformance).sort(([,a], [,b]) => b - a);
    const slowestPage = sortedPages[0]?.[0] || 'N/A';
    const fastestPage = sortedPages[sortedPages.length - 1]?.[0] || 'N/A';

    return {
      summary: {
        avgLoadTime,
        avgRenderTime,
        slowestPage,
        fastestPage
      },
      details: this.metrics
    };
  }

  // 检查性能阈值
  checkPerformanceThresholds(): {
    passed: boolean;
    issues: string[];
  } {
    const issues: string[] = [];
    const thresholds = {
      maxLoadTime: 3000, // 3秒
      maxRenderTime: 1000, // 1秒
      maxApiTime: 5000 // 5秒
    };

    Object.entries(this.metrics).forEach(([key, values]) => {
      const avgTime = values.reduce((sum, v) => sum + v.loadTime + v.renderTime, 0) / values.length;
      
      if (key.includes('load') && avgTime > thresholds.maxLoadTime) {
        issues.push(`${key} 加载时间过长: ${avgTime.toFixed(2)}ms (阈值: ${thresholds.maxLoadTime}ms)`);
      }
      
      if (key.includes('render') && avgTime > thresholds.maxRenderTime) {
        issues.push(`${key} 渲染时间过长: ${avgTime.toFixed(2)}ms (阈值: ${thresholds.maxRenderTime}ms)`);
      }
      
      if (key.includes('api') && avgTime > thresholds.maxApiTime) {
        issues.push(`${key} API响应时间过长: ${avgTime.toFixed(2)}ms (阈值: ${thresholds.maxApiTime}ms)`);
      }
    });

    return {
      passed: issues.length === 0,
      issues
    };
  }

  // 内存使用监控
  startMemoryMonitoring(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        console.log('内存使用情况:', {
          used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
        });
      }, 10000); // 每10秒检查一次
    }
  }

  // 网络性能检查
  async checkNetworkPerformance(): Promise<{
    connectionType: string;
    effectiveType: string;
    downlink: number;
    rtt: number;
  }> {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      return {
        connectionType: connection.type || 'unknown',
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0
      };
    }
    
    return {
      connectionType: 'unknown',
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0
    };
  }

  // 打印性能报告
  printPerformanceReport(): void {
    const report = this.getPerformanceReport();
    const thresholdCheck = this.checkPerformanceThresholds();
    
    console.log('\n🚀 性能测试报告:');
    console.log(`📊 平均加载时间: ${report.summary.avgLoadTime.toFixed(2)}ms`);
    console.log(`🎨 平均渲染时间: ${report.summary.avgRenderTime.toFixed(2)}ms`);
    console.log(`🐌 最慢页面: ${report.summary.slowestPage}`);
    console.log(`⚡ 最快页面: ${report.summary.fastestPage}`);
    
    if (thresholdCheck.passed) {
      console.log('✅ 所有性能指标均在可接受范围内');
    } else {
      console.log('⚠️ 发现性能问题:');
      thresholdCheck.issues.forEach(issue => {
        console.log(`  - ${issue}`);
      });
    }
  }

  // 清除所有指标
  clearMetrics(): void {
    this.metrics = {};
  }
}

// 导出性能测试实例
export const performanceTestSuite = new PerformanceTestSuite();

// 自动开始内存监控
if (typeof window !== 'undefined') {
  performanceTestSuite.startMemoryMonitoring();
}
