<!--订单评价页面-->
<navigation-bar title="订单评价" back="{{false}}"></navigation-bar>

<!-- 返回按钮 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<scroll-view class="scrollarea page-with-custom-nav" scroll-y type="list">
  <view class="container">

    <!-- 订单信息 -->
    <view class="order-card" wx:if="{{orderInfo}}">
      <view class="order-header">
        <text class="order-title">评价{{targetRole}}</text>
        <text class="order-status completed">已完成</text>
      </view>
      <view class="order-info">
        <text class="order-time">{{orderInfo.createTimeText}}</text>
        <text class="order-duration">时长: {{orderInfo.duration}}小时</text>
      </view>
      <view class="order-amount">
        <text class="amount">¥{{orderInfo.totalAmount}}</text>
      </view>
    </view>

    <!-- 评价表单 -->
    <view class="evaluation-form">

      <!-- 服务评分 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">服务评分</text>
          <text class="required">*</text>
        </view>
        <view class="rating-container">
          <view class="stars">
            <view
              class="star {{item <= rating ? 'active' : ''}}"
              wx:for="{{[1,2,3,4,5]}}"
              wx:key="*this"
              bindtap="setRating"
              data-rating="{{item}}"
            >
              ⭐
            </view>
          </view>
          <text class="rating-text">{{ratingTexts[rating - 1] || '请评分'}}</text>
        </view>
      </view>

      <!-- 评价标签 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">服务标签</text>
        </view>
        <view class="tags-container">
          <view
            class="tag {{tagStyles[item] ? 'selected' : ''}}"
            style="{{tagStyles[item] ? 'background: #ff6b35 !important; color: #ffffff !important; border-color: #ff6b35 !important; font-weight: bold !important;' : ''}}"
            wx:for="{{evaluationTags}}"
            wx:key="index"
            bindtap="toggleTag"
            data-tag="{{item}}"
          >
            {{item}}
          </view>
        </view>
      </view>

      <!-- 评价内容 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">评价内容</text>
          <text class="optional">(可选)</text>
        </view>
        <textarea
          class="evaluation-textarea"
          placeholder="请描述您对本次服务的感受..."
          value="{{evaluationContent}}"
          bindinput="onContentInput"
          maxlength="500"
          show-confirm-bar="{{false}}"
        ></textarea>
        <view class="char-count">{{evaluationContent.length}}/500</view>
      </view>

      <!-- 是否匿名 -->
      <view class="form-section">
        <view class="anonymous-option">
          <text class="option-text">匿名评价</text>
          <switch
            checked="{{isAnonymous}}"
            bindchange="onAnonymousChange"
            color="#ff6b35"
          />
        </view>
      </view>

    </view>

    <!-- 提交按钮 -->
    <view class="submit-container">
      <button
        class="submit-btn {{canSubmit ? '' : 'disabled'}}"
        bindtap="submitEvaluation"
        disabled="{{!canSubmit || submitting}}"
      >
        {{submitting ? '提交中...' : '提交评价'}}
      </button>
    </view>

  </view>
</scroll-view>