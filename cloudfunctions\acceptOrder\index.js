// 接单/抢单云函数 - 强制修复版本
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { orderId, action } = event;

  console.log('=== acceptOrder 云函数开始 ===');
  console.log('云函数版本: 2025-07-15-FORCE-FIXED (强制修复版)');
  console.log('接收到的事件数据:', event);
  console.log('用户 openid:', wxContext.OPENID);

  // 版本检查功能
  if (action === 'version') {
    return {
      success: true,
      version: '2025-07-15-FORCE-FIXED',
      description: '强制修复版，移除分布式锁，使用原子更新',
      timestamp: new Date().toISOString(),
      features: {
        atomicUpdate: true,
        conditionalUpdate: true,
        retryMechanism: true,
        immediateVerification: true,
        noDistributedLock: true
      }
    };
  }

  try {
    // 验证必填字段
    if (!orderId) {
      return {
        success: false,
        error: '订单ID不能为空'
      };
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 简化的并发控制方案：原子更新 + 严格验证
    console.log(`🔒 [并发控制] 开始原子抢单操作, 用户:`, user._id, '订单:', orderId);

    // 使用优化的原子更新方案
    let retryCount = 0;
    const maxRetries = 2; // 减少重试次数
    let transactionResult;

    while (retryCount < maxRetries) {
      try {
        console.log(`🔄 [原子更新] 第${retryCount + 1}次尝试抢单`);

        transactionResult = await db.runTransaction(async transaction => {
          // 查找订单
          const orderResult = await transaction.collection('orders').doc(orderId).get();
          if (!orderResult.data) {
            console.log('❌ [事务] 订单不存在:', orderId);
            throw new Error('GRAB_ERROR_ORDER_NOT_FOUND');
          }

          const order = orderResult.data;
          console.log('📋 [事务] 找到订单:', {
            _id: orderId,
            status: order.status,
            accepterId: order.accepterId,
            customerId: order.customerId,
            timestamp: new Date().toISOString()
          });

          // 严格的业务逻辑检查
          if (order.status !== 'pending') {
            console.log('❌ [事务] 订单状态不允许接单:', order.status);
            throw new Error('GRAB_ERROR_ORDER_ALREADY_TAKEN');
          }

          if (order.customerId === user._id) {
            console.log('❌ [事务] 不能接受自己发布的订单');
            throw new Error('GRAB_ERROR_OWN_ORDER');
          }

          if (order.accepterId) {
            console.log('❌ [事务] 订单已被其他用户接单:', order.accepterId);
            throw new Error('GRAB_ERROR_ORDER_ALREADY_TAKEN');
          }

          // 关键：使用条件更新确保原子性
          // 只有当订单状态为pending且没有accepterId时才更新
          const updateResult = await transaction.collection('orders').where({
            _id: orderId,
            status: 'pending',
            accepterId: _.eq(null)
          }).update({
            data: {
              accepterId: user._id,
              status: 'accepted',
              acceptTime: new Date(),
              updateTime: new Date(),
              grabTimestamp: Date.now() // 添加抢单时间戳用于调试
            }
          });

          // 检查更新结果
          if (!updateResult || updateResult.updated === 0) {
            console.log('❌ [事务] 条件更新失败，订单可能已被抢:', {
              orderId,
              updateResult,
              retryCount
            });
            throw new Error('GRAB_ERROR_CONCURRENT_UPDATE_FAILED');
          }

          console.log('✅ [事务] 条件更新成功:', {
            orderId,
            accepterId: user._id,
            updated: updateResult.updated,
            retryCount
          });

          // 立即验证更新结果
          const verifyResult = await transaction.collection('orders').doc(orderId).get();
          if (!verifyResult.data || verifyResult.data.accepterId !== user._id) {
            console.log('❌ [事务] 更新验证失败:', {
              expected: user._id,
              actual: verifyResult.data?.accepterId,
              orderId
            });
            throw new Error('GRAB_ERROR_VERIFICATION_FAILED');
          }

          console.log('✅ [事务] 更新验证成功');
          return order;
        }, 1); // 减少事务内部重试次数

        // 如果执行到这里，说明抢单成功
        break;

      } catch (error) {
        retryCount++;
        console.log(`⚠️ [原子更新] 第${retryCount}次尝试失败:`, error.message);

        // 如果是并发冲突且还有重试机会，继续重试
        if ((error.message.includes('GRAB_ERROR_CONCURRENT_UPDATE_FAILED') ||
             error.message.includes('GRAB_ERROR_VERIFICATION_FAILED')) &&
            retryCount < maxRetries) {

          const waitTime = 20 + Math.random() * 30; // 减少等待时间：20-50ms
          console.log(`🔄 [原子更新] 等待${waitTime.toFixed(0)}ms后重试...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }

        // 其他错误或重试次数用完，直接抛出
        throw error;
      }
    }

    if (!transactionResult) {
      console.log('❌ [原子更新] 所有重试尝试都失败了');
      throw new Error('GRAB_ERROR_MAX_RETRIES_EXCEEDED');
    }

    console.log('✅ [并发控制] 抢单操作成功完成，所有验证通过');
    const order = transactionResult;

    // 自动创建聊天室
    let chatRoomId = null;
    try {
      console.log('🔄 开始为订单创建聊天室...');

      // 首先检查是否已存在聊天室
      const existingChatRoomResult = await db.collection('chatRooms').where({
        orderId: orderId
      }).get();

      if (existingChatRoomResult.data.length > 0) {
        chatRoomId = existingChatRoomResult.data[0]._id;
        console.log('✅ 聊天室已存在，使用现有聊天室:', chatRoomId);
      } else {
        // 获取客户信息
        const customerResult = await db.collection('users').doc(order.customerId).get();
        if (!customerResult.data) {
          console.log('❌ 无法获取客户信息');
          throw new Error('无法获取客户信息');
        }

        const customer = customerResult.data;

        // 创建聊天室
        const chatRoomData = {
          orderId: orderId,
          customerId: order.customerId,
          accepterId: user._id,
          customerInfo: {
            nickName: customer.nickName,
            avatarUrl: customer.avatarUrl
          },
          accepterInfo: {
            nickName: user.nickName,
            avatarUrl: user.avatarUrl
          },
          orderInfo: {
            title: order.title,
            reward: order.reward || order.pricing?.totalAmount,
            status: 'accepted'
          },
          lastMessage: null,
          lastMessageTime: null,
          userActiveTime: {},
          status: 'active', // 确保设置聊天室状态
          createTime: new Date(),
          updateTime: new Date()
        };

        const chatRoomResult = await db.collection('chatRooms').add({
          data: chatRoomData
        });

        chatRoomId = chatRoomResult._id;
        console.log('✅ 聊天室创建成功:', chatRoomId);
      }

      // 更新订单的聊天室ID（确保使用数据库生成的_id）
      await db.collection('orders').doc(orderId).update({
        data: {
          chatRoomId: chatRoomId,
          updateTime: new Date()
        }
      });

      console.log('✅ 订单聊天室ID更新成功:', { orderId, chatRoomId });

      // 发送系统欢迎消息
      try {
        const welcomeMessage = {
          chatRoomId: chatRoomId,
          senderId: 'system',
          senderInfo: {
            nickName: '系统',
            avatarUrl: ''
          },
          content: `🎉 接单成功！\n订单：${order.title}\n请双方友好沟通，祝您服务愉快！`,
          type: 'system',
          createTime: new Date(),
          isRead: false
        };

        await db.collection('messages').add({
          data: welcomeMessage
        });

        // 更新聊天室的最后消息
        await db.collection('chatRooms').doc(chatRoomId).update({
          data: {
            lastMessage: {
              content: welcomeMessage.content,
              type: 'system',
              senderId: 'system'
            },
            lastMessageTime: new Date(),
            updateTime: new Date()
          }
        });

        console.log('✅ 欢迎消息发送成功');
      } catch (messageError) {
        console.log('❌ 发送欢迎消息失败:', messageError);
        // 欢迎消息失败不影响接单流程
      }

    } catch (chatRoomError) {
      console.log('❌ 创建聊天室失败:', chatRoomError);
      // 聊天室创建失败不影响接单成功
    }

    return {
      success: true,
      message: '接单成功',
      data: {
        orderId: orderId,
        accepterId: user._id,
        acceptTime: new Date().toISOString(),
        chatRoomId: chatRoomId
      }
    };

  } catch (error) {
    console.error('❌ [抢单失败] 错误详情:', {
      message: error.message,
      code: error.code,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    // 处理不同类型的错误
    let errorMessage = '接单失败';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.message) {
      // 处理标准化的业务逻辑错误代码
      switch (error.message) {
        case 'GRAB_ERROR_ORDER_NOT_FOUND':
          errorMessage = '订单不存在';
          errorCode = 'ORDER_NOT_FOUND';
          break;
        case 'GRAB_ERROR_ORDER_ALREADY_TAKEN':
          errorMessage = '订单已被其他用户接单';
          errorCode = 'ORDER_ALREADY_TAKEN';
          break;
        case 'GRAB_ERROR_ORDER_CANCELLED':
          errorMessage = '订单已取消';
          errorCode = 'ORDER_CANCELLED';
          break;
        case 'GRAB_ERROR_ORDER_COMPLETED':
          errorMessage = '订单已完成';
          errorCode = 'ORDER_COMPLETED';
          break;
        case 'GRAB_ERROR_INVALID_STATUS':
          errorMessage = '订单状态不允许接单';
          errorCode = 'INVALID_STATUS';
          break;
        case 'GRAB_ERROR_OWN_ORDER':
          errorMessage = '不能接受自己发布的订单';
          errorCode = 'OWN_ORDER';
          break;
        case 'GRAB_ERROR_CONCURRENT_CONFLICT':
          errorMessage = '订单已被其他用户抢走';
          errorCode = 'CONCURRENT_CONFLICT';
          break;
        case 'GRAB_ERROR_MAX_RETRIES_EXCEEDED':
          errorMessage = '订单抢夺失败，请重试';
          errorCode = 'MAX_RETRIES_EXCEEDED';
          break;
        case 'GRAB_ERROR_CONCURRENT_UPDATE_FAILED':
          errorMessage = '订单已被其他用户抢走';
          errorCode = 'CONCURRENT_UPDATE_FAILED';
          break;
        case 'GRAB_ERROR_VERIFICATION_FAILED':
          errorMessage = '订单验证失败，可能已被其他用户抢走';
          errorCode = 'VERIFICATION_FAILED';
          break;
        default:
          // 处理旧版本的错误消息（向后兼容）
          if (error.message.includes('订单不存在')) {
            errorMessage = '订单不存在';
            errorCode = 'ORDER_NOT_FOUND';
          } else if (error.message.includes('订单状态不允许接单')) {
            errorMessage = '订单状态不允许接单';
            errorCode = 'INVALID_STATUS';
          } else if (error.message.includes('不能接受自己发布的订单')) {
            errorMessage = '不能接受自己发布的订单';
            errorCode = 'OWN_ORDER';
          } else if (error.message.includes('订单已被其他用户接单')) {
            errorMessage = '订单已被其他用户接单';
            errorCode = 'ORDER_ALREADY_TAKEN';
          } else if (error.message.includes('用户不存在')) {
            errorMessage = '用户不存在';
            errorCode = 'USER_NOT_FOUND';
          } else {
            errorMessage = error.message;
            errorCode = 'BUSINESS_ERROR';
          }
          break;
      }
    }

    // 处理数据库事务冲突错误
    if (error.code && error.code === 'DATABASE_TRANSACTION_CONFLICT') {
      console.log('⚠️ [事务冲突] 检测到并发抢单冲突，这是正常的并发保护机制');
      errorMessage = '订单已被其他用户接单';
      errorCode = 'CONCURRENT_CONFLICT';
    }

    // 处理其他数据库错误
    if (error.code && error.code.includes('DATABASE')) {
      console.log('⚠️ [数据库错误]:', error.code);
      errorMessage = '订单已被其他用户接单';
      errorCode = 'DATABASE_ERROR';
    }

    console.log('📤 [错误返回] 最终错误信息:', {
      errorMessage,
      errorCode,
      originalError: error.message
    });

    return {
      success: false,
      error: errorMessage,
      errorCode: errorCode
    };
  }
};
