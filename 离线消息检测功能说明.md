# 离线消息检测功能修复说明

## 问题描述
当用户不在小程序内时收到消息，后续进入小程序时不会显示有新消息的问题。

## 问题原因分析
1. **全局聊天监听器在小程序隐藏时被关闭**：在 `app.js` 的 `onHide()` 方法中，全局聊天监听器被关闭，导致用户离线时无法接收到新消息的实时更新。

2. **未读消息状态没有在小程序重新显示时同步**：虽然有未读消息管理机制，但在小程序重新显示时没有主动同步服务器上的最新消息状态。

3. **缺少进入小程序时的消息状态检查**：在 `app.js` 的 `onShow()` 方法中，虽然重新启动了监听器，但没有检查在离线期间是否有新消息。

## 解决方案

### 1. 修改 app.js
- **添加离线时间记录**：在 `onHide()` 方法中记录小程序隐藏时间
- **添加离线消息检查**：在 `onShow()` 方法中调用 `checkOfflineMessages()` 方法
- **实现离线消息检查逻辑**：新增 `checkOfflineMessages()` 方法，调用云函数检查离线期间的新消息

### 2. 扩展云函数 chatMessage
- **新增 checkOfflineMessages action**：在云函数中添加检查离线消息的功能
- **实现离线消息查询逻辑**：查找用户参与的聊天室中在指定时间之后的新消息

### 3. 修改聊天列表页面
- **添加离线消息事件监听**：监听 `offlineMessagesDetected` 事件
- **自动刷新聊天列表**：当检测到离线消息时，自动刷新聊天列表和未读状态
- **显示新消息提示**：向用户显示收到新消息的提示

## 修改文件清单

### app.js
1. 在 `globalData` 中添加 `lastHideTime` 字段
2. 在 `onHide()` 方法中记录隐藏时间
3. 在 `onShow()` 方法中添加离线消息检查
4. 新增 `checkOfflineMessages()` 方法

### cloudfunctions/chatMessage/index.js
1. 新增 `checkOfflineMessages()` 函数
2. 在主函数中添加 `checkOfflineMessages` action 处理

### pages/chat/list/list.js
1. 在 `onLoad()` 中添加离线消息监听初始化
2. 新增 `setupOfflineMessageListener()` 方法
3. 新增 `cleanupOfflineMessageListener()` 方法
4. 在 `onUnload()` 中添加监听清理

## 功能流程

1. **用户离开小程序**：
   - 记录离开时间到 `globalData.lastHideTime`
   - 关闭实时监听器以节省资源

2. **用户重新进入小程序**：
   - 重新启动实时监听器
   - 调用 `checkOfflineMessages()` 检查离线期间的新消息

3. **离线消息检查**：
   - 调用云函数查询离线期间的新消息
   - 更新未读消息状态
   - 触发 `offlineMessagesDetected` 事件

4. **聊天列表响应**：
   - 监听到离线消息事件
   - 刷新聊天列表显示
   - 更新未读状态
   - 显示新消息提示

## 技术特点

1. **性能优化**：只在小程序重新显示时检查离线消息，避免频繁查询
2. **准确性**：基于时间戳精确查询离线期间的新消息
3. **用户体验**：自动更新界面并提供友好的新消息提示
4. **资源管理**：合理管理监听器的启动和停止，避免资源浪费

## 注意事项

1. **时区一致性**：确保客户端和服务器的时间处理一致
2. **权限验证**：云函数会验证用户权限，只返回用户有权查看的消息
3. **消息去重**：避免重复计算未读消息数量
4. **错误处理**：包含完整的错误处理机制，确保功能稳定性

## 测试建议

1. **基本功能测试**：
   - 用户A发送消息给用户B
   - 用户B离开小程序
   - 用户B重新进入小程序，检查是否显示新消息

2. **边界情况测试**：
   - 离线期间收到多条消息
   - 离线期间没有新消息
   - 网络异常情况下的处理

3. **性能测试**：
   - 大量离线消息的处理性能
   - 频繁进出小程序的性能影响
