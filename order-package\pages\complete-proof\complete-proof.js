const app = getApp();
const API = require('../../../utils/api');

Page({
  data: {
    orderId: '',
    orderInfo: null,
    
    // 上传的证明材料
    proofImages: [], // 截图列表
    proofVideo: null, // 录屏视频
    description: '', // 文字说明
    
    // 上传状态
    uploading: false,
    uploadProgress: 0,
    
    // 提交状态
    submitting: false,

    // 是否可以提交
    canSubmitOrder: false,

    // 最大上传数量
    maxImages: 6,
    maxVideoSize: 100 * 1024 * 1024, // 100MB
  },

  onLoad(options) {
    console.log('完成证明页面加载:', options);
    
    if (options.orderId) {
      this.setData({
        orderId: options.orderId
      });
      this.loadOrderInfo();
      this.updateCanSubmit();
    } else {
      wx.showToast({
        title: '订单信息错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单信息
  async loadOrderInfo() {
    try {
      const result = await API.getOrderDetail(this.data.orderId);
      if (result.success && result.data) {
        console.log('=== 完成证明页面订单信息 ===');
        console.log('订单ID:', this.data.orderId);
        console.log('订单状态:', result.data.status);
        console.log('订单数据:', JSON.stringify(result.data, null, 2));
        console.log('=== 完成证明页面订单信息结束 ===');

        this.setData({
          orderInfo: result.data
        });
      } else {
        wx.showToast({
          title: '加载订单信息失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('加载订单信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 选择图片
  chooseImages() {
    const { proofImages, maxImages } = this.data;
    const remainingCount = maxImages - proofImages.length;
    
    if (remainingCount <= 0) {
      wx.showToast({
        title: `最多只能上传${maxImages}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        this.uploadImages(res.tempFiles);
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        });
      }
    });
  },

  // 选择视频
  chooseVideo() {
    if (this.data.proofVideo) {
      wx.showModal({
        title: '提示',
        content: '已有录屏视频，是否替换？',
        success: (res) => {
          if (res.confirm) {
            this.selectVideo();
          }
        }
      });
    } else {
      this.selectVideo();
    }
  },

  selectVideo() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['video'],
      sourceType: ['album', 'camera'],
      maxDuration: 300, // 5分钟
      camera: 'back',
      success: (res) => {
        const video = res.tempFiles[0];
        
        // 检查文件大小
        if (video.size > this.data.maxVideoSize) {
          wx.showToast({
            title: '视频文件过大，请选择小于100MB的视频',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        
        this.uploadVideo(video);
      },
      fail: (error) => {
        console.error('选择视频失败:', error);
        wx.showToast({
          title: '选择视频失败',
          icon: 'error'
        });
      }
    });
  },

  // 上传图片
  async uploadImages(tempFiles) {
    this.setData({ uploading: true, uploadProgress: 0 });
    
    try {
      const uploadPromises = tempFiles.map(async (file, index) => {
        const cloudPath = `order-proof/${this.data.orderId}/images/${Date.now()}_${index}.jpg`;
        
        const uploadResult = await API.uploadFile(file.tempFilePath, cloudPath);
        
        return {
          fileID: uploadResult.fileID,
          tempFilePath: file.tempFilePath,
          size: file.size,
          uploadTime: new Date()
        };
      });

      const uploadedImages = await Promise.all(uploadPromises);
      
      this.setData({
        proofImages: [...this.data.proofImages, ...uploadedImages],
        uploading: false
      });

      this.updateCanSubmit();
      
      wx.showToast({
        title: '图片上传成功',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('上传图片失败:', error);
      this.setData({ uploading: false });
      wx.showToast({
        title: '上传失败，请重试',
        icon: 'error'
      });
    }
  },

  // 上传视频
  async uploadVideo(videoFile) {
    this.setData({ uploading: true, uploadProgress: 0 });
    
    try {
      const cloudPath = `order-proof/${this.data.orderId}/video/${Date.now()}.mp4`;
      
      const uploadResult = await API.uploadFile(videoFile.tempFilePath, cloudPath);
      
      this.setData({
        proofVideo: {
          fileID: uploadResult.fileID,
          tempFilePath: videoFile.tempFilePath,
          size: videoFile.size,
          duration: videoFile.duration,
          uploadTime: new Date(),
          sizeText: (videoFile.size / 1024 / 1024).toFixed(1) + 'MB',
          durationText: Math.floor(videoFile.duration || 0) + '秒'
        },
        uploading: false
      });

      this.updateCanSubmit();

      wx.showToast({
        title: '视频上传成功',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('上传视频失败:', error);
      this.setData({ uploading: false });
      wx.showToast({
        title: '上传失败，请重试',
        icon: 'error'
      });
    }
  },

  // 删除图片
  deleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const proofImages = [...this.data.proofImages];
    proofImages.splice(index, 1);
    this.setData({ proofImages });
    this.updateCanSubmit();
  },

  // 删除视频
  deleteVideo() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个视频吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ proofVideo: null });
          this.updateCanSubmit();
        }
      }
    });
  },

  // 预览图片
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const urls = this.data.proofImages.map(img => img.tempFilePath);
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  // 输入文字说明
  onDescriptionInput(e) {
    this.setData({
      description: e.detail.value
    });
    this.updateCanSubmit();
  },

  // 更新提交状态
  updateCanSubmit() {
    const canSubmit = this.canSubmit();
    this.setData({
      canSubmitOrder: canSubmit
    });
  },

  // 检查是否可以提交
  canSubmit() {
    const { proofImages, proofVideo, description } = this.data;

    // 至少需要一张图片或一个视频
    const hasProof = proofImages.length > 0 || proofVideo;

    // 文字说明不是必须的，但建议填写
    return hasProof;
  },

  // 提交完成证明
  async submitProof() {
    if (!this.canSubmit()) {
      wx.showToast({
        title: '请至少上传一张截图或录屏视频',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (this.data.submitting) {
      return;
    }

    wx.showModal({
      title: '确认提交',
      content: '确认任务已完成并提交证明材料吗？提交后将无法修改。',
      success: async (res) => {
        if (res.confirm) {
          await this.doSubmitProof();
        }
      }
    });
  },

  async doSubmitProof() {
    this.setData({ submitting: true });

    try {
      // 检查订单状态
      if (!this.data.orderInfo) {
        wx.showToast({
          title: '订单信息不存在',
          icon: 'error'
        });
        this.setData({ submitting: false });
        return;
      }

      if (this.data.orderInfo.status !== 'in_progress') {
        wx.showToast({
          title: `订单状态不正确，当前状态：${this.data.orderInfo.status}`,
          icon: 'none'
        });
        this.setData({ submitting: false });
        return;
      }

      // 验证必填项
      if (this.data.proofImages.length === 0 && !this.data.proofVideo) {
        wx.showToast({
          title: '请至少上传一张图片或视频',
          icon: 'none'
        });
        this.setData({ submitting: false });
        return;
      }

      if (!this.data.description.trim()) {
        wx.showToast({
          title: '请填写完成说明',
          icon: 'none'
        });
        this.setData({ submitting: false });
        return;
      }

      app.utils.showLoading('提交中...');

      // 准备证明材料数据
      const proofData = {
        images: this.data.proofImages.map(img => ({
          fileID: img.fileID,
          size: img.size,
          uploadTime: img.uploadTime
        })),
        video: this.data.proofVideo ? {
          fileID: this.data.proofVideo.fileID,
          size: this.data.proofVideo.size,
          duration: this.data.proofVideo.duration,
          uploadTime: this.data.proofVideo.uploadTime
        } : null,
        description: this.data.description.trim(),
        submitTime: new Date()
      };

      // 调用API提交完成证明
      console.log('提交完成证明:', {
        orderId: this.data.orderId,
        currentOrderStatus: this.data.orderInfo?.status,
        proofData: proofData
      });

      const result = await API.submitOrderProof(this.data.orderId, proofData);

      console.log('提交完成证明结果:', result);

      if (result.success) {
        app.utils.showSuccess('证明提交成功，等待客户确认');

        // 延迟返回到订单详情页
        setTimeout(() => {
          wx.navigateBack({
            delta: 1
          });
        }, 1500);
      } else {
        app.utils.showError(result.error || '提交失败');
      }
      
    } catch (error) {
      console.error('提交完成证明失败:', error);
      app.utils.showError('提交失败，请重试');
    } finally {
      this.setData({ submitting: false });
      app.utils.hideLoading();
    }
  }
});
