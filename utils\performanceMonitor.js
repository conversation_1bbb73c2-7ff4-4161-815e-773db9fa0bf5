/**
 * 性能监控工具
 * 用于监控消息发送性能和用户体验指标
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map(); // 性能指标存储
    this.thresholds = {
      messageSend: 2000, // 消息发送阈值 2秒
      messageLoad: 1500, // 消息加载阈值 1.5秒
      permissionCheck: 500, // 权限检查阈值 0.5秒
      dbQuery: 1000 // 数据库查询阈值 1秒
    };
    
    console.log('📊 [性能监控] 性能监控器初始化完成');
  }

  /**
   * 开始性能计时
   */
  startTimer(operationId, operationType, metadata = {}) {
    const timer = {
      id: operationId,
      type: operationType,
      startTime: Date.now(),
      metadata: metadata
    };
    
    this.metrics.set(operationId, timer);
    console.log(`⏱️ [性能监控] 开始计时: ${operationType} (${operationId})`);
    
    return operationId;
  }

  /**
   * 结束性能计时
   */
  endTimer(operationId, success = true, errorMessage = null) {
    const timer = this.metrics.get(operationId);
    if (!timer) {
      console.warn(`⏱️ [性能监控] 未找到计时器: ${operationId}`);
      return null;
    }

    const endTime = Date.now();
    const duration = endTime - timer.startTime;
    
    const result = {
      ...timer,
      endTime: endTime,
      duration: duration,
      success: success,
      errorMessage: errorMessage
    };

    // 检查是否超过阈值
    const threshold = this.thresholds[timer.type] || 3000;
    const isSlowOperation = duration > threshold;
    
    if (isSlowOperation) {
      console.warn(`🐌 [性能警告] ${timer.type} 操作耗时过长: ${duration}ms (阈值: ${threshold}ms)`);
    } else {
      console.log(`⚡ [性能监控] ${timer.type} 操作完成: ${duration}ms`);
    }

    // 记录性能数据
    this.recordMetric(result);
    
    // 清除计时器
    this.metrics.delete(operationId);
    
    return result;
  }

  /**
   * 记录性能指标
   */
  recordMetric(metric) {
    try {
      // 这里可以将性能数据发送到分析服务
      // 或者存储到本地进行分析
      
      // 简单的本地存储示例
      const storageKey = `perf_${metric.type}_${new Date().toDateString()}`;
      let dailyMetrics = wx.getStorageSync(storageKey) || [];
      
      dailyMetrics.push({
        timestamp: metric.endTime,
        duration: metric.duration,
        success: metric.success,
        metadata: metric.metadata
      });
      
      // 限制每日存储的指标数量
      if (dailyMetrics.length > 100) {
        dailyMetrics = dailyMetrics.slice(-100);
      }
      
      wx.setStorageSync(storageKey, dailyMetrics);
      
    } catch (error) {
      console.error('📊 [性能监控] 记录指标失败:', error);
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(operationType, days = 1) {
    try {
      const stats = {
        totalOperations: 0,
        successfulOperations: 0,
        failedOperations: 0,
        averageDuration: 0,
        maxDuration: 0,
        minDuration: Infinity,
        slowOperations: 0
      };

      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const storageKey = `perf_${operationType}_${date.toDateString()}`;
        const dailyMetrics = wx.getStorageSync(storageKey) || [];

        dailyMetrics.forEach(metric => {
          stats.totalOperations++;
          
          if (metric.success) {
            stats.successfulOperations++;
          } else {
            stats.failedOperations++;
          }

          stats.maxDuration = Math.max(stats.maxDuration, metric.duration);
          stats.minDuration = Math.min(stats.minDuration, metric.duration);
          
          const threshold = this.thresholds[operationType] || 3000;
          if (metric.duration > threshold) {
            stats.slowOperations++;
          }
        });
      }

      // 计算平均值
      if (stats.totalOperations > 0) {
        const totalDuration = stats.totalOperations * stats.averageDuration;
        stats.averageDuration = Math.round(totalDuration / stats.totalOperations);
      }

      if (stats.minDuration === Infinity) {
        stats.minDuration = 0;
      }

      return stats;
    } catch (error) {
      console.error('📊 [性能监控] 获取统计失败:', error);
      return null;
    }
  }

  /**
   * 消息发送性能监控
   */
  monitorMessageSend(chatRoomId, messageType = 'text') {
    const operationId = `msg_send_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return this.startTimer(operationId, 'messageSend', {
      chatRoomId: chatRoomId,
      messageType: messageType
    });
  }

  /**
   * 消息加载性能监控
   */
  monitorMessageLoad(chatRoomId, page, pageSize) {
    const operationId = `msg_load_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return this.startTimer(operationId, 'messageLoad', {
      chatRoomId: chatRoomId,
      page: page,
      pageSize: pageSize
    });
  }

  /**
   * 权限检查性能监控
   */
  monitorPermissionCheck(chatRoomId, userId) {
    const operationId = `perm_check_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return this.startTimer(operationId, 'permissionCheck', {
      chatRoomId: chatRoomId,
      userId: userId
    });
  }

  /**
   * 数据库查询性能监控
   */
  monitorDbQuery(collection, operation) {
    const operationId = `db_query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return this.startTimer(operationId, 'dbQuery', {
      collection: collection,
      operation: operation
    });
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      messageSend: this.getPerformanceStats('messageSend', 7),
      messageLoad: this.getPerformanceStats('messageLoad', 7),
      permissionCheck: this.getPerformanceStats('permissionCheck', 7),
      dbQuery: this.getPerformanceStats('dbQuery', 7)
    };

    console.log('📊 [性能报告] 7天性能统计:', report);
    return report;
  }

  /**
   * 清理过期的性能数据
   */
  cleanupOldMetrics(daysToKeep = 7) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      // 获取所有存储的键
      const info = wx.getStorageInfoSync();
      const keysToRemove = info.keys.filter(key => {
        if (!key.startsWith('perf_')) return false;
        
        // 提取日期部分
        const datePart = key.split('_').slice(2).join('_');
        const metricDate = new Date(datePart);
        
        return metricDate < cutoffDate;
      });

      // 删除过期数据
      keysToRemove.forEach(key => {
        wx.removeStorageSync(key);
      });

      console.log(`🗑️ [性能监控] 已清理 ${keysToRemove.length} 个过期指标`);
    } catch (error) {
      console.error('🗑️ [性能监控] 清理过期数据失败:', error);
    }
  }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor();

// 导出监控实例
module.exports = performanceMonitor;
