/**
 * 应用内通知组件
 * 用于显示聊天消息、订单状态等通知
 */
Component({
  properties: {
    // 是否显示通知
    show: {
      type: Boolean,
      value: false
    },
    // 通知类型
    type: {
      type: String,
      value: 'message' // message, order, system
    },
    // 通知标题
    title: {
      type: String,
      value: ''
    },
    // 通知内容
    content: {
      type: String,
      value: ''
    },
    // 发送者头像
    avatar: {
      type: String,
      value: ''
    },
    // 自动隐藏时间（毫秒）
    duration: {
      type: Number,
      value: 1500 // 1.5秒，快速显示
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: true
    },
    // 聊天室ID（用于跳转）
    chatRoomId: {
      type: String,
      value: ''
    },
    // 消息数据（用于跳转）
    messageData: {
      type: Object,
      value: null
    }
  },

  data: {
    visible: false,
    animationClass: '',
    progressDuration: 1500, // 进度条动画时长，与duration保持一致
    progressPaused: false // 进度条是否暂停
  },



  observers: {
    'show': function(show) {
      if (show && !this.data.visible) {
        // 只有在show为true且当前不可见时才显示
        this.showNotification();
      } else if (!show && this.data.visible) {
        // 只有在show为false且当前可见时才隐藏
        this.hideNotification();
      }
    },
    'duration': function(duration) {
      // 当duration变化时，更新进度条动画时长
      this.setData({
        progressDuration: duration
      });
    }
  },

  methods: {
    // 显示通知
    showNotification() {
      this.setData({
        visible: true,
        animationClass: 'slide-in',
        progressDuration: this.data.duration,
        progressPaused: false
      });

      // 自动隐藏
      if (this.data.duration > 0) {
        this.autoHideTimer = setTimeout(() => {
          this.hideNotification();
        }, this.data.duration);
      }
    },

    // 隐藏通知
    hideNotification() {
      // 如果已经在隐藏过程中，避免重复执行
      if (!this.data.visible) {
        return;
      }

      this.setData({
        animationClass: 'slide-out'
      });

      // 清除定时器
      if (this.autoHideTimer) {
        clearTimeout(this.autoHideTimer);
        this.autoHideTimer = null;
      }

      // 动画结束后隐藏
      setTimeout(() => {
        this.setData({
          visible: false,
          animationClass: ''
        });
      }, 300);
    },

    // 点击通知
    onNotificationTap() {
      if (this.data.clickable) {
        this.triggerEvent('tap', {
          type: this.data.type,
          title: this.data.title,
          content: this.data.content,
          chatRoomId: this.data.chatRoomId,
          messageData: this.data.messageData
        });
      }
    },

    // 点击关闭按钮
    onCloseTap() {
      this.hideNotification();
    },

    // 暂停自动隐藏和进度条
    pauseAutoHide() {
      if (this.autoHideTimer) {
        clearTimeout(this.autoHideTimer);
        this.autoHideTimer = null;
      }
      this.setData({
        progressPaused: true
      });
    },

    // 恢复自动隐藏和进度条
    resumeAutoHide() {
      if (this.data.visible && this.data.duration > 0 && !this.autoHideTimer) {
        // 重新启动自动隐藏定时器
        this.autoHideTimer = setTimeout(() => {
          this.hideNotification();
        }, this.data.duration);

        this.setData({
          progressPaused: false
        });
      }
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化时同步进度条时长
      this.setData({
        progressDuration: this.data.duration
      });
    },
    detached() {
      // 组件销毁时清除定时器
      if (this.autoHideTimer) {
        clearTimeout(this.autoHideTimer);
        this.autoHideTimer = null;
      }
    }
  }
});
