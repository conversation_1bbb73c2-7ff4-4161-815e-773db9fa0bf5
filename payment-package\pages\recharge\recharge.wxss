/* 充值页面样式 - 科技感主题 */
.recharge-container {
  min-height: 100vh;
  background: transparent;
  padding: var(--space-lg);
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.recharge-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + var(--space-lg));
}

/* 余额卡片 - 科技感样式 */
.balance-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0.1;
  z-index: -1;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.balance-title {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
  opacity: 0.9;
}

.history-btn {
  font-size: 24rpx;
  padding: var(--space-sm) var(--space-md);
  background: rgba(0, 212, 255, 0.1);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.history-btn:active {
  background: rgba(0, 212, 255, 0.2);
  color: var(--primary-color);
}

.balance-amount {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 36rpx;
  margin-right: var(--space-xs);
  color: var(--text-secondary);
  opacity: 0.8;
}

.amount {
  font-size: 64rpx;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 充值金额选择 - 科技感样式 */
.amount-section {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-md);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  letter-spacing: 0.5rpx;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.amount-item {
  position: relative;
  background: rgba(0, 212, 255, 0.05);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
  border-radius: var(--radius-md);
  padding: var(--space-lg) var(--space-md);
  text-align: center;
  transition: all 0.3s ease;
  overflow: hidden;
}

.amount-item.selected {
  background: rgba(0, 212, 255, 0.1);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-glow);
}

.amount-item:active {
  transform: scale(0.95);
}

.amount-label {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.popular-tag {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 自定义金额 */
.custom-amount {
  border-top: 2rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.custom-amount-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.custom-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
}

.amount-range {
  font-size: 24rpx;
  color: #999;
}

.custom-input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
}

.currency-symbol {
  font-size: 28rpx;
  color: #666;
  margin-right: 12rpx;
}

.custom-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 支付方式 - 科技感样式 */
.payment-section {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-md);
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg);
  background: rgba(0, 212, 255, 0.05);
  border: 1rpx solid rgba(0, 212, 255, 0.1);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.payment-item.selected {
  background: rgba(0, 212, 255, 0.1);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-glow);
}

.payment-item:active {
  transform: translateX(4rpx);
}

.payment-info {
  display: flex;
  align-items: center;
}

.payment-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.payment-name {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.payment-check {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #d9d9d9;
  color: white;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.check-icon.checked {
  background: #1890ff;
}

/* 充值说明 */
.notice-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 确认充值按钮 - 科技感样式 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  padding: var(--space-lg);
  border-top: 1rpx solid var(--border-light);
  box-shadow: var(--shadow-xl);
}

.amount-summary {
  text-align: center;
  margin-bottom: var(--space-md);
}

.summary-text {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
}

.recharge-btn {
  width: 100%;
  background: rgba(0, 212, 255, 0.1);
  color: var(--text-disabled);
  border: 1rpx solid rgba(0, 212, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.recharge-btn.active {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
  box-shadow: var(--shadow-glow);
}

.recharge-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-md);
}

.recharge-btn[disabled] {
  background: rgba(0, 212, 255, 0.05);
  color: var(--text-disabled);
  border-color: rgba(0, 212, 255, 0.1);
}
