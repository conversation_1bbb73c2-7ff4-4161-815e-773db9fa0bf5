// 清理过期订单锁的云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  
  try {
    const now = new Date();

    // 查找所有过期的锁
    const expiredLocks = await db.collection('order_locks')
      .where({
        expireTime: _.lt(now)
      })
      .get();
    
    if (expiredLocks.data.length === 0) {
      return {
        success: true,
        message: '没有过期的锁需要清理',
        cleanedCount: 0
      };
    }
    
    // 批量删除过期锁
    let cleanedCount = 0;
    for (const lock of expiredLocks.data) {
      try {
        await db.collection('order_locks').doc(lock._id).remove();
        cleanedCount++;
      } catch (deleteError) {
        console.error('清理锁失败:', lock._id, deleteError.message);
      }
    }
    
    return {
      success: true,
      message: `成功清理${cleanedCount}个过期锁`,
      cleanedCount: cleanedCount,
      totalFound: expiredLocks.data.length
    };
    
  } catch (error) {
    console.error('❌ 清理过期锁失败:', error);
    
    return {
      success: false,
      error: error.message || '清理失败',
      errorCode: error.errCode
    };
  }
};
