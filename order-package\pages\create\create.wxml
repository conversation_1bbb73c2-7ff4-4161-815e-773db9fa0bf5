<!--create.wxml-->
<navigation-bar title="{{editMode ? '修改订单' : '创建订单'}}" back="{{false}}"></navigation-bar>

<!-- 返回按钮 -->
<back-button position="auto-position" size="normal" back-type="auto"></back-button>

<view class="create-container page-with-custom-nav">
  <!-- 订单信息表单 -->
  <view class="order-form">
    <!-- 订单类型选择 -->
    <view class="form-section">
      <view class="section-title">🎯 订单类型</view>
      <view class="order-type-tabs">
        <view class="type-tab {{formData.orderType === 'immediate' ? 'active' : ''}}"
              bindtap="switchOrderType"
              data-type="immediate">
          <view class="tab-icon">⚡</view>
          <view class="tab-text">立即订单</view>
          <view class="tab-desc">立即开始服务</view>
        </view>
        <view class="type-tab {{formData.orderType === 'scheduled' ? 'active' : ''}}"
              bindtap="switchOrderType"
              data-type="scheduled">
          <view class="tab-icon">📅</view>
          <view class="tab-text">预约订单</view>
          <view class="tab-desc">指定时间履约</view>
        </view>
      </view>
    </view>

    <!-- 预约时间设置 -->
    <view class="form-section" wx:if="{{formData.orderType === 'scheduled'}}">
      <view class="section-title">⏰ 预约时间</view>

      <!-- 日期选择 -->
      <view class="schedule-item">
        <view class="schedule-label">📅 预约日期</view>
        <picker mode="date"
                value="{{formData.scheduledDate}}"
                start="{{minDate}}"
                end="{{maxDate}}"
                bindchange="onDateChange">
          <view class="schedule-value {{formData.scheduledDate ? '' : 'placeholder'}}">
            {{formData.scheduledDate || '请选择日期'}}
          </view>
        </picker>
      </view>

      <!-- 时间选择 -->
      <view class="schedule-item" wx:if="{{formData.scheduledDate}}">
        <view class="schedule-label">🕐 预约时间</view>
        <view class="time-picker-container">
          <picker mode="selector"
                  range="{{hourSlots}}"
                  range-key="label"
                  value="{{formData.scheduledHour}}"
                  bindchange="onHourChange"
                  class="time-picker">
            <view class="schedule-value">
              {{hourSlots[formData.scheduledHour] ? hourSlots[formData.scheduledHour].label : '00时'}}
            </view>
          </picker>
          <picker mode="selector"
                  range="{{minuteSlots}}"
                  range-key="label"
                  value="{{formData.scheduledMinute}}"
                  bindchange="onMinuteChange"
                  class="time-picker">
            <view class="schedule-value">
              {{minuteSlots[formData.scheduledMinute] ? minuteSlots[formData.scheduledMinute].label : '00分'}}
            </view>
          </picker>
        </view>
      </view>

      <!-- 预约提示 -->
      <view class="schedule-tip">
        <text class="tip-icon">💡</text>
        <text class="tip-text">预约订单将在指定时间开始服务，请确保时间安排合理</text>
      </view>
    </view>

    <!-- 订单标题 -->
    <view class="form-section">
      <view class="section-title">📝 订单标题</view>
      <input class="title-input"
             placeholder="请输入订单标题"
             placeholder-class="title-input-placeholder"
             value="{{formData.title}}"
             bindinput="onTitleInput"
             maxlength="30" />
      <view class="char-count">{{formData.title.length}}/30</view>
    </view>

    <!-- 任务内容 -->
    <view class="form-section">
      <view class="section-title">📋 任务内容</view>
      <textarea class="content-input"
                placeholder="详细描述您的需求，如：希望技术好一点，有耐心指导新手，主要玩烽火地带模式..."
                value="{{formData.content}}"
                bindinput="onContentInput"
                maxlength="200" />
      <view class="char-count">{{formData.content.length}}/200</view>
    </view>

    <!-- 平台类型 -->
    <view class="form-section">
      <view class="section-title">🎮 平台类型</view>
      <view class="platform-selector-horizontal">
        <view class="platform-option-horizontal {{formData.platformType === 'pc' ? 'active' : ''}}"
              bindtap="switchPlatformType"
              data-type="pc">
          <view class="platform-icon-large">💻</view>
          <view class="platform-text-large">电脑端</view>
          <view class="platform-desc">PC游戏平台</view>
        </view>
        <view class="platform-option-horizontal {{formData.platformType === 'mobile' ? 'active' : ''}}"
              bindtap="switchPlatformType"
              data-type="mobile">
          <view class="platform-icon-large">📱</view>
          <view class="platform-text-large">手游端</view>
          <view class="platform-desc">移动游戏平台</view>
        </view>
      </view>
    </view>

    <!-- 报酬 -->
    <view class="form-section">
      <view class="section-title">💰 报酬</view>
      <view class="reward-input-container-full">
        <text class="currency-symbol">¥</text>
        <input class="reward-input-full"
               type="number"
               placeholder="请输入您的报价"
               value="{{formData.reward}}"
               bindinput="onRewardInput" />
        <text class="reward-unit">元</text>
      </view>
    </view>

    <!-- 服务时间 -->
    <view class="form-section">
      <view class="section-title">⏰ 服务时间</view>

      <!-- 服务类型选择 -->
      <view class="service-type-tabs">
        <view class="service-type-tab {{formData.serviceType === 'duration' ? 'active' : ''}}"
              bindtap="switchServiceType"
              data-type="duration">
          <text class="tab-text">按时长</text>
        </view>
        <view class="service-type-tab {{formData.serviceType === 'rounds' ? 'active' : ''}}"
              bindtap="switchServiceType"
              data-type="rounds">
          <text class="tab-text">按局数</text>
        </view>
      </view>

      <!-- 时长选择 -->
      <view class="time-grid" wx:if="{{formData.serviceType === 'duration'}}">
        <view class="time-card {{formData.duration === item.value ? 'selected' : ''}}"
              wx:for="{{durationOptions}}"
              wx:key="value"
              bindtap="selectDuration"
              data-value="{{item.value}}">
          {{item.label}}
        </view>
      </view>
      <view class="custom-duration" wx:if="{{formData.serviceType === 'duration' && formData.duration === 'custom'}}">
        <input class="duration-input"
               type="number"
               placeholder="输入小时数"
               value="{{formData.customDuration}}"
               bindinput="onCustomDurationInput" />
        <text class="duration-unit">小时</text>
      </view>

      <!-- 局数选择 -->
      <view class="rounds-grid" wx:if="{{formData.serviceType === 'rounds'}}">
        <view class="rounds-card {{formData.rounds === item.value ? 'selected' : ''}}"
              wx:for="{{roundsOptions}}"
              wx:key="value"
              bindtap="selectRounds"
              data-value="{{item.value}}">
          {{item.label}}
        </view>
      </view>
      <view class="custom-rounds" wx:if="{{formData.serviceType === 'rounds' && formData.rounds === 'custom'}}">
        <input class="rounds-input"
               type="number"
               placeholder="输入局数"
               value="{{formData.customRounds}}"
               bindinput="onCustomRoundsInput" />
        <text class="rounds-unit">局</text>
      </view>
    </view>

    <!-- 订单标签 -->
    <view class="form-section">
      <view class="section-title">🏷️ 订单标签</view>
      <view class="tags-container">
        <view class="tag-item {{item.selected ? 'selected' : ''}}"
              wx:for="{{orderTagsWithState}}"
              wx:key="value"
              bindtap="toggleTag"
              data-value="{{item.value}}">
          {{item.label}}
        </view>
      </view>
      <!-- 调试信息 -->
      <view class="debug-info" wx:if="{{false}}">
        <text>选中标签: {{formData.selectedTags.join(', ')}}</text>
      </view>
    </view>
  </view>

  <!-- 订单预览 -->
  <view class="preview-card" wx:if="{{formData.title || formData.reward}}">
    <view class="card-title">订单预览</view>

    <view class="preview-item">
      <text class="preview-label">类型</text>
      <text class="preview-value {{formData.orderType === 'scheduled' ? 'scheduled-type' : ''}}">
        {{formData.orderType === 'immediate' ? '⚡ 立即订单' : '📅 预约订单'}}
      </text>
    </view>

    <view class="preview-item" wx:if="{{formData.orderType === 'scheduled' && formData.scheduledDate}}">
      <text class="preview-label">预约时间</text>
      <text class="preview-value scheduled-time">
        {{formData.scheduledDate}} {{formData.scheduledTime}}
      </text>
    </view>

    <view class="preview-item" wx:if="{{formData.title}}">
      <text class="preview-label">标题</text>
      <text class="preview-value">{{formData.title}}</text>
    </view>

    <view class="preview-item" wx:if="{{formData.reward}}">
      <text class="preview-label">报酬</text>
      <text class="preview-value reward-highlight">¥{{formData.reward}}</text>
    </view>

    <view class="preview-item" wx:if="{{formData.duration !== 'custom' || formData.customDuration}}">
      <text class="preview-label">时长</text>
      <text class="preview-value">
        {{formData.duration === 'custom' ? formData.customDuration : formData.duration}}小时
      </text>
    </view>

    <view class="preview-tags" wx:if="{{formData.selectedTags.length > 0}}">
      <text class="preview-label">标签</text>
      <view class="preview-tag-list">
        <text class="preview-tag" wx:for="{{selectedTagsDisplay}}" wx:key="value">
          {{item.label}}
        </text>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn"
            bindtap="submitOrder"
            disabled="{{!canSubmit || submitting}}">
      {{submitting ? (editMode ? '修改中...' : '提交中...') : (editMode ? '确认修改' : (formData.orderType === 'immediate' ? '确认下单' : '确认预约'))}}
    </button>

  </view>
</view>
