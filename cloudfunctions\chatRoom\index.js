/**
 * 聊天室管理云函数
 * 功能：创建聊天室、查询聊天室信息
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 工具函数：统一的订单查询
async function findOrderByAnyId(orderId) {
  // 方式1：按 orderNo 查询（主要方式）
  let result = await db.collection('orders').where({ orderNo: orderId }).get();
  if (result.data.length > 0) {
    return result.data[0];
  }

  // 方式2：按 _id 查询（兼容方式）
  try {
    result = await db.collection('orders').doc(orderId).get();
    if (result.data) {
      return result.data;
    }
  } catch (error) {
    // 查询失败，继续尝试其他方式
  }

  return null;
}

// 工具函数：获取接单者ID
function getAccepterId(order) {
  return order.companionId || order.accepterId || null;
}

// 工具函数：验证用户权限
function validateUserPermission(order, userId) {
  const accepterId = getAccepterId(order);
  const isCustomer = order.customerId === userId;
  const isAccepter = accepterId === userId;
  
  return {
    isCustomer,
    isAccepter,
    hasPermission: isCustomer || isAccepter,
    userRole: isCustomer ? 'customer' : (isAccepter ? 'accepter' : null),
    accepterId
  };
}

// 工具函数：检查聊天室是否已存在
async function findExistingChatRoom(orderNo) {
  console.log('🔍 [聊天室查询] 检查聊天室是否存在:', orderNo);
  
  const result = await db.collection('chatRooms').where({
    orderNo: orderNo
  }).get();
  
  if (result.data.length > 0) {
    console.log('✅ [聊天室查询] 找到已存在的聊天室');
    return result.data[0];
  }
  
  console.log('📋 [聊天室查询] 聊天室不存在');
  return null;
}

// 工具函数：获取用户信息
async function getUserInfo(userId) {
  const result = await db.collection('users').doc(userId).get();
  if (result.data) {
    return {
      nickName: result.data.nickName || '未知用户',
      avatarUrl: result.data.avatarUrl || ''
    };
  }
  return { nickName: '未知用户', avatarUrl: '' };
}

// 创建聊天室
async function createChatRoom(order, currentUserId) {
  console.log('🏗️ [聊天室创建] 开始创建聊天室');
  
  const accepterId = getAccepterId(order);
  const orderNo = order.orderNo || order._id;
  
  // 获取用户信息
  const [customerInfo, accepterInfo] = await Promise.all([
    getUserInfo(order.customerId),
    getUserInfo(accepterId)
  ]);
  
  // 构建聊天室数据
  const chatRoomData = {
    // 关联信息
    orderNo: orderNo,
    orderDbId: order._id,
    
    // 参与用户
    customerId: order.customerId,
    accepterId: accepterId,
    
    // 用户信息快照
    customerInfo: customerInfo,
    accepterInfo: accepterInfo,
    
    // 订单信息快照
    orderInfo: {
      title: order.title || '订单',
      reward: order.reward || order.pricing?.totalAmount || 0,
      status: order.status || 'unknown'
    },
    
    // 最后消息信息
    lastMessage: null,
    
    // 聊天室状态
    status: 'active',
    
    // 时间戳
    createTime: new Date(),
    updateTime: new Date()
  };
  
  console.log('📋 [聊天室创建] 聊天室数据:', {
    orderNo: chatRoomData.orderNo,
    customerId: chatRoomData.customerId,
    accepterId: chatRoomData.accepterId,
    customerName: customerInfo.nickName,
    accepterName: accepterInfo.nickName
  });
  
  // 创建聊天室
  const result = await db.collection('chatRooms').add({
    data: chatRoomData
  });
  
  console.log('✅ [聊天室创建] 聊天室创建成功:', result._id);
  
  return {
    chatRoomId: result._id,
    chatRoomData: chatRoomData
  };
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action = 'create', orderId, chatRoomId, userId } = event;

  console.log('=== 聊天室管理云函数开始 ===');
  console.log('📋 [请求信息] 动作:', action, '订单ID:', orderId);
  console.log('📋 [用户信息] OpenID:', wxContext.OPENID);

  try {
    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在，请先登录'
      };
    }

    const user = userResult.data[0];
    console.log('✅ [用户验证] 当前用户:', user._id, user.nickName);

    // 处理不同的动作
    if (action === 'create') {
      return await handleCreateChatRoom(orderId, user);
    } else if (action === 'get') {
      return await handleGetChatRoom(orderId, user);
    } else if (action === 'checkParticipant') {
      return await handleCheckParticipant(chatRoomId, userId);
    } else {
      return {
        success: false,
        error: '不支持的操作类型'
      };
    }

  } catch (error) {
    console.error('❌ [云函数错误]:', error);
    return {
      success: false,
      error: error.message || '服务器内部错误'
    };
  }
};

// 处理创建聊天室
async function handleCreateChatRoom(orderId, user) {
  if (!orderId) {
    return {
      success: false,
      error: '订单ID不能为空'
    };
  }

  // 查找订单
  const order = await findOrderByAnyId(orderId);
  if (!order) {
    return {
      success: false,
      error: '订单不存在'
    };
  }

  // 验证用户权限
  const permission = validateUserPermission(order, user._id);
  if (!permission.hasPermission) {
    return {
      success: false,
      error: '您没有权限访问此订单的聊天室'
    };
  }

  // 检查订单状态
  if (!permission.accepterId) {
    return {
      success: false,
      error: '订单尚未被接单，无法创建聊天室'
    };
  }

  const orderNo = order.orderNo || order._id;

  // 检查聊天室是否已存在
  const existingChatRoom = await findExistingChatRoom(orderNo);
  if (existingChatRoom) {
    console.log('📋 [聊天室创建] 聊天室已存在，返回现有聊天室');
    return {
      success: true,
      message: '聊天室已存在',
      data: {
        chatRoomId: existingChatRoom._id,
        isExisting: true,
        userRole: permission.userRole
      }
    };
  }

  // 创建新聊天室
  const createResult = await createChatRoom(order, user._id);

  return {
    success: true,
    message: '聊天室创建成功',
    data: {
      chatRoomId: createResult.chatRoomId,
      isExisting: false,
      userRole: permission.userRole
    }
  };
}

// 处理获取聊天室信息
async function handleGetChatRoom(orderId, user) {
  if (!orderId) {
    return {
      success: false,
      error: '订单ID不能为空'
    };
  }

  // 查找订单
  const order = await findOrderByAnyId(orderId);
  if (!order) {
    return {
      success: false,
      error: '订单不存在'
    };
  }

  // 验证用户权限
  const permission = validateUserPermission(order, user._id);
  if (!permission.hasPermission) {
    return {
      success: false,
      error: '您没有权限访问此订单的聊天室'
    };
  }

  const orderNo = order.orderNo || order._id;

  // 查找聊天室
  const chatRoom = await findExistingChatRoom(orderNo);
  if (!chatRoom) {
    return {
      success: false,
      error: '聊天室不存在'
    };
  }

  return {
    success: true,
    data: {
      chatRoom: chatRoom,
      userRole: permission.userRole
    }
  };
}

// 处理检查聊天室参与者
async function handleCheckParticipant(chatRoomId, userId) {
  console.log('🔍 [检查参与者] 聊天室ID:', chatRoomId, '用户ID:', userId);

  try {
    // 查询聊天室信息
    const chatRoomResult = await db.collection('chatRooms').doc(chatRoomId).get();

    if (!chatRoomResult.data) {
      console.log('❌ [检查参与者] 聊天室不存在');
      return {
        success: true,
        isParticipant: false
      };
    }

    const chatRoom = chatRoomResult.data;

    // 检查用户是否是聊天室的参与者
    const isParticipant = chatRoom.customerId === userId || chatRoom.accepterId === userId;

    console.log('✅ [检查参与者] 检查结果:', {
      customerId: chatRoom.customerId,
      accepterId: chatRoom.accepterId,
      userId: userId,
      isParticipant: isParticipant
    });

    return {
      success: true,
      isParticipant: isParticipant
    };

  } catch (error) {
    console.error('❌ [检查参与者] 查询失败:', error);
    return {
      success: false,
      error: error.message,
      isParticipant: false
    };
  }
}
