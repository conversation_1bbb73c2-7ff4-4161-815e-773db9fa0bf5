/* 用户资料编辑页面样式 */

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: var(--cyber-blue);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
  box-shadow: 0 0 10rpx var(--cyber-blue);
}

.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 80%; animation-delay: 1s; }
.particle:nth-child(3) { top: 80%; left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { top: 40%; left: 70%; animation-delay: 0.5s; }
.particle:nth-child(5) { top: 10%; left: 90%; animation-delay: 1.5s; }

/* 主容器 */
.edit-container {
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
  min-height: 100vh;
  box-sizing: border-box;
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.edit-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + var(--space-lg) + 20rpx);
  /* 确保容器不会超出屏幕 */
  max-width: 100vw;
  overflow-x: hidden;
}

/* 科技感卡片 */
.cyber-card {
  position: relative;
  background: rgba(15, 23, 42, 0.8);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  backdrop-filter: blur(10rpx);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  /* 确保卡片内容不会超出边界 */
  max-width: 100%;
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.1) 0%,
    transparent 50%,
    rgba(0, 212, 255, 0.05) 100%);
  border-radius: var(--radius-lg);
  pointer-events: none;
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-lg);
  position: relative;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--cyber-blue);
  margin-right: var(--space-md);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.title-line {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(90deg, var(--cyber-blue), transparent);
}

/* 头像编辑区域 */
.avatar-edit-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-preview {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  cursor: pointer;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  /* 移除蓝色边框，保留原始头像 */
  /* border: 3rpx solid var(--cyber-blue); */
}

.avatar-image.default-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border: 2rpx solid var(--primary-color);
}

.avatar-image .avatar-text {
  font-size: 60rpx;
  color: var(--primary-color);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-preview:active .avatar-overlay {
  opacity: 1;
}

.overlay-text {
  color: #ffffff;
  font-size: 24rpx;
  text-align: center;
}

/* 完全移除头像外圈装饰，保留原始头像显示 */
/*
.avatar-ring {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  border: 2rpx solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--cyber-blue), transparent, var(--cyber-blue));
  background-clip: border-box;
  animation: rotate 3s linear infinite;
}
*/

/* 表单项 */
.form-item {
  margin-bottom: var(--space-lg);
  width: 100%;
  box-sizing: border-box;
}

.item-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-sm);
}

.label-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.required-mark {
  color: #ff4757;
  font-size: 28rpx;
  margin-left: var(--space-xs);
}

.char-count {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 输入框容器 */
.input-container,
.textarea-container,
.picker-container {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

/* 科技感输入框 */
.cyber-input,
.cyber-textarea {
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-md);
  padding-left: var(--space-md);
  padding-right: var(--space-md);
  color: #ffffff;
  font-size: 28rpx;
  transition: all 0.3s ease;
  box-sizing: border-box;
  /* 确保输入框不会超出容器 */
  max-width: 100%;
  word-wrap: break-word;
  /* 文字垂直居中 */
  vertical-align: middle;
}

.cyber-input {
  height: 80rpx;
  line-height: 80rpx; /* 设置行高等于高度，确保文字垂直居中 */
  /* 重置padding，使用line-height来控制居中 */
  padding-top: 0;
  padding-bottom: 0;
  /* 移除flex布局，避免冲突 */
  display: block;
  /* 确保文字垂直居中 */
  text-align: left;
}

.cyber-textarea {
  min-height: 120rpx;
  resize: none;
  line-height: 1.5;
  /* 确保文本区域可以正确显示多行内容 */
  vertical-align: top;
  padding-top: var(--space-md);
  padding-bottom: var(--space-md);
}

.cyber-input:focus,
.cyber-textarea:focus {
  border-color: var(--cyber-blue);
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
}

.cyber-input.error,
.cyber-textarea.error {
  border-color: #ff4757;
  box-shadow: 0 0 20rpx rgba(255, 71, 87, 0.3);
}

.cyber-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  line-height: 80rpx; /* 与输入框高度一致 */
  vertical-align: middle;
}

.cyber-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.5;
  vertical-align: top;
}

.input-glow,
.textarea-glow,
.picker-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.1) 0%,
    transparent 50%);
  border-radius: var(--radius-md);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.cyber-input:focus + .input-glow,
.cyber-textarea:focus + .textarea-glow {
  opacity: 1;
}

/* 选择器 */
.cyber-picker {
  width: 100%;
  box-sizing: border-box;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  height: 80rpx;
  transition: all 0.3s ease;
  box-sizing: border-box;
  width: 100%;
}

.picker-text {
  color: #ffffff;
  font-size: 28rpx;
  line-height: 1;
  display: flex;
  align-items: center;
}

.picker-arrow {
  color: var(--cyber-blue);
  font-size: 24rpx;
  transition: transform 0.3s ease;
}

/* 错误提示 */
.error-text {
  color: #ff4757;
  font-size: 24rpx;
  margin-top: var(--space-xs);
  display: block;
}

/* 操作按钮区域 */
.action-section {
  margin-top: var(--space-xl);
  width: 100%;
  box-sizing: border-box;
  /* 确保按钮区域不会超出屏幕 */
  max-width: 100%;
}

.action-buttons {
  display: flex;
  gap: var(--space-md);
  width: 100%;
  box-sizing: border-box;
}

.cancel-btn,
.save-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-sizing: border-box;
  /* 确保按钮不会超出容器 */
  max-width: calc(50% - var(--space-md) / 2);
  min-width: 0;
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.primary-btn {
  background: linear-gradient(135deg, var(--cyber-blue), #0066cc);
  border: 1rpx solid var(--cyber-blue);
}

.primary-btn.loading {
  opacity: 0.7;
  pointer-events: none;
}

.btn-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.secondary-glow {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%);
}

.primary-glow {
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.3) 0%,
    transparent 50%);
}

.cancel-btn:active .btn-glow,
.save-btn:active .btn-glow {
  opacity: 1;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: var(--space-lg);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 212, 255, 0.3);
  border-top: 4rpx solid var(--cyber-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-md);
}

.loading-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
}

/* 响应式设计 - 小屏幕优化 */
@media (max-width: 375px) {
  .edit-container {
    padding: var(--space-md);
  }

  .cyber-card {
    padding: var(--space-md);
    margin-bottom: var(--space-md);
  }

  .cyber-input,
  .cyber-textarea {
    font-size: 26rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
  }

  .cyber-input {
    height: 76rpx;
    line-height: 76rpx; /* 行高等于高度 */
    padding-top: 0;
    padding-bottom: 0;
  }

  .cyber-input::placeholder {
    line-height: 76rpx; /* placeholder也使用相同行高 */
  }

  .cyber-textarea {
    padding-top: 24rpx;
    padding-bottom: 24rpx;
  }

  .action-buttons {
    gap: var(--space-sm);
  }

  .cancel-btn,
  .save-btn {
    height: 80rpx;
  }
}

/* 超小屏幕优化 */
@media (max-width: 320px) {
  .edit-container {
    padding: 20rpx;
  }

  .cyber-card {
    padding: 20rpx;
  }

  .cyber-input,
  .cyber-textarea {
    font-size: 24rpx;
    padding-left: 20rpx;
    padding-right: 20rpx;
  }

  .cyber-input {
    height: 72rpx;
    line-height: 72rpx; /* 行高等于高度 */
    padding-top: 0;
    padding-bottom: 0;
  }

  .cyber-input::placeholder {
    line-height: 72rpx; /* placeholder也使用相同行高 */
  }

  .cyber-textarea {
    padding-top: 20rpx;
    padding-bottom: 20rpx;
  }

  .label-text {
    font-size: 26rpx;
  }

  .btn-text {
    font-size: 28rpx;
  }
}

/* 动画 */
@keyframes dataFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(100rpx, 100rpx); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
