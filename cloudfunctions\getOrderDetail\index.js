// 获取订单详情云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { orderId } = event;

  try {
    // 验证必填字段
    if (!orderId) {
      return {
        success: false,
        error: '订单ID不能为空'
      };
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 查找订单（支持多种查询方式）
    let order = null;
    console.log('🔍 开始查询订单，传入的orderId:', orderId);
    console.log('🔍 orderId类型:', typeof orderId);
    console.log('🔍 orderId长度:', orderId ? orderId.length : 'null');

    // 方式1：按 orderNo 字段查询
    console.log('🔍 尝试按 orderNo 查询订单:', orderId);
    let orderResult = await db.collection('orders').where({
      orderNo: orderId
    }).get();
    console.log('📊 orderNo 查询结果数量:', orderResult.data.length);

    if (orderResult.data.length > 0) {
      order = orderResult.data[0];
      console.log('✅ 通过 orderNo 找到订单，订单ID:', order._id);
    } else {
      // 方式2：按 orderId 字段查询
      console.log('🔍 尝试按 orderId 查询订单:', orderId);
      orderResult = await db.collection('orders').where({
        orderId: orderId
      }).get();
      console.log('📊 orderId 查询结果数量:', orderResult.data.length);

      if (orderResult.data.length > 0) {
        order = orderResult.data[0];
        console.log('✅ 通过 orderId 找到订单，订单ID:', order._id);
      } else {
        // 方式3：按数据库 _id 查询
        console.log('🔍 尝试按 _id 查询订单:', orderId);
        try {
          orderResult = await db.collection('orders').doc(orderId).get();
          if (orderResult.data) {
            order = orderResult.data;
            order._id = orderId; // 确保_id字段存在
            console.log('✅ 通过 _id 找到订单');
          } else {
            console.log('❌ 按 _id 查询返回空数据');
          }
        } catch (docError) {
          console.log('❌ 按 _id 查询失败:', docError.message);
          console.log('❌ 错误详情:', docError);
        }
      }
    }

    if (!order) {
      return {
        success: false,
        error: '订单不存在'
      };
    }

    // 检查用户权限 - 兼容多种字段名
    const isCustomer = String(order.customerId) === String(user._id);

    // 兼容 accepterId 和 companionId 字段
    const accepterId = order.accepterId || order.companionId;
    const isAccepter = accepterId && String(accepterId) === String(user._id);

    // 调试信息
    console.log('权限检查调试信息:');
    console.log('当前用户ID:', user._id);
    console.log('订单发布者ID:', order.customerId);
    console.log('订单接单者ID (accepterId):', order.accepterId);
    console.log('订单接单者ID (companionId):', order.companionId);
    console.log('实际接单者ID:', accepterId);
    console.log('订单状态:', order.status);
    console.log('是否为发布者:', isCustomer);
    console.log('是否为接单者:', isAccepter);

    // 权限检查：
    // 1. 订单发布者（客户）可以查看
    // 2. 订单接单者可以查看
    // 3. 待接单状态的订单，所有用户都可以查看（用于抢单）
    // 4. 进行中状态的订单，所有用户都可以查看（用于查看详情）
    // 5. 临时：允许所有用户查看所有订单（调试用）
    const canView = true; // 临时允许所有用户查看
    // const canView = isCustomer || isAccepter || order.status === 'pending' || order.status === 'in_progress';

    console.log('是否有权限查看:', canView);
    console.log('🔍 临时开放权限，允许所有用户查看订单详情');

    if (!canView) {
      return {
        success: false,
        error: '您没有权限查看此订单'
      };
    }

    // 获取客户信息
    let customerInfo = null;

    if (order.customerId) {
      try {
        const customerResult = await db.collection('users').doc(order.customerId).get();
        console.log('📊 客户查询结果:', customerResult.data ? '找到客户' : '客户不存在');

        customerInfo = customerResult.data ? {
          _id: customerResult.data._id,
          nickName: customerResult.data.nickName,
          gameNickName: customerResult.data.gameNickName, // 添加游戏昵称
          avatarUrl: customerResult.data.avatarUrl,
          openid: customerResult.data.openid // 添加openid用于判断订单所有者
        } : null;
      } catch (customerError) {
        console.log('❌ 获取客户信息失败:', customerError.message);
        console.log('❌ 客户ID:', order.customerId);
        // 不要因为客户信息获取失败就终止整个流程
        customerInfo = {
          _id: order.customerId,
          nickName: '未知用户',
          avatarUrl: '',
          openid: ''
        };
      }
    } else {
      console.log('⚠️ 订单没有customerId字段');
    }

    // 获取接单者信息（兼容多种字段）
    let accepterInfo = null;
    console.log('🔍 准备获取接单者信息，accepterId:', accepterId);

    if (accepterId) {
      try {
        const accepterResult = await db.collection('users').doc(accepterId).get();
        console.log('📊 接单者查询结果:', accepterResult.data ? '找到接单者' : '接单者不存在');

        if (accepterResult.data) {
          accepterInfo = {
            _id: accepterResult.data._id,
            nickName: accepterResult.data.nickName,
            gameNickName: accepterResult.data.gameNickName, // 添加游戏昵称
            avatarUrl: accepterResult.data.avatarUrl
          };
        }
      } catch (accepterError) {
        console.log('❌ 获取接单者信息失败:', accepterError.message);
        console.log('❌ 接单者ID:', accepterId);
        // 不要因为接单者信息获取失败就终止整个流程
        accepterInfo = {
          _id: accepterId,
          nickName: '未知接单者',
          avatarUrl: ''
        };
      }
    } else {
      console.log('ℹ️ 订单暂无接单者');
    }

    // 获取聊天房间信息
    let chatRoomInfo = null;
    console.log('🔍 准备获取聊天房间信息，chatRoomId:', order.chatRoomId);

    if (order.chatRoomId) {
      try {
        const chatRoomResult = await db.collection('chatRooms').doc(order.chatRoomId).get();
        console.log('📊 聊天房间查询结果:', chatRoomResult.data ? '找到聊天房间' : '聊天房间不存在');

        if (chatRoomResult.data) {
          chatRoomInfo = chatRoomResult.data;
        }
      } catch (chatRoomError) {
        console.log('❌ 获取聊天房间信息失败:', chatRoomError.message);
        console.log('❌ 聊天房间ID:', order.chatRoomId);
        // 聊天房间信息获取失败不影响订单详情显示
      }
    } else {
      console.log('ℹ️ 订单暂无聊天房间');
    }

    // 检查用户角色（使用之前计算的值）
    // isCustomer 和 isAccepter 已在权限检查时计算

    // 构建返回数据
    const orderDetail = {
      ...order,
      customerInfo: customerInfo, // 使用 customerInfo 保持与列表页一致
      customer: customerInfo,
      accepter: accepterInfo,
      accepterInfo: accepterInfo,
      chatRoom: chatRoomInfo,
      userRole: isCustomer ? 'customer' : (isAccepter ? 'accepter' : 'viewer')
    };

    // 根据用户角色过滤敏感信息
    if (!isCustomer) {
      // 非客户不显示客户的敏感信息
      delete orderDetail.customer?.phone;
      delete orderDetail.customerInfo?.phone;
    }

    // 如果是查看者（非订单相关用户），进一步限制信息
    if (!isCustomer && !isAccepter) {
      // 只保留必要的订单信息，隐藏敏感数据
      delete orderDetail.chatRoom;
      delete orderDetail.customer?.phone;
      delete orderDetail.customerInfo?.phone;
    }

    return {
      success: true,
      data: orderDetail,
      userRole: isCustomer ? 'customer' : (isAccepter ? 'accepter' : 'viewer')
    };
  } catch (error) {
    console.error('获取订单详情失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
