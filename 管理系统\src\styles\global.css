/* 全局样式 - 科技感深色主题 */

/* CSS 变量定义 */
:root {
  /* 主色彩系统 - 科技蓝色调 */
  --primary-color: #00d4ff;
  --primary-gradient: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #006699 100%);
  --primary-light: rgba(0, 212, 255, 0.15);
  --primary-dark: #0099cc;

  /* 科技感配色 */
  --accent-color: #00ff88;
  --accent-light: rgba(0, 255, 136, 0.15);
  --cyber-blue: #00d4ff;
  --cyber-green: #00ff88;
  --cyber-purple: #8b5cf6;

  /* 中性色系统 - 高对比度 */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #64748b;
  --text-disabled: #475569;
  --text-accent: #00d4ff;

  /* 背景色系统 - 深色科技风 */
  --bg-primary: rgba(15, 23, 42, 0.95);
  --bg-secondary: rgba(30, 41, 59, 0.9);
  --bg-tertiary: rgba(51, 65, 85, 0.8);
  --bg-overlay: rgba(0, 0, 0, 0.7);
  --bg-glass: rgba(255, 255, 255, 0.05);
  --bg-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);

  /* 边框色系统 - 科技感发光 */
  --border-color: rgba(148, 163, 184, 0.3);
  --border-light: rgba(148, 163, 184, 0.2);
  --border-medium: rgba(148, 163, 184, 0.3);
  --border-dark: rgba(148, 163, 184, 0.4);
  --border-glow: rgba(0, 212, 255, 0.5);

  /* 状态色系统 - 科技感配色 */
  --success-color: #00ff88;
  --warning-color: #fbbf24;
  --error-color: #ef4444;
  --info-color: #00d4ff;

  /* 阴影系统 - 科技感发光效果 */
  --shadow-sm: 0 2px 12px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 6px 20px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 32px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
  --shadow-glow-strong: 0 0 30px rgba(0, 212, 255, 0.5);

  /* 圆角系统 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-full: 50%;

  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 24px;
  --space-2xl: 32px;
  --space-3xl: 40px;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-gradient);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  background: var(--bg-gradient);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-sm);
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-glow);
}

/* 科技感玻璃卡片样式 */
.glass-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-glow), transparent);
}

.glass-card:hover {
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
  border-color: var(--border-glow);
}

/* 发光效果 */
.glow-effect {
  box-shadow: var(--shadow-glow);
}

.glow-effect-strong {
  box-shadow: var(--shadow-glow-strong);
}

/* 渐变文字 */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mt-1 { margin-top: var(--space-xs); }
.mt-2 { margin-top: var(--space-sm); }
.mt-3 { margin-top: var(--space-md); }
.mt-4 { margin-top: var(--space-lg); }
.mt-6 { margin-top: var(--space-xl); }

.mb-1 { margin-bottom: var(--space-xs); }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-3 { margin-bottom: var(--space-md); }
.mb-4 { margin-bottom: var(--space-lg); }
.mb-6 { margin-bottom: var(--space-xl); }

.p-1 { padding: var(--space-xs); }
.p-2 { padding: var(--space-sm); }
.p-3 { padding: var(--space-md); }
.p-4 { padding: var(--space-lg); }
.p-6 { padding: var(--space-xl); }

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full {
    width: 100% !important;
  }
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}
