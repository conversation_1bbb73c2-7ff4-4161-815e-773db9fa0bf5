/* 科技感自定义 tabBar 样式 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: var(--bg-glass);
  backdrop-filter: blur(30rpx);
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 1000;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.4);
  border-top: 1rpx solid var(--border-glow);
}

.tab-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, var(--border-glow), transparent);
  box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.tab-bar-border {
  display: none;
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 16rpx 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: var(--radius-md);
  margin: 8rpx;
}

.tab-bar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 153, 204, 0.3));
  border-radius: var(--radius-md);
  border: 1rpx solid var(--border-glow);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-glow);
}

.tab-bar-item.selected::before {
  opacity: 1;
  transform: scale(1);
}

.tab-bar-item:active {
  transform: scale(0.95);
}

.tab-icon-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tab-icon {
  width: 52rpx;
  height: 52rpx;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.tab-bar-item.selected .tab-icon {
  transform: scale(1.1);
}

.tab-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #0f172a;
  box-shadow: 0 0 10rpx rgba(255, 71, 87, 0.5);
}

.badge-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 600;
  line-height: 1;
  padding: 0 6rpx;
}

.tab-emoji {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  line-height: 1;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.tab-bar-item.selected .tab-emoji {
  transform: scale(1.2);
  filter: drop-shadow(0 0 15rpx rgba(0, 212, 255, 0.8));
  text-shadow: 0 0 20rpx rgba(0, 212, 255, 0.6);
}

.tab-text {
  font-size: 22rpx;
  line-height: 1.2;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  z-index: 1;
  letter-spacing: 0.5rpx;
}

.tab-bar-item.selected .tab-text {
  color: var(--cyber-blue);
  font-weight: 600;
  transform: translateY(-2rpx);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}
