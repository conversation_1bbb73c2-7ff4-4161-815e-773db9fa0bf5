/* 科技感启动页样式 */
.splash-container {
  width: 100vw;
  height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 科技感背景 */
.tech-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 动态网格 */
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 80rpx 80rpx;
  animation: gridFlow 15s linear infinite;
  opacity: 0.3;
}

@keyframes gridFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(80rpx, 80rpx); }
}

/* 粒子效果 */
.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: #00d4ff;
  border-radius: 50%;
  animation: particleFloat 8s ease-in-out infinite;
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.8);
}

.particle:nth-child(1) { left: 10%; top: 20%; animation-duration: 6s; }
.particle:nth-child(2) { left: 80%; top: 30%; animation-duration: 8s; }
.particle:nth-child(3) { left: 30%; top: 70%; animation-duration: 7s; }
.particle:nth-child(4) { left: 70%; top: 80%; animation-duration: 9s; }
.particle:nth-child(5) { left: 50%; top: 10%; animation-duration: 5s; }
.particle:nth-child(6) { left: 90%; top: 60%; animation-duration: 10s; }

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-100rpx) scale(1.2);
    opacity: 1;
  }
}

/* 扫描线效果 */
.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: scanMove 4s ease-in-out infinite;
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.8);
}

@keyframes scanMove {
  0% {
    top: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}

/* 数据流效果 */
.data-stream {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.stream-line {
  position: absolute;
  width: 2rpx;
  height: 200rpx;
  background: linear-gradient(to bottom, transparent, #00ff88, transparent);
  animation: streamFlow 3s linear infinite;
}

.stream-line:nth-child(1) { left: 15%; animation-duration: 2.5s; }
.stream-line:nth-child(2) { left: 45%; animation-duration: 3.2s; }
.stream-line:nth-child(3) { left: 75%; animation-duration: 2.8s; }
.stream-line:nth-child(4) { left: 85%; animation-duration: 3.5s; }

@keyframes streamFlow {
  0% {
    top: -200rpx;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    top: 100vh;
    opacity: 0;
  }
}

/* 主要内容区域 */
.splash-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  width: 100%;
  height: 100%;
}

/* Logo容器 */
.logo-container {
  position: relative;
  width: 280rpx;
  height: 280rpx;
  margin-bottom: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 科技感边框 */
.tech-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.border-corner {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #00d4ff;
  animation: borderPulse 2s ease-in-out infinite;
}

.corner-tl {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.corner-tr {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.corner-bl {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.corner-br {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

@keyframes borderPulse {
  0%, 100% {
    opacity: 0.6;
    box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.3);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 30rpx rgba(0, 212, 255, 0.8);
  }
}

/* Logo内容 */
.logo-content {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  opacity: 0;
  transform: scale(0.5) rotateY(180deg);
  transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.logo-loaded {
  opacity: 1;
  transform: scale(1) rotateY(0deg);
}

/* Logo图片 */
.logo-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  box-shadow: 0 0 40rpx rgba(0, 212, 255, 0.4);
}

/* Logo备用图标 */
.logo-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  border-radius: 20rpx;
  box-shadow: 0 0 40rpx rgba(0, 212, 255, 0.6);
}

.logo-icon {
  font-size: 120rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.8);
}

/* 加载环形进度 */
.loading-ring {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  width: 320rpx;
  height: 320rpx;
  z-index: 3;
  opacity: 1;
  transition: opacity 0.8s ease;
}

.fade-out {
  opacity: 0;
}

.ring-segment {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6rpx;
  height: 40rpx;
  background: linear-gradient(to bottom, #00d4ff, transparent);
  border-radius: 3rpx;
  transform-origin: 3rpx 160rpx;
  animation: ringRotate 1.5s linear infinite;
}

.ring-segment:nth-child(1) { transform: translate(-50%, -50%) rotate(0deg); }
.ring-segment:nth-child(2) { transform: translate(-50%, -50%) rotate(45deg); }
.ring-segment:nth-child(3) { transform: translate(-50%, -50%) rotate(90deg); }
.ring-segment:nth-child(4) { transform: translate(-50%, -50%) rotate(135deg); }
.ring-segment:nth-child(5) { transform: translate(-50%, -50%) rotate(180deg); }
.ring-segment:nth-child(6) { transform: translate(-50%, -50%) rotate(225deg); }
.ring-segment:nth-child(7) { transform: translate(-50%, -50%) rotate(270deg); }
.ring-segment:nth-child(8) { transform: translate(-50%, -50%) rotate(315deg); }

@keyframes ringRotate {
  0% { opacity: 1; }
  12.5% { opacity: 1; }
  25% { opacity: 0.3; }
  100% { opacity: 0.3; }
}

.fade-out {
  opacity: 0;
}

/* 品牌信息区域 */
.brand-section {
  text-align: center;
  margin-bottom: 60rpx;
  opacity: 0;
  transform: translateY(50rpx);
  transition: all 1s ease 0.5s;
}

.brand-show {
  opacity: 1;
  transform: translateY(0);
}

/* 主标题 */
.brand-title {
  position: relative;
  margin-bottom: 30rpx;
}

.title-text {
  font-size: 48rpx;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 4rpx;
  text-shadow: 0 0 30rpx rgba(0, 212, 255, 0.8);
}

.title-underline {
  width: 120rpx;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  margin: 20rpx auto 0;
  animation: underlinePulse 2s ease-in-out infinite;
}

@keyframes underlinePulse {
  0%, 100% {
    opacity: 0.6;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.2);
  }
}

/* 副标题 */
.brand-subtitle {
  margin-bottom: 40rpx;
}

.subtitle-text {
  font-size: 28rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 6rpx;
  font-family: 'Courier New', monospace;
}

/* 科技标签 */
.tech-tags {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.tech-tag {
  padding: 8rpx 16rpx;
  background: rgba(0, 212, 255, 0.1);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  animation: tagGlow 3s ease-in-out infinite;
}

.tech-tag:nth-child(2) { animation-delay: 0.5s; }
.tech-tag:nth-child(3) { animation-delay: 1s; }

.tag-text {
  font-size: 20rpx;
  color: #00d4ff;
  font-weight: 500;
  letter-spacing: 1rpx;
}

@keyframes tagGlow {
  0%, 100% {
    box-shadow: 0 0 10rpx rgba(0, 212, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.6);
  }
}

/* 加载状态 */
.loading-status {
  margin-bottom: 40rpx;
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.8s ease 0.8s;
}

.status-show {
  opacity: 1;
  transform: translateY(0);
}

.status-bar {
  width: 300rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3rpx;
  margin: 0 auto 20rpx;
  overflow: hidden;
}

.status-progress {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff, #00ff88);
  border-radius: 3rpx;
  animation: progressMove 2s ease-in-out infinite;
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.6);
}

@keyframes progressMove {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.status-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 1rpx;
  text-align: center;
}

/* 版本信息 */
.version-info {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  opacity: 0;
  transform: translateX(-50%) translateY(30rpx);
  transition: all 0.8s ease 1s;
}

.version-show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.version-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  letter-spacing: 1rpx;
  margin-bottom: 10rpx;
}

.copyright-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.3);
  letter-spacing: 1rpx;
}

/* 跳过按钮 */
.skip-button {
  position: absolute;
  top: 80rpx;
  left: 40rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
  opacity: 0;
  transform: translateX(-50rpx);
  transition: all 0.8s ease 1.2s;
}

.skip-show {
  opacity: 1;
  transform: translateX(0);
}

.skip-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.skip-arrow {
  font-size: 20rpx;
  color: #00d4ff;
  transition: transform 0.3s ease;
}

.skip-button:active .skip-arrow {
  transform: translateX(5rpx);
}
