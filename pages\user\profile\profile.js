// profile.js
import API from '../../../utils/api.js';
const userActivityTracker = require('../../../utils/userActivityTracker.js');

const app = getApp();

Page({
  data: {
    userInfo: {},
    userStats: {},

    loading: false
  },

  onLoad() {
    // 保存原始方法
    this.originalShowLoading = app.utils?.showLoading;
    this.originalWxShowLoading = wx.showLoading;

    // 临时禁用所有loading显示
    if (app.utils) {
      app.utils.showLoading = () => {
        console.log('🚫 [个人中心] 阻止app.utils.showLoading调用');
      };
    }

    wx.showLoading = () => {
      console.log('🚫 [个人中心] 阻止wx.showLoading调用');
    };

    // 强制隐藏现有loading
    wx.hideLoading();

    this.loadUserData();

    // 监听用户信息更新事件
    this.userInfoUpdateHandler = (userInfo) => {
      this.setData({ userInfo });
    };
    app.$on('userInfoUpdated', this.userInfoUpdateHandler);

    // 页面加载完成后恢复原始方法
    setTimeout(() => {
      if (app.utils && this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      wx.showLoading = this.originalWxShowLoading;
    }, 1000);
  },

  onUnload() {
    // 移除事件监听
    if (this.userInfoUpdateHandler) {
      app.$off('userInfoUpdated', this.userInfoUpdateHandler);
    }
  },

  onShow() {
    console.log('=== 个人中心页面显示 ===');

    // 记录用户活跃
    userActivityTracker.recordActivity();

    // 临时禁用所有loading显示
    if (app.utils) {
      app.utils.showLoading = () => {
        console.log('🚫 [个人中心显示] 阻止app.utils.showLoading调用');
      };
    }

    wx.showLoading = () => {
      console.log('🚫 [个人中心显示] 阻止wx.showLoading调用');
    };

    // 强制隐藏现有loading
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);

    // 页面显示时刷新用户数据
    this.loadUserData();

    // 更新 tabBar 选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected();
    }

    // 监听用户信息更新事件
    this.checkUserInfoUpdate();

    // 恢复原始方法
    setTimeout(() => {
      if (app.utils && this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      if (this.originalWxShowLoading) {
        wx.showLoading = this.originalWxShowLoading;
      }
    }, 1000);
  },

  // 检查用户信息是否有更新
  checkUserInfoUpdate() {
    const currentUserInfo = wx.getStorageSync('userInfo');
    if (currentUserInfo && JSON.stringify(currentUserInfo) !== JSON.stringify(this.data.userInfo)) {
      this.setData({
        userInfo: currentUserInfo
      });
    }
  },

  onPullDownRefresh() {
    this.loadUserData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载用户数据
  async loadUserData() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 从本地存储获取用户信息
      const localUserInfo = wx.getStorageSync('userInfo');
      if (localUserInfo) {
        this.setData({ userInfo: localUserInfo });
      }

      let userInfo, userStats;

      // 调用API获取用户信息（静默调用，不显示系统loading）
      try {
        const result = await API.getUserInfo({ showLoading: false });
        if (result.success) {
          userInfo = result.data.userInfo;
          userStats = result.data.userStats;
        } else {
          throw new Error(result.error || '获取用户信息失败');
        }
      } catch (apiError) {
        console.log('API调用失败:', apiError);
        // 使用本地缓存的用户信息
        userInfo = localUserInfo;
        userStats = {
          totalOrders: 0,
          completedOrders: 0,
          totalEarnings: 0,
          rating: 5.0
        };
      }

      this.setData({
        userInfo,
        userStats,
        loading: false
      });

      // 更新本地存储和全局状态
      if (userInfo) {
        wx.setStorageSync('userInfo', userInfo);
        app.globalData.userInfo = userInfo;
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },



  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/user-package/pages/profile-edit/profile-edit'
    });
  },

  // 获取性别显示文本
  getGenderText(gender) {
    const genderMap = {
      0: '保密',
      1: '男',
      2: '女'
    };
    return genderMap[gender] || '保密';
  },

  // 导航到已完成订单页面
  navigateToOrders() {
    console.log('🔄 个人中心 - 点击我的订单按钮');

    // 跳转到专门的已完成订单页面
    wx.navigateTo({
      url: '/order-package/pages/completed/completed',
      success: function(res) {
        console.log('✅ 个人中心 - 导航到已完成订单成功', res);
      },
      fail: function(err) {
        console.error('❌ 个人中心 - 导航到已完成订单失败', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 导航到钱包页面
  navigateToWallet() {
    wx.navigateTo({
      url: '/payment-package/pages/wallet/wallet'
    });
  },

  // 导航到实名认证
  navigateToCertification() {
    wx.navigateTo({
      url: '/user-package/pages/certification/certification'
    });
  },



  // 导航到游戏账号
  navigateToGameAccounts() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 导航到客服中心
  navigateToCustomerService() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 跳转到数据统计
  goToStatistics() {
    wx.navigateTo({
      url: '/utility-package/pages/statistics/statistics'
    });
  },

  // 跳转到通知中心
  goToNotifications() {
    wx.navigateTo({
      url: '/utility-package/pages/notification/list'
    });
  },

  // 导航到设置
  navigateToSettings() {
    wx.navigateTo({
      url: '/user-package/pages/settings/settings'
    });
  },

  // 导航到关于我们
  navigateToAbout() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.clearStorageSync();
          
          // 重置全局数据
          app.globalData.userInfo = null;
          app.globalData.isLogin = false;

          // 跳转到登录页面
          wx.reLaunch({
            url: '/pages/common/login/login'
          });
        }
      }
    });
  },


});
