// 获取抢单大厅订单列表云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 创建示例数据
 */
async function createSampleData() {
  console.log('开始创建示例数据...');

  try {

    return { status: 'created', orderCount: orderResults.length };
  } catch (error) {
    console.error('创建示例数据失败:', error);
    return { status: 'error', error: error.message };
  }
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { page = 1, pageSize = 10 } = event;

  try {
    const db = cloud.database();
    const _ = db.command;

    // 查找当前用户
    let userResult;
    let currentUserId;

    try {
      userResult = await db.collection('users').where({
        openid: wxContext.OPENID
      }).get();

      if (userResult.data.length === 0) {
        return {
          success: false,
          error: '用户不存在'
        };
      }

      currentUserId = userResult.data[0]._id;
    } catch (userError) {
      // 如果用户集合不存在，先创建示例数据
      if (userError.errCode === -502005 || userError.errCode === -502002 ||
          userError.message.includes('collection not exists') ||
          userError.message.includes('not exist')) {
        console.log('用户集合不存在，创建示例数据...');

        try {
          await createSampleData();
          console.log('示例数据创建完成');
        } catch (createError) {
          console.error('创建示例数据失败:', createError);
        }

        // 重新尝试查找用户
        try {
          userResult = await db.collection('users').where({
            openid: wxContext.OPENID
          }).get();

          if (userResult.data.length === 0) {
            // 如果还是没有当前用户，创建一个临时用户ID
            currentUserId = 'temp_user_' + Date.now();
          } else {
            currentUserId = userResult.data[0]._id;
          }
        } catch (retryError) {
          console.log('重新查找用户失败，使用临时ID');
          currentUserId = 'temp_user_' + Date.now();
        }
      } else {
        throw userError;
      }
    }

    console.log('当前用户ID:', currentUserId);

    // 抢单大厅查询条件：只查询待接单状态的订单
    const whereCondition = {
      status: 'pending'
    };

    console.log('查询条件:', whereCondition);



    // 查询订单
    let orderResult;
    try {
      console.log('=== 所有订单结束 ===');

      // 先查询所有pending订单看看有哪些
      const allPendingResult = await db.collection('orders')
        .where(whereCondition)
        .orderBy('createTime', 'desc')
        .get();
      console.log('=== 所有pending订单 ===');
      console.log('pending订单总数:', allPendingResult.data.length);
      allPendingResult.data.forEach((order, index) => {
        console.log(`pending订单${index + 1}:`, {
          _id: order._id,
          title: order.title,
          status: order.status,
          customerId: order.customerId,
          createTime: order.createTime
        });
      });
      console.log('=== pending订单结束 ===');

      orderResult = await db.collection('orders')
        .where(whereCondition)
        .orderBy('createTime', 'desc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get();





      // 不再自动创建示例数据，直接返回查询结果
      console.log('查询完成，返回结果');
    } catch (orderError) {
      console.error('查询订单时发生错误:', orderError);
      console.error('错误代码:', orderError.errCode);
      console.error('错误消息:', orderError.message);
      console.error('完整错误对象:', JSON.stringify(orderError, null, 2));

      // 如果订单集合不存在，先创建示例数据
      if (orderError.errCode === -502005 || orderError.errCode === -502002 ||
          orderError.message.includes('collection not exists') ||
          orderError.message.includes('not exist')) {
        try {
          await createSampleData();
        } catch (createError) {
          console.error('创建示例数据失败:', createError);
        }

        // 重新尝试查询
        orderResult = await db.collection('orders')
          .where(whereCondition)
          .orderBy('_id', 'desc')
          .skip((page - 1) * pageSize)
          .limit(pageSize)
          .get();
      } else {
        throw orderError;
      }
    }

    // 获取相关的客户信息
    const customerIds = [...new Set(orderResult.data.map(order => order.customerId))];

    // 查询客户信息（包含openid用于判断订单所有者）
    const customerResult = await db.collection('users')
      .where({
        _id: _.in(customerIds)
      })
      .field({
        _id: true,
        nickName: true,
        avatarUrl: true,
        openid: true
      })
      .get();

    // 创建客户信息映射
    const customerMap = {};
    customerResult.data.forEach(customer => {
      customerMap[customer._id] = customer;
    });

    // 合并数据
    const orderList = orderResult.data.map(order => {
      const customer = customerMap[order.customerId] || {};

      return {
        ...order,
        customerInfo: {
          _id: customer._id,
          nickName: customer.nickName || '未知用户',
          avatarUrl: customer.avatarUrl || '',
          openid: customer.openid || ''
        },
        // 抢单大厅特有字段
        canGrab: true, // 标识可以抢单
        isGrabOrder: true // 标识这是抢单大厅的订单
      };
    });

    return {
      success: true,
      data: {
        list: orderList,
        page,
        pageSize,
        total: orderResult.data.length
      }
    };
  } catch (error) {
    console.error('获取抢单大厅订单失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
