<!--settings.wxml-->
<!-- 返回按钮 -->
<back-button position="auto-position" size="normal" back-type="auto"></back-button>

<view class="settings-container page-with-custom-nav">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <text class="navbar-title">设置</text>
    </view>
  </view>
  <!-- 账户设置 -->
  <view class="settings-group">
    <view class="group-title">账户设置</view>

    <view class="setting-item" bindtap="navigateToCertification">
      <view class="setting-info">
        <text class="setting-icon">🆔</text>
        <text class="setting-text">实名认证</text>
      </view>
      <view class="setting-action">
        <text class="setting-status {{userInfo.isVerified ? 'verified' : 'unverified'}}">
          {{userInfo.isVerified ? '已认证' : '未认证'}}
        </text>
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="changePassword">
      <view class="setting-info">
        <text class="setting-icon">🔒</text>
        <text class="setting-text">修改密码</text>
      </view>
      <view class="setting-action">
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="bindPhone">
      <view class="setting-info">
        <text class="setting-icon">📱</text>
        <text class="setting-text">绑定手机</text>
      </view>
      <view class="setting-action">
        <text class="setting-status">{{userInfo.phone ? '已绑定' : '未绑定'}}</text>
        <text class="setting-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 通知设置 -->
  <view class="settings-group">
    <view class="group-title">通知设置</view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🔔</text>
        <text class="setting-text">订单通知</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.orderNotification}}"
                bindchange="toggleSetting"
                data-key="orderNotification" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">💬</text>
        <text class="setting-text">消息通知</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.messageNotification}}"
                bindchange="toggleSetting"
                data-key="messageNotification" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🆕</text>
        <text class="setting-text">新订单通知</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.newOrderNotification}}"
                bindchange="toggleSetting"
                data-key="newOrderNotification" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">⭐</text>
        <text class="setting-text">评价通知</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.evaluationNotification}}"
                bindchange="toggleSetting"
                data-key="evaluationNotification" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">📢</text>
        <text class="setting-text">系统通知</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.systemNotification}}"
                bindchange="toggleSetting"
                data-key="systemNotification" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🔊</text>
        <text class="setting-text">声音提醒</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.soundNotification}}"
                bindchange="toggleSetting"
                data-key="soundNotification" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">📳</text>
        <text class="setting-text">震动提醒</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.vibrateNotification}}"
                bindchange="toggleSetting"
                data-key="vibrateNotification" />
      </view>
    </view>
  </view>

  <!-- 隐私设置 -->
  <view class="settings-group">
    <view class="group-title">隐私设置</view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🔐</text>
        <text class="setting-text">允许陌生人联系</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.allowStrangerContact}}"
                bindchange="toggleSetting"
                data-key="allowStrangerContact" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🟢</text>
        <text class="setting-text">显示在线状态</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.showOnlineStatus}}"
                bindchange="toggleSetting"
                data-key="showOnlineStatus" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">📱</text>
        <text class="setting-text">显示手机号</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.showPhoneNumber}}"
                bindchange="toggleSetting"
                data-key="showPhoneNumber" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">📋</text>
        <text class="setting-text">允许查看订单历史</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.allowOrderHistory}}"
                bindchange="toggleSetting"
                data-key="allowOrderHistory" />
      </view>
    </view>
  </view>

  <!-- 功能设置 -->
  <view class="settings-group">
    <view class="group-title">功能设置</view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">⚡</text>
        <text class="setting-text">自动接单</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.autoAcceptOrder}}"
                bindchange="toggleSetting"
                data-key="autoAcceptOrder" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">⏰</text>
        <text class="setting-text">订单提醒</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.orderReminder}}"
                bindchange="toggleSetting"
                data-key="orderReminder" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🌙</text>
        <text class="setting-text">深色模式</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.darkMode}}"
                bindchange="toggleSetting"
                data-key="darkMode" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🔄</text>
        <text class="setting-text">数据同步</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.dataSync}}"
                bindchange="toggleSetting"
                data-key="dataSync" />
      </view>
    </view>
  </view>

  <!-- 安全设置 -->
  <view class="settings-group">
    <view class="group-title">安全设置</view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">🔐</text>
        <text class="setting-text">登录验证</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.loginVerification}}"
                bindchange="toggleSetting"
                data-key="loginVerification" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">💳</text>
        <text class="setting-text">支付密码</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.paymentPassword}}"
                bindchange="toggleSetting"
                data-key="paymentPassword" />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-icon">👆</text>
        <text class="setting-text">生物识别认证</text>
      </view>
      <view class="setting-action">
        <switch checked="{{settings.biometricAuth}}"
                bindchange="toggleSetting"
                data-key="biometricAuth" />
      </view>
    </view>
  </view>

  <!-- 其他设置 -->
  <view class="settings-group">
    <view class="group-title">其他</view>

    <view class="setting-item" bindtap="showStatus">
      <view class="setting-info">
        <text class="setting-icon">📊</text>
        <text class="setting-text">功能状态</text>
      </view>
      <view class="setting-action">
        <text class="setting-desc">查看系统状态</text>
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="clearCache">
      <view class="setting-info">
        <text class="setting-icon">🧹</text>
        <text class="setting-text">清理缓存</text>
      </view>
      <view class="setting-action">
        <text class="setting-desc">{{cacheSize}}</text>
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="checkUpdate">
      <view class="setting-info">
        <text class="setting-icon">🔄</text>
        <text class="setting-text">检查更新</text>
      </view>
      <view class="setting-action">
        <text class="setting-desc">v{{appVersion}}</text>
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="showAbout">
      <view class="setting-info">
        <text class="setting-icon">ℹ️</text>
        <text class="setting-text">关于我们</text>
      </view>
      <view class="setting-action">
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="contactService">
      <view class="setting-info">
        <text class="setting-icon">🎧</text>
        <text class="setting-text">联系客服</text>
      </view>
      <view class="setting-action">
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="testUserPersistence">
      <view class="setting-info">
        <text class="setting-icon">🧪</text>
        <text class="setting-text">用户信息测试</text>
      </view>
      <view class="setting-action">
        <text class="setting-desc">数据持久化测试</text>
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="testLoginDebug">
      <view class="setting-info">
        <text class="setting-icon">🔍</text>
        <text class="setting-text">登录逻辑调试</text>
      </view>
      <view class="setting-action">
        <text class="setting-desc">登录保护测试</text>
        <text class="setting-arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="testRealtimeDebug">
      <view class="setting-info">
        <text class="setting-icon">📡</text>
        <text class="setting-text">实时监听调试</text>
      </view>
      <view class="setting-action">
        <text class="setting-desc">连接状态诊断</text>
        <text class="setting-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>