<!--completed.wxml - 已完成订单页面-->
<navigation-bar title="已完成订单" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 返回按钮 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<view class="completed-container page-with-custom-nav" style="{{navigationBarStyle}}">
  
  <!-- 订单列表 -->
  <scroll-view class="order-list"
               scroll-y
               scroll-top="{{preserveScrollPosition ? scrollTop : 0}}"
               bindscroll="onScroll"
               bindscrolltolower="loadMore"
               refresher-enabled="{{true}}"
               refresher-triggered="{{refreshing}}"
               bindrefresherrefresh="onRefresh">
    
    <!-- 使用统一的订单卡片组件 -->
    <order-card
      wx:for="{{orderList}}"
      wx:key="_id"
      order-data="{{item}}"
      bind:cardtap="navigateToDetail"
      bind:viewdetail="viewOrderDetail"
      bind:evaluateorder="evaluateOrder">
    </order-card>

    <!-- 加载状态 -->
    <view class="load-more" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>
    
    <view class="no-more" wx:if="{{!hasMore && orderList.length > 0}}">
      <text>没有更多了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && orderList.length === 0}}">
      <view class="empty-icon">✅</view>
      <text class="empty-text">暂无已完成订单</text>
      <text class="empty-desc">完成的订单将在这里显示</text>
      <button class="create-order-btn" bindtap="navigateToCreate">
        立即下单
      </button>
    </view>
  </scroll-view>
</view>
