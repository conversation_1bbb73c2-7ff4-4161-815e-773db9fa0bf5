// 调试用户数据云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: 'cloud1-9gsj7t48183e5a9f'
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔍 [调试用户] 开始调试用户数据');

  try {
    // 获取所有用户数据
    const usersResult = await db.collection('users').get();
    const users = usersResult.data;

    console.log('📊 [调试用户] 总用户数:', users.length);

    // 分析用户数据结构
    const userAnalysis = {
      totalUsers: users.length,
      usersWithLastActiveTime: 0,
      usersWithoutLastActiveTime: 0,
      recentActiveUsers: 0,
      userSamples: []
    };

    const now = new Date();
    const last30Minutes = new Date(now.getTime() - 30 * 60 * 1000);

    users.forEach((user, index) => {
      // 检查是否有 lastActiveTime 字段
      if (user.lastActiveTime) {
        userAnalysis.usersWithLastActiveTime++;
        
        // 检查是否在最近30分钟内活跃
        const lastActiveTime = new Date(user.lastActiveTime);
        if (lastActiveTime >= last30Minutes) {
          userAnalysis.recentActiveUsers++;
        }
      } else {
        userAnalysis.usersWithoutLastActiveTime++;
      }

      // 收集前5个用户的样本数据
      if (index < 5) {
        userAnalysis.userSamples.push({
          _id: user._id,
          nickName: user.nickName || '未设置',
          createTime: user.createTime,
          updateTime: user.updateTime,
          lastActiveTime: user.lastActiveTime || '无此字段',
          hasLastActiveTime: !!user.lastActiveTime
        });
      }
    });

    console.log('📊 [调试用户] 分析结果:', userAnalysis);

    // 尝试查询最近30分钟活跃的用户
    let queryResult = null;
    try {
      const onlineResult = await db.collection('users')
        .where({
          lastActiveTime: db.command.gte(last30Minutes)
        })
        .get();
      
      queryResult = {
        success: true,
        count: onlineResult.data.length,
        users: onlineResult.data.map(user => ({
          _id: user._id,
          nickName: user.nickName || '未设置',
          lastActiveTime: user.lastActiveTime
        }))
      };
    } catch (error) {
      queryResult = {
        success: false,
        error: error.message
      };
    }

    // 返回调试信息
    return {
      success: true,
      data: {
        timestamp: now.toISOString(),
        last30Minutes: last30Minutes.toISOString(),
        userAnalysis: userAnalysis,
        queryResult: queryResult,
        recommendations: generateRecommendations(userAnalysis)
      }
    };

  } catch (error) {
    console.error('❌ [调试用户] 调试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 生成修复建议
function generateRecommendations(analysis) {
  const recommendations = [];

  if (analysis.usersWithoutLastActiveTime > 0) {
    recommendations.push({
      issue: `有 ${analysis.usersWithoutLastActiveTime} 个用户缺少 lastActiveTime 字段`,
      solution: '需要为这些用户初始化 lastActiveTime 字段',
      action: 'initializeLastActiveTime'
    });
  }

  if (analysis.recentActiveUsers === 0 && analysis.usersWithLastActiveTime > 0) {
    recommendations.push({
      issue: '所有用户的 lastActiveTime 都不在最近30分钟内',
      solution: '用户可能需要重新登录或触发活跃时间更新',
      action: 'updateActiveTime'
    });
  }

  if (analysis.totalUsers > 0 && analysis.usersWithLastActiveTime === 0) {
    recommendations.push({
      issue: '所有用户都缺少 lastActiveTime 字段',
      solution: '需要批量初始化所有用户的 lastActiveTime 字段',
      action: 'batchInitializeLastActiveTime'
    });
  }

  return recommendations;
}
