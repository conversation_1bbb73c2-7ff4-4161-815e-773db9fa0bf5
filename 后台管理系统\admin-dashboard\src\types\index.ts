// 用户相关类型
export interface User {
  id: string;
  openid: string;
  nickname: string;
  avatar: string;
  phone?: string;
  isVerified: boolean;
  status: 'active' | 'disabled';
  createdAt: string;
  lastLoginAt?: string;
  walletBalance: number;
  orderCount: number;
  evaluationScore: number;
}

// 订单相关类型
export interface Order {
  id: string;
  userId: string;
  title: string;
  description: string;
  amount: number;
  status: 'pending' | 'paid' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  acceptedBy?: string;
  completedAt?: string;
  evaluation?: Evaluation;
}

// 聊天相关类型
export interface ChatRoom {
  id: string;
  orderId: string;
  participants: string[];
  lastMessage?: ChatMessage;
  lastMessageTime: string;
  unreadCount: number;
  status: 'active' | 'closed';
  customerInfo?: {
    nickName: string;
    avatarUrl: string;
  };
  accepterInfo?: {
    nickName: string;
    avatarUrl: string;
  };
  orderInfo?: {
    title: string;
    reward: number;
    status: string;
  };
}

export interface ChatMessage {
  id: string;
  roomId: string;
  senderId: string;
  content: string;
  type: 'text' | 'image' | 'system';
  timestamp: string;
  isRead: boolean;
  senderInfo?: {
    nickName: string;
    avatarUrl: string;
  };
}

// 钱包相关类型
export interface WalletTransaction {
  id: string;
  userId: string;
  type: 'recharge' | 'withdraw' | 'payment' | 'refund';
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  description: string;
  createdAt: string;
  completedAt?: string;
}

// 通知相关类型
export interface Notification {
  id: string;
  userId?: string; // 为空表示系统广播
  title: string;
  content: string;
  type: 'system' | 'order' | 'payment' | 'evaluation';
  isRead: boolean;
  createdAt: string;
}

// 评价相关类型
export interface Evaluation {
  id: string;
  orderId: string;
  evaluatorId: string;
  evaluatedId: string;
  score: number;
  comment: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  reviewedAt?: string;
  // 用户信息
  evaluatorInfo?: {
    nickName: string;
    avatarUrl: string;
  };
  evaluatedInfo?: {
    nickName: string;
    avatarUrl: string;
  };
}

// 统计数据类型
export interface DashboardStats {
  totalUsers: number;
  todayOrders: number;
  totalWalletBalance: number;
  pendingEvaluations: number;
  userGrowth: Array<{ date: string; count: number }>;
  orderTrends: Array<{ date: string; count: number; amount: number }>;
  recentActivities: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
  }>;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  total?: number;
}

// 分页参数
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// 管理员类型
export interface Admin {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'super_admin';
  permissions: string[];
  lastLoginAt?: string;
  createdAt: string;
}