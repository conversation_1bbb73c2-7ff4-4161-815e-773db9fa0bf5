// 定时密码更新云函数 - 凌晨12:00
const cloud = require('wx-server-sdk');

cloud.init({
  env: "cloud1-9gsj7t48183e5a9f"
});

const db = cloud.database();
const axios = require('axios');

exports.main = async (event, context) => {
  try {
    console.log('🔐 [定时任务12:00] 开始执行密码获取任务');
    
    const url = "https://www.onebiji.com/hykb_tools/sjz/mrmm/index.php";
    
    const headers = {
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    };
    
    // 发起HTTP请求
    const response = await axios.get(url, { 
      headers,
      timeout: 15000,
      validateStatus: function (status) {
        return status < 500;
      }
    });
    
    if (response.status !== 200) {
      throw new Error(`HTTP请求失败，状态码: ${response.status}`);
    }
    
    const htmlContent = response.data;
    console.log('🔐 [定时任务] HTTP请求成功');
    
    // 提取更新日期
    const datePattern = /(\d{2})月(\d{2})日每日密码已更新/;
    const dateMatch = htmlContent.match(datePattern);
    
    let updateDate = '';
    if (dateMatch) {
      const month = dateMatch[1];
      const day = dateMatch[2];
      updateDate = `${month}月${day}日`;
      console.log(`🔐 [定时任务] 找到更新日期: ${updateDate}`);
    } else {
      const now = new Date();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      updateDate = `${month}月${day}日`;
      console.log(`🔐 [定时任务] 未找到更新日期，使用当前日期: ${updateDate}`);
    }
    
    // 提取各个地点的密码
    const locations = [
      { name: "零号大坝", pattern: /零号大坝：(\d+)/ },
      { name: "长弓溪谷", pattern: /长弓溪谷：(\d+)/ },
      { name: "巴克什", pattern: /巴克什：(\d+)/ },
      { name: "航天基地", pattern: /航天基地：(\d+)/ },
      { name: "潮汐监狱", pattern: /潮汐监狱：(\d+)/ }
    ];
    
    const passwordData = [];
    let foundCount = 0;
    
    for (const location of locations) {
      const match = htmlContent.match(location.pattern);
      if (match) {
        passwordData.push({
          name: location.name,
          code: match[1]
        });
        foundCount++;
        console.log(`🔐 [定时任务] ${location.name}: ${match[1]}`);
      } else {
        passwordData.push({
          name: location.name,
          code: '----'
        });
        console.log(`🔐 [定时任务] ${location.name}: 破译失败`);
      }
    }
    
    // 保存到数据库
    const passwordRecord = {
      date: updateDate,
      locations: passwordData,
      foundCount: foundCount,
      totalCount: locations.length,
      updateTime: new Date(),
      source: 'scheduled_task_midnight',
      success: foundCount > 0
    };
    
    // 先查询今天是否已有记录
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD
    
    const existingRecord = await db.collection('daily_passwords')
      .where({
        date: updateDate,
        _createTime: db.command.gte(new Date(todayStr))
      })
      .orderBy('_createTime', 'desc')
      .limit(1)
      .get();
    
    if (existingRecord.data.length > 0) {
      // 更新现有记录
      await db.collection('daily_passwords')
        .doc(existingRecord.data[0]._id)
        .update({
          data: passwordRecord
        });
      console.log('🔐 [定时任务] 更新现有密码记录');
    } else {
      // 创建新记录
      await db.collection('daily_passwords').add({
        data: passwordRecord
      });
      console.log('🔐 [定时任务] 创建新密码记录');
    }
    
    const result = {
      success: true,
      data: passwordRecord,
      message: `定时任务完成，获取${foundCount}/${locations.length}个地点密码`
    };
    
    console.log('🔐 [定时任务] 密码获取任务完成:', result);
    return result;
    
  } catch (error) {
    console.error('🔐 [定时任务] 任务执行失败:', error);
    
    // 即使失败也要保存记录，标记为失败状态
    const now = new Date();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const defaultDate = `${month}月${day}日`;
    
    const failedRecord = {
      date: defaultDate,
      locations: [
        { name: '零号大坝', code: '----' },
        { name: '长弓溪谷', code: '----' },
        { name: '巴克什', code: '----' },
        { name: '航天基地', code: '----' },
        { name: '潮汐监狱', code: '----' }
      ],
      foundCount: 0,
      totalCount: 5,
      updateTime: new Date(),
      source: 'scheduled_task',
      success: false,
      error: error.message
    };
    
    try {
      await db.collection('daily_passwords').add({
        data: failedRecord
      });
    } catch (dbError) {
      console.error('🔐 [定时任务] 保存失败记录也失败:', dbError);
    }
    
    return {
      success: false,
      data: failedRecord,
      message: '定时任务失败',
      error: error.message
    };
  }
};
