// 获取钱包统计数据云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;

  try {


    // 获取用户的所有交易记录
    const transactionsResult = await db.collection('transactions')
      .where({
        userId: openid
      })
      .get();

    const transactions = transactionsResult.data || [];


    // 计算统计数据
    let totalIncome = 0;
    let totalExpense = 0;
    let transactionCount = transactions.length;

    transactions.forEach(transaction => {
      const amount = parseFloat(transaction.amount) || 0;
      
      if (amount > 0) {
        // 正数为收入（充值、订单收入、退款等）
        totalIncome += amount;
      } else {
        // 负数为支出（提现、支付等）
        totalExpense += Math.abs(amount);
      }
    });

    // 格式化金额为两位小数
    const stats = {
      totalIncome: totalIncome.toFixed(2),
      totalExpense: totalExpense.toFixed(2),
      transactionCount: transactionCount,
      netAmount: (totalIncome - totalExpense).toFixed(2)
    };



    return {
      success: true,
      data: stats
    };

  } catch (error) {
    console.error('❌ 获取钱包统计数据失败:', error);
    
    return {
      success: false,
      error: error.message || '获取钱包统计数据失败',
      data: {
        totalIncome: '0.00',
        totalExpense: '0.00',
        transactionCount: 0,
        netAmount: '0.00'
      }
    };
  }
};
