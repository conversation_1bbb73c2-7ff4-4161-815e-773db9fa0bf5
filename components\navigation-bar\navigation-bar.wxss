.weui-navigation-bar {
  --weui-FG-0:rgba(0,0,0,.9);
  --height: 50px;
  --left: 0px; /* 完全消除左边距 */
}
.weui-navigation-bar .android {
  --height: 54px;
}

.weui-navigation-bar {
  overflow: hidden;
  color: var(--weui-FG-0);
  flex: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1002; /* 提高z-index确保在最顶层 */
}

.weui-navigation-bar__inner {
  position: relative;
  top: 0;
  left: 0;
  /* height和padding-top由JavaScript动态设置 */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;

  /* 科技感背景 */
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.95) 50%,
    rgba(15, 23, 42, 0.95) 100%);
  backdrop-filter: blur(30px);

  /* 科技感边框 */
  border-bottom: 2px solid transparent;
  border-image: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 212, 255, 0.6) 20%,
    rgba(0, 212, 255, 0.8) 50%,
    rgba(0, 212, 255, 0.6) 80%,
    transparent 100%) 1;

  /* 发光效果 */
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 1px 0 rgba(0, 212, 255, 0.2) inset,
    0 -1px 0 rgba(0, 212, 255, 0.1) inset;

  /* 确保左侧完全贴边，右侧有足够空间避开胶囊按钮 */
  padding-left: 0; /* 完全消除左边距 */
  padding-right: env(safe-area-inset-right);

  /* 添加科技感纹理 */
  position: relative;
  overflow: hidden;
}

/* 科技感背景纹理 */
.weui-navigation-bar__inner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(90deg, transparent 0%, rgba(0, 212, 255, 0.03) 50%, transparent 100%),
    repeating-linear-gradient(90deg,
      transparent 0px,
      transparent 2px,
      rgba(0, 212, 255, 0.05) 2px,
      rgba(0, 212, 255, 0.05) 4px);
  pointer-events: none;
  z-index: 1;
}

/* 动态扫描线效果 */
.weui-navigation-bar__inner::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 212, 255, 0.1) 50%,
    transparent 100%);
  animation: navScan 8s ease-in-out infinite;
  pointer-events: none;
  z-index: 2;
}

@keyframes navScan {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: -100%; }
}

.weui-navigation-bar__left {
  position: relative;
  /* 强制完全消除所有边距 */
  padding-left: 0 !important;
  padding-right: 0;
  margin-left: 0 !important;
  margin-right: 0;
  left: 0 !important;

  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start; /* 确保内容从最左边开始 */
  height: 100%;
  box-sizing: border-box;
  min-width: 60px;
  z-index: 10;

  /* 确保从屏幕边缘开始 */
  transform: translateX(0) !important;
}

/* 强制按钮容器完全贴边 */
.weui-navigation-bar__buttons {
  padding: 0 !important;
  margin: 0 !important;
  left: 0 !important;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  position: relative;
}

.weui-navigation-bar__buttons_goback {
  padding: 0 !important;
  margin: 0 !important;
  left: 0 !important;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  position: relative;
}

.weui-navigation-bar__btn_goback_wrapper {
  /* 完全消除所有边距和定位偏移 */
  padding: 8px 12px 8px 0px !important; /* 左边距强制为0 */
  margin: 0 !important; /* 强制移除所有边距 */

  /* 强制从屏幕最左边开始 */
  position: relative !important;
  left: 0 !important;
  margin-left: 0 !important;

  /* 移除所有装饰效果 */
  background: transparent;
  border-radius: 0;
  backdrop-filter: none;
  border: none;
  box-shadow: none;

  /* 简洁布局 */
  display: flex;
  align-items: center;
  gap: 6px;
  transition: opacity 0.2s ease;
  z-index: 10;

  /* 移除变换效果 */
  transform: none;

  /* 确保点击区域足够大 */
  min-height: 44px;
  min-width: 60px;

  /* 确保容器本身从最左边开始 */
  justify-self: flex-start;
}

/* 调试样式 - 可以临时启用来检查位置 */
/*
.weui-navigation-bar__btn_goback_wrapper {
  border: 1px solid red !important;
  background: rgba(255, 0, 0, 0.1) !important;
}
.weui-navigation-bar__left {
  border: 1px solid blue !important;
  background: rgba(0, 0, 255, 0.1) !important;
}
*/

/* 返回按钮激活状态 - 简化 */
.weui-navigation-bar__btn_goback_wrapper.weui-active {
  opacity: 0.7; /* 简单的透明度变化 */
}

/* 移除返回按钮发光效果 */
.weui-navigation-bar__btn_goback_wrapper::before {
  display: none;
}

.weui-navigation-bar__btn_goback_wrapper:hover::before {
  display: none;
}

.weui-navigation-bar__btn_goback {
  font-size: 16px;
  width: 20px;
  height: 20px;

  /* 使用简洁的箭头图标 */
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  -webkit-mask-size: contain;
  mask-size: contain;

  /* 简洁的白色图标，移除发光效果 */
  background: #ffffff;
  filter: none;

  flex-shrink: 0;
  transition: opacity 0.2s ease; /* 简化过渡效果 */
  position: relative;
}

/* 返回图标悬浮效果 - 简化 */
.weui-navigation-bar__btn_goback_wrapper:active .weui-navigation-bar__btn_goback {
  opacity: 0.7;
}

.weui-navigation-bar__btn_goback_text {
  font-size: 15px;
  font-weight: 500;
  white-space: nowrap;
  position: relative;

  /* 简洁的白色文字 */
  color: #ffffff;
  background: transparent;

  /* 移除发光效果 */
  filter: none;

  /* 字体优化 */
  letter-spacing: 0.3px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  transition: opacity 0.2s ease;
}

/* 返回文字悬浮效果 - 简化 */
.weui-navigation-bar__btn_goback_wrapper:active .weui-navigation-bar__btn_goback_text {
  opacity: 0.7;
}

.weui-navigation-bar__center {
  font-size: 18px;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  font-weight: 600;
  flex: 1;
  height: 44px;
  z-index: 10;

  /* 移除背景和边框效果 */
  background: transparent;
  border: none;
  border-radius: 0;
  backdrop-filter: none;
  box-shadow: none;

  /* 尺寸和间距 */
  margin: 0 auto;
  padding: 8px 16px;
  min-height: 32px;

  /* 绝对居中定位，向下偏移到可见区域 */
  position: absolute;
  left: 50%;
  top: calc(env(safe-area-inset-top) + 24px);
  transform: translateX(-50%);

  /* 字体优化 */
  letter-spacing: normal;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  writing-mode: horizontal-tb;

  /* 移除动画效果 */
  transition: none;
  animation: none;
}

/* 标题容器发光动画 */
@keyframes titleContainerGlow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(0, 212, 255, 0.15),
      0 0 0 1px rgba(0, 212, 255, 0.1) inset,
      0 1px 0 rgba(255, 255, 255, 0.05) inset;
    border-color: rgba(0, 212, 255, 0.25);
  }
  50% {
    box-shadow:
      0 0 30px rgba(0, 212, 255, 0.25),
      0 0 0 1px rgba(0, 212, 255, 0.2) inset,
      0 1px 0 rgba(255, 255, 255, 0.1) inset;
    border-color: rgba(0, 212, 255, 0.4);
  }
}

/* 移除装饰效果 */
.weui-navigation-bar__center::before,
.weui-navigation-bar__center::after {
  display: none;
}



/* 标题文字样式 */
.weui-navigation-bar__center text {
  position: relative;
  z-index: 10;

  /* 确保水平显示 */
  writing-mode: horizontal-tb;
  text-orientation: mixed;
  white-space: nowrap;
  display: inline-block;

  /* 简化的文字效果 */
  color: #ffffff;
  background: transparent;
  padding: 4px 12px;
  border-radius: 8px;
  text-shadow:
    0 0 10px rgba(0, 212, 255, 0.8),
    0 0 20px rgba(0, 212, 255, 0.4);

  /* 字体优化 */
  font-weight: 700;
  letter-spacing: normal;
  font-size: 18px;
}



/* 标题文字科技感装饰 - 已禁用 */
.weui-navigation-bar__center text::before {
  display: none;
}



.weui-navigation-bar__loading {
  margin-right: 4px;
  align-items: center;
}

.weui-loading {
  font-size: 16px;
  width: 16px;
  height: 16px;
  display: block;
}

/* 右侧区域样式 */
.weui-navigation-bar__right {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  min-width: 60px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .weui-navigation-bar__center {
    font-size: 16px;
    margin: 0 15px;
    padding: 6px 16px;
    letter-spacing: 1px;
  }

  .weui-navigation-bar__center .nav-title {
    letter-spacing: 1.5px;
  }

  .weui-navigation-bar__center::after {
    left: 8px;
    right: 8px;
  }

  .weui-navigation-bar__btn_goback_text {
    font-size: 14px;
  }

  .weui-navigation-bar__btn_goback_wrapper {
    padding: 8px 12px 8px 0px; /* 保持左边距为0 */
    margin: 0;
  }
}

@media (max-width: 320px) {
  .weui-navigation-bar__center {
    font-size: 15px;
    margin: 0 10px;
    padding: 5px 12px;
    letter-spacing: 0.5px;
  }

  .weui-navigation-bar__center .nav-title {
    letter-spacing: 1px;
  }

  .weui-navigation-bar__center::after {
    left: 6px;
    right: 6px;
  }

  .weui-navigation-bar__btn_goback_text {
    font-size: 13px;
  }

  .weui-navigation-bar__btn_goback_wrapper {
    padding: 8px 12px 8px 0px; /* 保持左边距为0 */
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .weui-navigation-bar__inner {
    background: linear-gradient(135deg,
      rgba(10, 15, 28, 0.98) 0%,
      rgba(20, 25, 38, 0.98) 50%,
      rgba(10, 15, 28, 0.98) 100%);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .weui-navigation-bar__inner {
    border-bottom: 3px solid #00d4ff;
  }

  .weui-navigation-bar__btn_goback_wrapper {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .weui-navigation-bar__inner::after,
  .weui-navigation-bar__center,
  .weui-navigation-bar__center .nav-title,
  .weui-navigation-bar__btn_goback_wrapper,
  .weui-navigation-bar__btn_goback,
  .weui-navigation-bar__btn_goback_text {
    animation: none;
    transition: none;
  }
}

