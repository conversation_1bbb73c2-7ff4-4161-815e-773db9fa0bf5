// 科技感启动页逻辑
Page({
  data: {
    logoUrl: '',
    logoLoaded: false,
    splashDuration: 3500, // 启动页显示时长（毫秒）
    loadingProgress: 0,
    systemReady: false,
  },

  onLoad() {
    console.log('🚀 科技感启动页加载');
    this.initSplash();
  },

  // 初始化启动页
  async initSplash() {
    try {
      // 并行执行初始化任务
      await Promise.all([
        this.loadLogo(),
        this.simulateSystemInit(),
        this.preloadResources()
      ]);

      // 设置自动跳转定时器
      this.setAutoRedirect();

    } catch (error) {
      console.error('❌ 启动页初始化失败:', error);
      // 即使失败也要跳转，避免卡在启动页
      this.redirectToHome();
    }
  },

  // 模拟系统初始化
  async simulateSystemInit() {
    const steps = [
      '正在连接服务器...',
      '正在验证用户身份...',
      '正在加载用户数据...',
      '系统初始化完成'
    ];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 300));
      this.setData({
        loadingProgress: ((i + 1) / steps.length) * 100
      });
    }

    this.setData({
      systemReady: true
    });
  },

  // 预加载资源
  async preloadResources() {
    try {
      // 这里可以预加载一些关键资源
      console.log('🔄 预加载关键资源...');
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('✅ 资源预加载完成');
    } catch (error) {
      console.error('❌ 资源预加载失败:', error);
    }
  },

  // 加载logo
  async loadLogo() {
    try {
      const logoFileId = 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/logos/gti-security-logo.png';

      // 使用统一的云存储方法
      const cloudStorage = require('../../utils/cloudStorage.js');
      const result = await cloudStorage.getTempFileURL(logoFileId);

      if (result.success) {
        this.setData({
          logoUrl: result.tempFileURL
        });
      } else {
        throw new Error(result.error);
      }

    } catch (error) {
      console.error('Logo加载失败:', error);
      // 使用备用logo或文字
      this.setData({
        logoUrl: '',
        logoLoaded: true // 直接显示备用图标
      });
    }
  },

  // Logo加载完成
  onLogoLoad() {
    console.log('✅ Logo图片加载完成');
    // 延迟一点时间显示加载完成效果
    setTimeout(() => {
      this.setData({
        logoLoaded: true
      });
    }, 300);
  },

  // Logo加载错误
  onLogoError(e) {
    console.error('❌ Logo图片加载错误:', e);
    this.setData({
      logoLoaded: true // 显示备用图标
    });
  },

  // 设置自动跳转
  setAutoRedirect() {
    this.redirectTimer = setTimeout(() => {
      this.redirectToHome();
    }, this.data.splashDuration);
  },

  // 跳转到首页
  redirectToHome() {
    console.log('🏠 跳转到首页');

    // 清除定时器
    if (this.redirectTimer) {
      clearTimeout(this.redirectTimer);
      this.redirectTimer = null;
    }

    // 添加页面切换动画
    wx.reLaunch({
      url: '/pages/index/index',
      success: () => {
        console.log('✅ 成功跳转到首页');
      },
      fail: (error) => {
        console.error('❌ 跳转首页失败:', error);
        // 备用跳转方式
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    });
  },

  // 点击跳过
  onSkip() {
    console.log('⏭️ 用户点击跳过');
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
    this.redirectToHome();
  },

  // 页面显示
  onShow() {
    console.log('👁️ 启动页显示');
  },

  // 页面隐藏
  onHide() {
    console.log('👁️‍🗨️ 启动页隐藏');
    // 清除定时器
    if (this.redirectTimer) {
      clearTimeout(this.redirectTimer);
      this.redirectTimer = null;
    }
  },

  // 页面卸载
  onUnload() {
    console.log('🔚 启动页卸载');
    // 清除定时器
    if (this.redirectTimer) {
      clearTimeout(this.redirectTimer);
      this.redirectTimer = null;
    }
  }
});
