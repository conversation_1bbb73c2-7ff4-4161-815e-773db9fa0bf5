// 仲裁处理页面
import API from '../../../utils/api.js';

const app = getApp();

Page({
  data: {
    orderId: '',
    orderInfo: null,
    disputeInfo: null,
    mode: 'apply', // apply: 申请仲裁, view: 查看仲裁
    loading: true,
    submitting: false,
    
    // 仲裁原因选项
    disputeReasons: [
      { value: 'incomplete', label: '任务未完成', description: '接单者未按要求完成任务' },
      { value: 'poor_quality', label: '服务质量差', description: '服务质量不符合预期' },
      { value: 'not_as_described', label: '与描述不符', description: '实际服务与订单描述不符' },
      { value: 'fake_proof', label: '证明材料造假', description: '提交的完成证明存在造假' },
      { value: 'communication', label: '沟通问题', description: '接单者沟通态度恶劣或不配合' },
      { value: 'other', label: '其他原因', description: '其他需要仲裁的问题' }
    ],
    selectedReason: '',
    customReason: '',
    description: '',
    evidenceImages: [],
    
    // 仲裁状态
    disputeStatus: {
      'pending': '待处理',
      'investigating': '调查中',
      'resolved': '已解决',
      'rejected': '已驳回'
    }
  },

  onLoad(options) {
    if (options.orderId) {
      this.setData({
        orderId: options.orderId,
        mode: options.mode || 'apply'
      });
      this.loadOrderInfo();
    } else {
      wx.showToast({
        title: '订单ID缺失',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单信息
  async loadOrderInfo() {
    try {
      this.setData({ loading: true });
      
      const result = await API.getOrderDetail(this.data.orderId);
      
      if (result.success && result.data) {
        const orderInfo = result.data;
        
        // 检查用户权限
        const currentUser = app.globalData.userInfo;
        if (!currentUser || currentUser._id !== orderInfo.customerId) {
          wx.showModal({
            title: '权限不足',
            content: '只有订单发布者可以申请仲裁',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
          return;
        }
        
        this.setData({
          orderInfo: orderInfo,
          disputeInfo: orderInfo.disputeInfo || null,
          loading: false
        });
        
        // 如果是查看模式且没有仲裁信息，切换到申请模式
        if (this.data.mode === 'view' && !orderInfo.disputeInfo) {
          this.setData({ mode: 'apply' });
        }
        
      } else {
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载订单信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 选择仲裁原因
  selectReason(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      selectedReason: value
    });
  },

  // 输入自定义原因
  onCustomReasonInput(e) {
    this.setData({
      customReason: e.detail.value
    });
  },

  // 输入详细描述
  onDescriptionInput(e) {
    this.setData({
      description: e.detail.value
    });
  },

  // 选择证据图片
  chooseEvidenceImages() {
    const maxCount = 9 - this.data.evidenceImages.length;
    
    if (maxCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseMedia({
      count: maxCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFiles = res.tempFiles.map(file => ({
          tempFilePath: file.tempFilePath,
          size: file.size
        }));
        
        this.setData({
          evidenceImages: [...this.data.evidenceImages, ...tempFiles]
        });
      }
    });
  },

  // 删除证据图片
  removeEvidenceImage(e) {
    const { index } = e.currentTarget.dataset;
    const evidenceImages = [...this.data.evidenceImages];
    evidenceImages.splice(index, 1);
    this.setData({
      evidenceImages
    });
  },

  // 预览证据图片
  previewEvidenceImage(e) {
    const { index } = e.currentTarget.dataset;
    const urls = this.data.evidenceImages.map(img => img.tempFilePath || img.fileID);
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  // 提交仲裁申请
  async submitDispute() {
    // 验证表单
    if (!this.data.selectedReason) {
      wx.showToast({
        title: '请选择仲裁原因',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.selectedReason === 'other' && !this.data.customReason.trim()) {
      wx.showToast({
        title: '请输入自定义原因',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.description.trim()) {
      wx.showToast({
        title: '请详细描述问题',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.submitting) return;
    
    this.setData({ submitting: true });
    
    try {
      app.utils.showLoading('提交中...');
      
      // 上传证据图片
      let evidenceUrls = [];
      if (this.data.evidenceImages.length > 0) {
        evidenceUrls = await this.uploadEvidenceImages();
      }
      
      // 准备仲裁原因
      const reason = this.data.selectedReason === 'other' 
        ? this.data.customReason.trim()
        : this.data.disputeReasons.find(r => r.value === this.data.selectedReason)?.label;
      
      // 提交仲裁申请
      const disputeReason = `${reason}: ${this.data.description.trim()}`;
      const result = await API.disputeOrder(this.data.orderId, disputeReason);
      
      if (result.success) {
        app.utils.showSuccess('仲裁申请已提交');
        
        // 延迟返回
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        app.utils.showError(result.error || '提交失败');
      }
      
    } catch (error) {
      console.error('提交仲裁申请失败:', error);
      app.utils.showError('提交失败');
    } finally {
      app.utils.hideLoading();
      this.setData({ submitting: false });
    }
  },

  // 上传证据图片
  async uploadEvidenceImages() {
    const uploadPromises = this.data.evidenceImages.map(async (image, index) => {
      try {
        const cloudPath = `dispute-evidence/${this.data.orderId}/${Date.now()}_${index}.jpg`;
        
        const result = await wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: image.tempFilePath
        });
        
        return {
          fileID: result.fileID,
          size: image.size
        };
      } catch (error) {
        console.error('上传证据图片失败:', error);
        throw error;
      }
    });
    
    return await Promise.all(uploadPromises);
  },

  // 格式化时间
  formatTime(time) {
    if (!time) return '';
    const date = new Date(time);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }
});
