// wallet.js - 科技感钱包页面
const app = getApp();
const API = require('../../utils/api');

Page({
  data: {
    loading: true,
    navLoading: false,
    userInfo: {
      balance: 0,
      frozenBalance: 0
    },
    stats: {
      totalIncome: '0.00',
      totalExpense: '0.00',
      transactionCount: 0
    },
    recentTransactions: []
  },

  onLoad(options) {
    console.log('🔄 钱包页面 - 页面加载');

    // 保存原始方法
    this.originalShowLoading = app.utils?.showLoading;
    this.originalWxShowLoading = wx.showLoading;

    // 临时禁用所有loading显示
    if (app.utils) {
      app.utils.showLoading = () => {
        console.log('🚫 [钱包页面] 阻止app.utils.showLoading调用');
      };
    }

    wx.showLoading = () => {
      console.log('🚫 [钱包页面] 阻止wx.showLoading调用');
    };

    // 强制隐藏现有loading
    wx.hideLoading();

    this.loadWalletData();

    // 页面加载完成后恢复原始方法
    setTimeout(() => {
      if (app.utils && this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      wx.showLoading = this.originalWxShowLoading;
    }, 1000);
  },

  onShow() {
    console.log('🔄 钱包页面 - 页面显示');

    // 临时禁用所有loading显示
    if (app.utils) {
      app.utils.showLoading = () => {
        console.log('🚫 [钱包页面显示] 阻止app.utils.showLoading调用');
      };
    }

    wx.showLoading = () => {
      console.log('🚫 [钱包页面显示] 阻止wx.showLoading调用');
    };

    // 强制隐藏现有loading
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);

    // 每次显示页面时刷新数据
    this.loadWalletData();

    // 页面显示完成后恢复原始方法
    setTimeout(() => {
      if (app.utils && this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      if (this.originalWxShowLoading) {
        wx.showLoading = this.originalWxShowLoading;
      }
    }, 800);
  },

  onUnload() {
    // 确保页面卸载时恢复原始方法
    if (app.utils && this.originalShowLoading) {
      app.utils.showLoading = this.originalShowLoading;
    }
    if (this.originalWxShowLoading) {
      wx.showLoading = this.originalWxShowLoading;
    }
  },

  onPullDownRefresh() {
    console.log('🔄 钱包页面 - 下拉刷新');
    this.loadWalletData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载钱包数据
  async loadWalletData() {
    try {
      this.setData({ loading: true });

      // 并行加载用户信息、统计数据和最近交易
      const [userPromise, statsPromise, transactionsPromise] = await Promise.allSettled([
        this.loadUserInfo(),
        this.loadWalletStats(),
        this.loadRecentTransactions()
      ]);

      // 处理Promise.allSettled的结果
      const userResult = userPromise.status === 'fulfilled' ? userPromise.value : null;
      const statsResult = statsPromise.status === 'fulfilled' ? statsPromise.value : null;
      const transactionsResult = transactionsPromise.status === 'fulfilled' ? transactionsPromise.value : null;

      console.log('✅ 钱包页面 - 数据加载完成', {
        userResult: userResult ? '成功' : '失败',
        statsResult: statsResult ? '成功' : '失败',
        transactionsResult: transactionsResult ? '成功' : '失败'
      });

    } catch (error) {
      console.error('❌ 钱包页面 - 数据加载失败:', error);
      app.utils.showError('加载钱包数据失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 先从本地存储获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({
          userInfo: {
            ...userInfo,
            balance: userInfo.balance || 0,
            frozenBalance: userInfo.frozenBalance || 0
          }
        });
      }

      // 获取最新的用户信息
      const result = await API.getUserInfo({ showLoading: false });
      if (result.success) {
        this.setData({
          userInfo: {
            ...result.data,
            balance: result.data.balance || 0,
            frozenBalance: result.data.frozenBalance || 0
          }
        });

        // 更新本地存储
        wx.setStorageSync('userInfo', result.data);
        return result;
      } else {
        throw new Error(result.error || '获取用户信息失败');
      }
    } catch (error) {
      console.error('❌ 钱包页面 - 加载用户信息失败:', error);
      throw error;
    }
  },

  // 加载钱包统计数据
  async loadWalletStats() {
    try {
      console.log('🔄 钱包页面 - 开始加载钱包统计数据');
      const result = await API.getWalletStats({ showLoading: false });

      if (result && result.success) {
        console.log('✅ 钱包页面 - 钱包统计数据加载成功:', result.data);
        this.setData({
          stats: {
            totalIncome: result.data.totalIncome || '0.00',
            totalExpense: result.data.totalExpense || '0.00',
            transactionCount: result.data.transactionCount || 0
          }
        });
        return result;
      } else {
        throw new Error('钱包统计API返回失败');
      }
    } catch (error) {
      console.error('❌ 钱包页面 - 加载钱包统计失败:', error);

      // 检查是否是云函数不存在的错误
      if (error.message && error.message.includes('FunctionName parameter could not be found')) {
        console.log('⚠️ 钱包页面 - 云函数getWalletStats不存在，使用默认数据');
      }

      // 使用默认数据，确保页面正常显示
      this.setData({
        stats: {
          totalIncome: '0.00',
          totalExpense: '0.00',
          transactionCount: 0
        }
      });

      return {
        success: false,
        error: error.message,
        data: this.data.stats
      };
    }
  },

  // 加载最近交易记录
  async loadRecentTransactions() {
    try {
      const result = await API.getTransactionList({
        page: 1,
        pageSize: 5, // 只获取最近5条记录
        type: 'all',
        showLoading: false
      });

      if (result.success) {
        const transactions = result.data.list || [];

        // 格式化交易记录
        const formattedTransactions = transactions.map(item => ({
          ...item,
          typeText: this.getTransactionTypeText(item.type),
          statusText: this.getTransactionStatusText(item.status),
          createTimeText: this.formatTime(item.createTime)
        }));

        this.setData({
          recentTransactions: formattedTransactions
        });

        // 如果有统计数据，也更新统计
        if (result.data.stats) {
          this.setData({
            stats: {
              totalIncome: result.data.stats.totalIncome || '0.00',
              totalExpense: result.data.stats.totalExpense || '0.00',
              transactionCount: result.data.stats.transactionCount || 0
            }
          });
        }

        return result;
      } else {
        // 如果API不存在，返回空数据
        this.setData({ recentTransactions: [] });
        return { success: true, data: { list: [] } };
      }
    } catch (error) {
      console.error('❌ 钱包页面 - 加载最近交易失败:', error);
      // 返回空数据，不抛出错误
      this.setData({ recentTransactions: [] });
      return { success: true, data: { list: [] } };
    }
  },

  // 获取交易类型文本
  getTransactionTypeText(type) {
    const typeMap = {
      'recharge': '充值',
      'withdraw': '提现',
      'income': '收入',
      'payment': '支付',
      'refund': '退款'
    };
    return typeMap[type] || '其他';
  },

  // 获取交易状态文本
  getTransactionStatusText(status) {
    const statusMap = {
      'pending': '处理中',
      'completed': '已完成',
      'failed': '失败',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知';
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    // 如果是今天
    if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
      return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
    
    // 如果是昨天
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth()) {
      return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
    
    // 其他情况显示完整日期
    return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  },

  // 刷新余额
  async refreshBalance() {
    try {
      this.setData({ navLoading: true });
      await this.loadUserInfo();
      app.utils.showSuccess('余额已刷新');
    } catch (error) {
      console.error('❌ 钱包页面 - 刷新余额失败:', error);
      app.utils.showError('刷新失败');
    } finally {
      this.setData({ navLoading: false });
    }
  },

  // 导航到充值页面
  navigateToRecharge() {
    wx.navigateTo({
      url: '/pages/payment/recharge/recharge'
    });
  },

  // 导航到提现页面
  navigateToWithdraw() {
    wx.navigateTo({
      url: '/pages/payment/withdraw/withdraw'
    });
  },

  // 导航到交易记录页面
  navigateToRecords() {
    wx.navigateTo({
      url: '/pages/payment/records/records'
    });
  },

  // 查看交易详情
  viewTransactionDetail(e) {
    const { id } = e.currentTarget.dataset;
    if (id) {
      // 暂时跳转到交易记录页面
      wx.navigateTo({
        url: `/pages/payment/records/records?highlightId=${id}`
      });
    }
  },


});
