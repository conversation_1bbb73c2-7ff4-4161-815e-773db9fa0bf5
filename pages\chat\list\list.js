/**
 * 聊天列表页面 - 重写版
 */
const API = require('../../../utils/api.js');

const app = getApp();

Page({
  data: {
    chatList: [],
    loading: false,
    refreshing: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    userInfo: null,
    stats: null,
    themeLoaded: true, // 立即设置主题加载状态，避免白色背景闪烁
    // 实时监听相关
    messageWatcher: null,
    isWatcherActive: false,
    // 聊天室监听相关
    chatRoomWatcher: null,
    isChatRoomWatcherActive: false
  },

  // 防抖定时器
  loadingTimer: null,
  // 刷新检查定时器
  refreshChecker: null,

  onLoad() {
    // 立即隐藏任何可能的系统加载提示
    wx.hideLoading();
    // 立即设置主题加载状态，避免白色背景闪烁
    this.setData({ themeLoaded: true });
    this.initUserInfo();

    // 初始化未读消息监听
    this.initUnreadMessageListener();

    // 监听全局撤回事件
    this.setupRecallListener();

    // 监听离线消息检测事件
    this.setupOfflineMessageListener();
  },

  onShow() {
    console.log('=== 聊天列表页面显示 ===');
    // 确保隐藏任何可能的系统加载提示
    wx.hideLoading();

    // 检查是否需要刷新
    const app = getApp();
    const needRefresh = app.globalData.needRefreshChatList;
    if (needRefresh) {
      console.log('🔄 [全局刷新] 检测到需要刷新聊天列表');
      app.globalData.needRefreshChatList = false;

      // 静默刷新：不显示加载状态，直接更新数据
      console.log('🔄 [静默刷新] 静默更新聊天列表数据');
      this.loadChatListSilently();
    } else if (this.data.chatList.length === 0) {
      // 如果没有数据且不是刷新场景，进行初始加载
      console.log('🔄 [初始加载] 页面无数据，开始加载');
      this.loadChatList(true);
    } else {
      console.log('✅ [页面显示] 已有数据，无需重新加载');
    }

    // 更新 tabBar 选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected();
    }

    // 更新未读状态（确保页面显示时未读数正确）
    if (this.data.chatList.length > 0) {
      this.updateAllUnreadStatus();
    }

    // 启动实时监听
    if (this.data.userInfo) {
      console.log('🚀 [聊天列表] 准备启动实时监听器');

      // 启动消息监听器
      if (!this.data.isWatcherActive) {
        this.startMessageWatcher();
      } else {
        console.log('🚀 [聊天列表] 消息监听器已启动，跳过');
      }

      // 启动聊天室监听器
      if (!this.data.isChatRoomWatcherActive) {
        this.startChatRoomWatcher();
      } else {
        console.log('🚀 [聊天列表] 聊天室监听器已启动，跳过');
      }
    } else {
      console.log('🚀 [聊天列表] 跳过启动监听器 - 无用户信息');
    }

    // 启动定期检查刷新标志的定时器
    this.startRefreshChecker();
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
      console.log('✅ [用户信息] 用户:', userInfo._id, userInfo.nickName);
    } else {
      console.log('❌ [用户信息] 用户信息不存在，跳转到登录页');
      wx.redirectTo({
        url: '/pages/auth/login/login'
      });
    }
  },

  // 加载聊天列表
  async loadChatList(refresh = false) {
    // 防抖机制：清除之前的定时器
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
      this.loadingTimer = null;
    }

    if (this.data.loading) {
      return;
    }

    const { userInfo } = this.data;
    if (!userInfo) {
      return;
    }

    // 直接进行网络请求加载数据

    try {
      // 确保隐藏任何可能的系统加载提示
      wx.hideLoading();
      this.setData({
        loading: true,
        refreshing: refresh
      });

      const page = refresh ? 1 : this.data.page;

      // 禁用系统加载提示，使用页面内的加载状态
      const result = await API.callFunction('chatList', {
        page: page,
        pageSize: this.data.pageSize,
        status: 'active'
      }, { showLoading: false });

      if (result.success) {
        const newChatList = result.data.list || [];
        const stats = result.data.stats || {};

        // 处理数据
        let chatList;
        if (refresh || page === 1) {
          chatList = newChatList;
        } else {
          chatList = [...this.data.chatList, ...newChatList];
        }



        // 应用全局撤回状态保护
        chatList = this.applyRecallProtection(chatList);

        // 先更新页面显示，确保用户立即看到数据
        this.setData({
          chatList: chatList,
          page: page,
          hasMore: stats.hasMore || false,
          stats: stats
        });

        // 更新未读状态
        this.updateAllUnreadStatus();

        // 只在真正需要时才进行后台修复
        const needsFix = chatList.some(chat =>
          !chat.lastMessage ||
          !chat.lastMessageDisplay ||
          chat.lastMessageDisplay === '💬 开始聊天吧'
        );

        if (needsFix) {
          setTimeout(() => {
            this.fixChatListDisplaySilently(chatList);
          }, 100);
        }

      } else {
        console.error('❌ [聊天列表] 加载失败:', result.error);
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('❌ [聊天列表] 加载异常:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      // 确保隐藏系统加载提示和页面加载状态
      wx.hideLoading();
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  // 静默加载聊天列表（不显示加载状态）
  async loadChatListSilently() {
    const { userInfo } = this.data;
    if (!userInfo) {
      return;
    }

    try {
      // 静默更新聊天列表数据

      // 完全静默的API调用，不显示任何加载提示
      const result = await API.callFunction('chatList', {
        page: 1,
        pageSize: this.data.pageSize,
        status: 'active'
      }, { showLoading: false });

      if (result.success) {
        const newChatList = result.data.list || [];
        const stats = result.data.stats || {};

        // 智能合并数据：保留现有数据的完整性，只更新有变化的部分
        const mergedChatList = this.smartMergeChatList(this.data.chatList, newChatList);

        // 智能合并完成

        // 直接更新数据，不显示任何加载状态
        this.setData({
          chatList: mergedChatList,
          page: 1,
          hasMore: stats.hasMore || false,
          stats: stats
        });

        // 更新未读状态
        this.updateAllUnreadStatus();

        // 页面数据已更新

      } else {
        console.error('❌ [静默加载] 加载失败:', result.error);
        // 静默失败，不显示错误提示，保持现有数据
      }

    } catch (error) {
      console.error('❌ [静默加载] 加载异常:', error);
      // 静默失败，不显示错误提示，保持现有数据
    }
  },

  // 智能合并聊天列表数据
  smartMergeChatList(oldList, newList) {
    // 智能合并聊天列表数据

    // 如果旧列表为空，直接返回新列表
    if (!oldList || oldList.length === 0) {
      return newList;
    }

    // 创建旧数据的映射，以便快速查找
    const oldChatMap = new Map();
    oldList.forEach(chat => {
      oldChatMap.set(chat._id, chat);
    });

    // 合并数据：优先使用新数据，但如果新数据的lastMessage不完整，则保留旧数据
    const mergedList = newList.map((newChat, index) => {
      const oldChat = oldChatMap.get(newChat._id);

      // 处理聊天室合并

      // 如果没有旧数据，直接使用新数据，但确保设置必要的UI字段
      if (!oldChat) {
        console.log(`✅ [智能合并] 聊天室 ${newChat._id} 无旧数据，使用新数据`);
        return {
          ...newChat,
          chatRoomId: newChat._id,
          unreadCount: 0
        };
      }

      // 检查新数据的lastMessage是否完整
      // 注意：[消息已撤回] 是有效的完整消息，不应被视为不完整
      const newMessageIncomplete = !newChat.lastMessage ||
        !newChat.lastMessageDisplay ||
        (newChat.lastMessageDisplay === '💬 开始聊天吧');

      const oldMessageComplete = oldChat.lastMessage &&
        oldChat.lastMessageDisplay &&
        oldChat.lastMessageDisplay !== '💬 开始聊天吧';

      // 特殊处理：如果新数据是撤回消息，应该优先使用新数据
      const isNewMessageRecalled = newChat.lastMessageDisplay === '[消息已撤回]' ||
        (newChat.lastMessage && newChat.lastMessage.type === 'recalled');

      // 检查全局撤回状态（必须先定义，后面会用到）
      const globalRecallStatus = getApp().globalData.recalledMessages.isRecalled(newChat._id);

      // 调试日志：如果检测到撤回状态，记录详细信息
      if (globalRecallStatus) {
        console.log(`🚨 [智能合并] 检测到撤回状态 - 聊天室: ${newChat._id}`);
        console.log(`🚨 [智能合并] 撤回内容: ${globalRecallStatus.content}`);
        console.log(`🚨 [智能合并] 撤回时间: ${new Date(globalRecallStatus.timestamp)}`);
        console.log(`🚨 [智能合并] 新消息内容: ${newChat.lastMessageDisplay}`);
        console.log(`🚨 [智能合并] 旧消息内容: ${oldChat?.lastMessageDisplay}`);
      }

      // 检查新消息是否真的是撤回消息（从数据库返回的撤回状态）
      const isNewMessageActuallyRecalled = newChat.lastMessage &&
        (newChat.lastMessage.type === 'recalled' || newChat.lastMessage.isRecalled) &&
        newChat.lastMessageDisplay === '[消息已撤回]';

      // 检查旧数据是否有强制撤回标记或撤回内容
      // 但如果全局撤回状态不存在，说明撤回状态已被清除，不应该保护旧的撤回状态
      const oldHasRecallMark = !globalRecallStatus ? false : (
        (oldChat._forceRecalled && (Date.now() - oldChat._recallTime < 30000)) || // 30秒内的撤回标记有效
        oldChat.lastMessageDisplay === '[消息已撤回]' // 或者内容就是撤回状态
      );

      // 检查全局撤回保护标记
      const globalRecallProtection = getApp().globalData.disableSmartMergeForRoom === newChat._id;

      // 最高优先级：新消息确实是撤回消息（从数据库返回的真实撤回状态）
      if (isNewMessageActuallyRecalled) {
        console.log(`✅ [智能合并] 聊天室 ${newChat._id} 检测到真实撤回消息，使用新数据`);
        return {
          ...newChat,
          // 保留重要的UI状态字段
          chatRoomId: oldChat.chatRoomId || oldChat._id,
          unreadCount: oldChat.unreadCount || 0
        };
      }

      // 次高优先级：全局撤回状态（优先保护撤回状态）
      if (globalRecallStatus) {
        // 检查新消息是否是撤回消息本身
        const isNewMessageRecalledContent = newChat.lastMessageDisplay === '[消息已撤回]' ||
          (newChat.lastMessage && newChat.lastMessage.type === 'recalled');

        if (isNewMessageRecalledContent) {
          // 如果新消息就是撤回消息，应用撤回状态
          console.log(`🔒 [智能合并] 聊天室 ${newChat._id} 新消息是撤回消息，应用撤回状态`);
          return {
            ...newChat,
            lastMessage: {
              content: globalRecallStatus.content,
              type: 'recalled',
              createTime: new Date(globalRecallStatus.timestamp)
            },
            lastMessageDisplay: globalRecallStatus.content,
            _globalRecalled: true,
            _recallTime: globalRecallStatus.timestamp,
            // 保留重要的UI状态字段
            chatRoomId: oldChat.chatRoomId || oldChat._id,
            unreadCount: oldChat.unreadCount || 0
          };
        }

        // 检查是否应该保护撤回状态（关键修复）
        // 如果旧消息已经是撤回状态，且全局撤回状态仍然有效，则保护撤回状态
        if (oldChat && oldChat.lastMessageDisplay === '[消息已撤回]') {
          console.log(`🛡️ [智能合并] 聊天室 ${newChat._id} 保护现有撤回状态，忽略新消息`);
          return {
            ...newChat,
            lastMessage: {
              content: globalRecallStatus.content,
              type: 'recalled',
              createTime: new Date(globalRecallStatus.timestamp)
            },
            lastMessageDisplay: globalRecallStatus.content,
            _globalRecalled: true,
            _recallTime: globalRecallStatus.timestamp,
            // 保留重要的UI状态字段
            chatRoomId: oldChat.chatRoomId || oldChat._id,
            unreadCount: oldChat.unreadCount || 0
          };
        }

        // 使用时间比较来决定是否应用撤回状态
        const newMessageTime = newChat.lastMessage ? newChat.lastMessage.createTime : null;
        if (newMessageTime && getApp().globalData.recalledMessages.isRecallNewerThan(newChat._id, newMessageTime)) {
          console.log(`🔒 [智能合并] 聊天室 ${newChat._id} 应用全局撤回状态（撤回时间更新）`);
          return {
            ...newChat,
            lastMessage: {
              content: globalRecallStatus.content,
              type: 'recalled',
              createTime: new Date(globalRecallStatus.timestamp)
            },
            lastMessageDisplay: globalRecallStatus.content,
            _globalRecalled: true,
            _recallTime: globalRecallStatus.timestamp,
            // 保留重要的UI状态字段
            chatRoomId: oldChat.chatRoomId || oldChat._id,
            unreadCount: oldChat.unreadCount || 0
          };
        } else if (newMessageTime) {
          // 只有当新消息确实不是撤回消息且比撤回时间更新时，才清除撤回状态
          const isReallyNewerMessage = newMessageTime > globalRecallStatus.timestamp;
          if (isReallyNewerMessage && !isNewMessageRecalledContent) {
            console.log(`🔄 [智能合并] 聊天室 ${newChat._id} 新消息比撤回状态更新且非撤回消息，清除撤回状态`);
            getApp().globalData.recalledMessages.clear(newChat._id);
          } else {
            console.log(`🛡️ [智能合并] 聊天室 ${newChat._id} 保护撤回状态，新消息可能是过期数据`);
            return {
              ...newChat,
              lastMessage: {
                content: globalRecallStatus.content,
                type: 'recalled',
                createTime: new Date(globalRecallStatus.timestamp)
              },
              lastMessageDisplay: globalRecallStatus.content,
              _globalRecalled: true,
              _recallTime: globalRecallStatus.timestamp,
              // 保留重要的UI状态字段
              chatRoomId: oldChat.chatRoomId || oldChat._id,
              unreadCount: oldChat.unreadCount || 0
            };
          }
        }
      }

      // 第三优先级：全局撤回保护（临时保护机制）
      if (globalRecallProtection) {
        const forceRecallDisplay = getApp().globalData.forceRecallDisplay || '[消息已撤回]';
        console.log(`🚀 [智能合并] 聊天室 ${newChat._id} 全局撤回保护激活:`, forceRecallDisplay);
        return {
          ...newChat,
          lastMessage: {
            content: forceRecallDisplay,
            type: 'recalled',
            createTime: new Date()
          },
          lastMessageDisplay: forceRecallDisplay,
          _forceRecalled: true,
          _recallTime: Date.now(),
          // 保留重要的UI状态字段
          chatRoomId: oldChat.chatRoomId || oldChat._id,
          unreadCount: oldChat.unreadCount || 0
        };
      }

      // 第四优先级：如果旧数据有撤回标记且全局撤回状态仍然有效，保护撤回状态
      if (oldHasRecallMark && globalRecallStatus) {
        // 验证旧撤回状态是否仍然有效（时间检查）
        const oldRecallTime = oldChat._recallTime || 0;
        const newMessageTime = newChat.lastMessage ? new Date(newChat.lastMessage.createTime).getTime() : 0;

        if (oldRecallTime > newMessageTime) {
          console.log(`🛡️ [智能合并] 聊天室 ${newChat._id} 保护有效撤回状态:`, oldChat.lastMessageDisplay);
          return {
            ...newChat,
            lastMessage: oldChat.lastMessage,
            lastMessageDisplay: oldChat.lastMessageDisplay,
            lastMessageTime: oldChat.lastMessageTime,
            _forceRecalled: oldChat._forceRecalled,
            _recallTime: oldChat._recallTime,
            // 保留重要的UI状态字段
            chatRoomId: oldChat.chatRoomId || oldChat._id,
            unreadCount: oldChat.unreadCount || 0
          };
        } else {
          console.log(`🔄 [智能合并] 聊天室 ${newChat._id} 旧撤回状态已过期，使用新数据`);
        }
      }

      // 如果新数据不完整但旧数据完整，保留旧数据的消息信息
      if (newMessageIncomplete && oldMessageComplete) {
        // 保留旧消息数据
        return {
          ...newChat,
          lastMessage: oldChat.lastMessage,
          lastMessageDisplay: oldChat.lastMessageDisplay,
          lastMessageTime: oldChat.lastMessageTime,
          // 保留重要的UI状态字段
          chatRoomId: oldChat.chatRoomId || oldChat._id,
          unreadCount: oldChat.unreadCount || 0
        };
      }

      // 否则使用新数据，但保留UI状态字段
      // 使用新数据
      return {
        ...newChat,
        // 确保保留重要的UI状态字段
        chatRoomId: oldChat?.chatRoomId || newChat._id,
        unreadCount: oldChat?.unreadCount || 0
      };
    });

    // 合并完成
    return mergedList;
  },

  // 进入聊天室
  enterChatRoom(e) {
    const { id, orderno } = e.currentTarget.dataset;
    
    if (!id) {
      wx.showToast({
        title: '聊天室ID无效',
        icon: 'none'
      });
      return;
    }

    console.log('🚀 [页面跳转] 进入聊天室:', id, '订单:', orderno);

    wx.navigateTo({
      url: `/chat-package/pages/room/room?roomId=${id}&orderNo=${orderno || ''}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('🔄 [下拉刷新] 刷新聊天列表');
    this.loadChatList(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      console.log('📥 [上拉加载] 加载更多聊天记录');
      this.setData({
        page: this.data.page + 1
      });
      this.loadChatList(false);
    }
  },



  // 显示空状态操作
  showEmptyActions() {
    wx.showActionSheet({
      itemList: ['查看订单大厅', '刷新列表'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 跳转到订单大厅
          wx.switchTab({
            url: '/pages/order/hall/hall'
          });
        } else if (res.tapIndex === 1) {
          // 刷新列表
          this.setData({
            chatList: [],
            page: 1,
            hasMore: true,
            loading: false
          });
          this.loadChatList(true);
        }
      }
    });
  },

  // 长按聊天项
  onChatItemLongPress(e) {
    const { id } = e.currentTarget.dataset;

    wx.showActionSheet({
      itemList: ['删除聊天', '彻底删除', '清空消息', '查看订单详情', '强制刷新'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.deleteChatRoom(id, 'soft');
        } else if (res.tapIndex === 1) {
          this.deleteChatRoom(id, 'hard');
        } else if (res.tapIndex === 2) {
          this.deleteChatRoom(id, 'clear');
        } else if (res.tapIndex === 3) {
          this.viewOrderDetail(id);
        } else if (res.tapIndex === 4) {
          this.setData({
            chatList: [],
            page: 1,
            hasMore: true,
            loading: false
          });
          this.loadChatList(true);
        }
      }
    });
  },

  // 删除聊天室
  deleteChatRoom(chatRoomId, action = 'soft') {
    let title = '删除聊天';
    let content = '删除后聊天将从列表中移除，确定要删除吗？';
    let confirmText = '删除';

    if (action === 'hard') {
      title = '彻底删除';
      content = '彻底删除将永久删除聊天室和所有消息，无法恢复！确定要删除吗？';
      confirmText = '彻底删除';
    } else if (action === 'clear') {
      title = '清空消息';
      content = '清空后聊天记录将无法恢复，但聊天室会保留，确定要清空吗？';
      confirmText = '清空';
    }

    wx.showModal({
      title: title,
      content: content,
      confirmText: confirmText,
      confirmColor: '#ff3b30',
      success: async (res) => {
        if (res.confirm) {
          await this.performDeleteChatRoom(chatRoomId, action);
        }
      }
    });
  },

  // 执行删除聊天室操作
  async performDeleteChatRoom(chatRoomId, action = 'soft') {
    try {
      let loadingText = '删除中...';
      if (action === 'hard') {
        loadingText = '彻底删除中...';
      } else if (action === 'clear') {
        loadingText = '清空中...';
      }

      wx.showLoading({
        title: loadingText,
        mask: true
      });

      console.log('🗑️ [删除聊天室] 开始删除:', chatRoomId, '操作类型:', action);

      // 使用新的云函数
      const result = await API.simpleDeleteChat(chatRoomId, action);

      wx.hideLoading();

      if (result.success) {
        console.log('✅ [删除聊天室] 删除成功:', result.data);

        // 根据操作类型处理前端显示
        if (action === 'hard' || action === 'soft') {
          // 硬删除和软删除：从列表中移除
          const chatList = this.data.chatList.filter(chat => chat._id !== chatRoomId);
          this.setData({ chatList });
          this.updateStatsAfterDelete();
        } else if (action === 'clear') {
          // 清空消息：更新聊天室显示，但保留在列表中
          const chatList = this.data.chatList.map(chat => {
            if (chat._id === chatRoomId) {
              return {
                ...chat,
                lastMessage: null,
                lastMessageDisplay: '💬 开始聊天吧',
                lastMessageTime: new Date()
              };
            }
            return chat;
          });
          this.setData({ chatList });
        }

        wx.showToast({
          title: result.message || '操作成功',
          icon: 'success'
        });

      } else {
        console.error('❌ [删除聊天室] 删除失败:', result.error);
        wx.showToast({
          title: result.error || '操作失败',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('❌ [删除聊天室] 删除异常:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 更新删除后的统计信息
  updateStatsAfterDelete() {
    const { chatList } = this.data;
    const stats = {
      total: chatList.length,
      asCustomer: chatList.filter(chat => chat.userRole === 'customer').length,
      asAccepter: chatList.filter(chat => chat.userRole === 'accepter').length
    };

    this.setData({ stats });
    console.log('📊 [统计更新] 删除后统计:', stats);
  },



  // 查看订单详情
  viewOrderDetail(chatRoomId) {
    const chat = this.data.chatList.find(chat => chat._id === chatRoomId);
    if (chat && chat.orderNo) {
      wx.navigateTo({
        url: `/order-package/pages/detail/detail?id=${chat.orderNo}`
      });
    } else {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
    }
  },



  // 强制刷新聊天列表（用于从聊天室返回时调用）
  forceRefresh() {
    this.setData({
      chatList: [],
      page: 1,
      hasMore: true,
      loading: false
    });
    this.loadChatList(true);
  },

  // 静默修复聊天列表显示问题（完全后台处理，不影响用户界面）
  async fixChatListDisplaySilently(chatList) {
    // 找出需要修复的聊天室（lastMessageDisplay为默认值或空的）
    const problemChats = chatList.filter(chat =>
      !chat.lastMessage ||
      !chat.lastMessageDisplay ||
      chat.lastMessageDisplay === '💬 开始聊天吧' ||
      chat.lastMessageDisplay.includes('🔧 系统自动修复聊天室状态')
    );

    if (problemChats.length === 0) {
      return; // 没有需要修复的聊天室
    }

    // 并行修复所有问题聊天室，禁用所有系统加载提示
    const fixPromises = problemChats.map(async (chat) => {
      try {
        // 静默获取消息，不显示任何加载提示
        const messagesResult = await API.callFunction('chatMessage', {
          action: 'get',
          chatRoomId: chat._id,
          page: 1,
          pageSize: 10
        }, { showLoading: false });

        if (messagesResult.success && messagesResult.data.list.length > 0) {
          // 先按时间排序，确保最新的消息在前面
          const sortedMessages = messagesResult.data.list.sort((a, b) => {
            return new Date(b.createTime) - new Date(a.createTime);
          });

          // 优先选择最新的非系统修复消息
          let selectedMessage = sortedMessages.find(msg =>
            !msg.content.includes('🔧 系统自动修复聊天室状态') &&
            !msg.content.includes('🎉 接单成功！')
          );

          // 如果没有找到非系统消息，使用最新的消息（第一个）
          if (!selectedMessage) {
            selectedMessage = sortedMessages[0];
          }

          // 更新聊天室对象
          chat.lastMessage = {
            content: selectedMessage.content,
            type: selectedMessage.type,
            senderId: selectedMessage.senderId,
            createTime: selectedMessage.createTime
          };

          // 更新显示文本
          let displayContent = selectedMessage.content;

          // 特殊处理系统消息的显示
          if (selectedMessage.type === 'system') {
            if (displayContent.includes('🎉 接单成功！')) {
              displayContent = '🎉 接单成功！';
            } else if (displayContent.includes('🔧 系统自动修复')) {
              displayContent = '💬 开始聊天吧';
            }
          } else if (selectedMessage.type === 'image') {
            displayContent = '[图片]';
          } else if (selectedMessage.type === 'voice') {
            displayContent = '[语音]';
          }

          // 更新聊天室的lastMessageDisplay字段
          chat.lastMessageDisplay = displayContent.length > 30 ? displayContent.substring(0, 30) + '...' : displayContent;
          chat.lastMessageTime = selectedMessage.createTime;

          // 同时更新数据库中的lastMessage字段，确保下次加载时显示正确
          try {
            await wx.cloud.database().collection('chatRooms').doc(chat._id).update({
              data: {
                lastMessage: chat.lastMessage,
                updateTime: new Date()
              }
            });
            console.log(`✅ [数据库更新] 聊天室 ${chat._id} lastMessage更新成功`);
          } catch (dbError) {
            console.error(`❌ [数据库更新] 更新失败: ${chat._id}`, dbError);
          }

          return true;
        }
      } catch (error) {
        console.error(`❌ [静默修复] 修复聊天室 ${chat._id} 失败:`, error);
      }

      // 如果获取消息失败，设置默认显示
      if (!chat.lastMessage) {
        chat.lastMessage = {
          content: '💬 开始聊天吧',
          type: 'text',
          senderId: '',
          createTime: chat.createTime || new Date().toISOString()
        };
        chat.lastMessageDisplay = '💬 开始聊天吧';
        chat.lastMessageTime = chat.createTime || new Date().toISOString();
      }
      return false;
    });

    // 等待所有修复完成
    const results = await Promise.all(fixPromises);
    const successCount = results.filter(r => r).length;
    if (successCount > 0) {
      console.log(`✅ [静默修复] 修复完成，成功修复${successCount}个聊天室`);
      // 静默更新页面数据，不触发loading状态
      this.setData({
        chatList: [...chatList]
      });

      // 重新设置未读状态（因为setData会重置数据）
      console.log('🔄 [静默修复] 重新设置未读状态');
      this.updateAllUnreadStatus();
    }
  },

  // 在显示前修复聊天列表显示问题（后台处理，用户无感知）- 已弃用，保留用于兼容
  async fixChatListDisplayBeforeShow(chatList) {
    const problemChats = chatList.filter(chat => !chat.lastMessage);
    if (problemChats.length === 0) {
      return; // 没有需要修复的聊天室
    }

    console.log(`🔧 [后台修复] 发现${problemChats.length}个需要修复显示的聊天室`);

    // 并行修复所有问题聊天室
    const fixPromises = problemChats.map(async (chat) => {
      try {
        // 获取更多消息，以便找到最新的非系统消息
        const messagesResult = await API.getMessages(chat._id, 1, 10);
        if (messagesResult.success && messagesResult.data.list.length > 0) {
          // 先按时间排序，确保最新的消息在前面
          const sortedMessages = messagesResult.data.list.sort((a, b) => {
            return new Date(b.createTime) - new Date(a.createTime);
          });

          // 优先选择最新的非系统修复消息
          let selectedMessage = sortedMessages.find(msg =>
            !msg.content.includes('🔧 系统自动修复聊天室状态') &&
            !msg.content.includes('🎉 接单成功！')
          );

          // 如果没有找到非系统消息，使用最新的消息（第一个）
          if (!selectedMessage) {
            selectedMessage = sortedMessages[0];
          }

          // 更新聊天室对象
          chat.lastMessage = {
            content: selectedMessage.content,
            type: selectedMessage.type,
            senderId: selectedMessage.senderId,
            createTime: selectedMessage.createTime
          };

          // 更新显示文本
          let displayContent = selectedMessage.content;

          // 特殊处理系统消息的显示
          if (selectedMessage.type === 'system') {
            if (displayContent.includes('🎉 接单成功！')) {
              displayContent = '🎉 接单成功！';
            } else if (displayContent.includes('🔧 系统自动修复')) {
              displayContent = '💬 开始聊天吧';
            }
          }

          chat.lastMessageDisplay = displayContent.length > 30 ? displayContent.substring(0, 30) + '...' : displayContent;
          chat.lastMessageTime = selectedMessage.createTime;

          // 同时更新数据库中的lastMessage字段，确保下次加载时显示正确
          try {
            await wx.cloud.database().collection('chatRooms').doc(chat._id).update({
              data: {
                lastMessage: chat.lastMessage,
                updateTime: new Date()
              }
            });
          } catch (dbError) {
            console.error(`❌ [数据库更新] 更新失败: ${chat._id}`, dbError);
          }

          return true;
        }
      } catch (error) {
        console.error(`❌ [后台修复] 修复失败: ${chat._id}`, error);
        // 设置默认显示
        chat.lastMessageDisplay = '💬 开始聊天吧';
        chat.lastMessageTime = chat.createTime || new Date().toISOString();
      }
      return false;
    });

    // 等待所有修复完成
    const results = await Promise.all(fixPromises);
    const successCount = results.filter(r => r).length;

  },

  // 修复聊天列表显示问题（原有的实时修复方法，保留用于其他场景）
  async fixChatListDisplay(chatList) {
    const problemChats = chatList.filter(chat => !chat.lastMessage);
    if (problemChats.length === 0) {
      return;
    }

    // 批量获取这些聊天室的最新消息
    const fixPromises = problemChats.map(async (chat) => {
      try {
        // 获取更多消息，以便找到非系统消息
        const messagesResult = await API.getMessages(chat._id, 1, 10);
        if (messagesResult.success && messagesResult.data.list.length > 0) {
          // 优先选择非系统修复消息
          let selectedMessage = messagesResult.data.list.find(msg =>
            !msg.content.includes('🔧 系统自动修复聊天室状态') &&
            !msg.content.includes('🎉 接单成功！')
          );

          // 如果没有找到非系统消息，使用最新的消息
          if (!selectedMessage) {
            selectedMessage = messagesResult.data.list[0];
          }

          // 更新本地显示
          chat.lastMessage = {
            content: selectedMessage.content,
            type: selectedMessage.type,
            senderId: selectedMessage.senderId,
            createTime: selectedMessage.createTime
          };

          // 更新显示文本
          let displayContent = selectedMessage.content;

          // 特殊处理系统消息的显示
          if (selectedMessage.type === 'system') {
            if (displayContent.includes('🎉 接单成功！')) {
              displayContent = '🎉 接单成功！';
            } else if (displayContent.includes('🔧 系统自动修复')) {
              displayContent = '💬 开始聊天吧';
            }
          }

          chat.lastMessageDisplay = displayContent.length > 30 ? displayContent.substring(0, 30) + '...' : displayContent;
          chat.lastMessageTime = selectedMessage.createTime;

          console.log(`✅ [显示修复] 修复聊天室显示: ${chat._id} -> ${chat.lastMessageDisplay}`);
          return true;
        }
      } catch (error) {
        console.error(`❌ [显示修复] 修复失败: ${chat._id}`, error);
      }
      return false;
    });

    // 等待所有修复完成
    const results = await Promise.all(fixPromises);
    const fixedCount = results.filter(r => r).length;

    if (fixedCount > 0) {


      // 更新页面显示
      this.setData({
        chatList: chatList
      });
    }
  },

  // 启动消息实时监听
  startMessageWatcher() {
    if (this.data.isWatcherActive || !this.data.userInfo) {
      return;
    }

    try {
      const db = wx.cloud.database();

      console.log('🔄 [聊天列表监听] 创建监听器，用户ID:', this.data.userInfo._id);

      // 监听所有消息，但在处理时过滤当前用户相关的消息
      // 这样可以确保监听到新创建的聊天室的消息
      const watcher = db.collection('messages')
        .orderBy('createTime', 'desc')
        .limit(50) // 监听最新50条消息
        .watch({
          onChange: (snapshot) => {
            console.log('🔔 [聊天列表监听] 收到变更通知:', {
              type: snapshot.type,
              docChanges: snapshot.docChanges?.length || 0,
              isWatcherActive: this.data?.isWatcherActive,
              hasData: !!this.data
            });

            // 确保页面仍然存在且监听器仍然活跃
            if (this.data && this.data.isWatcherActive) {
              this.handleMessageChange(snapshot);
            } else {
              console.log('⚠️ [聊天列表监听] 忽略变更 - 页面状态不符合条件');
            }
          },
          onError: (error) => {
            console.error('❌ [聊天列表监听] 监听器错误:', error);

            // 更新状态
            if (this.data) {
              this.setData({
                isWatcherActive: false,
                messageWatcher: null
              });
            }

            // 监听失败时，延迟重新启动
            setTimeout(() => {
              if (this.data && !this.data.isWatcherActive) {
                this.startMessageWatcher();
              }
            }, 3000);
          }
        });

      // 保存监听器引用并更新状态
      this.setData({
        messageWatcher: watcher,
        isWatcherActive: true
      });



    } catch (error) {
      console.error('❌ [聊天列表监听] 启动监听器失败:', error);

      // 启动失败时重置状态
      this.setData({
        isWatcherActive: false,
        messageWatcher: null
      });
    }
  },

  // 启动聊天室实时监听
  startChatRoomWatcher() {
    if (!this.data.userInfo) {
      console.log('🚀 [聊天室监听] 跳过启动监听器 - 无用户信息');
      return;
    }

    // 如果已经有监听器在运行，先停止它
    if (this.data.isChatRoomWatcherActive) {
      console.log('🔄 [聊天室监听] 检测到已有监听器，先停止');
      this.stopChatRoomWatcher();
    }

    console.log('🔄 [聊天室监听] 启动聊天室监听器');

    try {
      const db = wx.cloud.database();

      // 监听用户相关的聊天室变更
      const chatRoomWatcher = db.collection('chatRooms')
        .where({
          $or: [
            { publisherId: this.data.userInfo._id },
            { accepterId: this.data.userInfo._id }
          ]
        })
        .orderBy('updateTime', 'desc')
        .limit(50)
        .watch({
          onChange: (snapshot) => {
            // 确保页面仍然存在且监听器仍然活跃
            if (this.data && this.data.isChatRoomWatcherActive) {
              this.handleChatRoomChange(snapshot);
            }
          },
          onError: (error) => {
            console.error('❌ [聊天室监听] 监听器错误:', error);

            // 更新状态
            if (this.data) {
              this.setData({
                isChatRoomWatcherActive: false,
                chatRoomWatcher: null
              });
            }

            // 监听失败时，延迟重新启动
            setTimeout(() => {
              if (this.data && !this.data.isChatRoomWatcherActive) {
                this.startChatRoomWatcher();
              }
            }, 3000);
          }
        });

      // 保存监听器引用并更新状态
      this.setData({
        chatRoomWatcher: chatRoomWatcher,
        isChatRoomWatcherActive: true
      });

      console.log('✅ [聊天室监听] 聊天室监听器启动成功');

    } catch (error) {
      console.error('❌ [聊天室监听] 启动监听器失败:', error);

      // 启动失败时重置状态
      this.setData({
        isChatRoomWatcherActive: false,
        chatRoomWatcher: null
      });
    }
  },

  // 停止消息监听器
  stopMessageWatcher() {
    if (!this.data) {
      return; // 页面已销毁
    }

    if (this.data.messageWatcher || this.data.isWatcherActive) {
      console.log('🛑 [聊天列表监听] 停止消息监听器');

      try {
        // 关闭监听器
        if (this.data.messageWatcher && typeof this.data.messageWatcher.close === 'function') {
          this.data.messageWatcher.close();
        }

        // 清理状态
        this.setData({
          messageWatcher: null,
          isWatcherActive: false
        });

        console.log('✅ [聊天列表监听] 监听器已停止');
      } catch (error) {
        console.error('❌ [聊天列表监听] 停止监听器失败:', error);

        // 即使出错也要清理状态
        try {
          this.setData({
            messageWatcher: null,
            isWatcherActive: false
          });
        } catch (setDataError) {
          console.error('❌ [聊天列表监听] 清理状态失败:', setDataError);
        }
      }
    }
  },

  // 停止聊天室监听器
  stopChatRoomWatcher() {
    if (!this.data) {
      return; // 页面已销毁
    }

    if (this.data.chatRoomWatcher || this.data.isChatRoomWatcherActive) {
      console.log('🛑 [聊天室监听] 停止聊天室监听器');

      try {
        // 关闭监听器
        if (this.data.chatRoomWatcher && typeof this.data.chatRoomWatcher.close === 'function') {
          this.data.chatRoomWatcher.close();
        }

        // 清理状态
        this.setData({
          chatRoomWatcher: null,
          isChatRoomWatcherActive: false
        });

        console.log('✅ [聊天室监听] 聊天室监听器已停止');
      } catch (error) {
        console.error('❌ [聊天室监听] 停止聊天室监听器失败:', error);

        // 即使出错也要清理状态
        try {
          this.setData({
            chatRoomWatcher: null,
            isChatRoomWatcherActive: false
          });
        } catch (setDataError) {
          console.error('❌ [聊天室监听] 清理聊天室监听器状态失败:', setDataError);
        }
      }
    }
  },

  // 处理消息变更事件
  handleMessageChange(snapshot) {
    console.log('📨 [聊天列表监听] 收到消息变更:', {
      type: snapshot.type,
      docChanges: snapshot.docChanges?.length || 0
    });

    // 如果是初始化数据，不处理（避免与loadChatList冲突）
    if (snapshot.type === 'init') {
      console.log('📋 [聊天列表监听] 跳过初始化数据');
      return;
    }

    // 检查是否有新消息
    let hasNewMessage = false;
    let messageDetails = [];

    snapshot.docChanges.forEach((change, index) => {
      console.log(`📋 [聊天列表监听] 消息变更 ${index + 1}:`, {
        queueType: change.queueType,
        dataType: change.dataType,
        docId: change.doc._id
      });

      if (change.queueType === 'enqueue' && change.dataType === 'add') {
        const message = change.doc;

        // 检查消息是否属于当前用户的聊天室
        const isUserRelatedMessage = this.isMessageRelatedToUser(message);
        if (!isUserRelatedMessage) {
          console.log('⚠️ [聊天列表监听] 跳过非相关消息:', message.chatRoomId);
          return;
        }

        hasNewMessage = true;
        const messageInfo = {
          messageId: message._id,
          chatRoomId: message.chatRoomId,
          content: message.content,
          type: message.type || 'text', // 添加消息类型字段
          senderId: message.senderId,
          createTime: message.createTime
        };
        messageDetails.push(messageInfo);
        console.log('📨 [聊天列表监听] 检测到新消息:', messageInfo);

        // 处理未读消息状态
        this.handleNewMessageForUnread(messageInfo);
      } else if (change.queueType === 'update' && change.dataType === 'update') {
        // 处理消息更新（如撤回）
        const message = change.doc;
        console.log('🔄 [聊天列表监听] 检测到消息更新:', {
          messageId: message._id,
          chatRoomId: message.chatRoomId,
          content: message.content,
          isRecalled: message.isRecalled
        });

        // 如果是撤回消息，触发聊天列表更新
        if (message.isRecalled) {
          console.log('🔄 [聊天列表监听] 检测到撤回消息，触发聊天列表更新');

          // 确保全局撤回状态被设置，包含消息时间信息
          const recallContent = message.content || '[消息已撤回]';
          const messageTime = message.createTime;
          console.log('🔒 [聊天列表监听] 设置全局撤回状态:', {
            chatRoomId: message.chatRoomId,
            content: recallContent,
            messageTime: messageTime
          });
          getApp().globalData.recalledMessages.markAsRecalled(message.chatRoomId, recallContent, messageTime);

          // 立即更新聊天列表显示
          this.updateChatItemRecallStatus(message.chatRoomId, recallContent);

          // 延迟执行静默刷新作为备用
          setTimeout(() => {
            console.log('🔄 [聊天列表监听] 执行撤回后的静默刷新');
            this.loadChatListSilently();
          }, 500);
        }
      }
    });

    // 如果有新消息，静默刷新聊天列表
    if (hasNewMessage) {
      console.log('🔄 [聊天列表监听] 检测到新消息，准备更新聊天列表');
      console.log('🔄 [聊天列表监听] 消息详情:', messageDetails);

      // 检查并清理过期的撤回状态（但不清理撤回消息本身）
      messageDetails.forEach(msg => {
        const globalRecallStatus = getApp().globalData.recalledMessages.isRecalled(msg.chatRoomId);
        if (globalRecallStatus) {
          const newMessageTime = new Date(msg.createTime).getTime();
          const recallTime = globalRecallStatus.timestamp;

          // 只有当新消息确实比撤回状态更新，且不是撤回消息本身时，才清除撤回状态
          const isRecalledMessage = msg.content === '[消息已撤回]' || msg.content.includes('已撤回');

          if (newMessageTime > recallTime && !isRecalledMessage) {
            console.log(`🔄 [聊天列表监听] 新消息比撤回状态更新且非撤回消息，清除聊天室 ${msg.chatRoomId} 的撤回状态`);
            getApp().globalData.recalledMessages.clear(msg.chatRoomId);
          } else if (isRecalledMessage) {
            console.log(`🔒 [聊天列表监听] 检测到撤回消息，保持撤回状态: ${msg.chatRoomId}`);
          }
        }
      });

      // 直接更新聊天列表中对应聊天室的最新消息
      this.updateChatListWithNewMessages(messageDetails);

      // 执行备用静默刷新，但只在必要时（如撤回状态同步）
      setTimeout(() => {
        console.log('🔄 [聊天列表监听] 检查是否需要执行备用静默刷新');

        // 检查是否有撤回状态需要同步
        const needsRecallSync = messageDetails.some(detail => {
          const currentChat = this.data.chatList.find(chat => chat._id === detail.chatRoomId);
          const hasGlobalRecall = getApp().globalData.recalledMessages.isRecalled(detail.chatRoomId);

          // 如果全局有撤回状态，但当前显示不是撤回状态，需要同步
          return hasGlobalRecall && currentChat && currentChat.lastMessageDisplay !== '[消息已撤回]';
        });

        if (needsRecallSync) {
          console.log('🔄 [聊天列表监听] 检测到撤回状态需要同步，执行备用刷新');
          this.loadChatListSilently();
        } else {
          console.log('ℹ️ [聊天列表监听] 无需执行备用刷新');
        }
      }, 2000);
    } else {
      console.log('⚠️ [聊天列表监听] 没有检测到新消息');
    }
  },

  // 处理聊天室变更事件
  handleChatRoomChange(snapshot) {
    console.log('📨 [聊天列表监听] 收到聊天室变更:', {
      type: snapshot.type,
      docChanges: snapshot.docChanges?.length || 0
    });

    // 如果是初始化数据，不处理（避免与loadChatList冲突）
    if (snapshot.type === 'init') {
      console.log('📋 [聊天列表监听] 跳过初始化数据');
      return;
    }

    // 检查是否有聊天室更新（通常是lastMessage更新）
    let hasUpdate = false;
    let updateDetails = [];

    snapshot.docChanges.forEach((change, index) => {
      console.log(`📋 [聊天列表监听] 变更 ${index + 1}:`, {
        queueType: change.queueType,
        dataType: change.dataType,
        docId: change.doc._id
      });

      if (change.queueType === 'enqueue' && (change.dataType === 'update' || change.dataType === 'add')) {
        hasUpdate = true;
        const chatRoom = change.doc;
        const updateInfo = {
          chatRoomId: chatRoom._id,
          lastMessage: chatRoom.lastMessage?.content,
          lastMessageType: chatRoom.lastMessage?.type,
          lastMessageSender: chatRoom.lastMessage?.senderId,
          updateTime: chatRoom.updateTime,
          orderNo: chatRoom.orderNo,
          isRecalled: chatRoom.lastMessage?.isRecalled
        };
        updateDetails.push(updateInfo);
        console.log('📨 [聊天列表监听] 检测到聊天室更新:', updateInfo);

        // 特别关注撤回消息的更新
        if (chatRoom.lastMessage?.type === 'recalled' || chatRoom.lastMessage?.isRecalled) {
          console.log('🔔 [聊天列表监听] 检测到撤回消息更新:', {
            chatRoomId: chatRoom._id,
            content: chatRoom.lastMessage.content,
            type: chatRoom.lastMessage.type
          });
        }
      }
    });

    // 如果有更新，智能处理刷新逻辑
    if (hasUpdate) {
      console.log('🔄 [聊天列表监听] 检测到聊天室更新，准备智能处理');
      console.log('🔄 [聊天列表监听] 更新详情:', updateDetails);

      // 检查是否有撤回消息需要特殊处理
      const hasRecalledMessage = updateDetails.some(update =>
        update.lastMessageType === 'recalled' || update.isRecalled
      );

      if (hasRecalledMessage) {
        // 只有撤回消息才需要延迟静默刷新
        console.log('🔄 [聊天列表监听] 检测到撤回消息，执行延迟静默刷新');
        setTimeout(() => {
          console.log('🔄 [聊天列表监听] 开始执行延迟静默刷新（用于撤回状态同步）');
          this.loadChatListSilently();
        }, 800);
      } else {
        // 普通消息更新，直接使用聊天室数据更新本地列表，避免不必要的刷新
        console.log('🔄 [聊天列表监听] 普通消息更新，直接更新本地列表');
        this.updateChatListWithChatRoomData(updateDetails);
      }
    } else {
      console.log('⚠️ [聊天列表监听] 没有检测到需要处理的更新');
    }
  },

  // 启动定期检查刷新标志
  startRefreshChecker() {
    // 清除之前的定时器
    if (this.refreshChecker) {
      clearInterval(this.refreshChecker);
    }

    console.log('⏰ [定期检查] 启动刷新检查定时器');

    // 每2秒检查一次是否需要刷新
    this.refreshChecker = setInterval(() => {
      const app = getApp();
      console.log('⏰ [定期检查] 检查刷新标志:', app.globalData.needRefreshChatList);
      if (app.globalData.needRefreshChatList) {
        console.log('⏰ [定期检查] 检测到需要刷新，执行静默刷新');
        app.globalData.needRefreshChatList = false;
        this.loadChatListSilently();
      }
    }, 2000);
  },

  // 停止定期检查
  stopRefreshChecker() {
    if (this.refreshChecker) {
      console.log('⏰ [定期检查] 停止刷新检查定时器');
      clearInterval(this.refreshChecker);
      this.refreshChecker = null;
    }
  },

  // 测试方法：手动触发刷新
  testRefresh() {
    console.log('🧪 [测试] 手动触发刷新');
    this.loadChatListSilently();
  },

  // 测试方法：检查全局标志
  testGlobalFlag() {
    const app = getApp();
    console.log('🧪 [测试] 全局刷新标志:', app.globalData.needRefreshChatList);
    if (app.globalData.needRefreshChatList) {
      console.log('🧪 [测试] 发现刷新标志，执行刷新');
      app.globalData.needRefreshChatList = false;
      this.loadChatListSilently();
    }
  },

  // 直接更新聊天列表中的新消息
  updateChatListWithNewMessages(messageDetails) {
    console.log('🔄 [直接更新] 开始直接更新聊天列表');

    if (!messageDetails || messageDetails.length === 0) {
      console.log('⚠️ [直接更新] 没有新消息需要更新');
      return;
    }

    const currentChatList = [...this.data.chatList];
    let hasUpdates = false;

    messageDetails.forEach(message => {
      console.log('🔄 [直接更新] 处理消息:', {
        chatRoomId: message.chatRoomId,
        content: message.content,
        senderId: message.senderId
      });

      // 找到对应的聊天室
      const chatIndex = currentChatList.findIndex(chat => chat._id === message.chatRoomId);

      if (chatIndex !== -1) {
        const chat = currentChatList[chatIndex];

        // 检查消息时间，确保只更新更新的消息
        const currentLastMessageTime = chat.lastMessage?.createTime ? new Date(chat.lastMessage.createTime) : new Date(0);
        const newMessageTime = new Date(message.createTime);

        if (newMessageTime >= currentLastMessageTime) {
          // 更新最后消息信息
          chat.lastMessage = {
            content: message.content,
            type: message.type || 'text',
            senderId: message.senderId,
            createTime: message.createTime
          };

          // 格式化显示内容
          let displayContent = message.content;
          if (message.type === 'image') {
            displayContent = '[图片]';
          } else if (message.type === 'voice') {
            displayContent = '[语音]';
          } else {
            displayContent = message.content.length > 30 ?
              message.content.substring(0, 30) + '...' : message.content;
          }

          chat.lastMessageDisplay = displayContent;
          chat.lastMessageTime = message.createTime;

          // 移动到列表顶部
          currentChatList.splice(chatIndex, 1);
          currentChatList.unshift(chat);

          hasUpdates = true;

          console.log('✅ [直接更新] 聊天室更新成功:', {
            chatRoomId: message.chatRoomId,
            newDisplay: displayContent,
            messageTime: newMessageTime.toISOString()
          });
        } else {
          console.log('⚠️ [直接更新] 跳过旧消息:', {
            chatRoomId: message.chatRoomId,
            currentTime: currentLastMessageTime.toISOString(),
            newTime: newMessageTime.toISOString()
          });
        }
      } else {
        console.log('⚠️ [直接更新] 未找到对应的聊天室:', message.chatRoomId);
      }
    });

    if (hasUpdates) {
      console.log('✅ [直接更新] 更新聊天列表显示');
      this.setData({
        chatList: currentChatList
      });
    } else {
      console.log('⚠️ [直接更新] 没有需要更新的聊天室');
    }
  },

  // 使用聊天室数据更新聊天列表
  updateChatListWithChatRoomData(updateDetails) {
    console.log('🔄 [聊天室更新] 开始使用聊天室数据更新聊天列表');

    if (!updateDetails || updateDetails.length === 0) {
      console.log('⚠️ [聊天室更新] 没有聊天室更新需要处理');
      return;
    }

    const currentChatList = [...this.data.chatList];
    let hasUpdates = false;

    updateDetails.forEach(update => {
      console.log('🔄 [聊天室更新] 处理聊天室更新:', {
        chatRoomId: update.chatRoomId,
        lastMessage: update.lastMessage,
        lastMessageType: update.lastMessageType
      });

      // 找到对应的聊天室
      const chatIndex = currentChatList.findIndex(chat => chat._id === update.chatRoomId);

      if (chatIndex !== -1) {
        const chat = currentChatList[chatIndex];

        // 检查是否需要更新（避免覆盖更新的消息）
        const currentLastMessageTime = chat.lastMessage?.createTime ? new Date(chat.lastMessage.createTime) : new Date(0);
        const updateTime = update.updateTime ? new Date(update.updateTime) : new Date();

        // 只有当聊天室的更新时间更新时才更新
        if (updateTime >= currentLastMessageTime) {
          // 更新最后消息信息
          if (update.lastMessage) {
            chat.lastMessage = {
              content: update.lastMessage,
              type: update.lastMessageType || 'text',
              senderId: update.lastMessageSender,
              createTime: update.updateTime
            };

            // 格式化显示内容
            let displayContent = update.lastMessage;
            if (update.lastMessageType === 'image') {
              displayContent = '[图片]';
            } else if (update.lastMessageType === 'voice') {
              displayContent = '[语音]';
            } else if (update.lastMessageType === 'recalled' || update.isRecalled) {
              displayContent = '[消息已撤回]';
            } else {
              displayContent = update.lastMessage.length > 30 ?
                update.lastMessage.substring(0, 30) + '...' : update.lastMessage;
            }

            chat.lastMessageDisplay = displayContent;
            chat.lastMessageTime = update.updateTime;

            // 移动到列表顶部
            currentChatList.splice(chatIndex, 1);
            currentChatList.unshift(chat);

            hasUpdates = true;

            console.log('✅ [聊天室更新] 聊天室更新成功:', {
              chatRoomId: update.chatRoomId,
              newDisplay: displayContent
            });
          }
        } else {
          console.log('⚠️ [聊天室更新] 跳过过期的更新:', {
            chatRoomId: update.chatRoomId,
            currentTime: currentLastMessageTime,
            updateTime: updateTime
          });
        }
      } else {
        console.log('⚠️ [聊天室更新] 未找到对应的聊天室:', update.chatRoomId);
      }
    });

    if (hasUpdates) {
      console.log('✅ [聊天室更新] 更新聊天列表显示');
      this.setData({
        chatList: currentChatList
      });
    } else {
      console.log('⚠️ [聊天室更新] 没有需要更新的聊天室');
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '聊天列表',
      path: '/pages/chat/list/list'
    };
  },

  // 检查永久撤回标记
  checkPermanentRecallMark(roomId) {
    try {
      // 检查全局内存标记
      if (getApp().globalData.permanentRecallRooms &&
          getApp().globalData.permanentRecallRooms[roomId]) {
        const mark = getApp().globalData.permanentRecallRooms[roomId];
        // 检查标记是否在有效期内（1小时）
        if (Date.now() - mark.timestamp < 3600000) {
          return mark;
        }
      }

      // 检查本地存储标记
      const storedMark = wx.getStorageSync(`recall_${roomId}`);
      if (storedMark && storedMark.content) {
        // 检查标记是否在有效期内（1小时）
        if (Date.now() - storedMark.timestamp < 3600000) {
          return storedMark;
        } else {
          // 清除过期标记
          wx.removeStorageSync(`recall_${roomId}`);
        }
      }

      return null;

    } catch (error) {
      console.error('❌ [永久标记] 检查永久撤回标记失败:', error);
      return null;
    }
  },

  // 设置撤回事件监听器
  setupRecallListener() {
    console.log('🔧 [撤回监听] 设置撤回事件监听器');

    // 监听全局撤回事件
    getApp().$on('messageRecalled', (data) => {
      console.log('📡 [撤回监听] 收到撤回事件:', data);
      this.handleMessageRecalled(data);
    });

    // 监听撤回状态清除事件
    getApp().$on('recallStatusCleared', (data) => {
      console.log('📡 [撤回监听] 收到撤回清除事件:', data);
      this.handleRecallStatusCleared(data);
    });

    // 监听强制刷新事件
    getApp().$on('forceRefreshChatList', (data) => {
      console.log('📡 [撤回监听] 收到强制刷新事件:', data);
      if (data.reason === 'messageRecalled') {
        console.log('🔄 [撤回监听] 执行撤回相关的强制刷新');
        this.loadChatListSilently();
      }
    });
  },

  // 处理消息撤回事件
  handleMessageRecalled(data) {
    const { chatRoomId, content, timestamp } = data;
    console.log('🔄 [撤回处理] 开始处理撤回事件:', { chatRoomId, content });

    try {
      // 立即更新聊天列表中的对应项
      const chatList = [...this.data.chatList];
      const targetIndex = chatList.findIndex(chat => chat._id === chatRoomId);

      console.log('🔍 [撤回处理] 查找目标聊天室:', { targetIndex, chatRoomId });

      if (targetIndex !== -1) {
        console.log('✅ [撤回处理] 找到目标聊天室，更新显示');

        // 更新最后消息显示
        chatList[targetIndex].lastMessageDisplay = content;
        chatList[targetIndex].lastMessage = {
          content: content,
          type: 'recalled',
          createTime: new Date(timestamp)
        };

        // 立即更新页面数据
        this.setData({
          chatList: chatList
        });

        console.log('✅ [撤回处理] 聊天列表已更新');

        // 额外的备用刷新
        setTimeout(() => {
          console.log('🔄 [撤回处理] 执行备用静默刷新');
          this.loadChatListSilently();
        }, 1000);

      } else {
        console.log('⚠️ [撤回处理] 未找到目标聊天室，执行全量刷新');
        this.loadChatListSilently();
      }

    } catch (error) {
      console.error('❌ [撤回处理] 处理失败:', error);
      // 失败时执行全量刷新
      this.loadChatListSilently();
    }
  },

  // 处理撤回状态清除事件
  handleRecallStatusCleared(data) {
    try {
      // 触发静默刷新，让智能合并重新处理该聊天室
      this.loadChatListSilently();
    } catch (error) {
      console.error('❌ [撤回清除] 处理失败:', error);
    }
  },

  // 应用全局撤回状态保护
  applyRecallProtection(chatList) {
    return chatList.map(chat => {
      // 检查该聊天室是否有撤回状态
      const recallStatus = getApp().globalData.recalledMessages.isRecalled(chat._id);

      if (recallStatus) {
        return {
          ...chat,
          lastMessage: {
            content: recallStatus.content,
            type: 'recalled',
            createTime: new Date(recallStatus.timestamp)
          },
          lastMessageDisplay: recallStatus.content,
          _globalRecalled: true,
          _recallTime: recallStatus.timestamp
        };
      }

      return chat;
    });
  },

  // 立即更新聊天列表项的撤回状态
  updateChatItemRecallStatus(chatRoomId, content) {
    try {
      console.log('🔄 [立即更新] 更新聊天室撤回状态:', { chatRoomId, content });

      const chatList = [...this.data.chatList];
      const targetIndex = chatList.findIndex(chat => chat._id === chatRoomId);

      if (targetIndex !== -1) {
        chatList[targetIndex].lastMessageDisplay = content;
        chatList[targetIndex].lastMessage = {
          content: content,
          type: 'recalled',
          createTime: new Date()
        };

        this.setData({
          chatList: chatList
        });

        console.log('✅ [立即更新] 撤回状态更新成功');
      } else {
        console.log('⚠️ [立即更新] 未找到目标聊天室');
      }
    } catch (error) {
      console.error('❌ [立即更新] 更新撤回状态失败:', error);
    }
  },



  // 页面卸载时清理资源
  onUnload() {
    console.log('=== 聊天列表页面卸载 ===');

    // 停止消息监听器
    this.stopMessageWatcher();

    // 停止聊天室监听器
    this.stopChatRoomWatcher();

    // 停止定期检查
    this.stopRefreshChecker();

    // 清理未读消息监听
    this.cleanupUnreadMessageListener();

    // 清理撤回事件监听器
    getApp().$off('messageRecalled', this.handleMessageRecalled);
    getApp().$off('recallStatusCleared', this.handleRecallStatusCleared);
    getApp().$off('forceRefreshChatList', this.handleForceRefresh);

    // 清理防抖定时器
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
      this.loadingTimer = null;
    }

    // 确保隐藏任何可能的系统加载提示
    wx.hideLoading();

    // 清理所有引用，防止内存泄漏
    this.data.messageWatcher = null;
  },

  // 页面隐藏时的处理
  onHide() {
    console.log('=== 聊天列表页面隐藏 ===');
    // 确保隐藏任何可能的系统加载提示
    wx.hideLoading();
    // 注意：不停止实时监听器，保持后台监听以便及时更新聊天列表
    // 这样当用户不在聊天列表页面时，仍能收到新消息的实时更新
    console.log('📱 [聊天列表] 页面隐藏，保持监听器运行以接收实时更新');
    // 停止定期检查
    this.stopRefreshChecker();
  },

  // 页面卸载时的处理
  onUnload() {
    console.log('=== 聊天列表页面卸载 ===');
    // 页面真正卸载时才停止所有监听器
    this.stopMessageWatcher();
    this.stopChatRoomWatcher();
    this.stopRefreshChecker();

    // 清理未读消息监听
    this.cleanupUnreadMessageListener();

    // 清理离线消息检测监听
    this.cleanupOfflineMessageListener();

    console.log('✅ [聊天列表] 页面卸载，所有监听器已停止');
  },

  // 检查消息是否与当前用户相关
  isMessageRelatedToUser(message) {
    if (!message || !message.chatRoomId || !this.data.userInfo) {
      return false;
    }

    // 检查消息的聊天室是否在当前用户的聊天列表中
    const isInChatList = this.data.chatList.some(chat => chat._id === message.chatRoomId);

    if (isInChatList) {
      console.log('✅ [消息过滤] 消息属于已有聊天室:', message.chatRoomId);
      return true;
    }

    // 如果不在聊天列表中，可能是新创建的聊天室，暂时允许通过
    // 后续会通过聊天室监听器来处理新聊天室的创建
    console.log('⚠️ [消息过滤] 消息来自未知聊天室，允许通过:', message.chatRoomId);
    return true;
  },

  // ==================== 未读消息管理方法 ====================

  // 初始化未读消息监听
  initUnreadMessageListener() {
    const app = getApp();

    // 监听未读消息状态更新事件
    this.unreadMessageUpdateHandler = (data) => {
      console.log('📱 [聊天列表] 收到未读消息更新事件:', data);
      this.updateChatListUnreadStatus(data);
    };

    app.$on('unreadMessageUpdate', this.unreadMessageUpdateHandler);

    // 初始化时更新一次未读状态
    this.updateAllUnreadStatus();

    console.log('📱 [聊天列表] 未读消息监听初始化完成');
  },

  // 清理未读消息监听
  cleanupUnreadMessageListener() {
    const app = getApp();

    if (this.unreadMessageUpdateHandler) {
      app.$off('unreadMessageUpdate', this.unreadMessageUpdateHandler);
      this.unreadMessageUpdateHandler = null;
    }

    console.log('📱 [聊天列表] 未读消息监听清理完成');
  },

  // 更新聊天列表的未读状态
  updateChatListUnreadStatus(data) {
    const { chatRoomId, unreadData } = data;
    const chatList = this.data.chatList;

    // 找到对应的聊天室并更新未读数
    const updatedChatList = chatList.map(chat => {
      const roomId = chat.chatRoomId || chat.chatId || chat._id;
      if (roomId === chatRoomId) {
        const unreadCount = unreadData.chatRooms[chatRoomId]?.count || 0;
        return {
          ...chat,
          chatRoomId: roomId, // 确保统一使用 chatRoomId 字段
          unreadCount: unreadCount
        };
      }
      return chat;
    });

    this.setData({
      chatList: updatedChatList
    });

    console.log('📱 [聊天列表] 更新聊天室未读状态:', {
      chatRoomId,
      unreadCount: unreadData.chatRooms[chatRoomId]?.count || 0
    });
  },

  // 更新所有聊天室的未读状态
  updateAllUnreadStatus() {
    const app = getApp();
    const unreadData = app.globalData.unreadMessages;
    const chatList = this.data.chatList;

    // 为每个聊天室设置未读数
    const updatedChatList = chatList.map((chat, index) => {
      // 使用正确的字段名：chatId 或 chatRoomId
      const roomId = chat.chatRoomId || chat.chatId || chat._id;
      const unreadCount = unreadData.getChatRoomCount(roomId);

      return {
        ...chat,
        chatRoomId: roomId, // 确保统一使用 chatRoomId 字段
        unreadCount: unreadCount
      };
    });

    this.setData({
      chatList: updatedChatList
    });

    console.log('📱 [聊天列表] 更新所有聊天室未读状态完成');
    console.log('📱 [聊天列表] 未读状态详情:', updatedChatList.map(chat => ({
      chatRoomId: chat.chatRoomId,
      nickName: chat.otherUser?.nickName,
      unreadCount: chat.unreadCount
    })));
  },

  // 处理新消息到达（集成到现有的消息监听中）
  handleNewMessageForUnread(messageData) {
    const app = getApp();
    const { chatRoomId, senderId } = messageData;

    // 检查是否是自己发送的消息
    const currentUserId = app.globalData.userInfo?._id;
    if (senderId === currentUserId) {
      console.log('📱 [聊天列表] 跳过自己发送的消息');
      return;
    }

    // 注意：未读消息计数由全局聊天监听器处理，这里不重复计数
    // 只更新最后消息信息
    app.globalData.unreadMessages.updateLastMessage(chatRoomId, messageData);

    console.log('📱 [聊天列表] 处理新消息未读状态:', {
      chatRoomId,
      senderId,
      content: messageData.content
    });
  },

  // 设置离线消息检测监听
  setupOfflineMessageListener() {
    const app = getApp();

    // 监听离线消息检测事件
    this.offlineMessageHandler = (data) => {
      console.log('📱 [聊天列表] 收到离线消息检测事件:', data);

      // 防止重复处理
      if (this.isProcessingOfflineMessages) {
        console.log('📱 [聊天列表] 正在处理离线消息，跳过重复处理');
        return;
      }

      this.isProcessingOfflineMessages = true;

      // 处理离线消息
      this.processOfflineMessages(data).finally(() => {
        this.isProcessingOfflineMessages = false;
      });
    };

    app.$on('offlineMessagesDetected', this.offlineMessageHandler);

    console.log('📱 [聊天列表] 离线消息检测监听初始化完成');
  },

  // 处理离线消息
  async processOfflineMessages(data) {
    try {
      console.log('📱 [聊天列表] 开始处理离线消息:', data.messageCount, '条');

      // 步骤1：立即更新聊天列表显示
      if (data.messages && data.messages.length > 0) {
        this.updateChatListWithOfflineMessages(data.messages);
      }

      // 步骤2：更新未读状态
      this.updateAllUnreadStatus();

      // 步骤3：延迟强制刷新，确保数据一致性
      await new Promise(resolve => setTimeout(resolve, 300));
      await this.forceRefreshChatList();

      // 步骤4：再次更新未读状态（确保数据同步）
      this.updateAllUnreadStatus();

      // 步骤5：显示提示
      if (data.messageCount > 0) {
        wx.showToast({
          title: `收到 ${data.messageCount} 条新消息`,
          icon: 'none',
          duration: 2000
        });
      }

      console.log('📱 [聊天列表] 离线消息处理完成');

    } catch (error) {
      console.error('❌ [聊天列表] 处理离线消息失败:', error);
    }
  },

  // 使用离线消息更新聊天列表
  updateChatListWithOfflineMessages(offlineMessages) {
    console.log('📱 [聊天列表] 开始更新离线消息到聊天列表:', offlineMessages.length, '条');

    const chatList = [...this.data.chatList];
    let hasUpdates = false;

    // 按聊天室分组离线消息，找到每个聊天室的最新消息
    const messagesByRoom = {};
    offlineMessages.forEach(message => {
      const roomId = message.chatRoomId;
      if (!messagesByRoom[roomId] || new Date(message.createTime) > new Date(messagesByRoom[roomId].createTime)) {
        messagesByRoom[roomId] = message;
      }
    });

    // 更新聊天列表中对应聊天室的最新消息
    chatList.forEach((chat, index) => {
      const roomId = chat.chatRoomId || chat.chatId || chat._id;
      const latestMessage = messagesByRoom[roomId];

      if (latestMessage) {
        console.log('📱 [聊天列表] 更新聊天室最新消息:', roomId, latestMessage.content);

        // 更新最后消息显示
        chatList[index] = {
          ...chat,
          lastMessage: latestMessage.content,
          lastMessageDisplay: this.formatMessageDisplay(latestMessage),
          lastMessageTime: latestMessage.createTime,
          updateTime: latestMessage.createTime
        };
        hasUpdates = true;
      }
    });

    if (hasUpdates) {
      console.log('📱 [聊天列表] 离线消息更新完成，刷新界面');
      this.setData({ chatList });
    }
  },

  // 格式化消息显示内容
  formatMessageDisplay(message) {
    if (message.type === 'text') {
      return message.content;
    } else if (message.type === 'image') {
      return '[图片]';
    } else if (message.type === 'voice') {
      return '[语音]';
    } else {
      return '[消息]';
    }
  },

  // 强制刷新聊天列表（不使用智能合并）
  async forceRefreshChatList() {
    const { userInfo } = this.data;
    if (!userInfo) {
      return;
    }

    console.log('🔄 [强制刷新] 开始强制刷新聊天列表');

    try {
      // 调用API获取最新的聊天列表数据
      const result = await API.callFunction('chatList', {
        page: 1,
        pageSize: this.data.pageSize,
        status: 'active'
      }, { showLoading: false });

      if (result.success) {
        const newChatList = result.data.list || [];
        const stats = result.data.stats || {};

        console.log('🔄 [强制刷新] 获取到最新数据，聊天室数量:', newChatList.length);

        // 直接使用新数据，不进行智能合并
        this.setData({
          chatList: newChatList.map(chat => ({
            ...chat,
            chatRoomId: chat._id,
            unreadCount: 0 // 未读数会通过 updateAllUnreadStatus 更新
          })),
          page: 1,
          hasMore: stats.hasMore || false,
          stats: stats
        });

        // 更新未读状态
        this.updateAllUnreadStatus();

        console.log('🔄 [强制刷新] 聊天列表强制刷新完成');

      } else {
        console.error('❌ [强制刷新] 刷新失败:', result.error);
      }

    } catch (error) {
      console.error('❌ [强制刷新] 刷新异常:', error);
    }
  },

  // 清理离线消息检测监听
  cleanupOfflineMessageListener() {
    const app = getApp();

    if (this.offlineMessageHandler) {
      app.$off('offlineMessagesDetected', this.offlineMessageHandler);
      this.offlineMessageHandler = null;
    }

    console.log('📱 [聊天列表] 离线消息检测监听清理完成');
  }
});
