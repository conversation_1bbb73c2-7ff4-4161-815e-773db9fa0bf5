// 订单确认提醒云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('开始执行订单确认提醒任务...');
  
  try {
    const now = new Date();
    
    // 查找所有待确认的订单
    const ordersResult = await db.collection('orders')
      .where({
        status: 'proof_submitted'
      })
      .get();
    
    if (!ordersResult.data || ordersResult.data.length === 0) {
      console.log('没有需要提醒的订单');
      return {
        success: true,
        message: '没有需要提醒的订单',
        processedCount: 0
      };
    }
    
    let processedCount = 0;
    let autoConfirmedCount = 0;
    
    for (const order of ordersResult.data) {
      try {
        const proofSubmitTime = new Date(order.proofSubmitTime);
        const autoConfirmTime = new Date(order.autoConfirmTime);
        const timeDiff = now - proofSubmitTime;
        const timeToAutoConfirm = autoConfirmTime - now;
        
        // 检查是否需要自动确认（超过72小时）
        if (timeToAutoConfirm <= 0) {
          await autoConfirmOrder(order);
          autoConfirmedCount++;
          console.log(`订单 ${order.orderNo} 已自动确认`);
          continue;
        }
        
        // 检查是否需要发送提醒
        const hours = Math.floor(timeDiff / (1000 * 60 * 60));
        
        // 24小时提醒
        if (hours >= 24 && hours < 25 && !order.reminder24h) {
          await sendConfirmReminder(order, '24小时');
          await markReminderSent(order._id, 'reminder24h');
          processedCount++;
        }
        // 48小时提醒
        else if (hours >= 48 && hours < 49 && !order.reminder48h) {
          await sendConfirmReminder(order, '48小时');
          await markReminderSent(order._id, 'reminder48h');
          processedCount++;
        }
        // 最后提醒（距离自动确认还有6小时）
        else if (timeToAutoConfirm <= 6 * 60 * 60 * 1000 && timeToAutoConfirm > 5 * 60 * 60 * 1000 && !order.reminderFinal) {
          await sendFinalReminder(order);
          await markReminderSent(order._id, 'reminderFinal');
          processedCount++;
        }
        
      } catch (orderError) {
        console.error(`处理订单 ${order._id} 时出错:`, orderError);
      }
    }
    
    console.log(`订单确认提醒任务完成，处理了 ${processedCount} 个提醒，自动确认了 ${autoConfirmedCount} 个订单`);
    
    return {
      success: true,
      message: '订单确认提醒任务完成',
      processedCount: processedCount,
      autoConfirmedCount: autoConfirmedCount
    };
    
  } catch (error) {
    console.error('订单确认提醒任务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 自动确认订单
async function autoConfirmOrder(order) {
  try {
    // 调用updateOrderStatus云函数进行确认
    const result = await cloud.callFunction({
      name: 'updateOrderStatus',
      data: {
        orderId: order._id,
        status: 'confirmed',
        updateData: {
          autoConfirmed: true,
          confirmTime: new Date()
        }
      }
    });
    
    if (result.result && result.result.success) {
      console.log(`订单 ${order.orderNo} 自动确认成功`);
      
      // 发送自动确认通知
      await sendAutoConfirmNotification(order);
    } else {
      console.error(`订单 ${order.orderNo} 自动确认失败:`, result.result?.error);
    }
    
  } catch (error) {
    console.error(`自动确认订单 ${order._id} 失败:`, error);
  }
}

// 发送确认提醒通知
async function sendConfirmReminder(order, timeLabel) {
  try {
    await cloud.callFunction({
      name: 'sendNotification',
      data: {
        type: 'confirmReminder',
        targetUserId: order.customerId,
        templateId: 'confirm_reminder_template',
        data: {
          title: '订单确认提醒',
          content: `您的订单已提交完成证明${timeLabel}，请及时确认。`,
          orderNo: order.orderNo || order._id,
          orderTitle: order.title || '订单',
          timeLabel: timeLabel
        },
        page: `order-package/pages/confirm-proof/confirm-proof?orderId=${order._id}`
      }
    });
    
    console.log(`已发送${timeLabel}确认提醒给订单 ${order.orderNo}`);
  } catch (error) {
    console.error(`发送确认提醒失败:`, error);
  }
}

// 发送最后提醒通知
async function sendFinalReminder(order) {
  try {
    await cloud.callFunction({
      name: 'sendNotification',
      data: {
        type: 'finalReminder',
        targetUserId: order.customerId,
        templateId: 'final_reminder_template',
        data: {
          title: '订单即将自动确认',
          content: '您的订单将在6小时后自动确认并结算，请及时处理。',
          orderNo: order.orderNo || order._id,
          orderTitle: order.title || '订单'
        },
        page: `order-package/pages/confirm-proof/confirm-proof?orderId=${order._id}`
      }
    });
    
    console.log(`已发送最后提醒给订单 ${order.orderNo}`);
  } catch (error) {
    console.error(`发送最后提醒失败:`, error);
  }
}

// 发送自动确认通知
async function sendAutoConfirmNotification(order) {
  try {
    // 通知客户
    await cloud.callFunction({
      name: 'sendNotification',
      data: {
        type: 'autoConfirmed',
        targetUserId: order.customerId,
        templateId: 'auto_confirm_template',
        data: {
          title: '订单已自动确认',
          content: '您的订单已超时自动确认并完成结算。',
          orderNo: order.orderNo || order._id,
          orderTitle: order.title || '订单'
        },
        page: `order-package/pages/detail/detail?orderId=${order._id}`
      }
    });
    
    // 通知接单者
    if (order.accepterId) {
      await cloud.callFunction({
        name: 'sendNotification',
        data: {
          type: 'autoConfirmed',
          targetUserId: order.accepterId,
          templateId: 'auto_confirm_template',
          data: {
            title: '订单已自动确认',
            content: '客户超时未确认，订单已自动确认并完成结算。',
            orderNo: order.orderNo || order._id,
            orderTitle: order.title || '订单'
          },
          page: `order-package/pages/detail/detail?orderId=${order._id}`
        }
      });
    }
    
    console.log(`已发送自动确认通知给订单 ${order.orderNo}`);
  } catch (error) {
    console.error(`发送自动确认通知失败:`, error);
  }
}

// 标记提醒已发送
async function markReminderSent(orderId, reminderType) {
  try {
    const updateData = {};
    updateData[reminderType] = true;
    
    await db.collection('orders').doc(orderId).update({
      data: updateData
    });
    
  } catch (error) {
    console.error(`标记提醒状态失败:`, error);
  }
}
