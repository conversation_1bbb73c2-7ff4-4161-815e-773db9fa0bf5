import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  Download, 
  UserPlus, 
  MoreHorizontal,
  Eye,
  Edit,
  Ban,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DataPagination } from '@/components/ui/data-pagination';
import { BulkActions, userBulkActions } from '@/components/ui/bulk-actions';
import { DataExport, userExportTemplates } from '@/components/ui/data-export';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { userApi } from '@/services/cloudApi';
import { exportData, userExportFields } from '@/utils/export';
import type { User, PaginationParams } from '@/types';
import type { ExportOptions } from '@/components/ui/data-export';

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // 批量选择状态
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isIndeterminate, setIsIndeterminate] = useState(false);

  // 格式化日期函数
  const formatDate = (dateStr: string) => {
    try {
      if (!dateStr) return '未知';
      
      // 处理不同的日期格式
      let date;
      if (dateStr.includes('T')) {
        // ISO格式: 2025-06-30T14:15:46.668Z
        date = new Date(dateStr);
      } else {
        // 其他格式
        date = new Date(dateStr);
      }
      
      if (isNaN(date.getTime())) return '无效日期';
      
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '格式错误';
    }
  };

  // 加载用户数据
  const loadUsers = async (page: number = currentPage, size: number = pageSize) => {
    try {
      setLoading(true);

      console.log('正在调用云开发API获取用户数据...', { page, size, searchTerm });

      // 调用云开发API获取用户数据
      const response = await userApi.getUserList({
        page,
        limit: size,
        search: searchTerm
      });
      
      console.log('API响应:', response);
      
      // 处理云开发API响应 - 适配不同的响应格式
      let rawUserData = [];
      
      if (response?.data?.success && response.data.data) {
        // 格式1: { data: { success: true, data: { users: [...] } } }
        rawUserData = response.data.data.users || response.data.data || [];
      } else if (response?.data?.users) {
        // 格式2: { data: { users: [...] } }
        rawUserData = response.data.users;
      } else if (response?.data && Array.isArray(response.data)) {
        // 格式3: { data: [...] }
        rawUserData = response.data;
      } else if (Array.isArray(response)) {
        // 格式4: [...]
        rawUserData = response;
      } else {
        console.warn('未知的API响应格式:', response);
        throw new Error('云开发API响应格式不正确');
      }
      
      // 映射数据库字段到前端字段
      const userData = rawUserData.map((user: any) => ({
        id: user._id || user.id,
        openid: user.openid,
        nickname: user.nickName || user.nickname || '未设置昵称',
        avatar: user.avatarUrl || user.avatar || '/placeholder.svg?height=40&width=40',
        phone: user.phone || '未绑定手机',
        isVerified: user.isVerified || false,
        status: user.status || 'active',
        createdAt: user.createTime || user.createdAt || new Date().toISOString(),
        lastLoginAt: user.updateTime || user.lastLoginAt || null,
        walletBalance: user.balance || 0,
        orderCount: user.orderCount || 0,
        evaluationScore: user.creditScore ? (user.creditScore / 20) : 0, // 信用分转评价分
        gameNickName: user.gameNickName,
        bio: user.bio,
        contactInfo: user.contactInfo,
        settings: user.settings
      }));
      
      setUsers(userData);

      // 处理分页信息
      const pagination = response?.data?.pagination;
      if (pagination) {
        setTotalItems(pagination.totalItems || userData.length);
        setTotalPages(pagination.totalPages || Math.ceil(userData.length / size));
        setCurrentPage(pagination.currentPage || page);
      } else {
        // 如果没有分页信息，假设这是所有数据
        setTotalItems(userData.length);
        setTotalPages(1);
        setCurrentPage(1);
      }

      toast({
        title: '数据加载成功',
        description: `已从云开发加载 ${userData.length} 个真实用户`,
      });

      console.log('成功加载真实用户数据:', userData);
      return;
      
    } catch (error: any) {
      console.error('云开发API调用失败:', error);
      
      // 检查是否是权限问题
      const errorData = error.response?.data;
      const isPermissionError = errorData?.error === '权限不足' || errorData?.success === false;
      
      if (isPermissionError) {
        console.log('检测到权限不足，使用管理员模拟数据');
        toast({
          title: '使用离线数据',
          description: '云开发API权限验证失败，使用本地管理员数据',
          variant: 'default',
        });
      } else {
        // 显示具体的错误信息
        const errorMessage = error.response?.data?.message || error.message || '未知错误';

        toast({
          title: '连接云开发失败',
          description: `错误: ${errorMessage}，请检查网络连接后重试`,
          variant: 'destructive',
        });
      }

      // 不再使用模拟数据，保持空状态
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 检查登录状态和token
    const checkAuth = () => {
      const token = localStorage.getItem('admin_token');
      console.log('当前管理员token:', token ? '已设置' : '未设置');
      
      if (!token) {
        console.warn('未找到管理员token，可能需要重新登录');
        toast({
          title: '权限验证',
          description: '未找到登录凭证，请重新登录',
          variant: 'destructive',
        });
        
        // 可以选择跳转到登录页面
        // window.location.href = '/login';
        return false;
      }
      return true;
    };
    
    if (checkAuth()) {
      loadUsers();
    }
  }, []);

  // 搜索用户
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      setCurrentPage(1); // 搜索时重置到第一页
      loadUsers(1, pageSize);
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadUsers(page, pageSize);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 改变页面大小时重置到第一页
    loadUsers(1, size);
  };

  // 批量选择处理函数
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allUserIds = users.map(user => user.id);
      setSelectedUsers(allUserIds);
      setIsAllSelected(true);
      setIsIndeterminate(false);
    } else {
      setSelectedUsers([]);
      setIsAllSelected(false);
      setIsIndeterminate(false);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    let newSelectedUsers: string[];
    if (checked) {
      newSelectedUsers = [...selectedUsers, userId];
    } else {
      newSelectedUsers = selectedUsers.filter(id => id !== userId);
    }

    setSelectedUsers(newSelectedUsers);

    // 更新全选状态
    const totalUsers = users.length;
    if (newSelectedUsers.length === 0) {
      setIsAllSelected(false);
      setIsIndeterminate(false);
    } else if (newSelectedUsers.length === totalUsers) {
      setIsAllSelected(true);
      setIsIndeterminate(false);
    } else {
      setIsAllSelected(false);
      setIsIndeterminate(true);
    }
  };

  // 批量操作处理函数
  const handleBulkAction = async (action: string, userIds: string[]) => {
    try {
      switch (action) {
        case 'export':
          // 导出选中用户数据
          await exportUsers(userIds);
          break;
        case 'enable':
          // 批量启用用户
          await bulkUpdateUserStatus(userIds, 'active');
          break;
        case 'disable':
          // 批量禁用用户
          await bulkUpdateUserStatus(userIds, 'disabled');
          break;
        case 'delete':
          // 批量删除用户
          await bulkDeleteUsers(userIds);
          break;
        default:
          console.warn('未知的批量操作:', action);
      }

      // 清空选择
      setSelectedUsers([]);
      setIsAllSelected(false);
      setIsIndeterminate(false);

      // 重新加载数据
      loadUsers(currentPage, pageSize);

    } catch (error) {
      console.error('批量操作失败:', error);
      toast({
        title: '操作失败',
        description: '批量操作执行失败，请重试',
        variant: 'destructive',
      });
    }
  };

  // 导出用户数据
  const exportUsers = async (userIds: string[]) => {
    const selectedUserData = users.filter(user => userIds.includes(user.id));
    const csvContent = generateUserCSV(selectedUserData);
    downloadCSV(csvContent, `用户数据_${new Date().toISOString().split('T')[0]}.csv`);

    toast({
      title: '导出成功',
      description: `已导出 ${userIds.length} 个用户的数据`,
    });
  };

  // 批量更新用户状态
  const bulkUpdateUserStatus = async (userIds: string[], status: 'active' | 'disabled') => {
    // 这里应该调用批量更新API
    // await userApi.bulkUpdateStatus(userIds, status);

    toast({
      title: '状态更新成功',
      description: `已${status === 'active' ? '启用' : '禁用'} ${userIds.length} 个用户`,
    });
  };

  // 批量删除用户
  const bulkDeleteUsers = async (userIds: string[]) => {
    // 这里应该调用批量删除API
    // await userApi.bulkDelete(userIds);

    toast({
      title: '删除成功',
      description: `已删除 ${userIds.length} 个用户`,
    });
  };

  // 生成CSV内容
  const generateUserCSV = (userData: User[]) => {
    const headers = ['用户ID', '昵称', '手机号', '状态', '注册时间', '最后登录'];
    const rows = userData.map(user => [
      user.id,
      user.nickname || '',
      user.phone || '',
      user.status === 'active' ? '正常' : '禁用',
      formatDate(user.createdAt),
      user.lastLoginAt ? formatDate(user.lastLoginAt) : '从未登录'
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  // 下载CSV文件
  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 高级数据导出处理函数
  const handleAdvancedExport = async (options: ExportOptions) => {
    try {
      let exportUsers = users;

      // 如果有日期范围筛选，需要重新获取数据
      if (options.dateRange) {
        // 这里应该调用API获取指定日期范围的用户数据
        // const response = await userApi.getUserList({
        //   page: 1,
        //   limit: 10000, // 获取所有数据用于导出
        //   dateRange: options.dateRange
        // });
        // exportUsers = response.data.users;
      }

      // 准备导出数据，添加计算字段
      const enrichedUsers = exportUsers.map(user => ({
        ...user,
        totalSpent: 0, // 这里应该从API获取用户总消费
        isVerified: user.status === 'active', // 示例：根据状态判断是否认证
      }));

      await exportData(enrichedUsers, userExportFields, options);

      toast({
        title: '导出成功',
        description: `已成功导出 ${enrichedUsers.length} 条用户数据`,
      });

    } catch (error) {
      console.error('导出失败:', error);
      toast({
        title: '导出失败',
        description: error instanceof Error ? error.message : '导出过程中发生错误',
        variant: 'destructive',
      });
    }
  };

  // 由于使用了服务端分页，不需要客户端过滤
  const filteredUsers = users;

  // 查看用户详情
  const handleViewUser = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) {
      toast({
        title: '错误',
        description: '未找到用户信息',
        variant: 'destructive',
      });
      return;
    }

    const userName = user.nickname || user.phone || `用户${user.id}`;
    
    toast({
      title: '查看用户详情',
      description: `正在查看用户 ${userName} 的详细信息`,
    });

    // 显示用户详细信息
    console.log('用户详情:', {
      ID: user.id,
      昵称: user.nickname || '未设置',
      手机号: user.phone || '未绑定',
      认证状态: user.isVerified ? '已认证' : '未认证',
      账户状态: user.status === 'active' ? '正常' : '禁用',
      钱包余额: `¥${(user.walletBalance || 0).toFixed(2)}`,
      订单数量: user.orderCount || 0,
      评价分数: (user.evaluationScore || 0).toFixed(1),
      注册时间: formatDate(user.createdAt),
      最后登录: user.lastLoginAt ? formatDate(user.lastLoginAt) : '从未登录'
    });

    // 这里可以打开用户详情弹窗
    alert(`用户详情：
ID: ${user.id}
昵称: ${user.nickname || '未设置'}
手机号: ${user.phone || '未绑定'}
认证状态: ${user.isVerified ? '已认证' : '未认证'}
账户状态: ${user.status === 'active' ? '正常' : '禁用'}
钱包余额: ¥${(user.walletBalance || 0).toFixed(2)}
订单数量: ${user.orderCount || 0}
评价分数: ${(user.evaluationScore || 0).toFixed(1)}
注册时间: ${formatDate(user.createdAt)}
最后登录: ${user.lastLoginAt ? formatDate(user.lastLoginAt) : '从未登录'}`);
  };

  // 编辑用户信息
  const handleEditUser = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) {
      toast({
        title: '错误',
        description: '未找到用户信息',
        variant: 'destructive',
      });
      return;
    }

    const userName = user.nickname || user.phone || `用户${user.id}`;
    
    toast({
      title: '编辑用户信息',
      description: `正在编辑用户 ${userName} 的信息`,
    });

    // 模拟编辑功能
    const newNickname = prompt(`编辑用户昵称（当前：${user.nickname || '未设置'}）:`, user.nickname || '');
    
    if (newNickname !== null && newNickname !== user.nickname) {
      // 更新用户昵称
      setUsers(users.map(u => 
        u.id === userId 
          ? { ...u, nickname: newNickname }
          : u
      ));
      
      toast({
        title: '更新成功',
        description: `用户昵称已更新为：${newNickname}`,
      });
    }
  };

  // 切换用户状态
  const handleToggleStatus = async (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) {
      toast({
        title: '错误',
        description: '未找到用户信息',
        variant: 'destructive',
      });
      return;
    }

    const newStatus = user.status === 'active' ? 'disabled' : 'active';
    const userName = user.nickname || user.phone || `用户${user.id}`;
    
    try {
      // 调用API更新用户状态
      const response = await userApi.updateUserStatus(userId, newStatus === 'disabled' ? 'inactive' : newStatus);
      
      // 检查响应数据结构
      const responseData = response.data || response;
      
      if (responseData.success || response.status === 200) {
        setUsers(users.map(u => 
          u.id === userId 
            ? { ...u, status: newStatus }
            : u
        ));
        
        toast({
          title: '操作成功',
          description: `用户 ${userName} 已${newStatus === 'active' ? '启用' : '禁用'}`,
        });
      } else {
        throw new Error(responseData.error || responseData.message || '操作失败');
      }
    } catch (error: any) {
      console.error('更新用户状态失败:', error);
      
      // 即使API失败，也在本地更新状态以提供用户反馈
      setUsers(users.map(u => 
        u.id === userId 
          ? { ...u, status: newStatus }
          : u
      ));
      
      toast({
        title: '状态已更新',
        description: `用户 ${userName} 已${newStatus === 'active' ? '启用' : '禁用'}（本地更新）`,
      });
    }
  };

  // 导出用户数据
  const handleExportUsers = () => {
    toast({
      title: '导出数据',
      description: '正在准备用户数据导出...',
    });

    // 模拟导出功能
    setTimeout(() => {
      toast({
        title: '导出完成',
        description: `已导出 ${users.length} 个用户的数据`,
      });
    }, 2000);
  };

  // 添加新用户
  const handleAddUser = () => {
    toast({
      title: '添加用户',
      description: '正在打开添加用户表单...',
    });

    // 这里可以打开添加用户的弹窗或表单
    console.log('添加新用户');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
            <p className="text-gray-600">管理平台所有用户信息</p>
          </div>
          <Button disabled>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            加载中...
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={`loading-${i}`} className="h-16 bg-gray-200 rounded animate-pulse"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
          <p className="text-gray-600">管理平台所有用户信息</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadUsers} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
          <Button variant="outline" onClick={handleExportUsers}>
            <Download className="mr-2 h-4 w-4" />
            快速导出
          </Button>
          <DataExport
            title="高级导出"
            description="自定义导出字段、格式和日期范围"
            fields={userExportFields}
            templates={userExportTemplates}
            onExport={handleAdvancedExport}
            disabled={loading}
            maxRecords={10000}
          />
          <Button onClick={handleAddUser}>
            <UserPlus className="mr-2 h-4 w-4" />
            添加用户
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
          <CardDescription>
            共 {users.length} 个用户，其中 {users.filter(u => u.status === 'active').length} 个活跃用户
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜索用户昵称或手机号..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
          </div>

          {/* 批量操作 */}
          <BulkActions
            selectedItems={selectedUsers}
            totalItems={filteredUsers.length}
            onSelectAll={handleSelectAll}
            onAction={handleBulkAction}
            actions={userBulkActions}
            isAllSelected={isAllSelected}
            isIndeterminate={isIndeterminate}
          />

          {/* 用户表格 */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                    ref={(ref) => {
                      if (ref) {
                        ref.indeterminate = isIndeterminate;
                      }
                    }}
                    aria-label="全选"
                  />
                </TableHead>
                <TableHead>用户信息</TableHead>
                <TableHead>认证状态</TableHead>
                <TableHead>钱包余额</TableHead>
                <TableHead>订单数量</TableHead>
                <TableHead>评价分数</TableHead>
                <TableHead>注册时间</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                      aria-label={`选择用户 ${user.nickname || user.id}`}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={user.avatar} alt={user.nickname || '用户'} />
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {user.nickname ? user.nickname.charAt(0) : user.phone ? user.phone.charAt(0) : 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{user.nickname || '未设置昵称'}</div>
                        <div className="text-sm text-gray-500">
                          {user.phone || '未绑定手机'}
                          {user.gameNickName && (
                            <span className="ml-2 text-blue-600">游戏: {user.gameNickName}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {user.isVerified ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        已认证
                      </Badge>
                    ) : (
                      <Badge variant="secondary">未认证</Badge>
                    )}
                  </TableCell>
                  <TableCell>¥{(user.walletBalance || 0).toFixed(2)}</TableCell>
                  <TableCell>{user.orderCount || 0}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <span className="mr-1">{(user.evaluationScore || 0).toFixed(1)}</span>
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <div
                            key={`star-${i}`}
                            className={`w-3 h-3 ${
                              i < Math.floor(user.evaluationScore || 0)
                                ? 'text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          >
                            ★
                          </div>
                        ))}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(user.createdAt)}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={user.status === 'active' ? 'default' : 'destructive'}
                      className={user.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                    >
                      {user.status === 'active' ? '正常' : '禁用'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleViewUser(user.id)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditUser(user.id)}>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑信息
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleToggleStatus(user.id)}
                          className={user.status === 'active' ? 'text-red-600' : 'text-green-600'}
                        >
                          {user.status === 'active' ? (
                            <>
                              <Ban className="mr-2 h-4 w-4" />
                              禁用用户
                            </>
                          ) : (
                            <>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              启用用户
                            </>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredUsers.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              <p>没有找到匹配的用户</p>
            </div>
          )}

          {/* 分页组件 */}
          <DataPagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            pageSizeOptions={[10, 20, 50, 100]}
            showPageSizeSelector={true}
            showInfo={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}