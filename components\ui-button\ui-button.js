// 统一按钮组件
Component({
  options: {
    multipleSlots: true
  },

  properties: {
    // 按钮类型
    type: {
      type: String,
      value: 'primary' // primary, secondary, success, warning, danger, info, text
    },
    
    // 按钮尺寸
    size: {
      type: String,
      value: 'normal' // mini, small, normal, large
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    
    // 是否加载中
    loading: {
      type: Boolean,
      value: false
    },
    
    // 是否为块级按钮
    block: {
      type: Boolean,
      value: false
    },
    
    // 是否为圆形按钮
    round: {
      type: Boolean,
      value: false
    },
    
    // 是否为朴素按钮
    plain: {
      type: Boolean,
      value: false
    },
    
    // 按钮文本
    text: {
      type: String,
      value: ''
    },
    
    // 图标
    icon: {
      type: String,
      value: ''
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 是否开启点击反馈
    hoverClass: {
      type: String,
      value: 'ui-button--active'
    },
    
    // 防抖延迟（毫秒）
    debounce: {
      type: Number,
      value: 300
    }
  },

  data: {
    isPressed: false,
    lastClickTime: 0
  },

  methods: {
    // 按钮点击事件
    handleTap(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }

      // 防抖处理
      const now = Date.now();
      if (now - this.data.lastClickTime < this.data.debounce) {
        return;
      }
      this.setData({ lastClickTime: now });

      // 触发点击事件
      this.triggerEvent('click', e.detail, {
        bubbles: true,
        composed: true
      });
    },

    // 按钮按下事件
    handleTouchStart() {
      if (!this.data.disabled && !this.data.loading) {
        this.setData({ isPressed: true });
      }
    },

    // 按钮释放事件
    handleTouchEnd() {
      this.setData({ isPressed: false });
    },

    // 按钮取消事件
    handleTouchCancel() {
      this.setData({ isPressed: false });
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化时的逻辑
    }
  }
});
