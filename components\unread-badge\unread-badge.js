// 未读消息徽章组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 未读数量
    count: {
      type: Number,
      value: 0
    },
    // 徽章类型：dot(红点) | count(数字)
    type: {
      type: String,
      value: 'count'
    },
    // 最大显示数量，超过显示 99+
    max: {
      type: Number,
      value: 99
    },
    // 是否显示0
    showZero: {
      type: Boolean,
      value: false
    },
    // 徽章位置：top-right | top-left | bottom-right | bottom-left
    position: {
      type: String,
      value: 'top-right'
    },
    // 徽章大小：normal | large | small
    size: {
      type: String,
      value: 'normal'
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showBadge: false,
    badgeType: 'count',
    displayText: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 更新徽章显示状态
    updateBadgeDisplay() {
      const { count, type, max, showZero, position, size } = this.properties



      // 判断是否显示徽章
      const showBadge = count > 0 || (count === 0 && showZero)

      // 确定徽章类型
      let badgeType = type
      if (type === 'count' && count === 0 && !showZero) {
        badgeType = 'dot'
      }
      
      // 计算显示文本
      let displayText = ''
      if (badgeType === 'count') {
        if (count > max) {
          displayText = `${max}+`
        } else {
          displayText = count.toString()
        }
      }

      // 构建CSS类名
      const badgeClass = `${badgeType} ${position} ${size}`



      this.setData({
        showBadge,
        badgeType: badgeClass,
        displayText
      })
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.updateBadgeDisplay()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'count, type, max, showZero, position, size': function() {
      this.updateBadgeDisplay()
    }
  }
})
