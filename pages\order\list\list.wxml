<!--list.wxml-->
<navigation-bar title="{{isGrabMode ? '抢单大厅' : '我的订单'}}" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)" loading="{{false}}"></navigation-bar>

<!-- 返回按钮 - 只在抢单大厅模式下显示 -->
<back-button wx:if="{{isGrabMode}}" position="top-left" size="normal" back-type="auto"></back-button>

<view class="list-container page-with-custom-nav {{isGrabMode ? 'grab-mode' : ''}}" style="{{navigationBarStyle}}">
  <!-- 状态筛选 (抢单大厅模式下隐藏) -->
  <view class="status-section" wx:if="{{!isGrabMode}}">
    <view class="status-tabs">
      <view class="status-tab {{currentStatus === item.value ? 'active' : ''}}"
            wx:for="{{statusTabs}}"
            wx:key="value"
            bindtap="switchStatus"
            data-status="{{item.value}}">
        <text class="tab-text">{{item.label}}</text>
        <view class="tab-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
      </view>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view class="order-list"
               scroll-y
               scroll-top="{{preserveScrollPosition ? scrollTop : 0}}"
               bindscroll="onScroll"
               bindscrolltolower="loadMore"
               refresher-enabled="{{true}}"
               refresher-triggered="{{refreshing}}"
               bindrefresherrefresh="onRefresh">
    
    <!-- 使用统一的订单卡片组件 -->
    <order-card
      wx:for="{{orderList}}"
      wx:key="_id"
      order-data="{{item}}"
      bind:cardtap="navigateToDetail"
      bind:graborder="grabOrder"
      bind:viewdetail="viewOrderDetail"
      bind:editorder="editOrder"
      bind:cancelorder="cancelOrder"
      bind:contactaccepter="contactAccepter"
      bind:enterchat="enterChat"
      bind:evaluateorder="evaluateOrder">
    </order-card>

    <!-- 加载状态 -->
    <view class="load-more" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>
    
    <view class="no-more" wx:if="{{!hasMore && orderList.length > 0}}">
      <text>没有更多了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && orderList.length === 0}}">
      <view class="empty-icon">{{isGrabMode ? '🎯' : '📋'}}</view>
      <text class="empty-text">{{isGrabMode ? '暂无可抢订单' : '暂无' + currentStatusText + '订单'}}</text>
      <text class="empty-desc" wx:if="{{isGrabMode}}">精英任务即将发布，请稍后刷新查看</text>
      <text class="empty-desc" wx:else>快来发布你的第一个任务吧</text>
      <button class="create-order-btn" bindtap="navigateToCreate">
        {{isGrabMode ? '发布任务' : '立即下单'}}
      </button>
    </view>
  </scroll-view>
</view>

<!-- 浮动创建订单按钮 -->
<view class="btn-floating fade-in" style="animation-delay: 0.8s;" bindtap="navigateToCreate">
  <image wx:if="{{floatingIcon && !iconLoading}}" class="floating-icon" src="{{floatingIcon}}" mode="aspectFit" />
  <text wx:elif="{{iconLoading}}" class="floating-text">⋯</text>
  <text wx:else class="floating-text">+</text>
</view>


