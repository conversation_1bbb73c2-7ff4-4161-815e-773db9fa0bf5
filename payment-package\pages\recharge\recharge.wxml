<!-- 充值页面 -->
<!-- 返回按钮 -->
<back-button position="auto-position" size="normal" back-type="auto"></back-button>

<view class="recharge-container page-with-custom-nav">
  <!-- 当前余额 -->
  <view class="balance-card">
    <view class="balance-header">
      <text class="balance-title">当前余额</text>
      <view class="balance-actions">
        <text class="history-btn" bindtap="viewRechargeHistory">充值记录</text>
      </view>
    </view>
    <view class="balance-amount">
      <text class="currency">¥</text>
      <text class="amount">{{currentBalance}}</text>
    </view>
  </view>

  <!-- 充值金额选择 -->
  <view class="amount-section">
    <view class="section-title">选择充值金额</view>

    <view class="amount-grid">
      <view class="amount-item {{selectedAmount === item.value && !useCustomAmount ? 'selected' : ''}}"
            wx:for="{{amountOptions}}"
            wx:key="value"
            bindtap="selectAmount"
            data-amount="{{item.value}}">
        <view class="amount-label">{{item.label}}</view>
        <view class="popular-tag" wx:if="{{item.popular}}">热门</view>
      </view>
    </view>

    <!-- 自定义金额 -->
    <view class="custom-amount">
      <view class="custom-amount-header">
        <text class="custom-label">自定义金额</text>
        <text class="amount-range">(1-10000元)</text>
      </view>
      <view class="custom-input-wrapper">
        <text class="currency-symbol">¥</text>
        <input class="custom-input"
               type="digit"
               placeholder="请输入充值金额"
               value="{{customAmount}}"
               bindinput="onCustomAmountInput" />
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-section">
    <view class="section-title">支付方式</view>

    <view class="payment-methods">
      <view class="payment-item {{selectedPaymentMethod === item.id ? 'selected' : ''}}"
            wx:for="{{paymentMethods}}"
            wx:key="id"
            bindtap="selectPaymentMethod"
            data-method="{{item.id}}">
        <view class="payment-info">
          <image class="payment-icon" src="{{item.icon}}" />
          <text class="payment-name">{{item.name}}</text>
        </view>
        <view class="payment-check">
          <view class="check-icon {{selectedPaymentMethod === item.id ? 'checked' : ''}}">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 充值说明 -->
  <view class="notice-section">
    <view class="section-title">充值说明</view>
    <view class="notice-list">
      <text class="notice-item">• 充值金额将实时到账，可用于支付订单费用</text>
      <text class="notice-item">• 充值金额不可提现，请根据实际需要充值</text>
      <text class="notice-item">• 如有疑问，请联系客服处理</text>
    </view>
  </view>

  <!-- 确认充值按钮 -->
  <view class="submit-section">
    <view class="amount-summary" wx:if="{{selectedAmount > 0 || (useCustomAmount && customAmount)}}">
      <text class="summary-text">充值金额：¥{{useCustomAmount ? customAmount : selectedAmount}}</text>
    </view>
    <button class="recharge-btn {{(selectedAmount > 0 || (useCustomAmount && customAmount)) && !submitting ? 'active' : ''}}"
            bindtap="confirmRecharge"
            disabled="{{!(selectedAmount > 0 || (useCustomAmount && customAmount)) || submitting}}">
      {{submitting ? '处理中...' : '确认充值'}}
    </button>
  </view>
</view>
<text>pages/payment/recharge/recharge.wxml</text>