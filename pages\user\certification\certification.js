// certification.js
import API from '../../../utils/api.js';

const app = getApp();

Page({
  data: {
    userInfo: {},
    formData: {
      realName: '',
      idCard: '',
      phone: '',
      idCardFront: '',
      idCardBack: ''
    },
    submitting: false,
    canSubmit: false,
    maskedIdCard: '',
    maskedPhone: '',
    certificationTime: ''
  },

  onLoad() {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        maskedIdCard: this.maskIdCard(userInfo.idCard),
        maskedPhone: this.maskPhone(userInfo.phone),
        certificationTime: userInfo.certificationTime ? this.formatTime(userInfo.certificationTime) : ''
      });
    }
  },

  // 输入框变化处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { realName, idCard, phone, idCardFront, idCardBack } = this.data.formData;
    const canSubmit = realName && idCard && phone && idCardFront && idCardBack;
    this.setData({ canSubmit });
  },

  // 上传身份证正面
  uploadIdCardFront() {
    this.uploadImage('idCardFront');
  },

  // 上传身份证反面
  uploadIdCardBack() {
    this.uploadImage('idCardBack');
  },

  // 上传图片
  uploadImage(field) {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        try {
          app.utils.showLoading('上传中...');
          
          // 生成云存储路径
          const cloudPath = `certification/${app.globalData.userInfo._id}/${field}_${Date.now()}.jpg`;
          
          // 上传到云存储
          const uploadResult = await API.uploadFile(tempFilePath, cloudPath);
          
          if (uploadResult.fileID) {
            this.setData({
              [`formData.${field}`]: uploadResult.fileID
            }, () => {
              this.checkCanSubmit();
            });
            app.utils.showSuccess('上传成功');
          }
        } catch (error) {
          console.error('上传失败:', error);
          app.utils.showError('上传失败，请重试');
        } finally {
          app.utils.hideLoading();
        }
      }
    });
  },

  // 提交认证
  async submitCertification() {
    if (this.data.submitting || !this.data.canSubmit) return;

    // 验证身份证格式
    if (!this.validateIdCard(this.data.formData.idCard)) {
      app.utils.showError('请输入正确的身份证号码');
      return;
    }

    // 验证手机号格式
    if (!this.validatePhone(this.data.formData.phone)) {
      app.utils.showError('请输入正确的手机号码');
      return;
    }

    wx.showModal({
      title: '确认提交',
      content: '请确认信息无误，提交后无法修改',
      success: async (res) => {
        if (res.confirm) {
          await this.doSubmitCertification();
        }
      }
    });
  },

  // 执行提交认证
  async doSubmitCertification() {
    this.setData({ submitting: true });

    try {
      const result = await API.certification(this.data.formData);
      
      if (result.success) {
        app.utils.showSuccess('提交成功，请等待审核');
        
        // 更新用户信息
        const updatedUserInfo = {
          ...this.data.userInfo,
          ...this.data.formData,
          isVerified: false, // 等待审核
          certificationStatus: 'pending'
        };
        
        wx.setStorageSync('userInfo', updatedUserInfo);
        app.globalData.userInfo = updatedUserInfo;
        
        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('提交认证失败:', error);
      app.utils.showError('提交失败，请重试');
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 验证身份证号
  validateIdCard(idCard) {
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return reg.test(idCard);
  },

  // 验证手机号
  validatePhone(phone) {
    const reg = /^1[3-9]\d{9}$/;
    return reg.test(phone);
  },

  // 脱敏身份证号
  maskIdCard(idCard) {
    if (!idCard) return '';
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
  },

  // 脱敏手机号
  maskPhone(phone) {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  },

  // 格式化时间
  formatTime(dateStr) {
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
});
