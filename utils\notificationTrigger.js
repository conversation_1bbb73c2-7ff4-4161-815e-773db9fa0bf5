// 通知触发器 - 自动创建各种类型的通知
const API = require('./api.js');

class NotificationTrigger {
  constructor() {
    this.notificationTypes = {
      ORDER_GRABBED: 'orderStatus',
      ORDER_COMPLETED: 'orderStatus', 
      ORDER_CANCELLED: 'orderStatus',
      NEW_ORDER: 'newOrder',
      NEW_MESSAGE: 'chatMessage',
      EVALUATION_RECEIVED: 'evaluation',
      SYSTEM_ANNOUNCEMENT: 'system'
    };
  }

  // 订单被抢通知
  async triggerOrderGrabbedNotification(order, grabberInfo) {
    try {
      console.log('📢 [通知触发] 订单被抢通知:', order.orderNo);
      
      // 通知订单发布者
      await API.createNotification({
        userId: order.customerId,
        type: this.notificationTypes.ORDER_GRABBED,
        title: '订单已被接单',
        content: `您的订单"${order.title || order.requirements}"已被${grabberInfo.nickName}接单`,
        relatedId: order._id || order.orderNo,
        relatedType: 'order',
        priority: 'high'
      });

      console.log('✅ [通知触发] 订单被抢通知发送成功');
      return true;
    } catch (error) {
      console.error('❌ [通知触发] 订单被抢通知失败:', error);
      return false;
    }
  }

  // 订单完成通知
  async triggerOrderCompletedNotification(order, customerInfo, accepterInfo) {
    try {
      console.log('📢 [通知触发] 订单完成通知:', order.orderNo);
      
      // 通知订单发布者
      await API.createNotification({
        userId: order.customerId,
        type: this.notificationTypes.ORDER_COMPLETED,
        title: '订单已完成',
        content: `您的订单"${order.title || order.requirements}"已完成，请进行评价`,
        relatedId: order._id || order.orderNo,
        relatedType: 'order',
        priority: 'normal'
      });

      // 通知接单者
      if (order.accepterId) {
        await API.createNotification({
          userId: order.accepterId,
          type: this.notificationTypes.ORDER_COMPLETED,
          title: '订单已完成',
          content: `订单"${order.title || order.requirements}"已完成，请进行评价`,
          relatedId: order._id || order.orderNo,
          relatedType: 'order',
          priority: 'normal'
        });
      }

      console.log('✅ [通知触发] 订单完成通知发送成功');
      return true;
    } catch (error) {
      console.error('❌ [通知触发] 订单完成通知失败:', error);
      return false;
    }
  }

  // 订单取消通知
  async triggerOrderCancelledNotification(order, reason, cancelledBy) {
    try {
      console.log('📢 [通知触发] 订单取消通知:', order.orderNo);
      
      const title = '订单已取消';
      const content = `订单"${order.title || order.requirements}"已被取消${reason ? `，原因：${reason}` : ''}`;
      
      // 通知相关用户（除了取消者）
      const notifyUsers = [];
      
      if (cancelledBy !== order.customerId) {
        notifyUsers.push(order.customerId);
      }
      
      if (order.accepterId && cancelledBy !== order.accepterId) {
        notifyUsers.push(order.accepterId);
      }
      
      for (const userId of notifyUsers) {
        await API.createNotification({
          userId,
          type: this.notificationTypes.ORDER_CANCELLED,
          title,
          content,
          relatedId: order._id || order.orderNo,
          relatedType: 'order',
          priority: 'normal'
        });
      }

      console.log('✅ [通知触发] 订单取消通知发送成功');
      return true;
    } catch (error) {
      console.error('❌ [通知触发] 订单取消通知失败:', error);
      return false;
    }
  }

  // 新订单通知（发布到抢单大厅）
  async triggerNewOrderNotification(order, publisherInfo) {
    try {
      console.log('📢 [通知触发] 新订单通知:', order.orderNo);
      
      // 这里可以通知关注该类型订单的用户
      // 暂时创建一个系统通知
      await API.createNotification({
        userId: 'system', // 系统通知
        type: this.notificationTypes.NEW_ORDER,
        title: '新订单发布',
        content: `新订单"${order.title || order.requirements}"已发布到抢单大厅`,
        relatedId: order._id || order.orderNo,
        relatedType: 'order',
        priority: 'low'
      });

      console.log('✅ [通知触发] 新订单通知发送成功');
      return true;
    } catch (error) {
      console.error('❌ [通知触发] 新订单通知失败:', error);
      return false;
    }
  }

  // 新消息通知
  async triggerNewMessageNotification(message, chatRoom, senderInfo, receiverId) {
    try {
      await API.createNotification({
        userId: receiverId,
        type: this.notificationTypes.NEW_MESSAGE,
        title: `${senderInfo.nickName}发来消息`,
        content: message.type === 'text' ? message.content : '[图片]',
        relatedId: chatRoom._id,
        relatedType: 'chatRoom',
        priority: 'normal'
      });

      return true;
    } catch (error) {
      console.error('❌ [通知触发] 新消息通知失败:', error);
      return false;
    }
  }

  // 收到评价通知
  async triggerEvaluationReceivedNotification(evaluation, order, evaluatorInfo, evaluatedUserId) {
    try {
      console.log('📢 [通知触发] 收到评价通知:', evaluation._id);
      
      await API.createNotification({
        userId: evaluatedUserId,
        type: this.notificationTypes.EVALUATION_RECEIVED,
        title: '收到新评价',
        content: `${evaluatorInfo.nickName}对订单"${order.title || order.requirements}"给出了评价`,
        relatedId: evaluation._id,
        relatedType: 'evaluation',
        priority: 'normal'
      });

      console.log('✅ [通知触发] 收到评价通知发送成功');
      return true;
    } catch (error) {
      console.error('❌ [通知触发] 收到评价通知失败:', error);
      return false;
    }
  }

  // 系统公告通知
  async triggerSystemAnnouncementNotification(title, content, targetUsers = 'all', priority = 'normal') {
    try {

      
      if (targetUsers === 'all') {
        // 发送给所有用户的逻辑需要在云函数中实现
        // 这里只是示例
        await API.createNotification({
          userId: 'all',
          type: this.notificationTypes.SYSTEM_ANNOUNCEMENT,
          title,
          content,
          relatedId: null,
          relatedType: 'system',
          priority
        });
      } else if (Array.isArray(targetUsers)) {
        // 发送给指定用户列表
        for (const userId of targetUsers) {
          await API.createNotification({
            userId,
            type: this.notificationTypes.SYSTEM_ANNOUNCEMENT,
            title,
            content,
            relatedId: null,
            relatedType: 'system',
            priority
          });
        }
      }

      console.log('✅ [通知触发] 系统公告通知发送成功');
      return true;
    } catch (error) {
      console.error('❌ [通知触发] 系统公告通知失败:', error);
      return false;
    }
  }

  // 批量创建通知
  async batchCreateNotifications(notifications) {
    try {
      const results = [];
      for (const notification of notifications) {
        try {
          const result = await API.createNotification(notification);
          results.push({ success: true, data: result });
        } catch (error) {
          results.push({ success: false, error: error.message });
        }
      }

      const successCount = results.filter(r => r.success).length;
      
      return {
        success: true,
        results,
        successCount,
        totalCount: notifications.length
      };
    } catch (error) {
      console.error('❌ [通知触发] 批量创建通知失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 创建全局实例
const notificationTrigger = new NotificationTrigger();

module.exports = notificationTrigger;
