import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Button, Typography, Space } from 'antd';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  MessageOutlined,
  WalletOutlined,
  BellOutlined,
  StarOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';
// import { useAuthStore } from '@/stores/authStore';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const StyledLayout = styled(Layout)`
  min-height: 100vh;
  background: var(--bg-gradient);
`;

const StyledSider = styled(Sider)`
  background: var(--bg-secondary) !important;
  border-right: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);

  .ant-layout-sider-trigger {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    border-top: 1px solid var(--border-light);
  }
`;

const StyledHeader = styled(Header)`
  background: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const StyledContent = styled(Content)`
  margin: 24px;
  padding: 24px;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  min-height: calc(100vh - 112px);
  overflow: auto;
`;

const Logo = styled.div`
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
  
  .logo-text {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .logo-collapsed {
    font-size: 24px;
    color: var(--primary-color);
  }
`;

const StyledMenu = styled(Menu)`
  background: transparent !important;
  border: none !important;

  .ant-menu-item {
    color: var(--text-secondary) !important;
    margin: 4px 8px !important;
    border-radius: var(--radius-md) !important;
    height: 48px !important;
    line-height: 48px !important;

    &:hover {
      background: var(--primary-light) !important;
      color: var(--text-primary) !important;
    }

    &.ant-menu-item-selected {
      background: var(--primary-light) !important;
      color: var(--primary-color) !important;
      
      &::after {
        display: none;
      }
    }
  }

  .ant-menu-item-icon {
    font-size: 16px;
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-primary);
`;

const TriggerButton = styled(Button)`
  background: transparent !important;
  border: none !important;
  color: var(--text-primary) !important;
  font-size: 18px;
  
  &:hover {
    background: var(--primary-light) !important;
    color: var(--primary-color) !important;
  }
`;

const AdminLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  // const { admin, logout } = useAuthStore();

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
    },
    {
      key: '/orders',
      icon: <ShoppingOutlined />,
      label: '订单管理',
    },
    {
      key: '/chat',
      icon: <MessageOutlined />,
      label: '聊天监控',
    },
    {
      key: '/wallet',
      icon: <WalletOutlined />,
      label: '钱包管理',
    },
    {
      key: '/notifications',
      icon: <BellOutlined />,
      label: '通知管理',
    },
    {
      key: '/evaluations',
      icon: <StarOutlined />,
      label: '评价管理',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleLogout = () => {
    // 临时：直接跳转到登录页
    navigate('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <StyledLayout>
      <StyledSider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={240}
        collapsedWidth={80}
      >
        <Logo>
          {collapsed ? (
            <div className="logo-collapsed">△</div>
          ) : (
            <div className="logo-text">三角洲管理系统</div>
          )}
        </Logo>
        
        <StyledMenu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </StyledSider>

      <Layout>
        <StyledHeader>
          <TriggerButton
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
          />

          <UserInfo>
            <Space>
              <Text style={{ color: 'var(--text-secondary)' }}>
                欢迎回来，
              </Text>
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                trigger={['click']}
              >
                <Space style={{ cursor: 'pointer' }}>
                  <Avatar
                    size="small"
                    icon={<UserOutlined />}
                    style={{
                      backgroundColor: 'var(--primary-color)',
                    }}
                  />
                  <Text style={{ color: 'var(--text-primary)' }}>
                    管理员
                  </Text>
                </Space>
              </Dropdown>
            </Space>
          </UserInfo>
        </StyledHeader>

        <StyledContent>
          <Outlet />
        </StyledContent>
      </Layout>
    </StyledLayout>
  );
};

export default AdminLayout;
