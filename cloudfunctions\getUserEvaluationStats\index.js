// 获取用户评价统计信息云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { userId, includeDetails = false } = event;

  console.log('=== getUserEvaluationStats 云函数开始 ===');
  console.log('接收到的参数:', { userId, includeDetails });
  console.log('用户 openid:', wxContext.OPENID);

  try {
    // 如果没有指定userId，则查询当前用户
    let targetUserId = userId;
    
    if (!targetUserId) {
      // 查找当前用户
      const userResult = await db.collection('users').where({
        openid: wxContext.OPENID
      }).get();

      if (userResult.data.length === 0) {
        return {
          success: false,
          error: '用户不存在'
        };
      }

      targetUserId = userResult.data[0]._id;
    }

    console.log('目标用户ID:', targetUserId);

    // 查询用户作为客户收到的评价（接单者对客户的评价）
    const customerEvaluationsResult = await db.collection('evaluations').where({
      customerId: targetUserId,
      evaluatorType: 'accepter'
    }).get();

    // 查询用户作为接单者收到的评价（客户对接单者的评价）
    const accepterEvaluationsResult = await db.collection('evaluations').where({
      accepterId: targetUserId,
      evaluatorType: 'customer'
    }).get();

    const customerEvaluations = customerEvaluationsResult.data;
    const accepterEvaluations = accepterEvaluationsResult.data;

    // 计算统计信息
    const stats = {
      // 作为客户的统计
      asCustomer: {
        totalCount: customerEvaluations.length,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        recentEvaluations: []
      },
      // 作为接单者的统计
      asAccepter: {
        totalCount: accepterEvaluations.length,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        recentEvaluations: []
      },
      // 总体统计
      overall: {
        totalCount: 0,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      }
    };

    // 计算客户评价统计
    if (customerEvaluations.length > 0) {
      const totalRating = customerEvaluations.reduce((sum, evaluation) => sum + evaluation.rating, 0);
      stats.asCustomer.averageRating = Number((totalRating / customerEvaluations.length).toFixed(1));

      customerEvaluations.forEach(evaluation => {
        stats.asCustomer.ratingDistribution[evaluation.rating]++;
      });

      // 最近的评价（最多5条）
      if (includeDetails) {
        stats.asCustomer.recentEvaluations = customerEvaluations
          .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
          .slice(0, 5)
          .map(evaluation => ({
            rating: evaluation.rating,
            tags: evaluation.tags || [],
            content: evaluation.content || '',
            createTime: evaluation.createTime,
            isAnonymous: evaluation.isAnonymous || false
          }));
      }
    }

    // 计算接单者评价统计
    if (accepterEvaluations.length > 0) {
      const totalRating = accepterEvaluations.reduce((sum, evaluation) => sum + evaluation.rating, 0);
      stats.asAccepter.averageRating = Number((totalRating / accepterEvaluations.length).toFixed(1));

      accepterEvaluations.forEach(evaluation => {
        stats.asAccepter.ratingDistribution[evaluation.rating]++;
      });

      // 最近的评价（最多5条）
      if (includeDetails) {
        stats.asAccepter.recentEvaluations = accepterEvaluations
          .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
          .slice(0, 5)
          .map(evaluation => ({
            rating: evaluation.rating,
            tags: evaluation.tags || [],
            content: evaluation.content || '',
            createTime: evaluation.createTime,
            isAnonymous: evaluation.isAnonymous || false
          }));
      }
    }

    // 计算总体统计
    const allEvaluations = [...customerEvaluations, ...accepterEvaluations];
    stats.overall.totalCount = allEvaluations.length;

    if (allEvaluations.length > 0) {
      const totalRating = allEvaluations.reduce((sum, evaluation) => sum + evaluation.rating, 0);
      stats.overall.averageRating = Number((totalRating / allEvaluations.length).toFixed(1));

      allEvaluations.forEach(evaluation => {
        stats.overall.ratingDistribution[evaluation.rating]++;
      });
    }

    console.log('=== 评价统计获取成功 ===');
    console.log('统计信息:', stats);

    return {
      success: true,
      message: '获取评价统计成功',
      data: {
        userId: targetUserId,
        stats: stats,
        lastUpdated: new Date()
      }
    };
  } catch (error) {
    console.error('=== 获取评价统计失败 ===');
    console.error('错误详情:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
