<!--评价详情页面-->
<view class="container">

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 评价内容 -->
  <view class="evaluation-content" wx:if="{{!loading && evaluation}}">
    
    <!-- 评价者信息 -->
    <view class="evaluator-info" wx:if="{{evaluation.evaluatorInfo}}">
      <view class="avatar-container">
        <image wx:if="{{evaluation.evaluatorInfo.avatarUrl}}" class="avatar" src="{{evaluation.evaluatorInfo.avatarUrl}}" mode="aspectFill" />
        <view wx:else class="avatar default-avatar">
          <text class="avatar-text">👤</text>
        </view>
      </view>
      <view class="user-info">
        <view class="nickname">{{evaluation.evaluatorInfo.nickName || evaluation.fromRole}}</view>
        <view class="role-tag">{{evaluation.fromRole}}</view>
      </view>
    </view>

    <!-- 评分显示 -->
    <view class="rating-section">
      <view class="stars-container">
        <view class="star-item" wx:for="{{[1,2,3,4,5]}}" wx:key="*this">
          <text class="star {{item <= evaluation.rating ? 'filled' : 'empty'}}">★</text>
        </view>
      </view>
      <view class="rating-text">{{evaluation.rating}}分 · {{evaluation.ratingText}}</view>
    </view>

    <!-- 评价标签 -->
    <view class="tags-section" wx:if="{{evaluation.tags && evaluation.tags.length > 0}}">
      <view class="section-title">评价标签</view>
      <view class="tags-container">
        <view 
          class="tag-item" 
          wx:for="{{evaluation.tags}}" 
          wx:key="index"
        >
          {{item}}
        </view>
      </view>
    </view>

    <!-- 评价内容 -->
    <view class="content-section" wx:if="{{evaluation.content}}">
      <view class="section-title">评价内容</view>
      <view class="content-text">{{evaluation.content}}</view>
    </view>

    <!-- 评价时间 -->
    <view class="time-section" wx:if="{{evaluation.evaluationTime}}">
      <view class="time-label">评价时间</view>
      <view class="time-text">{{evaluation.formattedTime}}</view>
    </view>

  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && !evaluation}}">
    <view class="empty-icon">📝</view>
    <view class="empty-text">暂无评价信息</view>
  </view>
</view>
