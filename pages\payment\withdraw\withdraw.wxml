<!-- 提现页面 -->
<!-- 返回按钮 -->
<back-button position="auto-position" size="normal" back-type="auto"></back-button>

<view class="withdraw-container page-with-custom-nav">
  <!-- 余额信息 -->
  <view class="balance-card">
    <view class="balance-header">
      <text class="balance-title">可提现余额</text>
      <view class="balance-actions">
        <text class="history-btn" bindtap="viewWithdrawHistory">提现记录</text>
      </view>
    </view>
    <view class="balance-amount">
      <text class="currency">¥</text>
      <text class="amount">{{currentBalance}}</text>
    </view>
    <view class="frozen-info" wx:if="{{frozenBalance > 0}}">
      <text class="frozen-text">冻结金额：¥{{frozenBalance}}</text>
    </view>
  </view>

  <!-- 提现金额 -->
  <view class="amount-section">
    <view class="section-title">提现金额</view>

    <view class="amount-input-wrapper">
      <text class="currency-symbol">¥</text>
      <input class="amount-input"
             type="digit"
             placeholder="请输入提现金额"
             value="{{withdrawAmount}}"
             bindinput="onWithdrawAmountInput" />
      <button class="all-btn" bindtap="withdrawAll">全部</button>
    </view>

    <view class="amount-tips">
      <text class="tip-text">最低提现金额：¥{{minWithdraw}}</text>
      <text class="tip-text">最高提现金额：¥{{maxWithdraw}}</text>
    </view>
  </view>

  <!-- 提现方式 -->
  <view class="method-section">
    <view class="section-title">提现方式</view>

    <view class="method-list">
      <view class="method-item {{selectedWithdrawMethod === item.id ? 'selected' : ''}}"
            wx:for="{{withdrawMethods}}"
            wx:key="id"
            bindtap="selectWithdrawMethod"
            data-method="{{item.id}}">
        <view class="method-info">
          <image class="method-icon" src="{{item.icon}}" />
          <view class="method-details">
            <text class="method-name">{{item.name}}</text>
            <text class="method-desc">{{item.desc}}</text>
          </view>
        </view>
        <view class="method-check">
          <view class="check-icon {{selectedWithdrawMethod === item.id ? 'checked' : ''}}">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 银行卡信息 -->
  <view class="bank-section" wx:if="{{selectedWithdrawMethod === 'bank'}}">
    <view class="section-title">银行卡信息</view>

    <view class="bank-form">
      <view class="form-item">
        <text class="form-label">开户银行</text>
        <input class="form-input"
               placeholder="请输入开户银行"
               value="{{bankInfo.bankName}}"
               bindinput="onBankInfoInput"
               data-field="bankName" />
      </view>

      <view class="form-item">
        <text class="form-label">银行卡号</text>
        <input class="form-input"
               type="number"
               placeholder="请输入银行卡号"
               value="{{bankInfo.cardNumber}}"
               bindinput="onBankInfoInput"
               data-field="cardNumber" />
      </view>

      <view class="form-item">
        <text class="form-label">持卡人姓名</text>
        <input class="form-input"
               placeholder="请输入持卡人姓名"
               value="{{bankInfo.cardHolder}}"
               bindinput="onBankInfoInput"
               data-field="cardHolder" />
      </view>
    </view>
  </view>

  <!-- 提现说明 -->
  <view class="notice-section">
    <view class="section-title">提现说明</view>
    <view class="notice-list">
      <text class="notice-item">• 微信零钱提现实时到账，银行卡提现1-3个工作日到账</text>
      <text class="notice-item">• 提现申请提交后将冻结相应金额，审核通过后到账</text>
      <text class="notice-item">• 每日提现次数限制为3次，请合理安排提现计划</text>
      <text class="notice-item">• 如有疑问，请联系客服处理</text>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <view class="amount-summary" wx:if="{{withdrawAmount}}">
      <text class="summary-text">提现金额：¥{{withdrawAmount}}</text>
      <text class="fee-text" wx:if="{{withdrawFee > 0}}">手续费：¥{{withdrawFee}}</text>
    </view>
    <button class="withdraw-btn {{withdrawAmount && !submitting ? 'active' : ''}}"
            bindtap="submitWithdraw"
            disabled="{{!withdrawAmount || submitting}}">
      {{submitting ? '提交中...' : '确认提现'}}
    </button>
  </view>
</view>