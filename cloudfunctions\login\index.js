// 用户登录云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('登录云函数开始执行');
  console.log('接收到的参数:', event);

  const wxContext = cloud.getWXContext();
  const { nickName, avatarUrl, gender } = event;

  console.log('微信上下文:', {
    OPENID: wxContext.OPENID,
    UNIONID: wxContext.UNIONID
  });

  try {
    // 查询用户是否已存在
    console.log('查询用户是否存在...');
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    console.log('查询结果:', userResult);

    let userData;

    if (userResult.data.length === 0) {
      console.log('新用户，创建用户记录...');
      // 新用户，创建用户记录
      const createResult = await db.collection('users').add({
        data: {
          openid: wxContext.OPENID,
          unionid: wxContext.UNIONID,
          nickName: nickName || '',
          avatarUrl: avatarUrl || '',
          gender: gender || 0,
          phone: '',
          realName: '',
          idCard: '',
          isVerified: false,
          gameAccounts: [],
          balance: 0,
          creditScore: 100,
          status: 'active',
          createTime: new Date(),
          updateTime: new Date(),
          lastActiveTime: new Date() // 添加最后活跃时间
        }
      });

      console.log('创建用户结果:', createResult);

      userData = {
        _id: createResult._id,
        openid: wxContext.OPENID,
        unionid: wxContext.UNIONID,
        nickName: nickName || '',
        avatarUrl: avatarUrl || '',
        gender: gender || 0,
        phone: '',
        realName: '',
        idCard: '',
        isVerified: false,
        gameAccounts: [],
        balance: 0,
        creditScore: 100,
        status: 'active',
        createTime: new Date(),
        updateTime: new Date(),
        lastActiveTime: new Date() // 添加最后活跃时间
      };
    } else {
      console.log('已存在用户，检查是否需要更新基本信息...');
      // 已存在用户，只在用户没有自定义信息时才更新
      userData = userResult.data[0];

      // 构建更新数据对象
      const updateData = {
        updateTime: new Date(),
        lastActiveTime: new Date() // 每次登录都更新活跃时间
      };

      // 昵称更新逻辑：只有当数据库中没有自定义昵称时，才使用微信昵称更新
      // 如果数据库中已有自定义昵称（不是空或"微信用户"），则保持不变
      console.log('🔍 [登录] 检查昵称保护逻辑');
      console.log('🔍 [登录] 数据库中的昵称:', userData.nickName);
      console.log('🔍 [登录] 微信传入的昵称:', nickName);

      const hasCustomNickName = userData.nickName &&
                                userData.nickName !== '' &&
                                userData.nickName !== '微信用户';

      console.log('🔍 [登录] 是否有自定义昵称:', hasCustomNickName);

      if (!hasCustomNickName) {
        // 数据库中没有自定义昵称，可以使用微信昵称（如果不是默认值）
        if (nickName && nickName !== '微信用户') {
          updateData.nickName = nickName;
          userData.nickName = nickName;
          console.log('🔄 [登录] 使用微信昵称更新:', nickName);
        } else {
          console.log('🔄 [登录] 微信昵称也是默认值，不更新');
        }
      } else {
        console.log('🔄 [登录] 保持现有自定义昵称:', userData.nickName);
      }

      // 头像更新逻辑：只有当数据库中没有自定义头像时，才使用微信头像更新
      // 自定义头像通常是云存储的fileID（包含cloud://）或者不是微信头像URL
      console.log('🔍 [登录] 检查头像保护逻辑');
      console.log('🔍 [登录] 数据库中的头像:', userData.avatarUrl);
      console.log('🔍 [登录] 微信传入的头像:', avatarUrl);

      const hasCustomAvatar = userData.avatarUrl &&
                             userData.avatarUrl !== '' &&
                             (userData.avatarUrl.includes('cloud://') ||
                              !userData.avatarUrl.includes('wx.qlogo.cn'));

      console.log('🔍 [登录] 是否有自定义头像:', hasCustomAvatar);

      if (!hasCustomAvatar) {
        // 数据库中没有自定义头像，可以使用微信头像（如果不是空）
        if (avatarUrl && avatarUrl !== '') {
          updateData.avatarUrl = avatarUrl;
          userData.avatarUrl = avatarUrl;
          console.log('🔄 [登录] 使用微信头像更新');
        } else {
          console.log('🔄 [登录] 微信头像为空，不更新');
        }
      } else {
        console.log('🔄 [登录] 保持现有自定义头像');
      }

      // 性别信息可以更新，因为这个不会被用户主动修改
      if (gender !== undefined && userData.gender !== gender) {
        updateData.gender = gender;
        userData.gender = gender;
      }

      // 只有当有数据需要更新时才执行数据库更新
      console.log('🔍 [登录] 准备更新的数据:', updateData);
      console.log('🔍 [登录] updateData字段数量:', Object.keys(updateData).length);

      if (Object.keys(updateData).length > 1) { // 大于1是因为updateTime总是存在
        console.log('🔄 [登录] 执行数据库更新:', updateData);
        await db.collection('users').doc(userData._id).update({
          data: updateData
        });
        console.log('✅ [登录] 数据库更新完成');
      } else {
        console.log('✅ [登录] 用户信息无需更新，保持现有自定义信息');
      }

      userData.updateTime = new Date();

      // 最终检查返回的用户数据
      console.log('🔍 [登录] 最终返回的用户数据:', {
        nickName: userData.nickName,
        avatarUrl: userData.avatarUrl,
        gameNickName: userData.gameNickName
      });
    }

    console.log('最终返回的用户数据:', userData);

    const result = {
      success: true,
      data: userData
    };

    console.log('返回结果:', result);
    return result;
  } catch (error) {
    console.error('登录失败:', error);
    const errorResult = {
      success: false,
      error: error.message
    };
    console.log('错误返回结果:', errorResult);
    return errorResult;
  }
};
