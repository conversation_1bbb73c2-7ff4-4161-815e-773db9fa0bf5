// 获取订单统计数量云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();

  try {
    // 查找用户
    let userResult;
    let userId;

    try {
      userResult = await db.collection('users').where({
        openid: wxContext.OPENID
      }).get();

      if (userResult.data.length === 0) {
        return {
          success: false,
          error: '用户不存在'
        };
      }

      userId = userResult.data[0]._id;
    } catch (userError) {
      // 如果用户集合不存在，返回默认统计
      if (userError.errCode === -502005 || userError.errCode === -502002) {
        console.log('用户集合不存在，返回默认统计');
        return {
          success: true,
          data: {
            all: 0,
            pending: 0,
            accepted: 0,
            in_progress: 0,
            completed: 0,
            cancelled: 0,
            waiting_match: 0
          }
        };
      } else {
        throw userError;
      }
    }

    // 构建基础查询条件（用户作为客户或接单者的订单）
    const baseCondition = _.or([
      { customerId: userId },
      { accepterId: userId }
    ]);

    // 并行查询各种状态的订单数量
    const [
      allCount,
      pendingCount,
      acceptedCount,
      inProgressCount,
      proofSubmittedCount,
      completedCount,
      cancelledCount,
      waitingMatchCount
    ] = await Promise.all([
      // 全部订单
      db.collection('orders').where(baseCondition).count(),
      
      // 待接单
      db.collection('orders').where({
        ...baseCondition,
        status: 'pending'
      }).count(),
      
      // 已接单
      db.collection('orders').where({
        ...baseCondition,
        status: 'accepted'
      }).count(),
      
      // 进行中
      db.collection('orders').where({
        ...baseCondition,
        status: 'in_progress'
      }).count(),

      // 待确认
      db.collection('orders').where({
        ...baseCondition,
        status: 'proof_submitted'
      }).count(),

      // 已完成
      db.collection('orders').where({
        ...baseCondition,
        status: 'completed'
      }).count(),
      
      // 已取消
      db.collection('orders').where({
        ...baseCondition,
        status: 'cancelled'
      }).count(),
      
      // 等待匹配
      db.collection('orders').where({
        ...baseCondition,
        status: 'waiting_match'
      }).count()
    ]);

    return {
      success: true,
      data: {
        all: allCount.total,
        pending: pendingCount.total,
        accepted: acceptedCount.total,
        in_progress: inProgressCount.total,
        proof_submitted: proofSubmittedCount.total,
        completed: completedCount.total,
        cancelled: cancelledCount.total,
        waiting_match: waitingMatchCount.total
      }
    };
  } catch (error) {
    console.error('获取订单统计失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
