import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        modifyVars: {
          // Ant Design 主题定制
          '@primary-color': '#00d4ff',
          '@success-color': '#00ff88',
          '@warning-color': '#fbbf24',
          '@error-color': '#ef4444',
          '@info-color': '#00d4ff',
          '@text-color': '#f8fafc',
          '@text-color-secondary': '#cbd5e1',
          '@background-color-base': '#0f1419',
          '@component-background': '#1e293b',
          '@border-color-base': '#334155',
          '@border-radius-base': '12px',
        },
      },
    },
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          charts: ['recharts'],
        },
      },
    },
  },
})
