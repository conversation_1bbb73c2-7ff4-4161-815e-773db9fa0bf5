// completed.js - 已完成订单页面
const API = require('../../../utils/api.js');
const { buildQueryString } = require('../../../utils/urlHelper.js');
const {
  ORDER_STATUS_NAMES,
  SERVICE_TYPE_NAMES,
  COMPANION_LEVEL_NAMES
} = require('../../../utils/constants.js');

const app = getApp();

Page({
  data: {
    orderList: [],
    loading: false,
    hasMore: true,
    refreshing: false,
    scrollTop: 0,
    preserveScrollPosition: false,
    navigationBarStyle: '',
    navLoading: false
  },

  async onLoad(options) {
    console.log('=== 已完成订单页面加载 ===');

    // 动态计算导航栏高度
    this.calculateNavigationBarHeight();

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '已完成订单'
    });

    // 确保用户信息已加载
    await this.ensureUserInfoLoaded();

    // 加载已完成订单列表
    await this.loadOrderList(true);
  },

  onShow() {
    console.log('=== 已完成订单页面显示 ===');
    
    // 页面显示时刷新数据
    this.loadOrderList(true);
  },

  onUnload() {
    console.log('=== 已完成订单页面卸载 ===');
  },

  // 动态计算导航栏高度
  calculateNavigationBarHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const navBarHeight = 44; // 导航栏高度
    const totalHeight = statusBarHeight + navBarHeight;
    
    this.setData({
      navigationBarStyle: `--nav-bar-height: ${totalHeight}px;`
    });
  },

  // 确保用户信息已加载
  async ensureUserInfoLoaded() {
    if (!app.globalData.userInfo) {
      try {
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo) {
          app.globalData.userInfo = userInfo;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    }
  },

  // 滚动事件处理
  onScroll(e) {
    // 保存当前滚动位置
    this.setData({
      scrollTop: e.detail.scrollTop
    });
  },

  // 下拉刷新
  onRefresh() {
    console.log('🔄 [已完成订单] 下拉刷新');

    // 强制隐藏任何可能的系统加载框
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);

    this.setData({ refreshing: true });

    this.loadOrderList(true).finally(() => {
      // 强制隐藏任何可能的系统加载框
      wx.hideLoading();
      setTimeout(() => wx.hideLoading(), 10);

      this.setData({ refreshing: false });
    });
  },

  // 加载更多
  loadMore() {
    if (!this.data.loading && this.data.hasMore) {
      this.loadOrderList(false);
    }
  },

  // 加载订单列表
  async loadOrderList(reset = false) {
    // 防止重复加载
    if (this.data.loading) {
      console.log('📋 [已完成订单] 正在加载中，跳过重复请求');
      return;
    }

    this.setData({ loading: true });

    try {
      const currentList = reset ? [] : this.data.orderList;
      const page = reset ? 1 : Math.floor(currentList.length / 10) + 1;
      
      console.log('📋 [已完成订单] 开始加载订单列表，页码:', page);

      // 检查是否为演示模式
      const userInfo = app.globalData.userInfo;
      let newList = [];

      if (userInfo && userInfo.isDemo) {
        // 演示模式：显示空状态
        console.log('📋 [已完成订单] 演示模式，显示空状态');
        newList = [];
      } else {
        // 正常模式：调用API获取已完成订单
        try {
          const params = {
            status: 'completed', // 只获取已完成订单
            role: 'all', // 获取用户作为客户或接单者的所有已完成订单
            page,
            pageSize: 10
          };

          console.log('📋 [已完成订单] 调用API参数:', params);
          const result = await API.getOrderList(params);
          console.log('📋 [已完成订单] API返回结果:', result);

          if (result.success && result.data && result.data.list) {
            console.log('📋 [已完成订单] API调用成功，订单数量:', result.data.list.length);

            // 检查返回的订单状态
            result.data.list.forEach((order, index) => {
              console.log(`📋 [已完成订单] 订单${index + 1}:`, {
                id: order._id,
                title: order.title,
                status: order.status,
                isCompleted: order.status === 'completed'
              });
            });

            // 前端再次筛选，确保只显示已完成订单
            const completedOrders = result.data.list.filter(order => order.status === 'completed');
            console.log('📋 [已完成订单] 筛选后的已完成订单数量:', completedOrders.length);

            // 格式化订单数据并过滤掉null值
            newList = completedOrders
              .map(order => this.formatOrderData(order))
              .filter(order => order !== null);
          } else {
            console.log('📋 [已完成订单] API调用失败:', result);
          }
        } catch (apiError) {
          console.error('📋 [已完成订单] API调用异常:', apiError);
          newList = [];
        }
      }

      const finalList = reset ? newList : [...currentList, ...newList];
      
      this.setData({
        orderList: finalList,
        hasMore: newList.length >= 10,
        preserveScrollPosition: !reset
      });

      console.log('📋 [已完成订单] 订单列表更新完成，总数:', finalList.length);

    } catch (error) {
      console.error('📋 [已完成订单] 加载订单列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 格式化订单数据
  formatOrderData(order) {
    // 确保只处理已完成订单
    if (order.status !== 'completed') {
      console.warn('⚠️ [已完成订单] 尝试格式化非已完成订单:', {
        id: order._id,
        title: order.title,
        status: order.status
      });
      return null;
    }

    // 复用原有的格式化逻辑
    const formattedOrder = {
      ...order,
      _id: order._id,
      orderNo: order.orderNo,
      statusText: ORDER_STATUS_NAMES[order.status] || order.status,
      serviceTypeText: SERVICE_TYPE_NAMES[order.serviceType] || order.serviceType,
      companionLevelText: order.companionLevel ? COMPANION_LEVEL_NAMES[order.companionLevel] : '',
      formattedCreateTime: this.formatTime(order.createTime),
      formattedServiceTime: this.formatTime(order.serviceTime),
      canGrab: false, // 已完成订单不能抢单
      canEdit: false, // 已完成订单不能编辑
      canCancel: false, // 已完成订单不能取消
      canEvaluate: order.status === 'completed' && !order.isEvaluated,
      showContactButton: false, // 已完成订单不显示联系按钮
      showChatButton: false // 已完成订单不显示聊天按钮
    };

    return formattedOrder;
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    
    return `${year}-${month}-${day} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  },

  // 导航到订单详情
  navigateToDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/order-package/pages/detail/detail?id=${id}`
    });
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const { id } = e.detail;
    wx.navigateTo({
      url: `/order-package/pages/detail/detail?id=${id}`
    });
  },

  // 评价订单
  evaluateOrder(e) {
    const { id } = e.detail;
    wx.navigateTo({
      url: `/order-package/pages/evaluation/evaluation?orderId=${id}`
    });
  },

  // 重新下单
  reorder(e) {
    const { id } = e.currentTarget.dataset;
    const order = this.data.orderList.find(item => item._id === id);

    if (order) {
      let url = '/order-package/pages/create/create';
      if (order.companionInfo) {
        url += `?companionId=${order.companionInfo._id}`;
      }
      wx.navigateTo({ url });
    }
  },

  // 导航到创建订单页面
  navigateToCreate() {
    wx.navigateTo({
      url: '/order-package/pages/create/create'
    });
  }
});
