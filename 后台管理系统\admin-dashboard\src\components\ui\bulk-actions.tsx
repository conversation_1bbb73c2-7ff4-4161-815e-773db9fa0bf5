import React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  Trash2, 
  Download, 
  Edit, 
  CheckCircle, 
  XCircle, 
  MoreHorizontal,
  Ban,
  UserCheck
} from 'lucide-react';

interface BulkAction {
  key: string;
  label: string;
  icon: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  requiresConfirmation?: boolean;
  confirmTitle?: string;
  confirmDescription?: string;
}

interface BulkActionsProps {
  selectedItems: string[];
  totalItems: number;
  onSelectAll: (checked: boolean) => void;
  onAction: (action: string, selectedItems: string[]) => void;
  actions: BulkAction[];
  isAllSelected: boolean;
  isIndeterminate: boolean;
}

export function BulkActions({
  selectedItems,
  totalItems,
  onSelectAll,
  onAction,
  actions,
  isAllSelected,
  isIndeterminate,
}: BulkActionsProps) {
  const hasSelection = selectedItems.length > 0;

  return (
    <div className="flex items-center justify-between py-4">
      {/* 左侧：全选复选框和选择信息 */}
      <div className="flex items-center space-x-4">
        <Checkbox
          checked={isAllSelected}
          onCheckedChange={onSelectAll}
          ref={(ref) => {
            if (ref) {
              ref.indeterminate = isIndeterminate;
            }
          }}
          aria-label="全选"
        />
        <span className="text-sm text-muted-foreground">
          {hasSelection ? (
            <>已选择 {selectedItems.length} 项</>
          ) : (
            <>共 {totalItems} 项</>
          )}
        </span>
      </div>

      {/* 右侧：批量操作按钮 */}
      {hasSelection && (
        <div className="flex items-center space-x-2">
          {actions.map((action) => (
            <ActionButton
              key={action.key}
              action={action}
              selectedItems={selectedItems}
              onAction={onAction}
            />
          ))}
          
          {/* 更多操作下拉菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>批量操作</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {actions.slice(3).map((action) => (
                <DropdownMenuItem
                  key={action.key}
                  onClick={() => onAction(action.key, selectedItems)}
                  className={action.variant === 'destructive' ? 'text-red-600' : ''}
                >
                  {action.icon}
                  <span className="ml-2">{action.label}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
    </div>
  );
}

interface ActionButtonProps {
  action: BulkAction;
  selectedItems: string[];
  onAction: (action: string, selectedItems: string[]) => void;
}

function ActionButton({ action, selectedItems, onAction }: ActionButtonProps) {
  if (action.requiresConfirmation) {
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button variant={action.variant || 'outline'} size="sm">
            {action.icon}
            <span className="ml-2">{action.label}</span>
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {action.confirmTitle || '确认操作'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {action.confirmDescription || 
                `您确定要对选中的 ${selectedItems.length} 项执行"${action.label}"操作吗？此操作不可撤销。`
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => onAction(action.key, selectedItems)}
              className={action.variant === 'destructive' ? 'bg-red-600 hover:bg-red-700' : ''}
            >
              确认
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <Button
      variant={action.variant || 'outline'}
      size="sm"
      onClick={() => onAction(action.key, selectedItems)}
    >
      {action.icon}
      <span className="ml-2">{action.label}</span>
    </Button>
  );
}

// 预定义的批量操作配置
export const userBulkActions: BulkAction[] = [
  {
    key: 'export',
    label: '导出',
    icon: <Download className="h-4 w-4" />,
    variant: 'outline',
  },
  {
    key: 'enable',
    label: '启用',
    icon: <CheckCircle className="h-4 w-4" />,
    variant: 'outline',
  },
  {
    key: 'disable',
    label: '禁用',
    icon: <Ban className="h-4 w-4" />,
    variant: 'outline',
    requiresConfirmation: true,
    confirmTitle: '确认禁用用户',
    confirmDescription: '禁用后，用户将无法登录和使用系统功能。',
  },
  {
    key: 'delete',
    label: '删除',
    icon: <Trash2 className="h-4 w-4" />,
    variant: 'destructive',
    requiresConfirmation: true,
    confirmTitle: '确认删除用户',
    confirmDescription: '删除后，用户数据将无法恢复，请谨慎操作。',
  },
];

export const orderBulkActions: BulkAction[] = [
  {
    key: 'export',
    label: '导出',
    icon: <Download className="h-4 w-4" />,
    variant: 'outline',
  },
  {
    key: 'complete',
    label: '标记完成',
    icon: <CheckCircle className="h-4 w-4" />,
    variant: 'outline',
  },
  {
    key: 'cancel',
    label: '取消订单',
    icon: <XCircle className="h-4 w-4" />,
    variant: 'outline',
    requiresConfirmation: true,
    confirmTitle: '确认取消订单',
    confirmDescription: '取消后，订单状态将变为已取消，此操作不可撤销。',
  },
  {
    key: 'delete',
    label: '删除',
    icon: <Trash2 className="h-4 w-4" />,
    variant: 'destructive',
    requiresConfirmation: true,
    confirmTitle: '确认删除订单',
    confirmDescription: '删除后，订单数据将无法恢复，请谨慎操作。',
  },
];
