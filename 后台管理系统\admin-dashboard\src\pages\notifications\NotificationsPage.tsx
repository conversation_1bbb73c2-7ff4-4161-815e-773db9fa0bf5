import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bell, 
  Search, 
  Filter, 
  Send,
  Plus,
  Eye,
  Edit,
  Trash2,
  Users,
  MessageSquare,
  AlertCircle,
  CheckCircle,
  MoreHorizontal,
  RefreshCw
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { notificationApi, userApi } from '@/services/cloudApi';
import type { Notification } from '@/types';

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newNotification, setNewNotification] = useState({
    title: '',
    content: '',
    type: 'system',
    isBroadcast: true,
  });

  // 用户信息缓存
  const [userCache, setUserCache] = useState<Record<string, any>>({});

  const { toast } = useToast();

  // 获取用户显示名称
  const getUserDisplayName = (userId: string) => {
    if (!userId) return '未知用户';

    // 检查缓存
    const cachedUser = userCache[userId];
    if (cachedUser) {
      return cachedUser.nickname || cachedUser.nickName || cachedUser.name || `用户${userId.slice(-6)}`;
    }

    // 返回用户ID的后6位作为显示名
    return `用户${userId.slice(-6)}`;
  };

  // 异步获取用户信息（后台加载）
  const loadUserInfo = async (userId: string) => {
    if (!userId || userCache[userId]) return;

    try {
      const response = await userApi.getUserDetail(userId);

      // 处理不同的响应格式
      let userInfo = null;
      if (response?.data?.success && response.data.data) {
        userInfo = response.data.data;
      } else if (response?.data && !response.data.success) {
        userInfo = response.data;
      } else if (response?.data) {
        userInfo = response.data;
      }

      if (userInfo) {
        setUserCache(prev => ({
          ...prev,
          [userId]: {
            _id: userInfo._id || userId,
            nickname: userInfo.nickname || userInfo.nickName || userInfo.name,
            avatar: userInfo.avatar || userInfo.avatarUrl,
            phone: userInfo.phone,
            ...userInfo
          }
        }));
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 设置默认用户信息
      setUserCache(prev => ({
        ...prev,
        [userId]: {
          _id: userId,
          nickname: `用户${userId.slice(-6)}`,
          avatar: null
        }
      }));
    }
  };

  // 批量加载用户信息
  const loadUsersInfo = async (notifications: any[]) => {
    const userIds = new Set<string>();

    // 收集所有用户ID
    notifications.forEach(notification => {
      if (notification.receiverId) userIds.add(notification.receiverId);
    });

    // 批量获取用户信息
    const userPromises = Array.from(userIds).map(userId => loadUserInfo(userId));

    // 等待所有用户信息加载完成
    try {
      await Promise.allSettled(userPromises);
      console.log('用户信息加载完成，缓存状态:', userCache);
    } catch (error) {
      console.error('批量加载用户信息失败:', error);
    }
  };

  // 加载通知数据
  const loadNotifications = async () => {
    try {
      setLoading(true);
      
      // 调用真实的API获取通知数据
      console.log('🔄 开始加载通知数据...');
      const response = await notificationApi.getNotificationList({
        page: 1,
        limit: 100,
        type: activeTab === 'all' ? undefined : activeTab,
        search: searchTerm
      });

      console.log('📥 通知API响应:', response);

      if (response.data && response.data.success) {
        const notifications = response.data.data?.notifications || [];
        console.log('✅ 成功获取通知数据:', notifications);

        // 转换数据格式以匹配前端期望的结构
        const formattedNotifications = notifications.map((notif: any) => ({
          id: notif._id,
          userId: notif.receiverId,
          title: notif.title,
          content: notif.content,
          type: notif.type,
          isRead: notif.status === 'read',
          createdAt: notif.createTime,
        }));

        setNotifications(formattedNotifications);

        // 异步加载用户信息
        loadUsersInfo(notifications);

        toast({
          title: '数据加载成功',
          description: `已从云数据库加载 ${formattedNotifications.length} 条真实通知`,
        });

        return; // 成功加载真实数据，直接返回
      } else {
        throw new Error(response.data?.error || '获取通知数据失败');
      }
    } catch (error: any) {
      console.error('加载通知数据失败:', error);

      toast({
        title: '数据加载失败',
        description: '无法连接到服务器，请检查网络连接后重试',
        variant: 'destructive',
      });

      // 不再使用模拟数据，保持空状态
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNotifications();
  }, [activeTab]);

  // 搜索通知
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm) {
        loadNotifications();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  const filteredNotifications = notifications.filter(notif => {
    const matchesSearch = notif.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notif.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = activeTab === 'all' || notif.type === activeTab;
    return matchesSearch && matchesType;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'system':
        return <AlertCircle className="h-4 w-4 text-blue-600" />;
      case 'order':
        return <MessageSquare className="h-4 w-4 text-green-600" />;
      case 'payment':
        return <CheckCircle className="h-4 w-4 text-purple-600" />;
      case 'evaluation':
        return <Eye className="h-4 w-4 text-orange-600" />;
      default:
        return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      system: { label: '系统', color: 'bg-blue-100 text-blue-800' },
      order: { label: '订单', color: 'bg-green-100 text-green-800' },
      payment: { label: '支付', color: 'bg-purple-100 text-purple-800' },
      evaluation: { label: '评价', color: 'bg-orange-100 text-orange-800' },
    };

    const config = typeConfig[type as keyof typeof typeConfig];
    if (!config) return null;

    return (
      <Badge variant="secondary" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleCreateNotification = async () => {
    try {
      // 调用API创建通知
      const response = await notificationApi.createNotification({
        title: newNotification.title,
        content: newNotification.content,
        type: newNotification.type,
        isBroadcast: newNotification.isBroadcast,
      });
      
      if (response.success) {
        const notification: Notification = {
          id: response.data.id || `notif_${Date.now()}`,
          userId: newNotification.isBroadcast ? undefined : 'user_001',
          title: newNotification.title,
          content: newNotification.content,
          type: newNotification.type as any,
          isRead: false,
          createdAt: new Date().toISOString(),
        };

        setNotifications([notification, ...notifications]);
        setNewNotification({ title: '', content: '', type: 'system', isBroadcast: true });
        setIsCreateDialogOpen(false);
        
        toast({
          title: '通知发送成功',
          description: `已${newNotification.isBroadcast ? '向所有用户' : '向指定用户'}发送通知`,
        });
      } else {
        throw new Error(response.error || '发送失败');
      }
    } catch (error: any) {
      console.error('创建通知失败:', error);
      
      toast({
        title: '发送失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteNotification = async (id: string) => {
    try {
      // 调用API删除通知
      const response = await notificationApi.deleteNotification(id);
      
      if (response.success) {
        setNotifications(notifications.filter(notif => notif.id !== id));
        
        toast({
          title: '删除成功',
          description: '通知已删除',
        });
      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (error: any) {
      console.error('删除通知失败:', error);
      
      toast({
        title: '删除失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 统计数据
  const totalNotifications = notifications.length;
  const unreadCount = notifications.filter(notif => !notif.isRead).length;
  const broadcastCount = notifications.filter(notif => !notif.userId).length;

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">通知管理</h1>
            <p className="text-gray-600">管理系统通知和消息推送</p>
          </div>
          <Button disabled>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            加载中...
          </Button>
        </div>
        
        <div className="grid gap-4 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">通知管理</h1>
          <p className="text-gray-600">管理系统通知和消息推送</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadNotifications} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                发送通知
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>发送新通知</DialogTitle>
                <DialogDescription>
                  创建并发送通知给用户
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">通知标题</Label>
                  <Input
                    id="title"
                    value={newNotification.title}
                    onChange={(e) => setNewNotification({...newNotification, title: e.target.value})}
                    placeholder="请输入通知标题"
                  />
                </div>
                <div>
                  <Label htmlFor="content">通知内容</Label>
                  <Textarea
                    id="content"
                    value={newNotification.content}
                    onChange={(e) => setNewNotification({...newNotification, content: e.target.value})}
                    placeholder="请输入通知内容"
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="type">通知类型</Label>
                  <Select value={newNotification.type} onValueChange={(value) => setNewNotification({...newNotification, type: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="system">系统通知</SelectItem>
                      <SelectItem value="order">订单通知</SelectItem>
                      <SelectItem value="payment">支付通知</SelectItem>
                      <SelectItem value="evaluation">评价通知</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="broadcast"
                    checked={newNotification.isBroadcast}
                    onChange={(e) => setNewNotification({...newNotification, isBroadcast: e.target.checked})}
                    className="rounded"
                  />
                  <Label htmlFor="broadcast">系统广播（发送给所有用户）</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateNotification} disabled={!newNotification.title || !newNotification.content}>
                  <Send className="mr-2 h-4 w-4" />
                  发送通知
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总通知数</CardTitle>
            <Bell className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalNotifications}</div>
            <p className="text-xs text-muted-foreground">所有通知记录</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">未读通知</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{unreadCount}</div>
            <p className="text-xs text-muted-foreground">待用户查看</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统广播</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{broadcastCount}</div>
            <p className="text-xs text-muted-foreground">全员通知</p>
          </CardContent>
        </Card>
      </div>

      {/* 通知列表 */}
      <Card>
        <CardHeader>
          <CardTitle>通知列表</CardTitle>
          <CardDescription>
            管理所有系统通知和用户消息
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="system">系统</TabsTrigger>
              <TabsTrigger value="order">订单</TabsTrigger>
              <TabsTrigger value="payment">支付</TabsTrigger>
              <TabsTrigger value="evaluation">评价</TabsTrigger>
            </TabsList>

            {/* 搜索和筛选 */}
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索通知标题或内容..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                筛选
              </Button>
            </div>

            <TabsContent value={activeTab}>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>通知信息</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>接收者</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>发送时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredNotifications.map((notification) => (
                    <TableRow key={notification.id}>
                      <TableCell>
                        <div className="flex items-start space-x-3">
                          {getTypeIcon(notification.type)}
                          <div>
                            <div className="font-medium">{notification.title}</div>
                            <div className="text-sm text-gray-500 max-w-md truncate">
                              {notification.content}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getTypeBadge(notification.type)}
                      </TableCell>
                      <TableCell>
                        {notification.userId ? (
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={userCache[notification.userId]?.avatar || "/placeholder.svg?height=24&width=24"} />
                              <AvatarFallback className="text-xs">用</AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col">
                              <span className="text-sm font-medium">{getUserDisplayName(notification.userId)}</span>
                              <span className="text-xs text-gray-500">#{notification.userId.slice(-8)}</span>
                            </div>
                          </div>
                        ) : (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700">
                            <Users className="mr-1 h-3 w-3" />
                            全员广播
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={notification.isRead ? 'default' : 'secondary'} className={
                          notification.isRead ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }>
                          {notification.isRead ? '已读' : '未读'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {formatDate(notification.createdAt)}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑通知
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteNotification(notification.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除通知
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredNotifications.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>没有找到匹配的通知</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}