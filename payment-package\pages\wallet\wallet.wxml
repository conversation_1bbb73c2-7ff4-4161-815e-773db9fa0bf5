<!--wallet.wxml - 科技感钱包页面-->
<navigation-bar title="我的钱包" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)" loading="{{navLoading}}"></navigation-bar>

<!-- 返回按钮 - 与聊天页面和订单详情页面保持一致 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
  </view>
</view>

<!-- G.T.I. SECURITY 科技感加载框 -->
<view wx:if="{{loading}}" class="gti-loading-container">
  <view class="gti-loading-backdrop"></view>
  <view class="gti-loading-content">
    <view class="gti-logo-container">
      <image class="gti-logo" src="cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/logos/gti-security-logo.png" mode="aspectFit"></image>
    </view>
    <view class="gti-loading-spinner">
      <view class="spinner-ring"></view>
      <view class="spinner-ring"></view>
      <view class="spinner-ring"></view>
    </view>
    <text class="gti-loading-text">正在加载钱包信息...</text>
    <view class="gti-loading-dots">
      <view class="dot dot-1"></view>
      <view class="dot dot-2"></view>
      <view class="dot dot-3"></view>
    </view>
  </view>
</view>

<view class="wallet-container page-with-custom-nav avoid-capsule">
  <!-- 科技感余额卡片 -->
  <view class="balance-card cyber-card fade-in" style="animation-delay: 0.1s;">
    <view class="card-glow"></view>
    <view class="balance-header">
      <view class="balance-title-section">
        <text class="balance-icon">💰</text>
        <text class="balance-title">账户余额</text>
      </view>
      <view class="balance-actions">
        <view class="action-btn" bindtap="refreshBalance">
          <text class="action-icon">🔄</text>
          <text class="action-text">刷新</text>
        </view>
      </view>
    </view>
    
    <view class="balance-amount-section">
      <view class="main-balance">
        <text class="currency">¥</text>
        <text class="amount">{{userInfo.balance || '0.00'}}</text>
      </view>
      <view class="balance-details" wx:if="{{userInfo.frozenBalance > 0}}">
        <text class="frozen-text">冻结金额：¥{{userInfo.frozenBalance || '0.00'}}</text>
      </view>
    </view>

    <!-- 科技感统计网格 -->
    <view class="balance-stats">
      <view class="stat-item">
        <view class="stat-icon">📈</view>
        <text class="stat-value">{{stats.totalIncome || '0.00'}}</text>
        <text class="stat-label">总收入</text>
        <view class="stat-glow"></view>
      </view>
      <view class="stat-item">
        <view class="stat-icon">📉</view>
        <text class="stat-value">{{stats.totalExpense || '0.00'}}</text>
        <text class="stat-label">总支出</text>
        <view class="stat-glow"></view>
      </view>
      <view class="stat-item">
        <view class="stat-icon">📊</view>
        <text class="stat-value">{{stats.transactionCount || '0'}}</text>
        <text class="stat-label">交易笔数</text>
        <view class="stat-glow"></view>
      </view>
    </view>
  </view>

  <!-- 科技感快捷操作 -->
  <view class="quick-actions cyber-card fade-in" style="animation-delay: 0.2s;">
    <view class="card-glow"></view>
    <view class="actions-header">
      <text class="actions-title">💳 快捷操作</text>
    </view>
    <view class="actions-grid">
      <view class="action-item cyber-btn" bindtap="navigateToRecharge">
        <view class="action-icon-container">
          <text class="action-emoji">💰</text>
          <view class="icon-glow"></view>
        </view>
        <text class="action-name">充值</text>
        <view class="btn-glow"></view>
      </view>
      <view class="action-item cyber-btn" bindtap="navigateToWithdraw">
        <view class="action-icon-container">
          <text class="action-emoji">💸</text>
          <view class="icon-glow"></view>
        </view>
        <text class="action-name">提现</text>
        <view class="btn-glow"></view>
      </view>
      <view class="action-item cyber-btn" bindtap="navigateToRecords">
        <view class="action-icon-container">
          <text class="action-emoji">📊</text>
          <view class="icon-glow"></view>
          <view class="action-badge" wx:if="{{transactionCount > 0}}">{{transactionCount}}</view>
        </view>
        <text class="action-name">交易记录</text>
        <view class="btn-glow"></view>
      </view>
    </view>
  </view>

  <!-- 科技感最近交易 -->
  <view class="recent-transactions cyber-card fade-in" style="animation-delay: 0.3s;">
    <view class="card-glow"></view>
    <view class="transactions-header">
      <text class="transactions-title">⚡ 最近交易</text>
      <view class="header-actions">
        <view class="more-link" bindtap="navigateToRecords">查看全部 →</view>
      </view>
    </view>

    <view class="transactions-list" wx:if="{{recentTransactions.length > 0}}">
      <view class="transaction-item"
            wx:for="{{recentTransactions}}"
            wx:key="_id"
            bindtap="viewTransactionDetail"
            data-id="{{item._id}}">
        
        <view class="transaction-info">
          <view class="transaction-icon {{item.type}}">
            <text class="icon-text">
              {{item.type === 'recharge' ? '💰' :
                item.type === 'withdraw' ? '💸' :
                item.type === 'income' ? '💵' :
                item.type === 'payment' ? '💳' :
                item.type === 'refund' ? '🔄' : '💼'}}
            </text>
          </view>

          <view class="transaction-details">
            <text class="transaction-title">{{item.typeText}}</text>
            <text class="transaction-desc">{{item.description}}</text>
            <text class="transaction-time">{{item.createTimeText}}</text>
          </view>
        </view>

        <view class="transaction-right">
          <text class="transaction-status {{item.status}}">{{item.statusText}}</text>
          <text class="amount-value {{item.amount > 0 ? 'income' : 'expense'}}">
            {{item.amount > 0 ? '+' : ''}}¥{{item.amount}}
          </text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-transactions" wx:if="{{recentTransactions.length === 0}}">
      <view class="empty-icon">📊</view>
      <text class="empty-text">暂无交易记录</text>
      <text class="empty-desc">完成订单或充值后即可查看交易记录</text>
    </view>
  </view>

  <!-- 科技感安全提示 -->
  <view class="security-tips cyber-card fade-in" style="animation-delay: 0.4s;">
    <view class="card-glow"></view>
    <view class="tips-header">
      <text class="tips-title">🔐 安全提示</text>
    </view>
    <view class="tips-content">
      <view class="tip-item">
        <text class="tip-icon">🛡️</text>
        <text class="tip-text">请妥善保管您的账户信息，不要向他人透露</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">⚠️</text>
        <text class="tip-text">如发现异常交易，请及时联系客服处理</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🔒</text>
        <text class="tip-text">建议定期检查交易记录，确保账户安全</text>
      </view>
    </view>
  </view>
</view>
