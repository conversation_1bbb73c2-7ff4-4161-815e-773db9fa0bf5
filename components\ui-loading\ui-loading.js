// 统一加载组件
Component({
  properties: {
    // 是否显示加载
    show: {
      type: Boolean,
      value: false
    },

    // 加载类型
    type: {
      type: String,
      value: 'spinner' // gti, spinner, dots, pulse, wave
    },

    // 加载文本
    text: {
      type: String,
      value: '加载中...'
    },

    // 是否显示文本
    showText: {
      type: Boolean,
      value: true
    },

    // 尺寸
    size: {
      type: String,
      value: 'normal' // small, normal, large
    },

    // 颜色主题
    theme: {
      type: String,
      value: 'primary' // primary, white, dark
    },

    // 是否为全屏加载
    fullscreen: {
      type: Boolean,
      value: false
    },

    // 背景遮罩透明度
    maskOpacity: {
      type: Number,
      value: 0.7
    },

    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    // G.T.I. SECURITY 矩阵字符
    matrixChars: ['G', 'T', 'I', 'S', 'E', 'C', 'U', 'R', 'I', 'T', 'Y'],
    currentCharIndex: 0
  },

  methods: {
    // 处理遮罩点击
    handleMaskTap() {
      // 阻止事件冒泡，但不关闭加载
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化
      if (this.data.type === 'gti') {
        this.startAnimation();
      }
    },

    detached() {
      // 组件销毁时清理
      this.stopAnimation();
    }
  },

  methods: {
    startAnimation() {
      if (this.animationTimer) return;

      this.animationTimer = setInterval(() => {
        this.setData({
          currentCharIndex: (this.data.currentCharIndex + 1) % this.data.matrixChars.length
        });
      }, 200);
    },

    stopAnimation() {
      if (this.animationTimer) {
        clearInterval(this.animationTimer);
        this.animationTimer = null;
      }
    },

    handleMaskTap() {
      // 阻止事件冒泡
    }
  }
});
