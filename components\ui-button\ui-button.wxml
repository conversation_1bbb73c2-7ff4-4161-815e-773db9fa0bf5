<!-- 统一按钮组件 -->
<button 
  class="ui-button ui-button--{{type}} ui-button--{{size}} {{customClass}} {{block ? 'ui-button--block' : ''}} {{round ? 'ui-button--round' : ''}} {{plain ? 'ui-button--plain' : ''}} {{disabled ? 'ui-button--disabled' : ''}} {{loading ? 'ui-button--loading' : ''}} {{isPressed ? 'ui-button--pressed' : ''}}"
  disabled="{{disabled || loading}}"
  hover-class="{{hoverClass}}"
  hover-stay-time="150"
  bindtap="handleTap"
  bindtouchstart="handleTouchStart"
  bindtouchend="handleTouchEnd"
  bindtouchcancel="handleTouchCancel"
>
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="ui-button__loading">
    <view class="ui-button__loading-icon"></view>
  </view>
  
  <!-- 图标 -->
  <view wx:if="{{icon && !loading}}" class="ui-button__icon">
    <text class="ui-button__icon-text">{{icon}}</text>
  </view>
  
  <!-- 按钮内容 -->
  <view class="ui-button__content">
    <!-- 自定义内容插槽 -->
    <slot wx:if="{{!text}}"></slot>
    <!-- 文本内容 -->
    <text wx:if="{{text}}" class="ui-button__text">{{text}}</text>
  </view>
</button>
