// 获取用户详情云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { userId } = event;

  try {
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空'
      };
    }

    // 查询用户信息
    const userResult = await db.collection('users')
      .doc(userId)
      .get();

    if (!userResult.data) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data;

    // 返回用户详细信息
    return {
      success: true,
      data: {
        _id: user._id,
        openid: user.openid,
        nickname: user.nickName || user.nickname,
        name: user.name,
        avatar: user.avatarUrl || user.avatar,
        avatarUrl: user.avatarUrl,
        phone: user.phone,
        isVerified: user.isVerified || false,
        status: user.status || 'active',
        createTime: user.createTime,
        updateTime: user.updateTime,
        balance: user.balance || 0,
        creditScore: user.creditScore || 0,
        gameNickName: user.gameNickName,
        bio: user.bio,
        contactInfo: user.contactInfo,
        settings: user.settings,
        orderCount: user.orderCount || 0
      }
    };
  } catch (error) {
    console.error('获取用户详情失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};