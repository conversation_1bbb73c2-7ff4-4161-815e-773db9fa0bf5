/* 客户确认订单完成页面样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow-x: hidden;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #00d4ff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 255, 0.3);
  border-top: 3px solid #00d4ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 16px;
  color: #00d4ff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主要内容 */
.content {
  position: relative;
  z-index: 1;
  padding: 20px;
  padding-bottom: 40px;
}

/* 页面头部 */
.page-header {
  position: relative;
  margin-bottom: 30px;
  text-align: center;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 80px;
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(255, 119, 198, 0.1));
  border-radius: 40px;
  filter: blur(20px);
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 20px 0;
}

.page-title {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  margin-bottom: 8px;
}

.page-subtitle {
  display: block;
  font-size: 14px;
  color: #888;
}

/* 订单信息卡片 */
.order-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.card-header {
  margin-bottom: 15px;
}

.order-title {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
}

.order-no {
  display: block;
  font-size: 14px;
  color: #888;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  font-size: 14px;
  color: #ccc;
}

/* 倒计时卡片 */
.countdown-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 119, 198, 0.1), rgba(0, 212, 255, 0.1));
  border: 1px solid rgba(255, 119, 198, 0.3);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
}

.countdown-icon {
  font-size: 24px;
  margin-right: 15px;
}

.countdown-content {
  flex: 1;
}

.countdown-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #ff77c6;
  margin-bottom: 5px;
}

.countdown-time {
  display: block;
  font-size: 14px;
  color: #00d4ff;
  margin-bottom: 3px;
}

.countdown-tip {
  display: block;
  font-size: 12px;
  color: #888;
}

/* 证明展示区域 */
.proof-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.title-text {
  font-size: 18px;
  font-weight: bold;
  color: #00d4ff;
}

.title-icon {
  font-size: 20px;
}

.proof-label {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 15px;
}

/* 图片证明 */
.proof-images {
  margin-bottom: 25px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.proof-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-item:active .image-overlay {
  opacity: 1;
}

.preview-text {
  color: #00d4ff;
  font-size: 12px;
}

/* 视频证明 */
.proof-video {
  margin-bottom: 25px;
}

.video-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.proof-video-player {
  width: 100%;
  height: 200px;
}

.video-info {
  background: rgba(0, 0, 0, 0.8);
  padding: 10px;
  display: flex;
  justify-content: space-between;
}

.video-size,
.video-duration {
  font-size: 12px;
  color: #888;
}

/* 文字说明 */
.proof-description {
  margin-bottom: 25px;
}

.description-content {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.description-text {
  font-size: 14px;
  color: #ccc;
  line-height: 1.6;
}

/* 无证明提示 */
.no-proof {
  text-align: center;
  padding: 40px 20px;
  color: #888;
}

.no-proof-text {
  font-size: 16px;
}

/* 操作区域 */
.action-section {
  position: relative;
}

.action-tips {
  text-align: center;
  margin-bottom: 20px;
}

.tip-text {
  font-size: 14px;
  color: #888;
  line-height: 1.5;
}

.button-group {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.action-btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.confirm-btn {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  border: 1px solid #00d4ff;
  color: #fff;
}

.dispute-btn {
  background: linear-gradient(135deg, rgba(255, 119, 198, 0.2), rgba(255, 119, 198, 0.1));
  border: 1px solid #ff77c6;
  color: #ff77c6;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: glow 2s infinite;
}

@keyframes glow {
  0% { left: -100%; }
  100% { left: 100%; }
}

.action-notes {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.note-item {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}
