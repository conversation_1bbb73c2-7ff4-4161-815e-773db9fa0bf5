// 通知中心页面
import API from '../../../utils/api.js';
import notificationManager from '../../../utils/notification.js';
import errorHandler from '../../../utils/errorHandler.js';

const app = getApp();

Page({
  data: {
    notificationList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    unreadCount: 0,
    
    // 筛选选项
    filterType: 'all', // all, orderStatus, newOrder, chatMessage, system
    filterOptions: [
      { value: 'all', label: '全部' },
      { value: 'orderStatus', label: '订单状态' },
      { value: 'newOrder', label: '新订单' },
      { value: 'chatMessage', label: '聊天消息' },
      { value: 'system', label: '系统通知' }
    ]
  },

  onLoad() {
    this.loadNotificationList(true);
    this.loadUnreadCount();
  },

  onShow() {
    // 页面显示时刷新未读数量
    this.loadUnreadCount();
  },

  onPullDownRefresh() {
    this.loadNotificationList(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadNotificationList(false);
    }
  },

  // 加载通知列表
  async loadNotificationList(reset = false) {
    if (this.data.loading) return;

    const page = reset ? 1 : this.data.page + 1;
    this.setData({ loading: true });

    try {
      const params = {
        page,
        pageSize: this.data.pageSize
      };

      // 添加类型筛选
      if (this.data.filterType !== 'all') {
        params.type = this.data.filterType;
      }

      const result = await API.getNotificationList(params);

      if (result.success) {
        const newList = result.data.notifications || [];

        this.setData({
          notificationList: reset ? newList : [...this.data.notificationList, ...newList],
          page,
          hasMore: result.data.hasMore || false
        });
      } else {
        errorHandler.handle(new Error(result.error || '加载失败'), '通知列表加载');
      }
    } catch (error) {
      errorHandler.handle(error, '通知列表加载异常');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载未读数量
  async loadUnreadCount() {
    try {
      const result = await API.getUnreadNotificationCount();
      if (result.success) {
        this.setData({ unreadCount: result.data.unreadCount || 0 });
      }
    } catch (error) {
      errorHandler.handle(error, '未读数量加载');
    }
  },

  // 切换筛选类型
  onFilterChange(e) {
    const filterType = e.detail.value;
    const filterOption = this.data.filterOptions[filterType];
    
    this.setData({
      filterType: filterOption.value
    }, () => {
      this.loadNotificationList(true);
    });
  },

  // 点击通知项
  onNotificationTap(e) {
    const { notification } = e.currentTarget.dataset;
    
    // 标记为已读
    if (!notification.isRead) {
      this.markAsRead(notification._id);
    }

    // 根据通知类型跳转到相应页面
    this.navigateByNotification(notification);
  },

  // 标记为已读
  async markAsRead(notificationId) {
    try {
      await API.markNotificationRead(notificationId);
      
      // 更新本地数据
      const notificationList = this.data.notificationList.map(item => {
        if (item._id === notificationId) {
          return { ...item, isRead: true, readTime: new Date() };
        }
        return item;
      });
      
      this.setData({ 
        notificationList,
        unreadCount: Math.max(0, this.data.unreadCount - 1)
      });
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  // 全部标记为已读
  async markAllAsRead() {
    if (this.data.unreadCount === 0) {
      app.utils.showError('没有未读通知');
      return;
    }

    wx.showModal({
      title: '确认操作',
      content: '确定要将所有通知标记为已读吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await API.markNotificationRead(null, true);
            
            // 更新本地数据
            const notificationList = this.data.notificationList.map(item => ({
              ...item,
              isRead: true,
              readTime: new Date()
            }));
            
            this.setData({ 
              notificationList,
              unreadCount: 0
            });
            
            app.utils.showSuccess('已全部标记为已读');
          } catch (error) {
            console.error('批量标记已读失败:', error);
            app.utils.showError('操作失败');
          }
        }
      }
    });
  },

  // 根据通知类型导航
  navigateByNotification(notification) {
    const { type, page, data } = notification;
    
    if (page) {
      // 如果有指定页面，直接跳转
      wx.navigateTo({
        url: page,
        fail: () => {
          // 跳转失败时的默认处理
          this.defaultNavigate(type, data);
        }
      });
    } else {
      // 根据类型进行默认跳转
      this.defaultNavigate(type, data);
    }
  },

  // 默认导航逻辑
  defaultNavigate(type, data) {
    switch (type) {
      case 'orderStatus':
      case 'newOrder':
        if (data.orderId) {
          wx.navigateTo({
            url: `/order-package/pages/detail/detail?id=${data.orderId}`
          });
        } else {
          wx.switchTab({
            url: '/pages/order/list/list'
          });
        }
        break;
      case 'chatMessage':
        if (data.chatRoomId) {
          wx.navigateTo({
            url: `/chat-package/pages/room/room?roomId=${data.chatRoomId}`
          });
        } else {
          wx.switchTab({
            url: '/pages/chat/list/list'
          });
        }
        break;
      case 'system':
      default:
        // 系统通知或其他类型，不跳转
        break;
    }
  },

  // 删除通知
  deleteNotification(e) {
    const { notification } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条通知吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以调用删除API
          // 暂时只做本地删除
          const notificationList = this.data.notificationList.filter(
            item => item._id !== notification._id
          );
          
          this.setData({ 
            notificationList,
            unreadCount: notification.isRead ? this.data.unreadCount : Math.max(0, this.data.unreadCount - 1)
          });
          
          app.utils.showSuccess('删除成功');
        }
      }
    });
  },

  // 获取通知类型显示文本
  getTypeText(type) {
    const typeMap = {
      orderStatus: '订单状态',
      newOrder: '新订单',
      chatMessage: '聊天消息',
      system: '系统通知'
    };
    return typeMap[type] || '通知';
  },

  // 获取通知图标
  getTypeIcon(type) {
    const iconMap = {
      orderStatus: '📋',
      newOrder: '🆕',
      chatMessage: '💬',
      system: '🔔'
    };
    return iconMap[type] || '📢';
  }
});
