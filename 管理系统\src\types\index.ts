// 用户相关类型定义
export interface User {
  _id: string;
  openid: string;
  unionid?: string;
  nickName: string;
  avatarUrl: string;
  gameNickName?: string;
  phone?: string;
  realName?: string;
  idCard?: string;
  isVerified: boolean;
  balance: number;
  creditScore: number;
  status: 'active' | 'banned' | 'inactive';
  createTime: Date;
  updateTime: Date;
  gender?: number;
  age?: number;
  bio?: string;
  contactInfo?: string;
}

// 订单相关类型定义
export interface Order {
  _id: string;
  orderNo: string;
  customerId: string;
  customerOpenid: string;
  accepterId?: string;
  title: string;
  content: string;
  reward: number;
  platformType: 'pc' | 'mobile';
  serviceType: 'duration' | 'rounds';
  duration?: number;
  rounds?: number;
  tags: string[];
  orderType: 'immediate' | 'scheduled';
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  scheduledDate?: string;
  scheduledTime?: string;
  chatRoomId?: string;
  evaluation?: {
    customerRating?: number;
    accepterRating?: number;
    customerComment?: string;
    accepterComment?: string;
  };
  createTime: Date;
  updateTime: Date;
  startTime?: Date;
  endTime?: Date;
}

// 聊天相关类型定义
export interface ChatRoom {
  _id: string;
  orderNo: string;
  orderDbId: string;
  customerId: string;
  accepterId: string;
  customerInfo: {
    nickName: string;
    avatarUrl: string;
  };
  accepterInfo: {
    nickName: string;
    avatarUrl: string;
  };
  orderInfo: {
    title: string;
    reward: number;
    status: string;
  };
  lastMessage?: ChatMessage;
  status: 'active' | 'closed';
  createTime: Date;
  updateTime: Date;
}

export interface ChatMessage {
  _id: string;
  chatRoomId: string;
  senderId: string;
  senderInfo: {
    nickName: string;
    avatarUrl: string;
  };
  type: 'text' | 'image' | 'voice' | 'system';
  content: string;
  imageUrl?: string;
  voiceUrl?: string;
  duration?: number;
  createTime: Date;
}

// 通知相关类型定义
export interface Notification {
  _id: string;
  type: 'order' | 'chat' | 'system';
  title: string;
  content: string;
  receiverId: string;
  senderId?: string;
  senderName?: string;
  status: 'unread' | 'read';
  orderId?: string;
  orderNo?: string;
  createTime: Date;
}

// 评价相关类型定义
export interface Evaluation {
  _id: string;
  orderId: string;
  orderNo: string;
  evaluatorId: string;
  evaluatedId: string;
  evaluatorInfo: {
    nickName: string;
    avatarUrl: string;
  };
  evaluatedInfo: {
    nickName: string;
    avatarUrl: string;
  };
  rating: number;
  comment: string;
  tags: string[];
  createTime: Date;
}

// 交易相关类型定义
export interface Transaction {
  _id: string;
  userId: string;
  type: 'recharge' | 'withdraw' | 'order_payment' | 'order_income' | 'refund';
  amount: number;
  balance: number;
  orderId?: string;
  orderNo?: string;
  description: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  createTime: Date;
  updateTime: Date;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  message?: string;
}

// 分页相关类型
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
}

export interface PaginationResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

// 统计数据类型
export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalOrders: number;
  completedOrders: number;
  totalRevenue: number;
  averageScore: number;
  newUsersToday: number;
  ordersToday: number;
  pendingEvaluations: number;
  evaluationCount: number;
}

export interface ChartData {
  name: string;
  value: number;
  date?: string;
}

// 管理员相关类型
export interface Admin {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'super_admin';
  permissions: string[];
  lastLoginTime?: Date;
  createTime: Date;
}

export interface LoginForm {
  username: string;
  password: string;
  remember?: boolean;
}

// 系统设置类型
export interface SystemSettings {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  contactPhone: string;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  orderCommissionRate: number;
  minWithdrawAmount: number;
  maxWithdrawAmount: number;
}
