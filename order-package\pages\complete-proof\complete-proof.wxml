<!--任务完成证明页面-->
<navigation-bar title="提交完成证明" back="{{true}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 科技感背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3]}}" wx:key="*this"></view>
  </view>
</view>

<scroll-view class="scrollarea page-with-custom-nav" scroll-y type="list">
  <view class="container">

    <!-- 订单信息卡片 -->
    <view class="info-card cyber-card" wx:if="{{orderInfo}}">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">📋</text>
        <text class="title-text">订单信息</text>
      </view>
      <view class="order-summary">
        <view class="summary-item">
          <text class="summary-label">订单标题：</text>
          <text class="summary-value">{{orderInfo.title}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">悬赏金额：</text>
          <text class="summary-value price">¥{{orderInfo.reward}}</text>
        </view>
        <view class="summary-item" wx:if="{{orderInfo.serviceType === 'rounds'}}">
          <text class="summary-label">服务局数：</text>
          <text class="summary-value">{{orderInfo.rounds}}局</text>
        </view>
        <view class="summary-item" wx:else>
          <text class="summary-label">服务时长：</text>
          <text class="summary-value">{{orderInfo.duration}}小时</text>
        </view>
      </view>
    </view>

    <!-- 上传说明 -->
    <view class="info-card cyber-card">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">📸</text>
        <text class="title-text">完成证明</text>
      </view>
      <view class="upload-tips">
        <view class="tip-item">
          <text class="tip-icon">✅</text>
          <text class="tip-text">请上传任务完成的截图或录屏作为证明</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">📱</text>
          <text class="tip-text">支持最多6张图片，视频不超过100MB</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">🔒</text>
          <text class="tip-text">证明材料仅用于验证任务完成，确保交易安全</text>
        </view>
      </view>
    </view>

    <!-- 图片上传区域 -->
    <view class="upload-card cyber-card">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">🖼️</text>
        <text class="title-text">上传截图</text>
        <text class="title-count">({{proofImages.length}}/{{maxImages}})</text>
      </view>
      
      <view class="image-upload-area">
        <!-- 已上传的图片 -->
        <view class="image-item" wx:for="{{proofImages}}" wx:key="index">
          <image 
            class="uploaded-image" 
            src="{{item.tempFilePath}}" 
            mode="aspectFill"
            bindtap="previewImage"
            data-index="{{index}}"
          ></image>
          <view class="image-delete" bindtap="deleteImage" data-index="{{index}}">
            <text class="delete-icon">×</text>
          </view>
        </view>
        
        <!-- 添加图片按钮 -->
        <view 
          class="add-image-btn cyber-upload-btn" 
          bindtap="chooseImages"
          wx:if="{{proofImages.length < maxImages}}"
        >
          <text class="add-icon">+</text>
          <text class="add-text">添加截图</text>
        </view>
      </view>
    </view>

    <!-- 视频上传区域 -->
    <view class="upload-card cyber-card">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">🎥</text>
        <text class="title-text">上传录屏</text>
        <text class="title-optional">(可选)</text>
      </view>
      
      <view class="video-upload-area">
        <!-- 已上传的视频 -->
        <view class="video-item" wx:if="{{proofVideo}}">
          <video 
            class="uploaded-video" 
            src="{{proofVideo.tempFilePath}}"
            poster="{{proofVideo.tempFilePath}}"
            controls="{{true}}"
            show-center-play-btn="{{true}}"
          ></video>
          <view class="video-info">
            <text class="video-size">大小: {{proofVideo.sizeText}}</text>
            <text class="video-duration" wx:if="{{proofVideo.duration}}">时长: {{proofVideo.durationText}}</text>
          </view>
          <view class="video-delete" bindtap="deleteVideo">
            <text class="delete-text">删除视频</text>
          </view>
        </view>
        
        <!-- 添加视频按钮 -->
        <view 
          class="add-video-btn cyber-upload-btn" 
          bindtap="chooseVideo"
          wx:if="{{!proofVideo}}"
        >
          <text class="add-icon">📹</text>
          <text class="add-text">添加录屏</text>
          <text class="add-desc">最大100MB，建议5分钟内</text>
        </view>
      </view>
    </view>

    <!-- 文字说明 -->
    <view class="upload-card cyber-card">
      <view class="card-glow"></view>
      <view class="card-title">
        <text class="title-icon">📝</text>
        <text class="title-text">补充说明</text>
        <text class="title-optional">(可选)</text>
      </view>
      
      <view class="description-area">
        <textarea 
          class="description-input cyber-input"
          placeholder="请简要说明任务完成情况，如遇到的问题、完成效果等..."
          value="{{description}}"
          bindinput="onDescriptionInput"
          maxlength="500"
          auto-height="{{true}}"
          show-confirm-bar="{{false}}"
        ></textarea>
        <view class="char-count">{{description.length}}/500</view>
      </view>
    </view>

    <!-- 上传进度 -->
    <view class="progress-card cyber-card" wx:if="{{uploading}}">
      <view class="card-glow"></view>
      <view class="progress-content">
        <text class="progress-text">正在上传...</text>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{uploadProgress}}%"></view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-area">
      <button
        class="submit-btn cyber-btn {{canSubmitOrder ? 'primary' : 'disabled'}}"
        bindtap="submitProof"
        disabled="{{!canSubmitOrder || submitting || uploading}}"
      >
        <text class="btn-text" wx:if="{{!submitting}}">确认完成订单</text>
        <text class="btn-text" wx:else>提交中...</text>
        <view class="btn-glow" wx:if="{{canSubmitOrder}}"></view>
      </button>
      
      <view class="submit-tips">
        <text class="tip-text">提交后将完成订单并进行资金结算</text>
      </view>
    </view>

  </view>
</scroll-view>
