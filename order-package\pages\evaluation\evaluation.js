// 订单评价页面
import API from '../../../utils/api.js';

const app = getApp();

Page({
  data: {
    orderId: '',
    orderInfo: null,
    userRole: '', // 'customer' 或 'accepter'
    targetRole: '', // 被评价者角色

    // 评价相关
    rating: 0,
    ratingTexts: ['很差', '较差', '一般', '满意', '非常满意'],
    selectedTags: [],
    evaluationTags: [
      '服务态度好', '技术过硬', '准时守信', '沟通顺畅',
      '耐心细致', '专业水准', '超出预期', '值得推荐'
    ],
    tagStyles: {}, // 标签样式映射
    evaluationContent: '',
    isAnonymous: false,

    // 状态
    loading: true,
    submitting: false,
    canSubmit: false
  },

  onLoad(options) {
    console.log('evaluation页面加载，参数:', options);
    if (options.orderId) {
      this.setData({ orderId: options.orderId });
      this.loadOrderDetail();
    } else {
      app.utils.showError('订单ID不能为空');
      wx.navigateBack();
    }
  },



  // 清理文本内容，去除多余空格和换行符
  cleanTextContent(text) {
    if (!text) return '';
    // 将换行符替换为空格，然后去除多余的空格
    return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      let orderData;

      // 正常模式：调用API
      const result = await API.getOrderDetail(this.data.orderId);
      if (result.success) {
        orderData = result.data;
      } else {
        throw new Error(result.error || '加载失败');
      }

      const orderInfo = {
        ...orderData,
        // 清理文本内容
        content: this.cleanTextContent(orderData.content),
        title: this.cleanTextContent(orderData.title),
        createTimeText: this.formatTime(orderData.createTime),
        duration: this.calculateDuration(orderData.startTime, orderData.endTime)
      };

      // 判断当前用户角色
      const userInfo = app.globalData.userInfo;
      let userRole = '';
      let targetRole = '';

      // 兼容 accepterId 和 companionId 字段
      const actualAccepterId = orderInfo.accepterId || orderInfo.companionId;

      if (userInfo && userInfo._id) {
        const isCustomer = String(orderInfo.customerId) === String(userInfo._id);
        const isAccepter = actualAccepterId && String(actualAccepterId) === String(userInfo._id);

        if (isCustomer) {
          userRole = 'customer';
          targetRole = '接单者';
        } else if (isAccepter) {
          userRole = 'accepter';
          targetRole = '客户';
        }
      }

      console.log('用户角色判断:', {
        currentUserId: userInfo?._id,
        customerId: orderInfo.customerId,
        accepterId: orderInfo.accepterId,
        companionId: orderInfo.companionId,
        actualAccepterId: actualAccepterId,
        userRole,
        targetRole
      });

      this.setData({
        orderInfo,
        userRole,
        targetRole,
        loading: false
      });

      // 检查订单状态
      if (orderInfo.status !== 'completed') {
        wx.showToast({
          title: '只能评价已完成的订单',
          icon: 'none'
        });
        setTimeout(() => wx.navigateBack(), 2000);
        return;
      }

      // 检查用户权限（临时开放权限）
      if (!userRole) {
        console.log('⚠️ 临时模式：用户没有明确角色，默认设为客户');
        userRole = 'customer';
        targetRole = '接单者';
        this.setData({ userRole, targetRole });
      }

      // 检查是否已评价（根据用户角色）
      const evaluation = orderInfo.evaluation || {};
      let hasEvaluated = false;

      if (userRole === 'customer' && evaluation.customerRating) {
        hasEvaluated = true;
      } else if (userRole === 'accepter' && evaluation.accepterRating) {
        hasEvaluated = true;
      }

      if (hasEvaluated) {
        wx.showToast({
          title: '您已经评价过此订单',
          icon: 'none'
        });
        setTimeout(() => wx.navigateBack(), 2000);
        return;
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
      setTimeout(() => wx.navigateBack(), 2000);
    }
  },



  // 设置评分
  setRating(e) {
    console.log('setRating被调用，事件对象:', e);
    console.log('currentTarget:', e.currentTarget);
    console.log('dataset:', e.currentTarget.dataset);

    const rating = parseInt(e.currentTarget.dataset.rating);
    console.log('设置评分:', rating);

    if (rating >= 1 && rating <= 5) {
      this.setData({ rating }, () => {
        console.log('评分设置完成，当前rating:', this.data.rating);
        // 立即检查提交条件
        this.checkCanSubmit();
      });
    } else {
      console.error('无效的评分值:', rating);
    }
  },

  // 切换标签
  toggleTag(e) {
    console.log('toggleTag被调用，事件对象:', e);
    console.log('currentTarget:', e.currentTarget);
    console.log('dataset:', e.currentTarget.dataset);

    const tag = e.currentTarget.dataset.tag;
    console.log('切换标签:', tag);

    const selectedTags = [...this.data.selectedTags];
    const index = selectedTags.indexOf(tag);

    if (index > -1) {
      selectedTags.splice(index, 1);
      console.log('移除标签:', tag);
    } else {
      selectedTags.push(tag);
      console.log('添加标签:', tag);
    }

    console.log('选中的标签:', selectedTags);

    // 生成标签样式映射
    const tagStyles = {};
    this.data.evaluationTags.forEach(tag => {
      tagStyles[tag] = selectedTags.includes(tag);
    });

    this.setData({
      selectedTags,
      tagStyles
    }, () => {
      console.log('标签设置完成，当前selectedTags:', this.data.selectedTags);
    });
  },

  // 评价内容输入
  onContentInput(e) {
    this.setData({
      evaluationContent: e.detail.value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 匿名选项变化
  onAnonymousChange(e) {
    this.setData({
      isAnonymous: e.detail.value
    });
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { rating, evaluationContent } = this.data;
    // 只要有评分就可以提交，评价内容可选
    const canSubmit = rating > 0;
    console.log('检查提交条件:', { rating, contentLength: evaluationContent.trim().length, canSubmit });
    this.setData({ canSubmit });
  },

  // 提交评价
  async submitEvaluation() {
    console.log('提交评价被调用');
    if (!this.data.canSubmit || this.data.submitting) {
      console.log('提交条件不满足:', { canSubmit: this.data.canSubmit, submitting: this.data.submitting });
      return;
    }

    const { orderId, rating, selectedTags, evaluationContent, isAnonymous } = this.data;
    console.log('提交数据:', { orderId, rating, selectedTags, evaluationContent, isAnonymous });

    this.setData({ submitting: true });

    try {
      app.utils.showLoading('提交评价中...');

      const result = await API.submitEvaluation({
        orderId,
        rating,
        tags: selectedTags,
        content: evaluationContent.trim(),
        isAnonymous
      });

      console.log('API调用结果:', result);

      if (result.success) {
        app.utils.hideLoading(); // 先隐藏loading
        app.utils.showSuccess('评价提交成功');

        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        app.utils.hideLoading(); // 先隐藏loading
        app.utils.showError(result.error || '评价提交失败');
      }
    } catch (error) {
      console.error('提交评价失败:', error);
      app.utils.hideLoading(); // 先隐藏loading
      app.utils.showError('提交失败，请重试');
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 格式化时间
  formatTime(dateStr) {
    const date = new Date(dateStr);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${month}-${day} ${hours}:${minutes}`;
  },

  // 计算时长
  calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return '未知';

    const start = new Date(startTime);
    const end = new Date(endTime);
    const duration = (end - start) / (1000 * 60 * 60); // 小时

    return duration.toFixed(1);
  }
})