/**
 * 语音性能监控组件
 * 用于监测语音发送性能优化效果
 */
Component({
  properties: {
    // 是否显示性能监控
    show: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 性能数据
    performanceData: {
      recordTime: 0,      // 录制耗时
      uploadTime: 0,      // 上传耗时
      sendTime: 0,        // 发送耗时
      totalTime: 0,       // 总耗时
      userPerceived: 0,   // 用户感知延迟
      fileSize: 0,        // 文件大小
      compressionRatio: 0 // 压缩比例
    },
    
    // 历史记录
    history: [],
    
    // 统计数据
    stats: {
      avgUserPerceived: 0,
      avgUploadTime: 0,
      avgTotalTime: 0,
      successRate: 100
    }
  },

  methods: {
    /**
     * 记录性能数据
     */
    recordPerformance(data) {
      const performanceData = {
        ...data,
        timestamp: Date.now(),
        date: new Date().toLocaleString()
      };

      // 更新当前数据
      this.setData({
        performanceData
      });

      // 添加到历史记录
      const history = [...this.data.history, performanceData].slice(-10); // 保留最近10条
      this.setData({ history });

      // 更新统计数据
      this.updateStats();

      // 输出性能报告
      this.logPerformanceReport(performanceData);
    },

    /**
     * 更新统计数据
     */
    updateStats() {
      const { history } = this.data;
      if (history.length === 0) return;

      const stats = {
        avgUserPerceived: this.calculateAverage(history, 'userPerceived'),
        avgUploadTime: this.calculateAverage(history, 'uploadTime'),
        avgTotalTime: this.calculateAverage(history, 'totalTime'),
        successRate: this.calculateSuccessRate(history)
      };

      this.setData({ stats });
    },

    /**
     * 计算平均值
     */
    calculateAverage(data, field) {
      const sum = data.reduce((acc, item) => acc + (item[field] || 0), 0);
      return Math.round(sum / data.length);
    },

    /**
     * 计算成功率
     */
    calculateSuccessRate(data) {
      const successCount = data.filter(item => !item.error).length;
      return Math.round((successCount / data.length) * 100);
    },

    /**
     * 输出性能报告
     */
    logPerformanceReport(data) {
      console.log('📊 [性能监控] 语音发送性能报告:');
      console.log('⚡ 用户感知延迟:', data.userPerceived, 'ms');
      console.log('☁️ 上传耗时:', data.uploadTime, 'ms');
      console.log('📤 发送耗时:', data.sendTime, 'ms');
      console.log('⏱️ 总耗时:', data.totalTime, 'ms');
      console.log('📦 文件大小:', data.fileSize, 'KB');
      console.log('🗜️ 压缩比例:', data.compressionRatio, '%');
      
      // 性能评级
      const grade = this.getPerformanceGrade(data.userPerceived);
      console.log('🏆 性能评级:', grade);
    },

    /**
     * 获取性能评级
     */
    getPerformanceGrade(userPerceived) {
      if (userPerceived < 300) return 'A+ 极佳';
      if (userPerceived < 500) return 'A 优秀';
      if (userPerceived < 1000) return 'B 良好';
      if (userPerceived < 2000) return 'C 一般';
      return 'D 需要优化';
    },

    /**
     * 清空历史记录
     */
    clearHistory() {
      this.setData({
        history: [],
        stats: {
          avgUserPerceived: 0,
          avgUploadTime: 0,
          avgTotalTime: 0,
          successRate: 100
        }
      });
    },

    /**
     * 导出性能数据
     */
    exportData() {
      const data = {
        current: this.data.performanceData,
        history: this.data.history,
        stats: this.data.stats,
        exportTime: new Date().toISOString()
      };

      console.log('📋 [性能监控] 导出数据:', JSON.stringify(data, null, 2));
      
      // 可以保存到本地存储
      wx.setStorageSync('voice_performance_data', data);
      
      wx.showToast({
        title: '数据已导出',
        icon: 'success'
      });
    }
  }
});