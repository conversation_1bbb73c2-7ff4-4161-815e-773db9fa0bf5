// API 功能测试
import { dashboardApi, userApi, orderApi, chatApi, walletApi } from '../services/api';

// 测试配置
// const TEST_CONFIG = {
//   timeout: 10000,
//   retryCount: 3
// };

// 测试结果接口
interface TestResult {
  name: string;
  success: boolean;
  error?: string;
  duration: number;
}

// 测试工具函数
const runTest = async (testName: string, testFn: () => Promise<any>): Promise<TestResult> => {
  const startTime = Date.now();
  
  try {
    await testFn();
    return {
      name: testName,
      success: true,
      duration: Date.now() - startTime
    };
  } catch (error) {
    return {
      name: testName,
      success: false,
      error: error instanceof Error ? error.message : String(error),
      duration: Date.now() - startTime
    };
  }
};

// API 测试套件
export class ApiTestSuite {
  private results: TestResult[] = [];

  // 仪表盘 API 测试
  async testDashboardApi(): Promise<TestResult[]> {
    const tests = [
      () => dashboardApi.getStats(),
      () => dashboardApi.getChartData('users', 'today'),
      () => dashboardApi.getRecentActivities()
    ];

    const testNames = [
      '获取仪表盘统计数据',
      '获取图表数据',
      '获取最近活动'
    ];

    const results = [];
    for (let i = 0; i < tests.length; i++) {
      const result = await runTest(testNames[i], tests[i]);
      results.push(result);
    }

    return results;
  }

  // 用户管理 API 测试
  async testUserApi(): Promise<TestResult[]> {
    const tests = [
      () => userApi.getUserList({ page: 1, limit: 10 }),
      () => userApi.getUserStats(),
      () => userApi.getUserDetail('test-user-id')
    ];

    const testNames = [
      '获取用户列表',
      '获取用户统计',
      '获取用户详情'
    ];

    const results = [];
    for (let i = 0; i < tests.length; i++) {
      const result = await runTest(testNames[i], tests[i]);
      results.push(result);
    }

    return results;
  }

  // 订单管理 API 测试
  async testOrderApi(): Promise<TestResult[]> {
    const tests = [
      () => orderApi.getOrderList({ page: 1, limit: 10 }),
      () => orderApi.getOrderStats(),
      () => orderApi.getOrderDetail('test-order-id')
    ];

    const testNames = [
      '获取订单列表',
      '获取订单统计',
      '获取订单详情'
    ];

    const results = [];
    for (let i = 0; i < tests.length; i++) {
      const result = await runTest(testNames[i], tests[i]);
      results.push(result);
    }

    return results;
  }

  // 聊天管理 API 测试
  async testChatApi(): Promise<TestResult[]> {
    const tests = [
      () => chatApi.getChatRoomList({ page: 1, limit: 10 }),
      () => chatApi.getChatStats(),
      () => chatApi.getChatMessages('test-room-id', { page: 1, limit: 20 })
    ];

    const testNames = [
      '获取聊天室列表',
      '获取聊天统计',
      '获取聊天消息'
    ];

    const results = [];
    for (let i = 0; i < tests.length; i++) {
      const result = await runTest(testNames[i], tests[i]);
      results.push(result);
    }

    return results;
  }

  // 钱包管理 API 测试
  async testWalletApi(): Promise<TestResult[]> {
    const tests = [
      () => walletApi.getTransactionList({ page: 1, limit: 10 }),
      () => walletApi.getWithdrawList({ page: 1, limit: 10 }),
      () => walletApi.getWalletStats()
    ];

    const testNames = [
      '获取交易记录',
      '获取提现申请',
      '获取钱包统计'
    ];

    const results = [];
    for (let i = 0; i < tests.length; i++) {
      const result = await runTest(testNames[i], tests[i]);
      results.push(result);
    }

    return results;
  }

  // 运行所有测试
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 开始运行 API 测试套件...');
    
    const allResults = [];
    
    console.log('📊 测试仪表盘 API...');
    const dashboardResults = await this.testDashboardApi();
    allResults.push(...dashboardResults);
    
    console.log('👥 测试用户管理 API...');
    const userResults = await this.testUserApi();
    allResults.push(...userResults);
    
    console.log('📦 测试订单管理 API...');
    const orderResults = await this.testOrderApi();
    allResults.push(...orderResults);
    
    console.log('💬 测试聊天管理 API...');
    const chatResults = await this.testChatApi();
    allResults.push(...chatResults);
    
    console.log('💰 测试钱包管理 API...');
    const walletResults = await this.testWalletApi();
    allResults.push(...walletResults);
    
    this.results = allResults;
    this.printTestResults();
    
    return allResults;
  }

  // 打印测试结果
  private printTestResults(): void {
    const successCount = this.results.filter(r => r.success).length;
    const totalCount = this.results.length;
    
    console.log('\n📋 测试结果汇总:');
    console.log(`✅ 成功: ${successCount}/${totalCount}`);
    console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);
    console.log(`⏱️ 总耗时: ${this.results.reduce((sum, r) => sum + r.duration, 0)}ms`);
    
    console.log('\n📝 详细结果:');
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const duration = `${result.duration}ms`;
      console.log(`${status} ${result.name} (${duration})`);
      if (!result.success && result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }

  // 获取测试报告
  getTestReport(): {
    summary: {
      total: number;
      success: number;
      failed: number;
      successRate: number;
      totalDuration: number;
    };
    details: TestResult[];
  } {
    const successCount = this.results.filter(r => r.success).length;
    const totalCount = this.results.length;
    
    return {
      summary: {
        total: totalCount,
        success: successCount,
        failed: totalCount - successCount,
        successRate: totalCount > 0 ? (successCount / totalCount) * 100 : 0,
        totalDuration: this.results.reduce((sum, r) => sum + r.duration, 0)
      },
      details: this.results
    };
  }
}

// 导出测试实例
export const apiTestSuite = new ApiTestSuite();
