<!-- 统一加载组件 -->
<view wx:if="{{show}}" class="ui-loading {{fullscreen ? 'ui-loading--fullscreen' : ''}} {{customClass}}">
  <!-- 背景遮罩 -->
  <view wx:if="{{fullscreen}}"
        class="ui-loading__mask"
        style="opacity: {{maskOpacity}}"
        bindtap="handleMaskTap">
  </view>

  <!-- 加载内容 -->
  <view class="ui-loading__content">

    <!-- 旋转加载 -->
    <view wx:if="{{type == 'spinner'}}" class="ui-loading__spinner">
      <view class="ui-loading__spinner-icon"></view>
      <text wx:if="{{showText}}" class="ui-loading__text">{{text}}</text>
    </view>

    <!-- G.T.I. SECURITY 科技感加载 -->
    <view wx:elif="{{type == 'gti'}}" class="ui-loading__gti">
      <view class="ui-loading__gti-frame">
        <view class="ui-loading__gti-corner ui-loading__gti-corner--tl"></view>
        <view class="ui-loading__gti-corner ui-loading__gti-corner--tr"></view>
        <view class="ui-loading__gti-corner ui-loading__gti-corner--bl"></view>
        <view class="ui-loading__gti-corner ui-loading__gti-corner--br"></view>

        <view class="ui-loading__gti-content">
          <view class="ui-loading__gti-logo">
            <text class="ui-loading__gti-text">G.T.I.</text>
            <text class="ui-loading__gti-subtext">SECURITY</text>
          </view>

          <view class="ui-loading__gti-matrix">
            <text class="ui-loading__gti-matrix-char">{{matrixChars[currentCharIndex]}}</text>
          </view>

          <view class="ui-loading__gti-rings">
            <view class="ui-loading__gti-ring ui-loading__gti-ring--1"></view>
            <view class="ui-loading__gti-ring ui-loading__gti-ring--2"></view>
            <view class="ui-loading__gti-ring ui-loading__gti-ring--3"></view>
          </view>
        </view>
      </view>

      <text wx:if="{{showText}}" class="ui-loading__gti-status">{{text}}</text>
    </view>

    <!-- 点点加载 -->
    <view wx:elif="{{type == 'dots'}}" class="ui-loading__dots">
      <view class="ui-loading__dot ui-loading__dot--1"></view>
      <view class="ui-loading__dot ui-loading__dot--2"></view>
      <view class="ui-loading__dot ui-loading__dot--3"></view>
      <text wx:if="{{showText}}" class="ui-loading__text">{{text}}</text>
    </view>

    <!-- 默认加载 -->
    <view wx:else class="ui-loading__spinner">
      <view class="ui-loading__spinner-icon"></view>
      <text wx:if="{{showText}}" class="ui-loading__text">{{text}}</text>
    </view>

  </view>
</view>
