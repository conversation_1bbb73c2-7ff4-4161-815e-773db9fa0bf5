// 初始化通知集合
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();



  try {
    // 检查集合是否存在
    try {
      const testResult = await db.collection('notifications').limit(1).get();

      
      return {
        success: true,
        message: 'notifications 集合已存在',
        data: {
          collectionExists: true,
          recordCount: testResult.data.length
        }
      };
    } catch (error) {
      if (error.errCode === -502005) {
        console.log('📋 [集合检查] notifications 集合不存在，开始创建...');
        
        // 创建一个示例通知记录来初始化集合
        const sampleNotification = {
          type: 'system',
          title: '系统初始化',
          content: '通知系统已初始化',
          receiverId: 'system',
          senderId: 'system',
          senderName: '系统',
          status: 'read',
          createTime: new Date(),
          orderId: null,
          orderNo: null
        };

        const createResult = await db.collection('notifications').add({
          data: sampleNotification
        });

        console.log('✅ [集合创建] notifications 集合创建成功:', createResult);

        // 删除示例记录
        await db.collection('notifications').doc(createResult._id).remove();
        console.log('✅ [清理] 示例记录已删除');

        return {
          success: true,
          message: 'notifications 集合创建成功',
          data: {
            collectionExists: true,
            created: true
          }
        };
      } else {
        throw error;
      }
    }

  } catch (error) {
    console.error('❌ [云函数错误]:', error);
    return {
      success: false,
      error: error.message || '初始化失败'
    };
  }
};
