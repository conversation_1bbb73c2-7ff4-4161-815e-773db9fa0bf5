# 统一订单卡片组件 (order-card)

## 概述

这是一个统一的订单卡片组件，用于在首页和订单大厅页面显示订单信息。组件采用横向长方形布局，左侧显示订单详细信息，右侧显示状态和操作按钮。

## 设计特点

- **横向长方形布局**：符合用户浏览习惯
- **信息层次清晰**：标题、价格、详细信息、需求、标签分层显示
- **智能按钮显示**：根据用户身份（发布者/非发布者）显示不同操作按钮
- **现代化视觉效果**：渐变背景、毛玻璃效果、动画交互

## 使用方法

### 1. 在页面配置中引入组件

```json
{
  "usingComponents": {
    "order-card": "/components/order-card/order-card"
  }
}
```

### 2. 在页面中使用组件

```xml
<order-card
  order-data="{{orderItem}}"
  bind:cardtap="onCardTap"
  bind:graborder="onGrabOrder"
  bind:viewdetail="onViewDetail"
  bind:editorder="onEditOrder"
  bind:cancelorder="onCancelOrder"
  bind:contactaccepter="onContactAccepter"
  bind:enterchat="onEnterChat"
  bind:evaluateorder="onEvaluateOrder">
</order-card>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| orderData | Object | {} | 订单数据对象 |
| showDetail | Boolean | true | 是否显示详细信息 |
| mode | String | 'normal' | 卡片模式：'normal' \| 'compact' |

## 订单数据结构

```javascript
{
  _id: "订单ID",
  title: "订单标题",
  content: "任务需求描述",
  duration: 2, // 时长（小时）
  status: "pending", // 订单状态
  statusText: "待接单", // 状态文本
  isOwner: false, // 是否为订单发布者
  totalAmount: 50, // 订单金额
  reward: 50, // 悬赏金额
  tags: ["获取战友", "战术分析"], // 订单标签
  gameInfo: {
    gameName: "三角洲行动",
    gameMode: "爆破模式"
  },
  pricing: {
    totalAmount: 50
  },
  requirements: {
    duration: 2,
    description: "任务需求详情"
  }
}
```

## 组件事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| cardtap | 卡片点击事件 | {orderId, orderData} |
| graborder | 抢单事件 | {orderId, orderData} |
| viewdetail | 查看详情事件 | {orderId, orderData} |
| editorder | 编辑订单事件 | {orderId, orderData} |
| cancelorder | 取消订单事件 | {orderId, orderData} |
| contactaccepter | 联系接单者事件 | {orderId, orderData} |
| enterchat | 进入聊天事件 | {orderId, orderData} |
| evaluateorder | 评价订单事件 | {orderId, orderData} |

## 事件处理示例

```javascript
// 页面事件处理方法
onCardTap(e) {
  const { orderId, orderData } = e.detail;
  // 处理卡片点击
},

onGrabOrder(e) {
  const { orderId, orderData } = e.detail;
  // 处理抢单逻辑
},

onViewDetail(e) {
  const { orderId } = e.detail;
  wx.navigateTo({
    url: `/pages/order/detail/detail?id=${orderId}`
  });
},

onEditOrder(e) {
  const { orderId } = e.detail;
  wx.navigateTo({
    url: `/pages/order/create/create?mode=edit&id=${orderId}`
  });
},

onCancelOrder(e) {
  const { orderId } = e.detail;
  // 处理取消订单逻辑
},

onContactAccepter(e) {
  const { orderId, orderData } = e.detail;
  // 处理联系接单者逻辑
},

onEnterChat(e) {
  const { orderId, orderData } = e.detail;
  // 处理进入聊天逻辑
},

onEvaluateOrder(e) {
  const { orderId, orderData } = e.detail;
  // 处理评价订单逻辑
}
```

## 样式定制

组件使用CSS变量，可以通过修改全局CSS变量来定制样式：

```css
:root {
  --primary-color: #00d4ff;
  --text-primary: #ffffff;
  --bg-glass: rgba(255, 255, 255, 0.05);
}
```

## 注意事项

1. 确保传入的订单数据包含必要的字段
2. 根据业务需求处理各种事件
3. 组件会自动根据 `isOwner` 字段显示不同的操作按钮
4. 组件内置了确认对话框，无需在外部重复处理
