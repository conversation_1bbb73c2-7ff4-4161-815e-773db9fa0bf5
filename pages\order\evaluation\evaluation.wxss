/* 订单评价页面 - 优化科技主题样式 */
.container {
  padding: 24rpx;
  padding-top: 40rpx; /* 增加顶部间距避开导航栏 */
  padding-bottom: 140rpx; /* 为提交按钮留出空间 */
  background: linear-gradient(135deg, #0a0e13 0%, #1a2332 50%, #0f1419 100%);
  min-height: 100vh;
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 75%, rgba(0, 212, 255, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 75% 25%, rgba(255, 107, 53, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* 订单信息卡片 - 优化设计 */
.order-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1rpx solid rgba(0, 212, 255, 0.25);
  border-radius: 20rpx;
  padding: 28rpx;
  margin-bottom: 32rpx;
  backdrop-filter: blur(15rpx);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
  z-index: 1;
  overflow: hidden;
}

.order-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.08) 0%,
    rgba(138, 43, 226, 0.05) 50%,
    rgba(255, 107, 53, 0.08) 100%);
  border-radius: 20rpx;
  z-index: -1;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}

.order-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.4);
  letter-spacing: 0.5rpx;
}

.order-status.completed {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.25) 0%, rgba(76, 175, 80, 0.15) 100%);
  color: #66bb6a;
  padding: 6rpx 14rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: 1rpx solid rgba(76, 175, 80, 0.3);
  text-shadow: 0 0 6rpx rgba(76, 175, 80, 0.4);
}

.order-info {
  display: flex;
  gap: 24rpx;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 2;
}

.order-time, .order-duration {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.65);
  letter-spacing: 0.3rpx;
}

.order-amount {
  text-align: right;
  position: relative;
  z-index: 2;
}

.amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #ff6b35;
  text-shadow: 0 0 10rpx rgba(255, 107, 53, 0.5);
}

/* 评价表单 - 优化设计 */
.evaluation-form {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1rpx solid rgba(0, 212, 255, 0.25);
  border-radius: 20rpx;
  padding: 28rpx;
  margin-bottom: 32rpx;
  backdrop-filter: blur(15rpx);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
  z-index: 1;
  overflow: hidden;
}

.evaluation-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.08) 0%,
    rgba(138, 43, 226, 0.05) 50%,
    rgba(255, 107, 53, 0.08) 100%);
  border-radius: 20rpx;
  z-index: -1;
}

.form-section {
  margin-bottom: 40rpx;
  position: relative;
  z-index: 2;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.4);
  letter-spacing: 0.5rpx;
}

.required {
  color: #ff6b35;
  margin-left: 8rpx;
  font-size: 28rpx;
  text-shadow: 0 0 8rpx rgba(255, 107, 53, 0.4);
}

.optional {
  color: rgba(255, 255, 255, 0.5);
  margin-left: 8rpx;
  font-size: 22rpx;
}

/* 评分组件 - 优化设计 */
.rating-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 16rpx 0;
}

.stars {
  display: flex;
  gap: 12rpx;
}

.star {
  font-size: 56rpx;
  color: rgba(255, 255, 255, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 0 8rpx rgba(255, 215, 0, 0.2);
  position: relative;
  z-index: 10;
  padding: 12rpx;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  min-width: 72rpx;
  min-height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.02);
}

.star.active {
  color: #ffd700;
  text-shadow:
    0 0 12rpx rgba(255, 215, 0, 0.9),
    0 0 20rpx rgba(255, 215, 0, 0.7),
    0 0 30rpx rgba(255, 215, 0, 0.5);
  transform: scale(1.15);
  background: radial-gradient(circle, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 50%, transparent 70%);
  box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.3);
}

.rating-text {
  font-size: 26rpx;
  color: #00d4ff;
  margin-left: 16rpx;
  font-weight: 500;
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.4);
  letter-spacing: 0.5rpx;
}

/* 标签组件 - 优化设计 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  padding: 8rpx 0;
}

.tag {
  position: relative;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1rpx solid rgba(0, 212, 255, 0.25);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.75);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  z-index: 10;
  pointer-events: auto;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  min-height: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  letter-spacing: 0.3rpx;
}

.tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.15), transparent);
  transition: left 0.6s ease;
}

.tag:active::before {
  left: 100%;
}

/* 选中状态样式 - 移除位置变动效果 */
.tag.selected {
  background: #ff6b35 !important;
  color: #ffffff !important;
  border-color: #ff6b35 !important;
  font-weight: 600 !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3) !important;
  /* 移除 transform: scale() 避免位置变动 */
}

.tags-container .tag.selected {
  background: #ff6b35 !important;
  color: #ffffff !important;
  border-color: #ff6b35 !important;
  font-weight: 600 !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3) !important;
  /* 移除 transform: scale() 避免位置变动 */
}

/* 禁用选中标签的伪元素效果 */
.tag.selected::before {
  display: none !important;
}

/* 文本域 - 优化设计 */
.evaluation-textarea {
  width: 100%;
  min-height: 180rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1rpx solid rgba(0, 212, 255, 0.25);
  border-radius: 16rpx;
  font-size: 26rpx;
  line-height: 1.6;
  color: #ffffff;
  box-sizing: border-box;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10rpx);
  letter-spacing: 0.3rpx;
}

.evaluation-textarea:focus {
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow:
    0 0 20rpx rgba(0, 212, 255, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
}

.evaluation-textarea::placeholder {
  color: rgba(255, 255, 255, 0.35);
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.45);
  margin-top: 8rpx;
  letter-spacing: 0.3rpx;
}

/* 匿名选项 - 优化设计 */
.anonymous-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-top: 1rpx solid rgba(0, 212, 255, 0.15);
}

.option-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

/* 提交按钮 - 优化设计 */
.submit-container {
  padding: 24rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(15, 20, 25, 0.95) 0%, rgba(26, 35, 50, 0.95) 100%);
  border-top: 1rpx solid rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(20rpx);
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.3);
}

.submit-btn {
  position: relative;
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 50%, #f7931e 100%);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8rpx 24rpx rgba(255, 107, 53, 0.35),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  letter-spacing: 1rpx;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
  transition: left 0.6s ease;
}

.submit-btn:active::before {
  left: 100%;
}

.submit-btn.disabled {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
  color: rgba(255, 255, 255, 0.3);
  box-shadow: none;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.submit-btn:not(.disabled):active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow:
    0 4rpx 16rpx rgba(255, 107, 53, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

/* 滚动区域 */
.scrollarea {
  height: 100vh;
  background: transparent;
}

/* 确保自定义导航栏页面的滚动区域有正确的顶部间距 */
.scrollarea.page-with-custom-nav {
  padding-top: 200rpx; /* 为导航栏留出充足空间 */
  box-sizing: border-box;
}
