// 绑定手机号云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { phone } = event;

  try {
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phone || !phoneRegex.test(phone)) {
      return {
        success: false,
        error: '手机号格式不正确'
      };
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 检查手机号是否已被其他用户绑定
    const phoneCheckResult = await db.collection('users').where({
      phone: phone,
      _id: db.command.neq(user._id)
    }).get();

    if (phoneCheckResult.data.length > 0) {
      return {
        success: false,
        error: '该手机号已被其他用户绑定'
      };
    }

    // 更新用户手机号
    await db.collection('users').doc(user._id).update({
      data: {
        phone: phone,
        updateTime: new Date()
      }
    });

    return {
      success: true,
      message: '手机号绑定成功',
      data: {
        phone: phone
      }
    };
  } catch (error) {
    console.error('绑定手机号失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
