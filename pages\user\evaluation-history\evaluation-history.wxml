<!-- 用户评价历史页面 -->
<view class="container">
  <!-- 科技感装饰背景 -->
  <view class="tech-bg">
    <view class="tech-grid"></view>
    <view class="tech-particles"></view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    
    <!-- 标签页导航 -->
    <view class="tab-nav">
      <view 
        class="tab-item {{activeTab === 'stats' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="stats"
      >
        <text class="tab-text">评价统计</text>
      </view>
      <view 
        class="tab-item {{activeTab === 'received' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="received"
      >
        <text class="tab-text">收到的评价</text>
      </view>
      <view 
        class="tab-item {{activeTab === 'given' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="given"
      >
        <text class="tab-text">给出的评价</text>
      </view>
    </view>

    <!-- 评价统计 -->
    <view wx:if="{{activeTab === 'stats' && stats}}" class="stats-content">
      
      <!-- 总体统计卡片 -->
      <view class="stats-card">
        <view class="card-header">
          <text class="card-title">总体评价</text>
        </view>
        <view class="stats-overview">
          <view class="stat-item">
            <text class="stat-value">{{stats.overall.averageRating || 0}}</text>
            <text class="stat-label">平均评分</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{stats.overall.totalCount}}</text>
            <text class="stat-label">总评价数</text>
          </view>
        </view>
        
        <!-- 评分分布 -->
        <view class="rating-distribution">
          <view class="distribution-title">评分分布</view>
          <view class="distribution-bars">
            <view 
              wx:for="{{[5,4,3,2,1]}}" 
              wx:key="*this" 
              class="distribution-bar"
            >
              <text class="bar-label">{{item}}星</text>
              <view class="bar-container">
                <view 
                  class="bar-fill" 
                  style="width: {{stats.overall.totalCount > 0 ? (stats.overall.ratingDistribution[item] / stats.overall.totalCount * 100) : 0}}%"
                ></view>
              </view>
              <text class="bar-count">{{stats.overall.ratingDistribution[item]}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 角色分别统计 -->
      <view class="role-stats">
        <view class="role-stat-card">
          <view class="role-header">
            <text class="role-title">作为客户</text>
          </view>
          <view class="role-data">
            <text class="role-rating">{{stats.asCustomer.averageRating || 0}}</text>
            <text class="role-count">{{stats.asCustomer.totalCount}}条评价</text>
          </view>
        </view>
        
        <view class="role-stat-card">
          <view class="role-header">
            <text class="role-title">作为接单者</text>
          </view>
          <view class="role-data">
            <text class="role-rating">{{stats.asAccepter.averageRating || 0}}</text>
            <text class="role-count">{{stats.asAccepter.totalCount}}条评价</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 收到的评价列表 -->
    <view wx:if="{{activeTab === 'received'}}" class="evaluation-list">
      <view wx:if="{{receivedEvaluations.length === 0}}" class="empty-state">
        <text class="empty-icon">📝</text>
        <text class="empty-text">暂无收到的评价</text>
      </view>
      
      <view wx:else>
        <view 
          wx:for="{{receivedEvaluations}}" 
          wx:key="index" 
          class="evaluation-item"
        >
          <view class="eval-header">
            <view class="eval-role">
              <text class="role-badge">{{item.evaluatorRole === 'customer' ? '客户评价' : '接单者评价'}}</text>
            </view>
            <view class="eval-time">
              <text class="time-text">{{formatTime(item.createTime)}}</text>
            </view>
          </view>
          
          <view class="eval-rating">
            <text class="stars">{{item.rating === 1 ? '★☆☆☆☆' : item.rating === 2 ? '★★☆☆☆' : item.rating === 3 ? '★★★☆☆' : item.rating === 4 ? '★★★★☆' : '★★★★★'}}</text>
            <text class="rating-text">{{item.rating === 1 ? '很差' : item.rating === 2 ? '较差' : item.rating === 3 ? '一般' : item.rating === 4 ? '满意' : '非常满意'}}</text>
          </view>
          
          <view wx:if="{{item.tags && item.tags.length > 0}}" class="eval-tags">
            <text 
              wx:for="{{item.tags}}" 
              wx:for-item="tag" 
              wx:key="index" 
              class="eval-tag"
            >
              {{tag}}
            </text>
          </view>
          
          <view wx:if="{{item.content}}" class="eval-content">
            <text class="content-text">{{item.content}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 给出的评价列表 -->
    <view wx:if="{{activeTab === 'given'}}" class="evaluation-list">
      <view wx:if="{{givenEvaluations.length === 0}}" class="empty-state">
        <text class="empty-icon">✍️</text>
        <text class="empty-text">暂无给出的评价</text>
      </view>
      
      <view wx:else>
        <!-- TODO: 实现给出的评价列表 -->
      </view>
    </view>

  </view>
</view>
