/**
 * 性能优化配置文件
 * 统一管理所有性能相关的配置参数
 */

const performanceConfig = {
  // 缓存配置
  cache: {
    // 权限缓存配置
    permission: {
      ttl: 5 * 60 * 1000, // 5分钟
      maxSize: 1000,
      enabled: true
    },
    
    // 消息缓存配置
    message: {
      ttl: 2 * 60 * 1000, // 2分钟
      maxSize: 500,
      enabled: true,
      preloadPages: 2 // 预加载页数
    },
    
    // 用户信息缓存配置
    userInfo: {
      ttl: 10 * 60 * 1000, // 10分钟
      maxSize: 200,
      enabled: true
    },
    
    // 聊天室信息缓存配置
    chatRoom: {
      ttl: 5 * 60 * 1000, // 5分钟
      maxSize: 300,
      enabled: true
    }
  },

  // 性能监控配置
  monitoring: {
    enabled: true,
    
    // 性能阈值配置（毫秒）
    thresholds: {
      messageSend: 2000, // 消息发送
      messageLoad: 1500, // 消息加载
      permissionCheck: 500, // 权限检查
      dbQuery: 1000, // 数据库查询
      cloudFunction: 3000 // 云函数调用
    },
    
    // 采样率配置
    sampling: {
      rate: 1.0, // 100% 采样
      maxDailyMetrics: 100 // 每日最大指标数
    },
    
    // 报告配置
    reporting: {
      enabled: true,
      interval: 24 * 60 * 60 * 1000, // 24小时
      retentionDays: 7 // 保留7天数据
    }
  },

  // 网络请求配置
  network: {
    // 超时配置
    timeout: {
      default: 10000, // 默认10秒
      messageSend: 8000, // 消息发送8秒
      messageLoad: 6000, // 消息加载6秒
      upload: 30000 // 文件上传30秒
    },
    
    // 重试配置
    retry: {
      enabled: true,
      maxAttempts: 3,
      backoffMultiplier: 1.5,
      initialDelay: 1000
    },
    
    // 并发控制
    concurrency: {
      maxConcurrentRequests: 5,
      queueTimeout: 5000
    }
  },

  // 用户体验配置
  userExperience: {
    // 加载状态配置
    loading: {
      showDelay: 300, // 300ms后显示加载状态
      minShowTime: 500, // 最少显示500ms
      maxShowTime: 10000 // 最多显示10秒
    },
    
    // 消息发送体验配置
    messageSend: {
      instantFeedback: true, // 立即反馈
      optimisticUpdate: true, // 乐观更新
      showProgress: false, // 不显示进度条
      retryOnFailure: true // 失败时重试
    },
    
    // 消息加载体验配置
    messageLoad: {
      pageSize: 20, // 每页消息数
      preloadThreshold: 5, // 剩余5条时预加载
      smoothScrolling: true, // 平滑滚动
      skeletonScreen: true // 骨架屏
    }
  },

  // 数据库优化配置
  database: {
    // 查询优化
    query: {
      useIndexes: true, // 使用索引
      limitFields: true, // 限制字段
      batchSize: 100, // 批处理大小
      maxRetries: 3 // 最大重试次数
    },
    
    // 连接池配置
    connection: {
      maxConnections: 10,
      idleTimeout: 30000,
      acquireTimeout: 10000
    }
  },

  // 云函数优化配置
  cloudFunction: {
    // 调用优化
    call: {
      enableBatching: true, // 启用批处理
      batchTimeout: 100, // 批处理超时
      maxBatchSize: 10, // 最大批处理大小
      enableCaching: true // 启用缓存
    },
    
    // 冷启动优化
    coldStart: {
      enablePrewarming: true, // 启用预热
      prewarmInterval: 5 * 60 * 1000, // 5分钟预热一次
      keepAliveRequests: true // 保持连接
    }
  },

  // 内存管理配置
  memory: {
    // 消息列表内存管理
    messageList: {
      maxMessages: 500, // 最多保留500条消息
      cleanupThreshold: 600, // 超过600条时清理
      cleanupBatchSize: 100 // 每次清理100条
    },
    
    // 缓存内存管理
    cache: {
      maxTotalSize: 50 * 1024 * 1024, // 最大50MB
      cleanupInterval: 10 * 60 * 1000, // 10分钟清理一次
      lowMemoryThreshold: 0.8 // 80%内存使用率时清理
    }
  },

  // 调试配置
  debug: {
    enabled: true, // 开发环境启用
    logLevel: 'info', // 日志级别
    performanceLogging: true, // 性能日志
    errorReporting: true, // 错误报告
    metricsCollection: true // 指标收集
  },

  // 功能开关配置
  features: {
    // 实验性功能
    experimental: {
      realtimeOptimization: true, // 实时优化
      predictiveLoading: false, // 预测性加载
      adaptivePerformance: false // 自适应性能
    },
    
    // 性能功能开关
    performance: {
      enableCaching: true, // 启用缓存
      enableMonitoring: true, // 启用监控
      enableOptimization: true, // 启用优化
      enableCompression: false // 启用压缩
    }
  }
};

// 环境相关配置
const environmentConfig = {
  development: {
    debug: { enabled: true, logLevel: 'debug' },
    monitoring: { sampling: { rate: 1.0 } },
    cache: { permission: { ttl: 1 * 60 * 1000 } } // 开发环境缓存时间短一些
  },
  
  production: {
    debug: { enabled: false, logLevel: 'error' },
    monitoring: { sampling: { rate: 0.1 } }, // 生产环境降低采样率
    cache: { permission: { ttl: 10 * 60 * 1000 } } // 生产环境缓存时间长一些
  }
};

// 获取当前环境配置
function getCurrentConfig() {
  const env = process.env.NODE_ENV || 'development';
  const baseConfig = JSON.parse(JSON.stringify(performanceConfig));
  const envConfig = environmentConfig[env] || {};
  
  // 深度合并配置
  return deepMerge(baseConfig, envConfig);
}

// 深度合并对象
function deepMerge(target, source) {
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      target[key] = target[key] || {};
      deepMerge(target[key], source[key]);
    } else {
      target[key] = source[key];
    }
  }
  return target;
}

module.exports = {
  performanceConfig,
  environmentConfig,
  getCurrentConfig
};
