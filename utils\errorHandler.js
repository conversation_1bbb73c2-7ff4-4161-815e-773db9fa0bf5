// 错误处理工具
class ErrorHandler {
  constructor() {
    this.errorQueue = [];
    this.isProcessing = false;
  }

  // 处理错误
  handle(error, context = '') {
    const errorInfo = {
      message: error.message || error,
      context,
      timestamp: new Date().toISOString(),
      stack: error.stack
    };

    // 添加到错误队列
    this.errorQueue.push(errorInfo);
    
    // 异步处理错误，避免阻塞主线程
    this.processErrorQueue();
    
    // 根据错误类型决定是否显示给用户
    this.showUserError(error, context);
  }

  // 异步处理错误队列
  async processErrorQueue() {
    if (this.isProcessing || this.errorQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    
    try {
      while (this.errorQueue.length > 0) {
        const error = this.errorQueue.shift();
        await this.logError(error);
      }
    } catch (logError) {
      console.warn('错误日志记录失败:', logError);
    } finally {
      this.isProcessing = false;
    }
  }

  // 记录错误日志
  async logError(errorInfo) {
    try {
      // 只在开发环境输出详细错误信息
      this.checkDevEnvironment((isDevtools) => {
        if (isDevtools) {
          console.group(`🚨 错误 [${errorInfo.context}]`);
          console.error('消息:', errorInfo.message);
          console.error('时间:', errorInfo.timestamp);
          if (errorInfo.stack) {
            console.error('堆栈:', errorInfo.stack);
          }
          console.groupEnd();
        }
      });

      // 在生产环境可以上报到错误监控服务
      // await this.reportToService(errorInfo);
    } catch (error) {
      // 静默处理日志错误
    }
  }

  // 检查是否为开发环境
  checkDevEnvironment(callback) {
    try {
      if (wx.getDeviceInfo) {
        wx.getDeviceInfo({
          success: (res) => {
            callback(res.platform === 'devtools');
          },
          fail: () => {
            callback(false);
          }
        });
      } else {
        callback(false);
      }
    } catch (error) {
      callback(false);
    }
  }

  // 显示用户友好的错误信息
  showUserError(error, context) {
    const userMessage = this.getUserFriendlyMessage(error, context);
    
    if (userMessage) {
      // 防抖处理，避免频繁弹窗
      this.debounceShowToast(userMessage);
    }
  }

  // 获取用户友好的错误信息
  getUserFriendlyMessage(error, context) {
    const message = error.message || error;
    
    // 网络相关错误
    if (message.includes('timeout') || message.includes('超时')) {
      return '网络连接超时，请检查网络后重试';
    }
    
    if (message.includes('network') || message.includes('网络')) {
      return '网络连接异常，请检查网络设置';
    }
    
    // 云函数相关错误
    if (context.includes('云函数') || context.includes('callFunction')) {
      return '服务暂时不可用，请稍后重试';
    }
    
    // 数据相关错误
    if (message.includes('数据') || message.includes('data')) {
      return '数据加载失败，请重试';
    }
    
    // 权限相关错误
    if (message.includes('permission') || message.includes('权限')) {
      return '权限不足，请检查设置';
    }
    
    // 默认不显示技术性错误给用户
    return null;
  }

  // 防抖显示Toast
  debounceShowToast(message) {
    if (this.lastToastTime && Date.now() - this.lastToastTime < 2000) {
      return;
    }
    
    this.lastToastTime = Date.now();
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }

  // 清理错误队列
  clear() {
    this.errorQueue = [];
  }
}

// 创建全局错误处理实例
const errorHandler = new ErrorHandler();

// 全局错误监听
wx.onError && wx.onError((error) => {
  errorHandler.handle(error, '全局错误');
});

// 全局未处理的Promise拒绝监听
wx.onUnhandledRejection && wx.onUnhandledRejection((res) => {
  errorHandler.handle(res.reason, 'Promise拒绝');
});

module.exports = errorHandler;
