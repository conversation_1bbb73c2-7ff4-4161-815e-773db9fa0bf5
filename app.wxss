/**app.wxss**/

/* 全局深色背景，避免白色闪烁 */
page {
  background-color: #0f1419 !important;
}

/* 引入现代化组件样式 */
@import "style/modern-components.wxss";

/* ==================== 设计系统 ==================== */

/* CSS 变量定义 - 科技感深色主题 */
page {
  /* 主色彩系统 - 科技蓝色调 */
  --primary-color: #00d4ff;
  --primary-gradient: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #006699 100%);
  --primary-light: rgba(0, 212, 255, 0.15);
  --primary-dark: #0099cc;

  /* 科技感配色 */
  --accent-color: #00ff88;
  --accent-light: rgba(0, 255, 136, 0.15);
  --cyber-blue: #00d4ff;
  --cyber-green: #00ff88;
  --cyber-purple: #8b5cf6;

  /* 中性色系统 - 高对比度 */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #64748b;
  --text-disabled: #475569;
  --text-accent: #00d4ff;

  /* 背景色系统 - 深色科技风 */
  --bg-primary: rgba(15, 23, 42, 0.95);
  --bg-secondary: rgba(30, 41, 59, 0.9);
  --bg-tertiary: rgba(51, 65, 85, 0.8);
  --bg-overlay: rgba(0, 0, 0, 0.7);
  --bg-glass: rgba(255, 255, 255, 0.05);
  --bg-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);

  /* 边框色系统 - 科技感发光 */
  --border-color: rgba(148, 163, 184, 0.3);
  --border-light: rgba(148, 163, 184, 0.2);
  --border-medium: rgba(148, 163, 184, 0.3);
  --border-dark: rgba(148, 163, 184, 0.4);
  --border-glow: rgba(0, 212, 255, 0.5);

  /* 状态色系统 - 科技感配色 */
  --success-color: #00ff88;
  --warning-color: #fbbf24;
  --error-color: #ef4444;
  --info-color: #00d4ff;

  /* 阴影系统 - 科技感发光效果 */
  --shadow-sm: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
  --shadow-md: 0 6rpx 20rpx rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10rpx 32rpx rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 20rpx rgba(0, 212, 255, 0.3);
  --shadow-glow-strong: 0 0 30rpx rgba(0, 212, 255, 0.5);

  /* 圆角系统 - 统一现代化圆角 */
  --radius-sm: 12rpx;
  --radius-md: 20rpx;
  --radius-lg: 28rpx;
  --radius-xl: 36rpx;
  --radius-full: 50%;

  /* 间距系统 - 更协调的间距规范 */
  --space-xs: 8rpx;
  --space-sm: 16rpx;
  --space-md: 24rpx;
  --space-lg: 32rpx;
  --space-xl: 48rpx;
  --space-2xl: 64rpx;
  --space-3xl: 80rpx;

  /* 导航栏高度 */
  --navbar-height: 88rpx;
  --nav-bar-height: 94px;
}

/* 全局样式重置 - 科技感背景 */
page {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  background-attachment: fixed;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 28rpx;
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  /* 为胶囊按钮预留空间 */
  --capsule-safe-area-right: 20rpx;
}

/* 通用容器 */
.container {
  min-height: 100vh;
  background: transparent;
}

/* 胶囊按钮安全区域相关样式 */
.capsule-safe-area {
  padding-right: var(--capsule-safe-area-right);
}

.capsule-safe-margin {
  margin-right: var(--capsule-safe-area-right);
}

/* 自定义导航栏页面的内容区域 */
.custom-nav-content {
  padding-top: calc(var(--nav-bar-height, 94px) + 20rpx);
}

/* 为所有使用自定义导航栏的页面容器添加顶部间距 */
.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 20rpx);
}

/* 避免胶囊按钮遮挡的通用类 */
.avoid-capsule {
  padding-right: calc(env(safe-area-inset-right) + 120rpx); /* 增加更多右边距避免胶囊按钮遮挡 */
}

.page-container {
  padding: var(--space-lg);
  min-height: calc(100vh - 48rpx);
}

/* 科技感玻璃卡片样式 */
.card {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
  min-height: auto;
  height: auto;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, var(--border-glow), transparent);
}

.card:hover {
  box-shadow: var(--shadow-glow);
  transform: translateY(-2rpx);
  border-color: var(--border-glow);
}

.card-title {
  font-size: 34rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  letter-spacing: 0.5rpx;
}

/* 现代化按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  border-radius: var(--radius-xl);
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  outline: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5rpx;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:active::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: #ffffff;
  box-shadow: var(--shadow-glow);
  border: 1rpx solid var(--border-glow);
  position: relative;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-primary:hover::after {
  opacity: 1;
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2rpx solid var(--border-medium);
}

.btn-outline {
  background: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:active {
  background: var(--primary-light);
}

.btn-disabled {
  background: var(--border-light);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* 现代化输入框样式 */
.input-group {
  margin-bottom: var(--space-xl);
}

.input-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  font-weight: 500;
}

.input-field {
  height: 96rpx;
  padding: 0 var(--space-lg);
  border: 2rpx solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  background: var(--bg-primary);
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx var(--primary-light);
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.list-item-desc {
  font-size: 24rpx;
  color: #999;
}

/* 头像样式 */
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.avatar-large {
  width: 120rpx;
  height: 120rpx;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.tag-primary {
  background-color: #fff2e8;
  color: #ff6b35;
}

.tag-success {
  background-color: #f0f9ff;
  color: #1890ff;
}

.tag-warning {
  background-color: #fffbe6;
  color: #faad14;
}

/* 状态样式 */
.status-online {
  color: #52c41a;
}

.status-offline {
  color: #999;
}

.status-busy {
  color: #faad14;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: #52c41a;
}

.text-warning {
  color: #faad14;
}

.text-danger {
  color: #ff4d4f;
}

.text-muted {
  color: #999;
}

.mt-10 {
  margin-top: 10rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.ml-10 {
  margin-left: 10rpx;
}

.mr-10 {
  margin-right: 10rpx;
}
