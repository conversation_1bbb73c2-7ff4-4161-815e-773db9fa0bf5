@echo off
echo ========================================
echo 部署后台管理系统云函数
echo ========================================
echo.

echo 1. 检查云函数目录...
if not exist "cloudfunctions\adminApi\index.js" (
    echo 错误：找不到云函数文件 cloudfunctions\adminApi\index.js
    pause
    exit /b 1
)

echo 2. 检查package.json...
if not exist "cloudfunctions\adminApi\package.json" (
    echo 错误：找不到package.json文件
    pause
    exit /b 1
)

echo 3. 云函数文件检查完成
echo.
echo 请按照以下步骤手动部署云函数：
echo.
echo 步骤1：打开微信开发者工具
echo 步骤2：选择当前项目（三角洲小程序）
echo 步骤3：点击"云开发"按钮
echo 步骤4：进入"云函数"管理页面
echo 步骤5：找到"adminApi"云函数
echo 步骤6：右键点击"adminApi"文件夹
echo 步骤7：选择"上传并部署：云端安装依赖"
echo 步骤8：等待部署完成
echo.
echo 部署完成后，刷新后台管理系统页面即可看到修复后的最近活动数据
echo.
pause
