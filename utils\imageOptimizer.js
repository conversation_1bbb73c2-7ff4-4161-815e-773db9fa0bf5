/**
 * 图片优化工具类
 * 提供图片压缩、缓存、预加载等功能
 */

class ImageOptimizer {
  constructor() {
    this.cache = new Map();
    this.maxCacheSize = 50; // 最多缓存50张图片
    this.compressionQuality = 0.8; // 压缩质量
    this.maxWidth = 1200; // 最大宽度
    this.maxHeight = 1200; // 最大高度
  }

  /**
   * 压缩图片
   * @param {string} src 图片路径
   * @param {Object} options 压缩选项
   * @returns {Promise<string>} 压缩后的图片路径
   */
  async compressImage(src, options = {}) {
    try {
      const {
        quality = this.compressionQuality,
        maxWidth = this.maxWidth,
        maxHeight = this.maxHeight
      } = options;

      console.log('🖼️ [图片压缩] 开始压缩:', src);

      // 检查缓存
      const cacheKey = `${src}_${quality}_${maxWidth}_${maxHeight}`;
      if (this.cache.has(cacheKey)) {
        console.log('📦 [图片缓存] 使用缓存图片');
        return this.cache.get(cacheKey);
      }

      return new Promise((resolve, reject) => {
        wx.compressImage({
          src: src,
          quality: Math.floor(quality * 100),
          success: (res) => {
            console.log('✅ [图片压缩] 压缩成功');
            
            // 缓存压缩后的图片
            this.setCacheImage(cacheKey, res.tempFilePath);
            
            resolve(res.tempFilePath);
          },
          fail: (error) => {
            console.error('❌ [图片压缩] 压缩失败:', error);
            // 压缩失败时返回原图
            resolve(src);
          }
        });
      });

    } catch (error) {
      console.error('❌ [图片压缩] 压缩异常:', error);
      return src; // 返回原图
    }
  }

  /**
   * 获取图片信息
   * @param {string} src 图片路径
   * @returns {Promise<Object>} 图片信息
   */
  async getImageInfo(src) {
    try {
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: src,
          success: (res) => {
            resolve({
              width: res.width,
              height: res.height,
              path: res.path,
              orientation: res.orientation,
              type: res.type
            });
          },
          fail: reject
        });
      });
    } catch (error) {
      console.error('❌ [图片信息] 获取失败:', error);
      return null;
    }
  }

  /**
   * 智能压缩图片（根据图片大小自动调整压缩参数）
   * @param {string} src 图片路径
   * @returns {Promise<string>} 压缩后的图片路径
   */
  async smartCompress(src) {
    try {
      const imageInfo = await this.getImageInfo(src);
      if (!imageInfo) {
        return src;
      }

      const { width, height } = imageInfo;
      const fileSize = await this.getFileSize(src);

      // 根据图片尺寸和文件大小智能调整压缩参数
      let quality = this.compressionQuality;
      let maxWidth = this.maxWidth;
      let maxHeight = this.maxHeight;

      // 大图片使用更高压缩率
      if (fileSize > 2 * 1024 * 1024) { // 大于2MB
        quality = 0.6;
        maxWidth = 800;
        maxHeight = 800;
      } else if (fileSize > 1 * 1024 * 1024) { // 大于1MB
        quality = 0.7;
        maxWidth = 1000;
        maxHeight = 1000;
      }

      // 超高分辨率图片特殊处理
      if (width > 2000 || height > 2000) {
        quality = 0.6;
        maxWidth = 1000;
        maxHeight = 1000;
      }

      console.log('🧠 [智能压缩] 参数:', { quality, maxWidth, maxHeight, originalSize: fileSize });

      return await this.compressImage(src, { quality, maxWidth, maxHeight });

    } catch (error) {
      console.error('❌ [智能压缩] 失败:', error);
      return src;
    }
  }

  /**
   * 获取文件大小（估算）
   * @param {string} src 文件路径
   * @returns {Promise<number>} 文件大小（字节）
   */
  async getFileSize(src) {
    try {
      return new Promise((resolve) => {
        wx.getFileInfo({
          filePath: src,
          success: (res) => {
            resolve(res.size);
          },
          fail: () => {
            // 无法获取文件大小时返回默认值
            resolve(1024 * 1024); // 1MB
          }
        });
      });
    } catch (error) {
      return 1024 * 1024; // 1MB
    }
  }

  /**
   * 预加载图片
   * @param {Array<string>} urls 图片URL数组
   * @returns {Promise<Array>} 预加载结果
   */
  async preloadImages(urls) {
    if (!Array.isArray(urls) || urls.length === 0) {
      return [];
    }

    console.log('🚀 [图片预加载] 开始预加载:', urls.length, '张图片');

    const promises = urls.map(url => this.preloadSingleImage(url));
    
    try {
      const results = await Promise.allSettled(promises);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      
      console.log(`✅ [图片预加载] 完成: ${successCount}/${urls.length}`);
      
      return results;
    } catch (error) {
      console.error('❌ [图片预加载] 失败:', error);
      return [];
    }
  }

  /**
   * 预加载单张图片
   * @param {string} url 图片URL
   * @returns {Promise<string>} 预加载结果
   */
  preloadSingleImage(url) {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            // 缓存下载的图片
            this.setCacheImage(url, res.tempFilePath);
            resolve(res.tempFilePath);
          } else {
            reject(new Error(`下载失败: ${res.statusCode}`));
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 缓存图片
   * @param {string} key 缓存键
   * @param {string} path 图片路径
   */
  setCacheImage(key, path) {
    try {
      // 限制缓存大小
      if (this.cache.size >= this.maxCacheSize) {
        const firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
      }

      this.cache.set(key, {
        path: path,
        timestamp: Date.now()
      });

      console.log('📦 [图片缓存] 缓存已保存:', key);
    } catch (error) {
      console.error('📦 [图片缓存] 保存失败:', error);
    }
  }

  /**
   * 获取缓存图片
   * @param {string} key 缓存键
   * @returns {string|null} 图片路径
   */
  getCacheImage(key) {
    try {
      const cached = this.cache.get(key);
      if (cached) {
        // 检查缓存是否过期（1小时）
        const maxAge = 60 * 60 * 1000;
        if (Date.now() - cached.timestamp < maxAge) {
          return cached.path;
        } else {
          this.cache.delete(key);
        }
      }
      return null;
    } catch (error) {
      console.error('📦 [图片缓存] 获取失败:', error);
      return null;
    }
  }

  /**
   * 清理过期缓存
   */
  clearExpiredCache() {
    try {
      const now = Date.now();
      const maxAge = 60 * 60 * 1000; // 1小时

      for (const [key, value] of this.cache.entries()) {
        if (now - value.timestamp > maxAge) {
          this.cache.delete(key);
        }
      }


    } catch (error) {
      console.error('📦 [图片缓存] 清理失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建全局实例
const imageOptimizer = new ImageOptimizer();

module.exports = imageOptimizer;
