// 初始化订单锁集合的云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {

  
  try {
    // 检查order_locks集合是否存在
    try {
      const testResult = await db.collection('order_locks').limit(1).get();
      console.log('✅ order_locks集合已存在');
      
      return {
        success: true,
        message: 'order_locks集合已存在',
        collectionExists: true
      };
    } catch (error) {
      if (error.errCode === -502005 || error.errCode === -502002) {
        console.log('📋 order_locks集合不存在，开始创建...');
        
        // 创建一个临时文档来初始化集合
        const initResult = await db.collection('order_locks').add({
          data: {
            _id: 'init_lock_temp',
            description: '临时初始化文档，用于创建集合',
            createTime: new Date(),
            expireTime: new Date(Date.now() + 1000) // 1秒后过期
          }
        });
        
        console.log('✅ 临时文档创建成功:', initResult._id);
        
        // 立即删除临时文档
        await db.collection('order_locks').doc('init_lock_temp').remove();
        console.log('✅ 临时文档已删除');
        
        // 设置集合权限（如果需要）
        console.log('📋 order_locks集合创建完成');
        
        return {
          success: true,
          message: 'order_locks集合创建成功',
          collectionExists: false,
          created: true
        };
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('❌ 初始化order_locks集合失败:', error);
    
    return {
      success: false,
      error: error.message || '初始化失败',
      errorCode: error.errCode
    };
  }
};
