// WebView页面
Page({
  data: {
    url: '',
    pageTitle: 'WebView',
    loading: true,
    error: false,
    errorMessage: ''
  },

  onLoad(options) {
    const { url, title } = options;

    if (url) {
      // 解码URL
      const decodedUrl = decodeURIComponent(url);
      this.setData({
        url: decodedUrl,
        pageTitle: title || 'WebView',
        loading: true,
        error: false
      });
    } else {
      this.setData({
        loading: false,
        error: false
      });
    }
  },

  // WebView加载完成
  onWebViewLoad() {
    this.setData({
      loading: false,
      error: false
    });
  },

  // WebView加载错误
  onWebViewError(e) {
    console.error('WebView加载错误:', e);
    this.setData({
      loading: false,
      error: true,
      errorMessage: '网络连接失败或页面不存在'
    });
  },

  // 重新加载
  retryLoad() {
    if (this.data.url) {
      this.setData({
        loading: true,
        error: false,
        errorMessage: ''
      });
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: this.data.pageTitle,
      path: `/pages/common/webview/webview?url=${encodeURIComponent(this.data.url)}&title=${this.data.pageTitle}`
    };
  }
})