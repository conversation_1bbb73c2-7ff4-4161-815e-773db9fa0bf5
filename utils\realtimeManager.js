// 实时监听管理器 - 统一管理所有实时监听器，避免状态冲突
class RealtimeManager {
  constructor() {
    this.watchers = new Map(); // 存储所有监听器
    this.watcherStates = new Map(); // 存储监听器状态
    this.reconnectTimers = new Map(); // 存储重连定时器
    this.maxReconnectAttempts = 3;
    this.reconnectDelay = 2000; // 基础重连延迟
  }

  // 创建监听器
  createWatcher(watcherId, config) {
    // 如果监听器已存在，先关闭它
    if (this.watchers.has(watcherId)) {
      this.closeWatcher(watcherId);
    }

    try {
      const db = wx.cloud.database();
      let query = db.collection(config.collection);

      // 应用查询条件
      if (config.where) {
        query = query.where(config.where);
      }

      // 应用排序
      if (config.orderBy) {
        query = query.orderBy(config.orderBy.field, config.orderBy.direction || 'desc');
      }

      // 应用限制
      if (config.limit) {
        query = query.limit(config.limit);
      }

      // 创建监听器
      const watcher = query.watch({
        onChange: (snapshot) => {
          const state = this.watcherStates.get(watcherId);
          if (state && state.active && config.onChange) {
            try {
              config.onChange(snapshot);
            } catch (error) {
              console.error(`❌ [实时管理器] 监听器 ${watcherId} onChange 处理失败:`, error);
            }
          }
        },
        onError: (error) => {
          console.error(`❌ [实时管理器] 监听器 ${watcherId} 错误:`, error);
          
          // 更新状态
          this.updateWatcherState(watcherId, { active: false, error: error });
          
          // 调用用户的错误处理函数
          if (config.onError) {
            try {
              config.onError(error);
            } catch (handlerError) {
              console.error(`❌ [实时管理器] 监听器 ${watcherId} onError 处理失败:`, handlerError);
            }
          }

          // 检查是否需要重连
          this.handleReconnect(watcherId, config, error);
        }
      });

      // 保存监听器和配置
      this.watchers.set(watcherId, watcher);
      this.updateWatcherState(watcherId, {
        active: true,
        config: config,
        reconnectAttempts: 0,
        error: null,
        createTime: new Date()
      });

      return watcher;

    } catch (error) {
      console.error(`❌ [实时管理器] 创建监听器 ${watcherId} 失败:`, error);
      this.updateWatcherState(watcherId, { active: false, error: error });
      
      if (config.onError) {
        config.onError(error);
      }
      
      return null;
    }
  }

  // 关闭监听器
  closeWatcher(watcherId) {
    console.log(`🛑 [实时管理器] 关闭监听器: ${watcherId}`);
    
    const watcher = this.watchers.get(watcherId);
    if (watcher) {
      try {
        watcher.close();
      } catch (error) {
        console.error(`❌ [实时管理器] 关闭监听器 ${watcherId} 失败:`, error);
      }
      this.watchers.delete(watcherId);
    }

    // 清理重连定时器
    const timer = this.reconnectTimers.get(watcherId);
    if (timer) {
      clearTimeout(timer);
      this.reconnectTimers.delete(watcherId);
    }

    // 更新状态
    this.updateWatcherState(watcherId, { active: false });
  }

  // 处理重连
  handleReconnect(watcherId, config, error) {
    const state = this.watcherStates.get(watcherId);
    if (!state || !config.autoReconnect) {
      return;
    }

    const reconnectAttempts = state.reconnectAttempts || 0;
    if (reconnectAttempts >= this.maxReconnectAttempts) {
      console.log(`❌ [实时管理器] 监听器 ${watcherId} 重连次数已达上限`);
      return;
    }

    // 检查错误类型，决定重连策略
    const errorMsg = error.message || error.errMsg || '';
    let delay = this.reconnectDelay;
    
    if (errorMsg.includes('ws connection not exists') || 
        errorMsg.includes('init watch fail') ||
        errorMsg.includes('INIT_WATCH_FAIL') ||
        errorMsg.includes('current state') ||
        errorMsg.includes('does not accept')) {
      // 对于状态冲突错误，使用更长的延迟
      delay = this.reconnectDelay * Math.pow(2, reconnectAttempts) + Math.random() * 1000;
    }

    console.log(`🔄 [实时管理器] 监听器 ${watcherId} 将在 ${delay}ms 后重连 (第${reconnectAttempts + 1}次)`);

    const timer = setTimeout(() => {
      this.reconnectTimers.delete(watcherId);
      
      // 更新重连次数
      this.updateWatcherState(watcherId, { 
        reconnectAttempts: reconnectAttempts + 1 
      });
      
      // 重新创建监听器
      this.createWatcher(watcherId, config);
    }, delay);

    this.reconnectTimers.set(watcherId, timer);
  }

  // 更新监听器状态
  updateWatcherState(watcherId, updates) {
    const currentState = this.watcherStates.get(watcherId) || {};
    const newState = { ...currentState, ...updates, updateTime: new Date() };
    this.watcherStates.set(watcherId, newState);
  }

  // 获取监听器状态
  getWatcherState(watcherId) {
    return this.watcherStates.get(watcherId);
  }

  // 获取所有监听器状态
  getAllWatcherStates() {
    const states = {};
    this.watcherStates.forEach((state, watcherId) => {
      states[watcherId] = state;
    });
    return states;
  }

  // 检查监听器是否活跃
  isWatcherActive(watcherId) {
    const state = this.watcherStates.get(watcherId);
    return state && state.active;
  }

  // 重启监听器
  restartWatcher(watcherId) {
    const state = this.watcherStates.get(watcherId);
    if (!state || !state.config) {
      console.error(`❌ [实时管理器] 无法重启监听器 ${watcherId}: 配置不存在`);
      return false;
    }

    console.log(`🔄 [实时管理器] 重启监听器: ${watcherId}`);
    
    // 关闭现有监听器
    this.closeWatcher(watcherId);
    
    // 重置重连次数
    const config = { ...state.config };
    
    // 延迟重启，避免状态冲突
    setTimeout(() => {
      this.createWatcher(watcherId, config);
    }, 1000);
    
    return true;
  }

  // 关闭所有监听器
  closeAllWatchers() {
    this.watchers.forEach((watcher, watcherId) => {
      this.closeWatcher(watcherId);
    });
    
    this.watchers.clear();
    this.watcherStates.clear();
    this.reconnectTimers.clear();
  }

  // 获取统计信息
  getStats() {
    const totalWatchers = this.watchers.size;
    const activeWatchers = Array.from(this.watcherStates.values())
      .filter(state => state.active).length;
    const errorWatchers = Array.from(this.watcherStates.values())
      .filter(state => state.error).length;
    
    return {
      total: totalWatchers,
      active: activeWatchers,
      error: errorWatchers,
      reconnecting: this.reconnectTimers.size
    };
  }
}

// 创建全局实例
const realtimeManager = new RealtimeManager();

module.exports = realtimeManager;
