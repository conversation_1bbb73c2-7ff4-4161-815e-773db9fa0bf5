// 真正实时语音发送 - 完全模拟文字消息体验
// 用户完全感受不到任何延迟或加载状态

const voiceOptimizer = require('../../utils/voice-optimizer');

const realtimeVoiceMethods = {
  
  /**
   * 🚀 真正实时语音发送 - 零感知延迟
   * 完全模拟文字消息的发送体验
   */
  async sendVoiceMessageRealtime(voicePath, duration) {
    console.log('⚡ [实时发送] 开始真正实时语音发送');
    
    try {
      // 🎯 核心策略：立即显示为"已发送"状态，无任何加载提示
      const messageId = this.addInstantVoiceMessage(voicePath, duration);
      
      // 🔄 静默后台处理 - 用户完全感知不到
      this.silentBackgroundProcess(voicePath, duration, messageId);
      
      // ✅ 用户体验：语音消息立即出现，就像文字消息一样
      console.log('⚡ [实时发送] 用户看到消息已发送，零延迟体验');
      
    } catch (error) {
      console.error('❌ [实时发送] 发送失败:', error);
      // 即使失败也不显示加载状态，静默处理
    }
  },

  /**
   * 立即显示语音消息 - 完全模拟已发送状态
   */
  addInstantVoiceMessage(voicePath, duration) {
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const durationInSeconds = Math.ceil(duration / 1000);
    
    // 🎯 关键：创建看起来完全正常的消息，无任何"发送中"标识
    const instantMessage = {
      _id: messageId,
      type: 'voice',
      content: voicePath, // 先使用本地路径，后台静默替换
      duration: durationInSeconds,
      isSelf: true,
      createTime: new Date(),
      formattedTime: this.formatMessageTime(new Date()),
      senderInfo: this.data.userInfo,
      // 🚀 关键：完全不显示任何加载状态
      isUploading: false,    // 不显示上传中
      isTemp: false,         // 不标记为临时
      isFailed: false,       // 不显示失败状态
      isRealtime: true       // 内部标记为实时消息
    };

    // 立即添加到消息列表，用户看到的就是正常发送完成的消息
    const messageList = [...this.data.messageList, instantMessage];
    this.setData({ messageList });
    this.scrollToBottom();
    
    console.log('📱 [实时显示] 消息立即显示为已发送状态');
    return messageId;
  },

  /**
   * 静默后台处理 - 用户完全感知不到
   */
  async silentBackgroundProcess(voicePath, duration, messageId) {
    try {
      console.log('🔇 [静默处理] 开始后台上传，用户无感知');
      
      // 🎯 策略1：预缓存 - 立即开始上传但不显示状态
      const uploadPromise = this.silentUpload(voicePath);
      
      // 🎯 策略2：预发送 - 准备消息数据
      const messageData = this.prepareMessageData(duration);
      
      // 🎯 策略3：并行处理 - 上传和准备同时进行
      const [uploadResult, preparedData] = await Promise.all([
        uploadPromise,
        messageData
      ]);
      
      // 🎯 策略4：静默更新 - 悄悄替换本地路径为云端路径
      this.silentUpdateMessage(messageId, uploadResult.fileID);
      
      // 🎯 策略5：静默发送到服务器
      await this.silentSendToServer(messageId, uploadResult.fileID, Math.ceil(duration / 1000));
      
      console.log('✅ [静默处理] 后台处理完成，用户完全无感知');
      
    } catch (error) {
      console.error('❌ [静默处理] 后台处理失败:', error);
      // 🎯 关键：即使失败也不打扰用户，静默重试
      this.silentRetry(voicePath, duration, messageId);
    }
  },

  /**
   * 静默上传 - 无任何UI提示
   */
  async silentUpload(voicePath) {
    // 使用优化的上传方法，但不显示任何进度
    return await voiceOptimizer.optimizedUpload(voicePath, null); // 不传递进度回调
  },

  /**
   * 准备消息数据
   */
  async prepareMessageData(duration) {
    return {
      duration: Math.ceil(duration / 1000),
      timestamp: Date.now(),
      userInfo: this.data.userInfo
    };
  },

  /**
   * 静默更新消息 - 悄悄替换本地路径为云端路径
   */
  silentUpdateMessage(messageId, cloudFileID) {
    const messageList = this.data.messageList.map(msg => {
      if (msg._id === messageId && msg.isRealtime) {
        return {
          ...msg,
          content: cloudFileID, // 悄悄替换为云端路径
          isRealtime: false     // 标记为已完成
        };
      }
      return msg;
    });
    
    // 静默更新，用户感知不到任何变化
    this.setData({ messageList });
    console.log('🔄 [静默更新] 已悄悄替换为云端路径');
  },

  /**
   * 静默发送到服务器
   */
  async silentSendToServer(messageId, fileID, duration) {
    const API = require('../../utils/api');
    
    try {
      const result = await API.sendVoiceMessage(
        this.data.roomId,
        fileID,
        duration,
        this.data.userInfo
      );
      
      // 静默更新消息ID为服务器返回的ID
      this.silentUpdateMessageId(messageId, result._id);
      
    } catch (error) {
      console.error('❌ [静默发送] 服务器发送失败:', error);
      // 静默重试，不打扰用户
      setTimeout(() => {
        this.silentSendToServer(messageId, fileID, duration);
      }, 2000);
    }
  },

  /**
   * 静默更新消息ID
   */
  silentUpdateMessageId(tempId, realId) {
    const messageList = this.data.messageList.map(msg => {
      if (msg._id === tempId) {
        return { ...msg, _id: realId };
      }
      return msg;
    });
    
    this.setData({ messageList });
  },

  /**
   * 静默重试 - 失败时自动重试，用户无感知
   */
  async silentRetry(voicePath, duration, messageId, retryCount = 0) {
    if (retryCount >= 3) {
      console.log('🔇 [静默重试] 达到最大重试次数，放弃重试');
      return;
    }
    
    console.log(`🔄 [静默重试] 第${retryCount + 1}次重试`);
    
    // 等待一段时间后重试
    setTimeout(() => {
      this.silentBackgroundProcess(voicePath, duration, messageId);
    }, 1000 * (retryCount + 1)); // 递增延迟
  },

  /**
   * 🎯 优化录音结束处理 - 直接调用实时发送
   */
  onVoiceTouchEndRealtime(e) {
    console.log('🎤 [实时录音] 录音结束，准备实时发送');
    
    if (this.data.isRecording) {
      wx.stopRecord({
        success: (res) => {
          console.log('🎤 [实时录音] 录音成功，立即实时发送');
          
          // 🚀 关键：直接调用实时发送，用户立即看到消息
          this.sendVoiceMessageRealtime(res.tempFilePath, res.duration);
          
          // 重置录音状态
          this.setData({
            isRecording: false,
            recordingTip: '按住说话',
            isCancelMode: false
          });
        },
        fail: (error) => {
          console.error('❌ [实时录音] 录音失败:', error);
          this.setData({
            isRecording: false,
            recordingTip: '按住说话',
            isCancelMode: false
          });
        }
      });
    }
  },

  /**
   * 🎯 语音播放优化 - 支持本地和云端路径
   */
  onVoicePlayRealtime(e) {
    const { src, messageId } = e.currentTarget.dataset;
    
    // 🎯 智能播放：自动识别本地路径和云端路径
    if (src.startsWith('http') || src.startsWith('cloud://')) {
      // 云端路径，正常播放
      this.playVoiceFromCloud(src, messageId);
    } else {
      // 本地路径，直接播放（上传期间）
      this.playVoiceFromLocal(src, messageId);
    }
  },

  /**
   * 播放云端语音
   */
  playVoiceFromCloud(cloudPath, messageId) {
    wx.downloadFile({
      url: cloudPath,
      success: (res) => {
        this.playVoiceFile(res.tempFilePath, messageId);
      },
      fail: (error) => {
        console.error('❌ [语音播放] 下载失败:', error);
      }
    });
  },

  /**
   * 播放本地语音
   */
  playVoiceFromLocal(localPath, messageId) {
    this.playVoiceFile(localPath, messageId);
  },

  /**
   * 播放语音文件
   */
  playVoiceFile(filePath, messageId) {
    // 停止当前播放
    wx.stopVoice();
    
    // 更新播放状态
    this.updateVoicePlayingState(messageId, true);
    
    wx.playVoice({
      filePath: filePath,
      success: () => {
        console.log('🎵 [语音播放] 播放完成');
        this.updateVoicePlayingState(messageId, false);
      },
      fail: (error) => {
        console.error('❌ [语音播放] 播放失败:', error);
        this.updateVoicePlayingState(messageId, false);
      }
    });
  },

  /**
   * 更新语音播放状态
   */
  updateVoicePlayingState(messageId, isPlaying) {
    const messageList = this.data.messageList.map(msg => {
      if (msg._id === messageId) {
        return { ...msg, isPlaying };
      } else {
        return { ...msg, isPlaying: false }; // 其他消息停止播放
      }
    });
    
    this.setData({ messageList });
  }
};

module.exports = realtimeVoiceMethods;