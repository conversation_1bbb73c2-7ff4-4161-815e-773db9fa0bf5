# 任务完成证明功能测试指南

## 功能概述

新增了任务完成证明功能，要求接单者在完成订单时必须上传截图或录屏作为任务完成的证明，确保交易安全和任务质量。

## 实现的功能

### 1. 完成证明页面
- **路径**: `order-package/pages/complete-proof/complete-proof`
- **功能**: 
  - 上传最多6张截图
  - 上传1个录屏视频（最大100MB）
  - 添加文字说明（可选）
  - 提交完成证明并完成订单

### 2. 修改的文件

#### 前端文件
- `order-package/pages/complete-proof/` - 新增完成证明页面
- `order-package/pages/detail/detail.js` - 修改完成订单逻辑
- `utils/api.js` - 添加新的API接口
- `app.json` - 注册新页面

#### 后端文件
- `cloudfunctions/updateOrderStatus/index.js` - 添加证明验证逻辑

## 测试步骤

### 1. 准备测试环境
1. 确保微信开发者工具已打开项目
2. 部署更新的云函数：`updateOrderStatus`
3. 确保有测试用的订单数据

### 2. 测试流程

#### 步骤1：创建测试订单
1. 使用客户账号创建一个订单
2. 使用接单者账号接受订单
3. 将订单状态推进到"进行中"

#### 步骤2：测试完成证明功能
1. 在订单详情页点击"完成订单"按钮
2. 验证是否跳转到完成证明页面
3. 测试上传功能：
   - 上传1-6张截图
   - 上传录屏视频（可选）
   - 填写文字说明（可选）
4. 点击"确认完成订单"按钮
5. 验证订单是否成功完成

#### 步骤3：验证数据存储
1. 在云开发控制台查看orders集合
2. 确认完成的订单包含`completionProof`字段
3. 验证证明材料的文件ID是否正确保存

### 3. 测试用例

#### 用例1：正常完成流程
- **前置条件**: 订单状态为"进行中"，当前用户为接单者
- **操作**: 上传截图 → 填写说明 → 提交
- **预期结果**: 订单完成，资金结算，发送通知

#### 用例2：无证明材料提交
- **前置条件**: 订单状态为"进行中"，当前用户为接单者
- **操作**: 不上传任何材料直接提交
- **预期结果**: 显示错误提示"请至少上传一张截图或录屏视频"

#### 用例3：权限验证
- **前置条件**: 订单状态为"进行中"，当前用户为客户
- **操作**: 尝试访问完成证明页面
- **预期结果**: 无法完成订单（只有接单者可以完成）

#### 用例4：文件上传限制
- **前置条件**: 在完成证明页面
- **操作**: 尝试上传超过6张图片或超过100MB的视频
- **预期结果**: 显示相应的限制提示

### 4. 验证要点

#### 前端验证
- [ ] 页面样式正常显示
- [ ] 图片/视频上传功能正常
- [ ] 文件大小和数量限制生效
- [ ] 提交按钮状态正确控制
- [ ] 错误提示正常显示

#### 后端验证
- [ ] 证明材料验证逻辑正确
- [ ] 订单状态更新正常
- [ ] 资金结算正确执行
- [ ] 通知发送正常
- [ ] 数据库字段正确保存

#### 业务流程验证
- [ ] 只有接单者可以完成订单
- [ ] 必须上传证明材料才能完成
- [ ] 完成后无法撤销
- [ ] 评价流程正常触发

## 已修复的问题

### 1. WXML语法错误 ✅
- **问题**: WXML中不能使用复杂的JavaScript表达式
- **修复**: 将计算逻辑移到JavaScript中，在data中存储计算结果
- **涉及**: 视频大小显示、时长显示、提交按钮状态

### 2. 函数调用错误 ✅
- **问题**: WXML中不能直接调用函数
- **修复**: 使用data变量存储函数计算结果，在状态变化时更新

## 可能的问题和解决方案

### 1. 页面跳转失败
- **原因**: 页面路径错误或未注册
- **解决**: 检查app.json中的页面注册

### 2. 文件上传失败
- **原因**: 云存储权限或网络问题
- **解决**: 检查云存储配置和网络连接

### 3. 云函数调用失败
- **原因**: 云函数未部署或代码错误
- **解决**: 重新部署云函数，检查日志

### 4. 数据保存失败
- **原因**: 数据格式错误或权限问题
- **解决**: 检查数据结构和数据库权限

## 后续优化建议

1. **AI识别**: 可以集成图像识别API验证截图内容
2. **人工审核**: 添加管理员审核证明材料的功能
3. **模板匹配**: 针对不同游戏类型提供证明模板
4. **自动验证**: 通过游戏API自动验证任务完成状态
5. **证明展示**: 在订单详情中展示完成证明给客户查看

## 注意事项

1. 确保云存储有足够的空间存储证明材料
2. 定期清理过期的证明文件以节省存储空间
3. 考虑添加证明材料的访问权限控制
4. 监控文件上传的成功率和失败原因
5. 建议添加证明材料的备份机制
