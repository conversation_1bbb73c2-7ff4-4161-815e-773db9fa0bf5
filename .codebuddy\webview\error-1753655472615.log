Webview errors at url: http://localhost:5176/users
error: 
 [Window Error]: [javascript-error]: JavaScript Error: Uncaught TypeError: Cannot read properties of undefined (reading 'toLowerCase') | Url: http://localhost:5176/src/pages/users/UsersPage.tsx | File: http://localhost:5176/src/pages/users/UsersPage.tsx:145:29
Uncaught TypeError: Cannot read properties of undefined (reading 'toLowerCase')
The above error occurred in the <UsersPage> component:

    at UsersPage (http://localhost:5176/src/pages/users/UsersPage.tsx:56:29)
    at RenderedRoute (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=1f1b9b18:5525:26)
    at Routes (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=1f1b9b18:6261:3)
    at main
    at div
    at div
    at Layout (http://localhost:5176/src/components/layout/Layout.tsx:22:34)
    at ProtectedRoute (http://localhost:5176/src/App.tsx:31:27)
    at RenderedRoute (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=1f1b9b18:5525:26)
    at Routes (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=1f1b9b18:6261:3)
    at div
    at Router (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=1f1b9b18:6204:13)
    at BrowserRouter (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=1f1b9b18:9273:3)
    at V (http://localhost:5176/node_modules/.vite/deps/next-themes.js?v=1f1b9b18:44:25)
    at J (http://localhost:5176/node_modules/.vite/deps/next-themes.js?v=1f1b9b18:42:18)
    at ThemeProvider (http://localhost:5176/src/components/theme-provider.tsx:20:33)
    at App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
[Window Error]: [javascript-error]: JavaScript Error: Uncaught TypeError: Cannot read properties of undefined (reading 'toLowerCase') | Url: http://localhost:5176/node_modules/.vite/deps/chunk-NUMECXU6.js?v=1f1b9b18 | File: http://localhost:5176/node_modules/.vite/deps/chunk-NUMECXU6.js?v=1f1b9b18:19413:13