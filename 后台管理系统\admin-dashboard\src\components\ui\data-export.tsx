import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  FileSpreadsheet, 
  FileText, 
  Calendar as CalendarIcon,
  Settings,
  CheckCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export interface ExportField {
  key: string;
  label: string;
  required?: boolean;
  type?: 'string' | 'number' | 'date' | 'boolean';
}

export interface ExportTemplate {
  id: string;
  name: string;
  description: string;
  fields: string[];
  format: 'csv' | 'xlsx';
}

interface DataExportProps {
  title: string;
  description?: string;
  fields: ExportField[];
  templates?: ExportTemplate[];
  onExport: (options: ExportOptions) => Promise<void>;
  disabled?: boolean;
  maxRecords?: number;
}

export interface ExportOptions {
  format: 'csv' | 'xlsx';
  fields: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  filters?: Record<string, any>;
  template?: string;
  filename?: string;
}

export function DataExport({
  title,
  description,
  fields,
  templates = [],
  onExport,
  disabled = false,
  maxRecords = 10000,
}: DataExportProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportFormat, setExportFormat] = useState<'csv' | 'xlsx'>('xlsx');
  const [selectedFields, setSelectedFields] = useState<string[]>(
    fields.filter(f => f.required).map(f => f.key)
  );
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [dateRange, setDateRange] = useState<{ start?: Date; end?: Date }>({});
  const [customFilename, setCustomFilename] = useState('');

  // 处理字段选择
  const handleFieldToggle = (fieldKey: string, checked: boolean) => {
    const field = fields.find(f => f.key === fieldKey);
    if (field?.required) return; // 必需字段不能取消选择

    if (checked) {
      setSelectedFields([...selectedFields, fieldKey]);
    } else {
      setSelectedFields(selectedFields.filter(key => key !== fieldKey));
    }
  };

  // 处理模板选择
  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedFields(template.fields);
      setExportFormat(template.format);
    }
  };

  // 处理导出
  const handleExport = async () => {
    if (selectedFields.length === 0) {
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      const exportOptions: ExportOptions = {
        format: exportFormat,
        fields: selectedFields,
        dateRange: dateRange.start && dateRange.end ? {
          start: dateRange.start,
          end: dateRange.end,
        } : undefined,
        template: selectedTemplate || undefined,
        filename: customFilename || undefined,
      };

      await onExport(exportOptions);
      
      clearInterval(progressInterval);
      setExportProgress(100);
      
      // 延迟关闭对话框
      setTimeout(() => {
        setIsOpen(false);
        setIsExporting(false);
        setExportProgress(0);
      }, 1000);

    } catch (error) {
      console.error('导出失败:', error);
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // 重置表单
  const resetForm = () => {
    setSelectedFields(fields.filter(f => f.required).map(f => f.key));
    setSelectedTemplate('');
    setDateRange({});
    setCustomFilename('');
    setExportFormat('xlsx');
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" disabled={disabled}>
          <Download className="mr-2 h-4 w-4" />
          {title}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileSpreadsheet className="mr-2 h-5 w-5" />
            {title}
          </DialogTitle>
          {description && (
            <DialogDescription>{description}</DialogDescription>
          )}
        </DialogHeader>

        {isExporting ? (
          <div className="space-y-4 py-6">
            <div className="text-center">
              <div className="text-lg font-medium mb-2">正在导出数据...</div>
              <Progress value={exportProgress} className="w-full" />
              <div className="text-sm text-muted-foreground mt-2">
                {exportProgress < 100 ? `进度: ${exportProgress}%` : '导出完成！'}
              </div>
            </div>
            {exportProgress === 100 && (
              <div className="flex items-center justify-center text-green-600">
                <CheckCircle className="mr-2 h-5 w-5" />
                导出成功！
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {/* 导出模板 */}
            {templates.length > 0 && (
              <div className="space-y-2">
                <Label>导出模板</Label>
                <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择预设模板（可选）" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">自定义导出</SelectItem>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        <div>
                          <div className="font-medium">{template.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {template.description}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* 导出格式 */}
            <div className="space-y-2">
              <Label>导出格式</Label>
              <Select value={exportFormat} onValueChange={(value: 'csv' | 'xlsx') => setExportFormat(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="xlsx">
                    <div className="flex items-center">
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Excel (.xlsx)
                    </div>
                  </SelectItem>
                  <SelectItem value="csv">
                    <div className="flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      CSV (.csv)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 字段选择 */}
            <div className="space-y-2">
              <Label>导出字段</Label>
              <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border rounded-md p-3">
                {fields.map((field) => (
                  <div key={field.key} className="flex items-center space-x-2">
                    <Checkbox
                      id={field.key}
                      checked={selectedFields.includes(field.key)}
                      onCheckedChange={(checked) => 
                        handleFieldToggle(field.key, checked as boolean)
                      }
                      disabled={field.required}
                    />
                    <Label 
                      htmlFor={field.key} 
                      className={`text-sm ${field.required ? 'font-medium' : ''}`}
                    >
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                  </div>
                ))}
              </div>
              <div className="text-sm text-muted-foreground">
                已选择 {selectedFields.length} 个字段
              </div>
            </div>

            {/* 日期范围 */}
            <div className="space-y-2">
              <Label>日期范围（可选）</Label>
              <div className="flex space-x-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="flex-1">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.start ? format(dateRange.start, 'yyyy-MM-dd', { locale: zhCN }) : '开始日期'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.start}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, start: date }))}
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="flex-1">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.end ? format(dateRange.end, 'yyyy-MM-dd', { locale: zhCN }) : '结束日期'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.end}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, end: date }))}
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* 自定义文件名 */}
            <div className="space-y-2">
              <Label>文件名（可选）</Label>
              <Input
                placeholder="留空使用默认文件名"
                value={customFilename}
                onChange={(e) => setCustomFilename(e.target.value)}
              />
            </div>
          </div>
        )}

        <DialogFooter>
          {!isExporting && (
            <>
              <Button variant="outline" onClick={resetForm}>
                重置
              </Button>
              <Button 
                onClick={handleExport}
                disabled={selectedFields.length === 0}
              >
                <Download className="mr-2 h-4 w-4" />
                开始导出
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 预定义的导出模板
export const userExportTemplates: ExportTemplate[] = [
  {
    id: 'basic',
    name: '基础信息',
    description: '用户基本信息和状态',
    fields: ['id', 'nickname', 'phone', 'status', 'createdAt'],
    format: 'xlsx',
  },
  {
    id: 'detailed',
    name: '详细信息',
    description: '包含钱包、订单等完整信息',
    fields: ['id', 'nickname', 'phone', 'status', 'walletBalance', 'orderCount', 'avgRating', 'createdAt', 'lastLoginAt'],
    format: 'xlsx',
  },
  {
    id: 'financial',
    name: '财务报表',
    description: '专注于财务相关数据',
    fields: ['id', 'nickname', 'walletBalance', 'orderCount', 'totalSpent', 'createdAt'],
    format: 'csv',
  },
];

export const orderExportTemplates: ExportTemplate[] = [
  {
    id: 'basic',
    name: '基础订单',
    description: '订单基本信息',
    fields: ['orderNo', 'title', 'reward', 'status', 'createTime'],
    format: 'xlsx',
  },
  {
    id: 'detailed',
    name: '详细订单',
    description: '包含用户信息的完整订单数据',
    fields: ['orderNo', 'title', 'reward', 'status', 'orderType', 'customerId', 'accepterId', 'createTime', 'updateTime'],
    format: 'xlsx',
  },
  {
    id: 'financial',
    name: '财务订单',
    description: '专注于财务分析的订单数据',
    fields: ['orderNo', 'reward', 'status', 'orderType', 'createTime', 'customerId'],
    format: 'csv',
  },
];
