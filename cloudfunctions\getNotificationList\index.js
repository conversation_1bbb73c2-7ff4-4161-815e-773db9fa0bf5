// 获取通知列表云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { 
    page = 1, 
    pageSize = 20, 
    type = null, 
    status = null,
    unreadOnly = false
  } = event;

  try {
    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 构建查询条件
    let whereCondition = {
      targetUserId: user._id
    };

    // 按类型筛选
    if (type) {
      whereCondition.type = type;
    }

    // 按状态筛选
    if (status) {
      whereCondition.status = status;
    }

    // 只显示未读通知
    if (unreadOnly) {
      whereCondition.readTime = null;
    }

    // 查询通知列表
    const notificationResult = await db.collection('notifications')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();

    // 格式化通知数据
    const notifications = notificationResult.data.map(notification => ({
      ...notification,
      isRead: !!notification.readTime,
      timeText: formatTime(notification.createTime)
    }));

    // 获取未读通知数量
    const unreadCountResult = await db.collection('notifications')
      .where({
        targetUserId: user._id,
        readTime: null
      })
      .count();

    return {
      success: true,
      data: {
        list: notifications,
        hasMore: notifications.length === pageSize,
        unreadCount: unreadCountResult.total || 0
      }
    };
  } catch (error) {
    console.error('获取通知列表失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 格式化时间
function formatTime(date) {
  const now = new Date();
  const time = new Date(date);
  const diff = now - time;

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;

  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`;
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`;
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`;
  } else {
    return time.toLocaleDateString('zh-CN');
  }
}
