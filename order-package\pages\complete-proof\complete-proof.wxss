/* 任务完成证明页面样式 */
@import '../../../style/modern-components.wxss';

.page-with-custom-nav {
  padding-top: 88rpx;
}

.container {
  padding: 20rpx;
  min-height: calc(100vh - 88rpx);
}

/* 科技感背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  z-index: -1;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 50rpx 50rpx;
  animation: grid-move 20s linear infinite;
}

.tech-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: #00d4ff;
  border-radius: 50%;
  animation: particle-float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.particle:nth-child(3) {
  top: 80%;
  left: 40%;
  animation-delay: 4s;
}

/* 卡片样式 */
.cyber-card {
  position: relative;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.card-glow {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  border-radius: 16rpx;
  z-index: -1;
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #00d4ff;
  flex: 1;
}

.title-count, .title-optional {
  font-size: 24rpx;
  color: #94a3b8;
}

/* 订单摘要 */
.order-summary {
  space-y: 16rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.summary-label {
  font-size: 28rpx;
  color: #cbd5e1;
}

.summary-value {
  font-size: 28rpx;
  color: #f1f5f9;
  font-weight: 500;
}

.summary-value.price {
  color: #00d4ff;
  font-weight: 600;
}

/* 上传提示 */
.upload-tips {
  space-y: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #cbd5e1;
  line-height: 1.5;
  flex: 1;
}

/* 图片上传区域 */
.image-upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 2px solid rgba(0, 212, 255, 0.3);
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(239, 68, 68, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.add-image-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2px dashed rgba(0, 212, 255, 0.5);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 212, 255, 0.05);
}

.add-icon {
  font-size: 48rpx;
  color: #00d4ff;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #00d4ff;
}

/* 视频上传区域 */
.video-upload-area {
  width: 100%;
}

.video-item {
  width: 100%;
}

.uploaded-video {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  border: 2px solid rgba(0, 212, 255, 0.3);
}

.video-info {
  display: flex;
  justify-content: space-between;
  margin: 16rpx 0;
  padding: 12rpx 16rpx;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 8rpx;
}

.video-size, .video-duration {
  font-size: 24rpx;
  color: #cbd5e1;
}

.video-delete {
  text-align: center;
  padding: 16rpx;
}

.delete-text {
  font-size: 26rpx;
  color: #ef4444;
}

.add-video-btn {
  width: 100%;
  height: 200rpx;
  border: 2px dashed rgba(0, 212, 255, 0.5);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 212, 255, 0.05);
}

.add-desc {
  font-size: 20rpx;
  color: #94a3b8;
  margin-top: 8rpx;
}

/* 文字说明区域 */
.description-area {
  position: relative;
}

.description-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12rpx;
  color: #f1f5f9;
  font-size: 28rpx;
  line-height: 1.5;
}

.description-input::placeholder {
  color: #64748b;
}

.char-count {
  position: absolute;
  bottom: 12rpx;
  right: 16rpx;
  font-size: 20rpx;
  color: #64748b;
}

/* 上传进度 */
.progress-content {
  text-align: center;
}

.progress-text {
  font-size: 28rpx;
  color: #00d4ff;
  margin-bottom: 16rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff, #0ea5e9);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 提交区域 */
.submit-area {
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #00d4ff 0%, #0ea5e9 100%);
  border: none;
  border-radius: 44rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.submit-btn.disabled {
  background: rgba(100, 116, 139, 0.5);
  color: #64748b;
}

.submit-btn.disabled .btn-glow {
  display: none;
}

.btn-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: btn-shine 2s ease-in-out infinite;
}

.submit-tips {
  text-align: center;
}

.submit-tips .tip-text {
  font-size: 24rpx;
  color: #94a3b8;
}

/* 动画 */
@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50rpx, 50rpx); }
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.5; }
  50% { transform: translateY(-20rpx) rotate(180deg); opacity: 1; }
}

@keyframes btn-shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}
