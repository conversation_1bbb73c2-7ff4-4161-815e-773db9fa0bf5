// 获取用户信息云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();

  console.log('🔍 [获取用户信息] 开始获取用户信息');
  console.log('🔍 [获取用户信息] 用户openid:', wxContext.OPENID);

  try {
    // 获取用户基本信息
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const userInfo = userResult.data[0];
    console.log('✅ [获取用户信息] 从数据库获取的用户信息:', {
      _id: userInfo._id,
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      updateTime: userInfo.updateTime
    });

    // 获取用户统计信息
    const orderStats = await db.collection('orders').where({
      $or: [
        { customerId: userInfo._id },
        { accepterId: userInfo._id }
      ]
    }).count();

    const completedOrderStats = await db.collection('orders').where({
      $or: [
        { customerId: userInfo._id },
        { accepterId: userInfo._id }
      ],
      status: 'completed'
    }).count();

    // 计算平均评分
    const evaluationResult = await db.collection('orders').where({
      $or: [
        { customerId: userInfo._id },
        { accepterId: userInfo._id }
      ],
      status: 'completed',
      'evaluation.customerRating': { $exists: true }
    }).get();

    let averageRating = 5.0;
    if (evaluationResult.data.length > 0) {
      const totalRating = evaluationResult.data.reduce((sum, order) => {
        return sum + (order.evaluation.customerRating || 0);
      }, 0);
      averageRating = (totalRating / evaluationResult.data.length).toFixed(1);
    }

    // 用户信息获取完成

    const userStats = {
      totalOrders: orderStats.total,
      completedOrders: completedOrderStats.total,
      rating: averageRating
    };

    return {
      success: true,
      data: {
        userInfo,
        userStats
      }
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
