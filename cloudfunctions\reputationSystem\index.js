// 用户信誉系统云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 信誉等级配置
const REPUTATION_LEVELS = [
  { level: 1, name: '新手', minScore: 0, maxScore: 99, color: '#999999', icon: '🌱' },
  { level: 2, name: '熟练', minScore: 100, maxScore: 299, color: '#52c41a', icon: '🌿' },
  { level: 3, name: '专业', minScore: 300, maxScore: 599, color: '#1890ff', icon: '⭐' },
  { level: 4, name: '专家', minScore: 600, maxScore: 999, color: '#722ed1', icon: '💎' },
  { level: 5, name: '大师', minScore: 1000, maxScore: 1999, color: '#f5222d', icon: '👑' },
  { level: 6, name: '传奇', minScore: 2000, maxScore: 9999, color: '#fa8c16', icon: '🏆' }
];

// 信誉积分规则
const SCORE_RULES = {
  ORDER_COMPLETED: 10,        // 完成订单
  GOOD_EVALUATION: 20,        // 获得好评
  BAD_EVALUATION: -30,        // 获得差评
  ORDER_CANCELLED: -10,       // 取消订单
  FIRST_ORDER: 50,           // 首次完成订单
  CONSECUTIVE_ORDERS: 5,      // 连续完成订单奖励
  RESPONSE_FAST: 5,          // 快速响应奖励
  LATE_RESPONSE: -5,         // 响应延迟扣分
  PERFECT_RATING: 30,        // 获得满分评价
  REPORT_VIOLATION: -50      // 违规举报
};

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action, data } = event;
  
  console.log('🏆 [信誉系统] 执行操作:', action);
  
  try {
    switch (action) {
      case 'getUserReputation':
        return await getUserReputation(data, wxContext);
      case 'updateReputation':
        return await updateReputation(data, wxContext);
      case 'getReputationHistory':
        return await getReputationHistory(data, wxContext);
      case 'getLeaderboard':
        return await getLeaderboard(data);
      case 'calculateOrderScore':
        return await calculateOrderScore(data, wxContext);
      case 'getBadges':
        return await getBadges(data, wxContext);
      default:
        return {
          success: false,
          error: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('❌ [信誉系统] 操作失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 获取用户信誉信息
async function getUserReputation(data, wxContext) {
  const { userId } = data;
  const targetUserId = userId || wxContext.OPENID;
  
  console.log('🏆 [获取信誉] 用户ID:', targetUserId);
  
  try {
    // 获取用户信誉记录
    const reputationResult = await db.collection('userReputations')
      .where({ userId: targetUserId })
      .get();
    
    let reputation;
    if (reputationResult.data.length === 0) {
      // 创建初始信誉记录
      reputation = {
        userId: targetUserId,
        totalScore: 0,
        level: 1,
        completedOrders: 0,
        goodEvaluations: 0,
        badEvaluations: 0,
        averageRating: 0,
        badges: [],
        createTime: new Date(),
        updateTime: new Date()
      };
      
      await db.collection('userReputations').add({
        data: reputation
      });
    } else {
      reputation = reputationResult.data[0];
    }
    
    // 计算等级信息
    const levelInfo = calculateLevel(reputation.totalScore);
    reputation.levelInfo = levelInfo;
    
    // 计算进度
    const progress = calculateProgress(reputation.totalScore, levelInfo);
    reputation.progress = progress;
    
    console.log('✅ [获取信誉] 成功:', reputation.totalScore);
    
    return {
      success: true,
      data: reputation
    };
    
  } catch (error) {
    console.error('❌ [获取信誉] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 更新用户信誉
async function updateReputation(data, wxContext) {
  const { userId, scoreType, orderId, evaluationData } = data;
  const targetUserId = userId || wxContext.OPENID;
  
  console.log('🏆 [更新信誉] 用户ID:', targetUserId, '类型:', scoreType);
  
  try {
    // 计算积分变化
    let scoreChange = 0;
    let description = '';
    
    switch (scoreType) {
      case 'ORDER_COMPLETED':
        scoreChange = SCORE_RULES.ORDER_COMPLETED;
        description = '完成订单';
        break;
      case 'GOOD_EVALUATION':
        scoreChange = SCORE_RULES.GOOD_EVALUATION;
        if (evaluationData && evaluationData.rating === 5) {
          scoreChange = SCORE_RULES.PERFECT_RATING;
          description = '获得满分评价';
        } else {
          description = '获得好评';
        }
        break;
      case 'BAD_EVALUATION':
        scoreChange = SCORE_RULES.BAD_EVALUATION;
        description = '获得差评';
        break;
      case 'ORDER_CANCELLED':
        scoreChange = SCORE_RULES.ORDER_CANCELLED;
        description = '取消订单';
        break;
      case 'FIRST_ORDER':
        scoreChange = SCORE_RULES.FIRST_ORDER;
        description = '首次完成订单';
        break;
      default:
        scoreChange = 0;
        description = '未知操作';
    }
    
    if (scoreChange === 0) {
      return {
        success: true,
        message: '无积分变化'
      };
    }
    
    // 获取当前信誉记录
    const currentReputation = await getUserReputation({ userId: targetUserId }, wxContext);
    if (!currentReputation.success) {
      throw new Error('获取用户信誉失败');
    }
    
    const reputation = currentReputation.data;
    const newScore = Math.max(0, reputation.totalScore + scoreChange);
    const newLevel = calculateLevel(newScore);
    
    // 更新信誉记录
    const updateData = {
      totalScore: newScore,
      level: newLevel.level,
      updateTime: new Date()
    };
    
    // 根据操作类型更新统计数据
    if (scoreType === 'ORDER_COMPLETED') {
      updateData.completedOrders = (reputation.completedOrders || 0) + 1;
    } else if (scoreType === 'GOOD_EVALUATION') {
      updateData.goodEvaluations = (reputation.goodEvaluations || 0) + 1;
    } else if (scoreType === 'BAD_EVALUATION') {
      updateData.badEvaluations = (reputation.badEvaluations || 0) + 1;
    }
    
    // 重新计算平均评分
    if (scoreType.includes('EVALUATION')) {
      const totalEvaluations = (updateData.goodEvaluations || reputation.goodEvaluations || 0) + 
                              (updateData.badEvaluations || reputation.badEvaluations || 0);
      if (totalEvaluations > 0) {
        updateData.averageRating = ((updateData.goodEvaluations || reputation.goodEvaluations || 0) * 4.5 + 
                                   (updateData.badEvaluations || reputation.badEvaluations || 0) * 2.0) / totalEvaluations;
      }
    }
    
    await db.collection('userReputations')
      .where({ userId: targetUserId })
      .update({
        data: updateData
      });
    
    // 记录信誉变化历史
    await db.collection('reputationHistory').add({
      data: {
        userId: targetUserId,
        scoreChange,
        newScore,
        oldScore: reputation.totalScore,
        scoreType,
        description,
        orderId,
        createTime: new Date()
      }
    });
    
    // 检查是否升级
    const levelUp = newLevel.level > reputation.level;
    
    console.log('✅ [更新信誉] 成功:', {
      scoreChange,
      newScore,
      levelUp
    });
    
    return {
      success: true,
      data: {
        scoreChange,
        newScore,
        oldScore: reputation.totalScore,
        newLevel,
        levelUp,
        description
      }
    };
    
  } catch (error) {
    console.error('❌ [更新信誉] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 获取信誉历史记录
async function getReputationHistory(data, wxContext) {
  const { userId, page = 1, pageSize = 20 } = data;
  const targetUserId = userId || wxContext.OPENID;
  
  console.log('🏆 [信誉历史] 用户ID:', targetUserId);
  
  try {
    const result = await db.collection('reputationHistory')
      .where({ userId: targetUserId })
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    console.log('✅ [信誉历史] 成功，记录数:', result.data.length);
    
    return {
      success: true,
      data: {
        history: result.data,
        hasMore: result.data.length === pageSize
      }
    };
    
  } catch (error) {
    console.error('❌ [信誉历史] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 获取排行榜
async function getLeaderboard(data) {
  const { type = 'score', limit = 50 } = data;
  
  console.log('🏆 [排行榜] 类型:', type);
  
  try {
    let orderBy = 'totalScore';
    if (type === 'orders') {
      orderBy = 'completedOrders';
    } else if (type === 'rating') {
      orderBy = 'averageRating';
    }
    
    const result = await db.collection('userReputations')
      .orderBy(orderBy, 'desc')
      .limit(limit)
      .get();
    
    // 获取用户基本信息
    const userIds = result.data.map(item => item.userId);
    const usersResult = await db.collection('users')
      .where({
        _openid: _.in(userIds)
      })
      .field({
        _openid: true,
        nickName: true,
        avatarUrl: true
      })
      .get();
    
    // 合并用户信息
    const leaderboard = result.data.map((reputation, index) => {
      const userInfo = usersResult.data.find(user => user._openid === reputation.userId);
      return {
        rank: index + 1,
        userId: reputation.userId,
        nickName: userInfo?.nickName || '匿名用户',
        avatarUrl: userInfo?.avatarUrl || '',
        totalScore: reputation.totalScore,
        level: reputation.level,
        levelInfo: calculateLevel(reputation.totalScore),
        completedOrders: reputation.completedOrders || 0,
        averageRating: reputation.averageRating || 0
      };
    });
    
    console.log('✅ [排行榜] 成功，用户数:', leaderboard.length);
    
    return {
      success: true,
      data: leaderboard
    };
    
  } catch (error) {
    console.error('❌ [排行榜] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 计算等级信息
function calculateLevel(score) {
  for (let i = REPUTATION_LEVELS.length - 1; i >= 0; i--) {
    const level = REPUTATION_LEVELS[i];
    if (score >= level.minScore) {
      return level;
    }
  }
  return REPUTATION_LEVELS[0];
}

// 计算等级进度
function calculateProgress(score, levelInfo) {
  const currentLevelScore = score - levelInfo.minScore;
  const levelRange = levelInfo.maxScore - levelInfo.minScore;
  const progress = Math.min(100, Math.max(0, (currentLevelScore / levelRange) * 100));
  
  return {
    current: currentLevelScore,
    total: levelRange,
    percentage: Math.round(progress),
    nextLevelScore: levelInfo.maxScore + 1
  };
}

// 计算订单完成后的积分
async function calculateOrderScore(data, wxContext) {
  const { orderId, userId } = data;
  const targetUserId = userId || wxContext.OPENID;
  
  try {
    // 获取订单信息
    const orderResult = await db.collection('orders').doc(orderId).get();
    if (!orderResult.data) {
      throw new Error('订单不存在');
    }
    
    const order = orderResult.data;
    let totalScore = SCORE_RULES.ORDER_COMPLETED;
    let bonuses = [];
    
    // 检查是否为首次订单
    const userReputation = await getUserReputation({ userId: targetUserId }, wxContext);
    if (userReputation.success && userReputation.data.completedOrders === 0) {
      totalScore += SCORE_RULES.FIRST_ORDER;
      bonuses.push('首次完成订单奖励');
    }
    
    // 检查响应速度（如果有接单时间记录）
    if (order.acceptTime && order.createTime) {
      const responseTime = new Date(order.acceptTime) - new Date(order.createTime);
      if (responseTime < 5 * 60 * 1000) { // 5分钟内响应
        totalScore += SCORE_RULES.RESPONSE_FAST;
        bonuses.push('快速响应奖励');
      }
    }
    
    return {
      success: true,
      data: {
        baseScore: SCORE_RULES.ORDER_COMPLETED,
        bonusScore: totalScore - SCORE_RULES.ORDER_COMPLETED,
        totalScore,
        bonuses
      }
    };
    
  } catch (error) {
    console.error('❌ [计算订单积分] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 获取用户徽章
async function getBadges(data, wxContext) {
  const { userId } = data;
  const targetUserId = userId || wxContext.OPENID;
  
  try {
    const reputation = await getUserReputation({ userId: targetUserId }, wxContext);
    if (!reputation.success) {
      throw new Error('获取用户信誉失败');
    }
    
    const badges = [];
    const reputationData = reputation.data;
    
    // 根据成就条件判断徽章
    if (reputationData.completedOrders >= 1) {
      badges.push({ id: 'first_order', name: '初次体验', icon: '🎯', description: '完成首个订单' });
    }
    
    if (reputationData.completedOrders >= 10) {
      badges.push({ id: 'veteran', name: '经验丰富', icon: '🏅', description: '完成10个订单' });
    }
    
    if (reputationData.completedOrders >= 50) {
      badges.push({ id: 'expert', name: '专业达人', icon: '💎', description: '完成50个订单' });
    }
    
    if (reputationData.averageRating >= 4.5) {
      badges.push({ id: 'high_rating', name: '好评如潮', icon: '⭐', description: '平均评分4.5以上' });
    }
    
    if (reputationData.totalScore >= 1000) {
      badges.push({ id: 'high_score', name: '信誉卓越', icon: '👑', description: '信誉积分达到1000' });
    }
    
    return {
      success: true,
      data: badges
    };
    
  } catch (error) {
    console.error('❌ [获取徽章] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
