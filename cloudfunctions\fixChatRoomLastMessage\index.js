/**
 * 修复聊天室lastMessage字段的云函数
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  console.log('=== 修复聊天室lastMessage字段云函数开始 ===');
  
  try {
    // 获取当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    console.log('📋 [修复] 当前用户:', user._id);
    
    // 查询用户的聊天室
    const chatRoomsResult = await db.collection('chatRooms')
      .where({
        $or: [
          { customerId: user._id },
          { accepterId: user._id }
        ],
        status: 'active'
      })
      .get();
    
    console.log('📋 [修复] 找到聊天室数量:', chatRoomsResult.data.length);
    
    let fixedCount = 0;
    
    for (const chatRoom of chatRoomsResult.data) {
      console.log('📋 [修复] 处理聊天室:', chatRoom._id);
      
      // 获取最新消息
      try {
        const messagesResult = await db.collection('messages')
          .where({ chatRoomId: chatRoom._id })
          .orderBy('createTime', 'desc')
          .limit(1)
          .get();
        
        if (messagesResult.data.length > 0) {
          const latestMessage = messagesResult.data[0];

          
          // 更新聊天室的lastMessage字段
          const updateResult = await db.collection('chatRooms').doc(chatRoom._id).update({
            data: {
              lastMessage: {
                content: latestMessage.content,
                type: latestMessage.type,
                senderId: latestMessage.senderId,
                createTime: latestMessage.createTime
              },
              updateTime: new Date()
            }
          });
          
          fixedCount++;
        }
      } catch (error) {
        console.error('❌ [修复] 处理聊天室失败:', chatRoom._id, error);
      }
    }
    
    return {
      success: true,
      data: {
        totalChatRooms: chatRoomsResult.data.length,
        fixedCount: fixedCount
      },
      message: `成功修复 ${fixedCount} 个聊天室`
    };
    
  } catch (error) {
    console.error('❌ [修复] 云函数执行失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
