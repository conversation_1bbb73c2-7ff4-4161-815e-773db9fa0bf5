import requests

url = "https://comm.ams.game.qq.com/ide/"

payload = "iChartId=362217&iSubChartId=362217&sIdeToken=r1mKrM&eas_url=http%3A%2F%2Fwechatmini.qq.com%2Fdfm%2F1112340199%2Fpages%2Fdetails%2Fdetails%2F&iDocid=1050294968085403203"

headers = {
  'User-Agent': "Mozilla%2F5.0+%28Linux%3B+Android+14%3B+23013RK75C+Build%2FUKQ1.230804.001%3B+wv%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Version%2F4.0+Chrome%2F118.0.0.0+Mobile+Safari%2F537.36 QQ/9.1.75.26070 V1_AND_SQ_9.1.75_10068_YYB_D QQ/MiniApp",
  'Accept-Encoding': "gzip",
  'referer': "https://appservice.qq.com/1112340199/2.1.0/page-frame.html",
  'content-type': "application/x-www-form-urlencoded;",
  'Cookie': "openid=772EBBE91E70944959B540FA1FB93FBB; acctype=qqmini; appid=1112340199; ieg_ams_session_token=7aa8dbf5bce643c0f7c5b9c13e453300019d555a4814ac0de69e4717ceb2a960ea89; ieg_ams_token=df7d027a5ee0894ddf0162ec166f8c5d; ieg_ams_token_time=1753886737"
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)