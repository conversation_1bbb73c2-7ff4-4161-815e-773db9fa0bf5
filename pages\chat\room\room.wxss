/* 聊天室页面样式 - 科技主题版 */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0f1419 !important; /* 强制深色背景，避免白色闪烁 */
  background-color: #0f1419 !important;
}

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 0 10rpx var(--primary-color);
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.particle:nth-child(3) {
  top: 80%;
  left: 40%;
  animation-delay: 4s;
}

@keyframes dataFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-100rpx, -100rpx); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) scale(1); opacity: 0.7; }
  50% { transform: translateY(-20rpx) scale(1.2); opacity: 1; }
}

.container {
  height: calc(100vh - 160rpx); /* 增加高度预留，适应多行输入框 */
  background: transparent;
  display: flex;
  flex-direction: column;
}

/* 确保自定义导航栏页面有正确的顶部间距 - 减少间距 */
.container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 10rpx); /* 减少顶部间距 */
}

/* 消息列表 */
.message-list {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 0; /* 移除底部间距，使用底部占位元素代替 */
  overflow-y: auto;
  box-sizing: border-box;
}

/* 底部占位元素 */
.bottom-anchor {
  height: 300rpx; /* 增加高度，确保有足够空间 */
  width: 100%;
  background: transparent;
  flex-shrink: 0; /* 防止被压缩 */
  min-height: 300rpx; /* 最小高度保证 */
}

/* 当表情包面板打开时，增加底部占位元素高度 */
.emoji-panel-open .bottom-anchor {
  height: 800rpx;
}

/* 当附件面板打开时，增加底部占位元素高度 */
.attachment-panel-open .bottom-anchor {
  height: 560rpx;
}

/* 历史消息加载 */
.load-more {
  text-align: center;
  padding: 20rpx;
}

.load-more-text {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 消息项 */
.message-item {
  margin-bottom: 30rpx;
}

.message-content {
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
}

/* 当有昵称时，调整对齐方式 */
.message-content:has(.avatar-container) {
  align-items: flex-start;
}

/* 为了兼容性，使用类选择器 */
.message-content.with-nickname {
  align-items: flex-start;
}

/* 确保消息气泡与头像顶部对齐 */
.message-content.with-nickname .message-bubble {
  margin-top: 30rpx; /* 与昵称高度对齐 */
  align-self: flex-start;
}

/* 对方消息 */
.other-message {
  justify-content: flex-start;
}

.other-message .message-bubble {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-radius: 20rpx 20rpx 20rpx 8rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(0, 212, 255, 0.2);
}

/* 自己的消息 */
.self-message {
  justify-content: flex-end;
}

.self-message .message-bubble {
  background: var(--primary-gradient);
  border-radius: 20rpx 20rpx 8rpx 20rpx;
  color: var(--text-primary);
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
}

/* 头像容器 */
.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  justify-content: flex-start; /* 让头像在顶部对齐 */
}

/* 昵称 */
.nickname {
  font-size: 22rpx;
  color: var(--text-tertiary);
  opacity: 0.8;
  text-align: center; /* 居中对齐 */
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transform: translateX(-10rpx); /* 向左偏移更多一点 */
}

/* 头像 */
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: 2rpx solid var(--primary-color);
}

.avatar.default-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
}

.avatar .avatar-text {
  font-size: 32rpx;
  color: var(--primary-color);
}

/* 消息气泡 */
.message-bubble {
  max-width: 500rpx;
  padding: 24rpx 30rpx;
  position: relative;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 撤回消息样式 */
.message-bubble.recalled {
  background: rgba(128, 128, 128, 0.3) !important;
  border: 1rpx solid rgba(128, 128, 128, 0.5);
  opacity: 0.7;
}

.recalled-text {
  color: rgba(255, 255, 255, 0.6) !important;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.recalled-icon {
  font-size: 24rpx;
  opacity: 0.8;
}

.message-time {
  font-size: 20rpx;
  opacity: 0.7;
  margin-top: 8rpx;
  text-align: right;
}

.other-message .message-time {
  text-align: left;
  color: var(--text-tertiary);
}

.self-message .message-time {
  color: rgba(255, 255, 255, 0.7);
}

/* 空状态 */
.empty-messages {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.3);
}



/* 输入区域 */
.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  border-top: 2rpx solid var(--border-color);
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 1000;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.3);
  min-height: 112rpx; /* 设置最小高度，适应多行输入 */
  max-height: 320rpx; /* 设置最大高度限制 */
}

.input-container {
  display: flex;
  align-items: flex-end; /* 改为底部对齐，适应多行输入框 */
  gap: 12rpx;
  position: relative;
  min-height: 72rpx; /* 设置最小高度 */
}

.message-input {
  flex: 1;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border: 1rpx solid var(--border-color);
  border-radius: 24rpx; /* 减少圆角，适应多行显示 */
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  outline: none;
  transition: all 0.3s ease;
  margin: 0 8rpx;
  min-height: 72rpx; /* 设置最小高度 */
  max-height: 240rpx; /* 设置最大高度，约6行 */
  line-height: 1.4; /* 设置行高 */
  word-wrap: break-word; /* 自动换行 */
  word-break: break-all; /* 强制换行 */
  resize: none; /* 禁用手动调整大小 */
  box-sizing: border-box;
}

.message-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
}

.message-input::placeholder {
  color: var(--text-tertiary);
}

/* ==================== 输入区域按钮样式 ==================== */

/* 语音按钮 */
.voice-button {
  width: 72rpx;
  height: 72rpx;
  background: var(--bg-glass);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.voice-button:active {
  background: var(--primary-light);
  transform: scale(0.95);
}

.voice-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

/* 表情包按钮 */
.emoji-button {
  width: 72rpx;
  height: 72rpx;
  background: var(--bg-glass);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-bottom: 0; /* 确保与输入框底部对齐 */
}

.emoji-button:active {
  background: var(--primary-light);
  transform: scale(0.95);
}

.emoji-icon {
  font-size: 36rpx;
  color: var(--text-primary);
}

/* 动作按钮容器 */
.action-button-container {
  position: relative;
  width: 72rpx;
  height: 72rpx;
  flex-shrink: 0;
  margin-bottom: 0; /* 确保与输入框底部对齐 */
}

/* 附件按钮 */
.attachment-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 72rpx;
  height: 72rpx;
  background: var(--bg-glass);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

.attachment-button.hidden {
  opacity: 0;
  transform: scale(0.8) rotate(45deg);
  pointer-events: none;
}

.attachment-button.visible {
  opacity: 1;
  transform: scale(1) rotate(0deg);
  pointer-events: auto;
}

.attachment-button:active {
  background: var(--primary-light);
  transform: scale(0.95) rotate(0deg);
}

.attachment-icon {
  font-size: 36rpx;
  color: var(--text-primary);
  font-weight: 300;
}

/* 发送按钮 */
.send-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 72rpx;
  height: 72rpx;
  background: var(--primary-gradient);
  color: var(--text-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  border: 1rpx solid var(--primary-color);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.send-button.visible {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

.send-button.hidden {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.send-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(0, 212, 255, 0.2);
}

/* ==================== 附件面板样式 ==================== */

.attachment-panel {
  background: var(--bg-secondary);
  border-top: 1rpx solid var(--border-color);
  padding: 30rpx 20rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}

.attachment-panel.show {
  max-height: 240rpx;
  opacity: 1;
  transform: translateY(0);
}

.attachment-panel.hide {
  max-height: 0;
  opacity: 0;
  transform: translateY(-20rpx);
  padding: 0 20rpx;
}

.attachment-options {
  display: flex;
  justify-content: center;
  gap: 80rpx;
  flex-wrap: wrap;
}

.attachment-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 120rpx;
}

.attachment-option:active {
  background: var(--primary-light);
  transform: scale(0.95);
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
}

.option-icon-text {
  font-size: 40rpx;
  color: var(--text-primary);
}

.option-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  text-align: center;
}

/* ==================== 表情包面板样式 ==================== */

.emoji-panel {
  background: var(--bg-secondary);
  border-top: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
  overflow: hidden;
}

.emoji-panel.show {
  height: 500rpx;
  opacity: 1;
  transform: translateY(0);
}

.emoji-panel.hide {
  height: 0;
  opacity: 0;
  transform: translateY(-20rpx);
}

.emoji-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx 10rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  background: var(--bg-tertiary);
}

.emoji-title {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
}

.emoji-close-hint {
  opacity: 0.7;
}

.close-hint-text {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

.emoji-scroll {
  height: calc(100% - 80rpx);
  padding: 20rpx;
}

.emoji-categories {
  padding-bottom: 20rpx;
}

.emoji-category {
  margin-bottom: 40rpx;
}

.category-title {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  font-weight: 600;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 20rpx;
  padding: 0 10rpx;
}

.emoji-item {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  cursor: pointer;
}

.emoji-item:active {
  background: var(--primary-light);
  transform: scale(1.2);
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
}

/* 顶部工具栏 */
.toolbar {
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  border-bottom: 2rpx solid var(--border-color);
  padding: 20rpx;
  display: flex;
  justify-content: center;
  flex-shrink: 0; /* 防止被压缩 */
}

.toolbar-button {
  background: var(--bg-glass);
  color: var(--primary-color);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border: 2rpx solid var(--primary-color);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.2);
}

.toolbar-button:active {
  background: var(--primary-light);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 212, 255, 0.3);
}

/* 响应式调整 */
@media (max-width: 375px) {
  .message-bubble {
    max-width: 400rpx;
  }

  .message-text {
    font-size: 28rpx;
  }
}

/* 动画效果 */
.message-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 科技感装饰效果 */
.message-bubble::before {
  content: '';
  position: absolute;
  top: -1rpx;
  left: -1rpx;
  right: -1rpx;
  bottom: -1rpx;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.self-message .message-bubble::before {
  opacity: 1;
}
