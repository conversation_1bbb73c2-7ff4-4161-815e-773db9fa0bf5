// 自定义 tabBar - 使用云存储图标
const cloudStorage = require('../utils/cloudStorage.js');

Component({
  data: {
    selected: 0,
    color: "#64748b",
    selectedColor: "#00d4ff",
    useEmoji: true,
    chatBadgeCount: 0, // 聊天未读消息数量
    list: [
      {
        pagePath: "pages/index/index",
        text: "首页",
        iconUrl: "",
        selectedIconUrl: "",
        emoji: "🏠"
      },

      {
        pagePath: "pages/order/list/list",
        text: "我的订单",
        iconUrl: "",
        selectedIconUrl: "",
        emoji: "📋"
      },
      {
        pagePath: "pages/chat/list/list",
        text: "消息",
        iconUrl: "",
        selectedIconUrl: "",
        emoji: "💬",
        badge: true // 支持徽章显示
      },
      {
        pagePath: "pages/user/profile/profile",
        text: "我的",
        iconUrl: "",
        selectedIconUrl: "",
        emoji: "👤"
      }
    ]
  },
  
  attached() {
    this.initTabBarIcons();
    this.updateSelected();
    this.initUnreadMessageListener();
  },

  detached() {
    this.cleanupUnreadMessageListener();
  },

  methods: {
    // 初始化 tabBar 图标
    async initTabBarIcons() {
      try {
        // 临时测试：直接使用您云存储中的图标URL
        const testIcons = {
          home: {
            iconPath: 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/icons/tab/home.png',
            selectedIconPath: 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/icons/tab/home-active.png'
          },
          order: {
            iconPath: 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/icons/tab/order.png',
            selectedIconPath: 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/icons/tab/order-active.png'
          },
          chat: {
            iconPath: 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/icons/tab/chat.png',
            selectedIconPath: 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/icons/tab/chat-active.png'
          },
          user: {
            iconPath: 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/icons/tab/user.png',
            selectedIconPath: 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/icons/tab/user-active.png'
          }
        };

        // 更新图标URL
        const iconKeys = ['home', 'order', 'chat', 'user'];
        const updatedList = this.data.list.map((item, index) => ({
          ...item,
          iconUrl: testIcons[iconKeys[index]]?.iconPath || '',
          selectedIconUrl: testIcons[iconKeys[index]]?.selectedIconPath || ''
        }));

        this.setData({
          list: updatedList,
          useEmoji: false
        });

      } catch (error) {
        console.error('初始化 tabBar 图标失败:', error);
        // 如果云存储图标加载失败，使用 emoji 作为备选方案
        this.setData({ useEmoji: true });
      }
    },



    // 更新选中状态
    updateSelected() {
      const pages = getCurrentPages();
      if (pages.length === 0) return;

      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;

      console.log('🔄 更新 tabBar 选中状态:', currentRoute);

      const selected = this.data.list.findIndex(item => item.pagePath === currentRoute);
      const newSelected = selected >= 0 ? selected : 0;

      console.log('📍 当前页面索引:', newSelected, '页面路径:', currentRoute);
      console.log('📋 tabBar 页面列表:', this.data.list.map(item => item.pagePath));

      this.setData({
        selected: newSelected
      });
    },

    // 切换标签
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = '/' + data.path;

      console.log('🔄 切换到页面:', url);

      // 对于订单页面，我们需要先跳转到页面，然后在页面内设置模式
      wx.switchTab({
        url,
        success: () => {
          // 如果是订单页面，通知页面切换到个人订单模式
          if (data.path === 'pages/order/list/list') {
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            if (currentPage && currentPage.setPersonalMode) {
              currentPage.setPersonalMode();
            }
          }
        }
      });

      this.setData({
        selected: data.index
      });
    },

    // 更新聊天徽章数量
    updateChatBadge(count) {
      this.setData({
        chatBadgeCount: count || 0
      });
    },

    // 初始化未读消息监听
    initUnreadMessageListener() {
      const app = getApp();

      // 监听未读消息状态更新事件
      this.unreadMessageUpdateHandler = (data) => {
        console.log('📱 [TabBar] 收到未读消息更新事件:', data);
        this.updateChatBadge(data.unreadData.total);
      };

      app.$on('unreadMessageUpdate', this.unreadMessageUpdateHandler);

      // 初始化时更新一次未读状态
      const unreadData = app.globalData.unreadMessages;
      this.updateChatBadge(unreadData.total);

      console.log('📱 [TabBar] 未读消息监听初始化完成，当前未读数:', unreadData.total);
    },

    // 清理未读消息监听
    cleanupUnreadMessageListener() {
      const app = getApp();

      if (this.unreadMessageUpdateHandler) {
        app.$off('unreadMessageUpdate', this.unreadMessageUpdateHandler);
        this.unreadMessageUpdateHandler = null;
      }

      console.log('📱 [TabBar] 未读消息监听清理完成');
    }
  }
});
