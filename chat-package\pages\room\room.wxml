<!--聊天室页面 - 科技主题版-->
<navigation-bar title="聊天" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 返回按钮 - 与订单详情页面保持一致 -->
<back-button position="top-left" size="normal" back-type="auto"></back-button>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3]}}" wx:key="*this"></view>
  </view>
</view>

<view class="container page-with-custom-nav">
  <!-- 顶部工具栏 -->
  <view class="toolbar" wx:if="{{orderNo}}">
    <button class="toolbar-button" bindtap="viewOrderDetail">
      查看订单
    </button>
  </view>

  <!-- 消息列表 -->
  <scroll-view
    class="message-list {{showEmojiPanel ? 'emoji-panel-open' : ''}} {{showAttachmentPanel ? 'attachment-panel-open' : ''}}"
    scroll-y="{{true}}"
    scroll-into-view="{{scrollToView}}"
    scroll-top="{{scrollTop}}"
    scroll-with-animation="{{false}}"
    enable-back-to-top="{{false}}"
    enhanced="{{true}}"
    show-scrollbar="{{false}}"
    bindscroll="onScroll"
    bindtap="onMessageAreaTap"
  >
    <!-- 历史消息加载提示 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-more-text">{{loading ? '加载中...' : '下拉加载更多'}}</text>
    </view>

    <!-- 消息项 -->
    <view
      class="message-item {{item.isSelf ? 'self' : 'other'}}"
      wx:for="{{messageList}}"
      wx:key="_id"
      id="msg-{{index}}"
    >
      <!-- 对方消息 -->
      <view class="message-content other-message with-nickname" wx:if="{{!item.isSelf}}">
        <view class="avatar-container">
          <view class="nickname">{{item.senderInfo.nickName || '未知用户'}}</view>
          <image wx:if="{{item.senderInfo.avatarUrl}}" class="avatar" src="{{item.senderInfo.avatarUrl}}" mode="aspectFill" />
          <view wx:else class="avatar default-avatar">
            <text class="avatar-text">👤</text>
          </view>
        </view>
        <view class="message-bubble {{item.isRecalled ? 'recalled' : ''}} {{item.type === 'voice' ? 'voice-message-bubble' : ''}}">
          <!-- 未撤回的消息 -->
          <view wx:if="{{!item.isRecalled}}">
            <!-- 文本消息 -->
            <view class="message-text" wx:if="{{item.type === 'text'}}">{{item.content}}</view>

            <!-- 图片消息 -->
            <view class="message-image-container" wx:if="{{item.type === 'image'}}">
              <image
                class="message-image"
                src="{{item.content}}"
                mode="aspectFit"
                bindtap="onImageTap"
                data-src="{{item.content}}"
                lazy-load="{{true}}"
              />
            </view>

            <!-- 语音消息 -->
            <view class="message-voice-container" wx:if="{{item.type === 'voice'}}">
              <view
                class="voice-bubble {{item.isPlaying ? 'playing' : ''}}"
                bindtap="onVoicePlay"
                data-src="{{item.content}}"
                data-message-id="{{item._id}}"
              >
                <text class="voice-icon">{{item.isPlaying ? '⏸️' : '▶️'}}</text>
                <text class="voice-duration">{{item.duration ? (item.duration + '"') : '0"'}}</text>
                <view class="voice-waves" wx:if="{{item.isPlaying}}">
                  <view class="wave" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
                </view>
                <view class="voice-status" wx:if="{{!item.isPlaying}}">
                  <text class="voice-status-text">点击播放</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 撤回的消息 -->
          <view class="message-text recalled-text" wx:else>
            <text class="recalled-icon">🚫</text>
            {{item.content}}
          </view>

          <view class="message-time">{{item.formattedTime}}</view>
        </view>
      </view>

      <!-- 自己的消息 -->
      <view class="message-content self-message with-nickname" wx:if="{{item.isSelf}}">
        <view
          class="message-bubble {{item.isRecalled ? 'recalled' : ''}} {{item.type === 'voice' ? 'voice-message-bubble' : ''}}"
          bindlongpress="onMessageLongPress"
          data-message-id="{{item._id}}"
          data-message-index="{{index}}"
          data-is-self="{{item.isSelf}}"
          data-is-recalled="{{item.isRecalled}}"
          data-create-time="{{item.createTime}}"
        >
          <!-- 未撤回的消息 -->
          <view wx:if="{{!item.isRecalled}}">
            <!-- 文本消息 -->
            <view class="message-text" wx:if="{{item.type === 'text'}}">{{item.content}}</view>

            <!-- 图片消息 -->
            <view class="message-image-container" wx:if="{{item.type === 'image'}}">
              <image
                class="message-image"
                src="{{item.content}}"
                mode="aspectFit"
                bindtap="onImageTap"
                data-src="{{item.content}}"
                lazy-load="{{true}}"
              />
            </view>

            <!-- 语音消息 -->
            <view class="message-voice-container" wx:if="{{item.type === 'voice'}}">
              <view
                class="voice-bubble {{item.isPlaying ? 'playing' : ''}}"
                bindtap="onVoicePlay"
                data-src="{{item.content}}"
                data-message-id="{{item._id}}"
              >
                <text class="voice-icon">{{item.isPlaying ? '⏸️' : '▶️'}}</text>
                <text class="voice-duration">{{item.duration ? (item.duration + '"') : '0"'}}</text>
                <view class="voice-waves" wx:if="{{item.isPlaying}}">
                  <view class="wave" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
                </view>
                <view class="voice-status" wx:if="{{!item.isPlaying}}">
                  <text class="voice-status-text">点击播放</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 撤回的消息 -->
          <view class="message-text recalled-text" wx:else>
            <text class="recalled-icon">🚫</text>
            {{item.content}}
          </view>
          <view class="message-time">{{item.formattedTime}}</view>
        </view>
        <view class="avatar-container">
          <view class="nickname">{{userInfo.nickName || '我'}}</view>
          <image wx:if="{{userInfo.avatarUrl}}" class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" />
          <view wx:else class="avatar default-avatar">
            <text class="avatar-text">👤</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-messages" wx:if="{{messageList.length === 0 && !loading}}">
      <text class="empty-text">开始聊天吧~</text>
    </view>

    <!-- 底部占位元素，用于滚动定位 -->
    <view id="bottom-anchor" class="bottom-anchor"></view>
  </scroll-view>


</view>

<!-- 输入区域 - 固定在底部 -->
<view class="input-area">
  <view class="input-container">
    <!-- 语音/键盘切换按钮 -->
    <view class="voice-toggle-button" bindtap="onVoiceToggle">
      <text class="voice-toggle-icon">{{isVoiceMode ? '⌨️' : '🎤'}}</text>
    </view>

    <!-- 文本输入模式 -->
    <view class="text-input-container" wx:if="{{!isVoiceMode}}">
      <textarea
        class="message-input"
        placeholder="输入消息..."
        value="{{inputText}}"
        bindinput="onInputChange"
        bindfocus="onInputFocus"
        bindblur="preventInputBlur"
        bindlinechange="onLineChange"
        auto-height="{{true}}"
        show-confirm-bar="{{false}}"
        cursor-spacing="20"
        max-height="{{240}}"
        focus="{{inputFocus}}"
        hold-keyboard="{{true}}"
        confirm-hold="{{true}}"
        adjust-position="{{true}}"
      />
    </view>

    <!-- 语音输入模式 -->
    <view class="voice-input-container" wx:else>
      <view
        class="voice-input-button {{isRecording ? 'recording' : ''}}"
        bindtouchstart="onVoiceTouchStart"
        bindtouchmove="onVoiceTouchMove"
        bindtouchend="onVoiceTouchEnd"
        bindtouchcancel="onVoiceTouchCancel"
      >
        <text class="voice-input-text">{{isRecording ? recordingTip : '按住说话'}}</text>
      </view>
    </view>

    <!-- 表情包按钮 -->
    <view class="emoji-button" bindtap="onEmojiButtonTap" wx:if="{{!isVoiceMode}}">
      <text class="emoji-icon">😊</text>
    </view>

    <!-- 附件/发送按钮 -->
    <view class="action-button-container">
      <!-- 文本模式：附件按钮（无文字时）或发送按钮（有文字时） -->
      <view wx:if="{{!isVoiceMode}}">
        <!-- 附件按钮（无文字时显示） -->
        <view
          class="attachment-button {{canSend ? 'hidden' : 'visible'}}"
          bindtap="onAttachmentButtonTap"
        >
          <text class="attachment-icon">+</text>
        </view>

        <!-- 发送按钮（有文字时显示） -->
        <view
          class="send-button {{canSend ? 'visible' : 'hidden'}}"
          catchtap="{{canSend ? 'sendMessage' : ''}}"
          data-disabled="{{!canSend}}"
          data-keep-focus="true"
        >
          发送
        </view>
      </view>

      <!-- 语音模式：始终显示附件按钮 -->
      <view
        class="attachment-button visible"
        bindtap="onAttachmentButtonTap"
        wx:if="{{isVoiceMode}}"
      >
        <text class="attachment-icon">+</text>
      </view>
    </view>
  </view>

  <!-- 附件选择面板 -->
  <view class="attachment-panel {{showAttachmentPanel ? 'show' : 'hide'}}" wx:if="{{showAttachmentPanel}}">
    <view class="attachment-options">
      <view class="attachment-option" bindtap="onSelectImage">
        <view class="option-icon">
          <text class="option-icon-text">🖼️</text>
        </view>
        <text class="option-text">图片</text>
      </view>
      <view class="attachment-option" bindtap="onSelectCamera">
        <view class="option-icon">
          <text class="option-icon-text">📷</text>
        </view>
        <text class="option-text">拍照</text>
      </view>
    </view>
  </view>

  <!-- 表情包面板 -->
  <view class="emoji-panel {{showEmojiPanel ? 'show' : 'hide'}}" wx:if="{{showEmojiPanel}}">
    <!-- 面板头部 -->
    <view class="emoji-header">
      <text class="emoji-title">选择表情</text>
      <view class="emoji-close-hint">
        <text class="close-hint-text">点击😊或消息区域关闭</text>
      </view>
    </view>

    <scroll-view class="emoji-scroll" scroll-y="{{true}}">
      <view class="emoji-categories">
        <view
          class="emoji-category"
          wx:for="{{emojiList}}"
          wx:key="category"
        >
          <view class="category-title">{{item.category}}</view>
          <view class="emoji-grid">
            <view
              class="emoji-item"
              wx:for="{{item.emojis}}"
              wx:for-item="emoji"
              wx:key="*this"
              data-emoji="{{emoji}}"
              bindtap="onSelectEmoji"
            >
              {{emoji}}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 语音录制状态提示 -->
  <view class="voice-recording-overlay" wx:if="{{isRecording}}">
    <view class="voice-recording-container {{isCancelMode ? 'cancel-mode' : ''}}">
      <view class="voice-recording-visual">
        <view class="voice-recording-circle {{isCancelMode ? 'cancel-circle' : ''}}">
          <text class="voice-recording-icon">{{isCancelMode ? '🚫' : '🎤'}}</text>
        </view>
        <view class="voice-recording-waves" wx:if="{{!isCancelMode}}">
          <view class="recording-wave" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
        </view>
      </view>

      <view class="voice-recording-time">
        <text class="recording-time-text">{{recordTimeDisplay}}</text>
      </view>

      <view class="voice-recording-hint">
        <text class="recording-hint-text" wx:if="{{!isCancelMode}}">{{recordingTip}}</text>
        <text class="recording-hint-text cancel" wx:else>{{recordingTip}}</text>
      </view>

      <!-- 取消模式的额外提示 -->
      <view class="cancel-hint" wx:if="{{isCancelMode}}">
        <text class="cancel-hint-text">上滑取消录音</text>
      </view>
    </view>
  </view>
</view>
