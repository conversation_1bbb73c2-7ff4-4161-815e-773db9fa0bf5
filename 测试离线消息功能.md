# 离线消息功能测试指南

## 测试准备

### 1. 部署云函数
确保 `chatMessage` 云函数已经部署到云端：
```bash
# 在项目根目录执行
cd cloudfunctions/chatMessage
npm install
# 然后在微信开发者工具中右键云函数文件夹选择"上传并部署"
```

### 2. 准备测试环境
- 两个测试账号（用户A和用户B）
- 确保两个账号都已经在系统中注册
- 创建一个聊天室（可以通过订单系统创建）

## 测试步骤

### 测试场景1：基本离线消息检测

1. **用户A登录小程序**
   - 进入聊天列表页面
   - 确认当前未读消息数为0

2. **用户B登录小程序**
   - 进入与用户A的聊天室
   - 发送一条测试消息："测试离线消息1"

3. **用户A离开小程序**
   - 按Home键或切换到其他应用
   - 等待几秒钟确保小程序进入后台

4. **用户B继续发送消息**
   - 发送第二条消息："测试离线消息2"
   - 发送第三条消息："测试离线消息3"

5. **用户A重新进入小程序**
   - 重新打开小程序
   - 观察是否显示新消息提示
   - 检查聊天列表是否显示未读消息数
   - 检查TabBar是否显示未读徽章

### 测试场景2：多聊天室离线消息

1. **准备多个聊天室**
   - 用户A与用户B的聊天室
   - 用户A与用户C的聊天室

2. **用户A离开小程序**

3. **多个用户发送消息**
   - 用户B在聊天室1发送消息
   - 用户C在聊天室2发送消息

4. **用户A重新进入小程序**
   - 检查是否正确显示多个聊天室的未读消息

### 测试场景3：边界情况测试

1. **无离线消息情况**
   - 用户A离开小程序
   - 没有人发送消息
   - 用户A重新进入小程序
   - 确认没有误报新消息

2. **快速进出小程序**
   - 用户A快速多次进出小程序
   - 确认功能正常，无异常错误

## 预期结果

### 正常情况下应该看到：

1. **重新进入小程序时**：
   - 控制台输出：`📱 [离线消息检查] 开始检查离线期间的新消息`
   - 如果有新消息，显示Toast提示：`收到 X 条新消息`

2. **聊天列表页面**：
   - 控制台输出：`📱 [聊天列表] 收到离线消息检测事件`
   - 聊天列表自动刷新
   - 未读消息数正确显示

3. **TabBar**：
   - 聊天Tab显示红色徽章
   - 徽章数字正确

## 调试信息

### 关键日志输出

在控制台中查找以下关键日志：

```javascript
// app.js 中的日志
📱 [离线消息检查] 开始检查离线期间的新消息
📱 [离线消息检查] 发现离线消息: X 条
📱 [离线消息检查] 已更新未读状态，触发页面刷新事件

// 聊天列表页面的日志
📱 [聊天列表] 收到离线消息检测事件
📱 [聊天列表] 更新所有聊天室未读状态完成

// 云函数的日志（在云开发控制台查看）
📱 [离线消息检查] 开始检查用户离线消息
📱 [离线消息检查] 用户参与的聊天室: X 个
📱 [离线消息检查] 发现离线消息: X 条
```

## 故障排除

### 如果功能不工作，检查：

1. **云函数是否正确部署**
   - 在云开发控制台查看云函数日志
   - 确认 `checkOfflineMessages` action 被正确调用

2. **时间同步问题**
   - 检查客户端和服务器时间是否一致
   - 确认 `lastHideTime` 被正确记录

3. **权限问题**
   - 确认用户有权限访问相关聊天室
   - 检查用户ID是否正确

4. **事件监听问题**
   - 确认聊天列表页面正确监听了 `offlineMessagesDetected` 事件
   - 检查事件处理函数是否被正确调用

### 常见问题

1. **Q: 重新进入小程序时没有检测到离线消息**
   A: 检查 `globalData.lastHideTime` 是否被正确设置，以及云函数是否正常工作

2. **Q: 未读消息数不正确**
   A: 检查未读消息管理逻辑，确认没有重复计算

3. **Q: TabBar徽章不显示**
   A: 确认TabBar组件正确监听了 `unreadMessageUpdate` 事件

## 性能监控

关注以下性能指标：
- 离线消息检查的响应时间
- 云函数的执行时间
- 聊天列表刷新的速度
- 内存使用情况
