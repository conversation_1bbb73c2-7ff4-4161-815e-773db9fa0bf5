<!--聊天列表页面 - 科技主题版-->
<navigation-bar title="消息" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3]}}" wx:key="*this"></view>
  </view>
</view>

<view class="container page-with-custom-nav">

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && chatList.length === 0}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 聊天列表 -->
  <scroll-view 
    class="chat-list" 
    scroll-y="{{true}}"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
    bindscrolltolower="onReachBottom"
    wx:if="{{chatList.length > 0}}"
  >
    <view
      class="chat-item avoid-capsule"
      wx:for="{{chatList}}"
      wx:key="_id"
      data-id="{{item._id}}"
      data-orderno="{{item.orderNo}}"
      bindtap="enterChatRoom"
      bindlongpress="onChatItemLongPress"
    >
      <!-- 头像 -->
      <view class="avatar-container">
        <unread-badge count="{{item.unreadCount || 0}}" type="count" max="99" size="normal">
          <image wx:if="{{item.otherUser.avatarUrl}}" class="avatar" src="{{item.otherUser.avatarUrl}}" mode="aspectFill" />
          <view wx:else class="avatar default-avatar">
            <text class="avatar-text">👤</text>
          </view>
        </unread-badge>
        <view class="role-badge role-{{item.otherUser.role}}">
          {{item.otherUser.role === 'customer' ? '客' : '接'}}
        </view>
      </view>

      <!-- 聊天信息 -->
      <view class="chat-info">
        <!-- 第一行：用户名和时间 -->
        <view class="info-row">
          <text class="user-name">{{item.otherUser.nickName}}</text>
          <text class="time">{{item.timeDisplay}}</text>
        </view>

        <!-- 第二行：订单标题 -->
        <view class="info-row">
          <text class="order-title">{{item.orderInfo.title || '订单'}}</text>
          <text class="order-reward" wx:if="{{item.orderInfo.reward}}">
            ¥{{item.orderInfo.reward}}
          </text>
        </view>

        <!-- 第三行：最后消息 -->
        <view class="info-row">
          <text class="last-message">{{item.lastMessageDisplay}}</text>
        </view>
      </view>

      <!-- 右侧箭头 -->
      <view class="arrow">
        <text class="arrow-icon">></text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && !loading}}">
      <text class="load-more-text">上拉加载更多</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && chatList.length > 0}}">
      <text class="no-more-text">没有更多聊天记录了</text>
    </view>
  </scroll-view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{chatList.length === 0 && !loading}}">
    <text class="empty-icon-text">💬</text>
    <text class="empty-title">暂无聊天记录</text>
    <text class="empty-desc">接单或发单后会显示聊天记录</text>
    <button class="empty-action" bindtap="showEmptyActions">
      查看订单大厅
    </button>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
