/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.login-container.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 20rpx);
  box-sizing: border-box;
  align-items: center;
  padding: 60rpx 40rpx;
}

/* 登录头部 */
.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 100rpx;
}

.logo-container {
  margin-bottom: 40rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.app-name {
  font-size: 48rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20rpx;
}

.app-desc {
  font-size: 28rpx;
  color: #ccc;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.welcome-text {
  font-size: 24rpx;
  color: #888;
  text-align: center;
  font-style: italic;
}

/* 登录内容 */
.login-content {
  width: 100%;
  max-width: 600rpx;
}

/* 特色功能 */
.feature-list {
  margin-bottom: 60rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-emoji {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

/* 登录按钮区域 */
.login-actions {
  margin-bottom: 60rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 53, 0.4);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-btn:not(.loading):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.3);
}

.login-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.login-btn::after {
  border: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.btn-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  margin-right: 16rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 游客模式 */
.guest-mode {
  text-align: center;
  margin-top: 40rpx;
  padding: 20rpx;
}

.guest-text {
  font-size: 28rpx;
  color: #ff6b35;
  text-decoration: underline;
}

.guest-desc {
  font-size: 22rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 登录提示 */
.login-tips {
  margin-bottom: 40rpx;
}

.tips-row {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.link-text {
  font-size: 24rpx;
  color: #ff6b35;
  text-decoration: underline;
  margin: 0 8rpx;
}

.security-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 107, 53, 0.2);
}

.security-icon {
  font-size: 24rpx;
  margin-right: 10rpx;
}

.security-text {
  font-size: 22rpx;
  color: #ccc;
}

/* 底部信息 */
.login-footer {
  text-align: center;
  padding: 40rpx;
  margin-top: auto;
}

.footer-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.footer-link {
  font-size: 24rpx;
  color: #ff6b35;
  text-decoration: underline;
}
