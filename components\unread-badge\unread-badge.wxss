/* 未读消息徽章组件样式 */
.unread-badge-container {
  position: relative;
  display: inline-block;
}

.unread-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #ff4d4f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 16rpx;
  min-height: 16rpx;
  z-index: 10;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.3);
}

/* 红点样式 */
.unread-badge.dot {
  width: 16rpx;
  height: 16rpx;
  min-width: 16rpx;
  min-height: 16rpx;
}

/* 数字徽章样式 */
.unread-badge.count {
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  border-radius: 16rpx;
}

/* 徽章文字 */
.badge-text {
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1;
  text-align: center;
}

/* 大数字样式调整 */
.unread-badge.count .badge-text {
  transform: scale(0.9);
  transform-origin: center;
}

/* 动画效果 */
.unread-badge {
  animation: badge-appear 0.3s ease-out;
}

@keyframes badge-appear {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 不同位置的徽章 */
.unread-badge.top-left {
  top: -6rpx;
  right: auto;
  left: -6rpx;
}

.unread-badge.bottom-right {
  top: auto;
  bottom: -6rpx;
  right: -6rpx;
}

.unread-badge.bottom-left {
  top: auto;
  bottom: -6rpx;
  right: auto;
  left: -6rpx;
}

/* 大尺寸徽章 */
.unread-badge.large {
  min-width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
}

.unread-badge.large .badge-text {
  font-size: 24rpx;
}

/* 小尺寸徽章 */
.unread-badge.small {
  min-width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
}

.unread-badge.small.dot {
  width: 12rpx;
  height: 12rpx;
  min-width: 12rpx;
  min-height: 12rpx;
}

.unread-badge.small .badge-text {
  font-size: 18rpx;
}
