/* 统一加载组件样式 */
.ui-loading {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.ui-loading--fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.7);
}

.ui-loading__mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.ui-loading__content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

/* G.T.I. SECURITY 科技感加载 */
.ui-loading__gti {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.ui-loading__gti-frame {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ui-loading__gti-corner {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #00ff88;
  animation: ui-loading-glow 2s ease-in-out infinite alternate;
}

.ui-loading__gti-corner--tl {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.ui-loading__gti-corner--tr {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.ui-loading__gti-corner--bl {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.ui-loading__gti-corner--br {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.ui-loading__gti-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.ui-loading__gti-logo {
  text-align: center;
}

.ui-loading__gti-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #00ff88;
  letter-spacing: 4rpx;
  text-shadow: 0 0 20rpx #00ff88;
  animation: ui-loading-pulse 1.5s ease-in-out infinite;
}

.ui-loading__gti-subtext {
  display: block;
  font-size: 20rpx;
  color: #66ffaa;
  letter-spacing: 2rpx;
  margin-top: 8rpx;
  opacity: 0.8;
}

.ui-loading__gti-matrix {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #00ff88;
  border-radius: 50%;
  background: rgba(0, 255, 136, 0.1);
}

.ui-loading__gti-matrix-char {
  font-size: 24rpx;
  font-weight: bold;
  color: #00ff88;
  animation: ui-loading-matrix 0.2s ease-in-out;
}

.ui-loading__gti-rings {
  position: absolute;
  width: 100%;
  height: 100%;
}

.ui-loading__gti-ring {
  position: absolute;
  border: 2rpx solid transparent;
  border-top-color: #00ff88;
  border-radius: 50%;
  animation: ui-loading-spin linear infinite;
}

.ui-loading__gti-ring--1 {
  width: 120rpx;
  height: 120rpx;
  top: 40rpx;
  left: 40rpx;
  animation-duration: 2s;
  opacity: 0.6;
}

.ui-loading__gti-ring--2 {
  width: 160rpx;
  height: 160rpx;
  top: 20rpx;
  left: 20rpx;
  animation-duration: 3s;
  opacity: 0.4;
}

.ui-loading__gti-ring--3 {
  width: 200rpx;
  height: 200rpx;
  top: 0;
  left: 0;
  animation-duration: 4s;
  opacity: 0.2;
}

.ui-loading__gti-status {
  font-size: 28rpx;
  color: #ffffff;
  text-align: center;
  animation: ui-loading-pulse 2s ease-in-out infinite;
}

/* 其他加载样式 */
.ui-loading__spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.ui-loading__spinner-icon {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: #ff6b35;
  border-radius: 50%;
  animation: ui-loading-spin 1s linear infinite;
}

.ui-loading__dots {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.ui-loading__dot {
  width: 16rpx;
  height: 16rpx;
  background: #ff6b35;
  border-radius: 50%;
  animation: ui-loading-bounce 1.4s ease-in-out infinite both;
}

.ui-loading__dot--1 {
  animation-delay: -0.32s;
}

.ui-loading__dot--2 {
  animation-delay: -0.16s;
}

.ui-loading__pulse {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.ui-loading__pulse-circle {
  width: 60rpx;
  height: 60rpx;
  background: #ff6b35;
  border-radius: 50%;
  animation: ui-loading-pulse-scale 1s ease-in-out infinite;
}

.ui-loading__wave {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.ui-loading__wave-bar {
  width: 8rpx;
  height: 40rpx;
  background: #ff6b35;
  border-radius: 4rpx;
  animation: ui-loading-wave 1.2s ease-in-out infinite;
}

.ui-loading__wave-bar--1 { animation-delay: 0s; }
.ui-loading__wave-bar--2 { animation-delay: 0.1s; }
.ui-loading__wave-bar--3 { animation-delay: 0.2s; }
.ui-loading__wave-bar--4 { animation-delay: 0.3s; }
.ui-loading__wave-bar--5 { animation-delay: 0.4s; }

.ui-loading__text {
  font-size: 28rpx;
  color: #ffffff;
  text-align: center;
  margin-top: 16rpx;
}

/* 动画定义 */
@keyframes ui-loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes ui-loading-glow {
  0% { 
    box-shadow: 0 0 10rpx #00ff88;
    opacity: 0.8;
  }
  100% { 
    box-shadow: 0 0 30rpx #00ff88;
    opacity: 1;
  }
}

@keyframes ui-loading-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes ui-loading-matrix {
  0% { transform: scale(0.8); opacity: 0.6; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes ui-loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes ui-loading-pulse-scale {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes ui-loading-wave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
