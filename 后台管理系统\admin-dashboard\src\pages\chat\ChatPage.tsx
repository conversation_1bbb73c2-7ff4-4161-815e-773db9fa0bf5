import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  MessageSquare, 
  Search, 
  Filter, 
  AlertTriangle,
  Users,
  Clock,
  Eye,
  Ban,
  Settings,
  RefreshCw,
  Plus,
  Play,
  Pause,
  Volume2,
  Download,
  Image
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { chatApi } from '@/services/cloudApi';
import type { ChatRoom, ChatMessage } from '@/types';

export default function ChatPage() {
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [selectedRoom, setSelectedRoom] = useState<ChatRoom | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('active');
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<{ [key: string]: HTMLAudioElement }>({});
  const { toast } = useToast();

  // 加载聊天数据
  const loadChatData = async () => {
    try {
      setLoading(true);
      
      const response = await chatApi.getChatRoomList({
        page: 1,
        limit: 100,
        status: activeTab === 'all' ? undefined : activeTab,
        search: searchTerm
      });
      
      console.log('聊天室API响应:', response);
      
      if (response.data?.success) {
        const chatRoomsData = response.data.data?.chatRooms || [];
        
        const transformedRooms = chatRoomsData.map((room: any) => ({
          id: room._id,
          orderId: room.orderInfo?.title || room.orderId || '未知订单',
          participants: [room.customerId, room.accepterId].filter(Boolean),
          lastMessage: room.lastMessage ? {
            id: `msg_${Date.now()}`,
            roomId: room._id,
            senderId: room.lastMessage.senderId || 'unknown',
            content: room.lastMessage.content || '暂无消息',
            type: room.lastMessage.type || 'text',
            timestamp: room.lastMessage.createTime || room.lastMessageTime || new Date().toISOString(),
            isRead: true,
          } : null,
          lastMessageTime: room.lastMessage?.createTime || room.lastMessageTime || room.updateTime,
          unreadCount: 0,
          status: room.status || 'active',
          customerInfo: room.customerInfo,
          accepterInfo: room.accepterInfo,
          orderInfo: room.orderInfo
        }));
        
        setChatRooms(transformedRooms);
        
        toast({
          title: '数据加载成功',
          description: `已加载 ${transformedRooms.length} 个聊天室`,
        });
      } else {
        throw new Error(response.data?.error || '获取聊天数据失败');
      }
    } catch (error: any) {
      console.error('加载聊天数据失败:', error);
      
      toast({
        title: '数据加载失败',
        description: '无法连接到服务器，请检查网络连接后重试',
        variant: 'destructive',
      });

      // 不再使用模拟数据，保持空状态
      setChatRooms([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载聊天消息
  const loadChatMessages = async (roomId: string) => {
    try {
      console.log('🔍 [聊天页面] 开始加载聊天消息');
      console.log('🔍 [聊天页面] roomId:', roomId);
      
      const response = await chatApi.getChatMessages(roomId, {
        page: 1,
        limit: 999999
      });
      
      console.log('🔍 [聊天页面] API完整响应:', response);
      
      if (response.data?.success) {
        const messages = response.data.data?.messages || [];
        console.log('✅ [聊天页面] 成功获取消息:', messages.length, '条');
        
        setMessages(messages);
        
        toast({
          title: '消息加载成功',
          description: `已加载 ${messages.length} 条真实消息`,
        });
        
        return;
      } else {
        console.error('❌ [聊天页面] API返回失败:', response.data?.error);
        throw new Error(response.data?.error || '获取消息失败');
      }
    } catch (error: any) {
      console.error('加载聊天消息失败:', error);
      
      toast({
        title: '消息加载失败',
        description: '无法连接到服务器，请检查网络连接后重试',
        variant: 'destructive',
      });

      // 不再使用模拟数据，保持空状态
      setMessages([]);
    }
  };

  useEffect(() => {
    loadChatData();
  }, [activeTab]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm) {
        loadChatData();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  const filteredRooms = chatRooms.filter(room => {
    const matchesSearch = (room.orderId || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = activeTab === 'all' || room.status === activeTab;
    return matchesSearch && matchesStatus;
  });

  const roomMessages = messages.filter(msg => msg.roomId === selectedRoom?.id);

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleRoomSelect = (room: ChatRoom) => {
    setSelectedRoom(room);
    loadChatMessages(room.id);
  };

  const handleBanUser = async (userId: string) => {
    try {
      const response = await chatApi.banUser(userId);
      
      if (response.data?.success) {
        toast({
          title: '操作成功',
          description: '用户已被禁言',
        });
      } else {
        throw new Error(response.data?.error || '禁言失败');
      }
    } catch (error: any) {
      console.error('禁言用户失败:', error);
      
      toast({
        title: '操作失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 语音播放功能
  const handlePlayAudio = async (audioUrl: string, messageId: string) => {
    try {
      if (playingAudio && playingAudio !== messageId) {
        const currentAudio = audioElements[playingAudio];
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.currentTime = 0;
        }
      }

      if (playingAudio === messageId) {
        const audio = audioElements[messageId];
        if (audio) {
          audio.pause();
          setPlayingAudio(null);
        }
        return;
      }

      let audio = audioElements[messageId];
      if (!audio) {
        audio = new Audio();
        
        let processedUrl = audioUrl;
        if (audioUrl.startsWith('cloud://')) {
          processedUrl = audioUrl.replace(
            'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/',
            'https://636c-cloud1-9gsj7t48183e5a9f-1366958750.tcb.qcloud.la/'
          );
        }
        
        audio.src = processedUrl;
        audio.preload = 'metadata';
        
        audio.addEventListener('ended', () => {
          setPlayingAudio(null);
        });
        
        audio.addEventListener('error', (e) => {
          console.error('音频播放错误:', e);
          toast({
            title: '播放失败',
            description: '无法播放该语音消息',
            variant: 'destructive',
          });
          setPlayingAudio(null);
        });

        setAudioElements(prev => ({
          ...prev,
          [messageId]: audio
        }));
      }

      setPlayingAudio(messageId);
      await audio.play();
      
    } catch (error) {
      console.error('播放语音失败:', error);
      toast({
        title: '播放失败',
        description: '无法播放该语音消息',
        variant: 'destructive',
      });
      setPlayingAudio(null);
    }
  };

  // 下载语音文件
  const handleDownloadAudio = (audioUrl: string, messageId: string) => {
    try {
      let downloadUrl = audioUrl;
      if (audioUrl.startsWith('cloud://')) {
        downloadUrl = audioUrl.replace(
          'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/',
          'https://636c-cloud1-9gsj7t48183e5a9f-1366958750.tcb.qcloud.la/'
        );
      }
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `voice_${messageId}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: '下载开始',
        description: '语音文件开始下载',
      });
    } catch (error) {
      console.error('下载语音失败:', error);
      toast({
        title: '下载失败',
        description: '无法下载该语音文件',
        variant: 'destructive',
      });
    }
  };

  // 获取语音时长
  const getAudioDuration = (audioUrl: string) => {
    const match = audioUrl.match(/duration(\d+)/);
    if (match) {
      const duration = parseInt(match[1]);
      return `${Math.floor(duration / 1000)}"`;
    }
    return '未知';
  };

  const totalActiveRooms = chatRooms.filter(room => room.status === 'active').length || 0;
  const totalUnreadMessages = chatRooms.reduce((sum, room) => sum + (room.unreadCount || 0), 0);
  const totalRooms = chatRooms.length || 0;

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">聊天监控</h1>
            <p className="text-gray-600">监控平台聊天消息和用户行为</p>
          </div>
          <Button disabled>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            加载中...
          </Button>
        </div>
        
        <div className="grid grid-cols-12 gap-6 h-[600px]">
          <Card className="col-span-4 animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-32"></div>
              <div className="h-4 bg-gray-200 rounded w-24"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={`loading-${i}`} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          <Card className="col-span-8 animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-48"></div>
            </CardHeader>
            <CardContent>
              <div className="h-96 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和统计 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">聊天监控</h1>
          <p className="text-gray-600">监控平台聊天消息和用户行为</p>
        </div>
        <div className="flex space-x-4">
          <Button variant="outline" onClick={loadChatData} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
          <Card className="px-4 py-2">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium">{totalActiveRooms}</div>
                <div className="text-xs text-gray-500">活跃聊天室</div>
              </div>
            </div>
          </Card>
          <Card className="px-4 py-2">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">{totalUnreadMessages}</div>
                <div className="text-xs text-gray-500">未读消息</div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* 聊天监控界面 */}
      <div className="grid grid-cols-12 gap-6 h-[600px]">
        {/* 左侧：聊天室列表 */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>聊天室列表</span>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </CardTitle>
            <CardDescription>
              共 {totalRooms} 个聊天室
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3 mx-4 mb-4">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="active">活跃</TabsTrigger>
                <TabsTrigger value="closed">已关闭</TabsTrigger>
              </TabsList>
              
              <div className="px-4 mb-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索订单号..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <ScrollArea className="h-[400px]">
                <div className="space-y-2 px-4">
                  {filteredRooms.map((room) => (
                    <div
                      key={room.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedRoom?.id === room.id
                          ? 'bg-blue-50 border-blue-200'
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => handleRoomSelect(room)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {room.orderId}
                          </Badge>
                          {room.status === 'active' && (
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          )}
                          {(room as any).orderInfo?.reward && (
                            <Badge variant="secondary" className="text-xs">
                              ¥{(room as any).orderInfo.reward}
                            </Badge>
                          )}
                        </div>
                        {room.unreadCount > 0 && (
                          <Badge variant="destructive" className="h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                            {room.unreadCount}
                          </Badge>
                        )}
                      </div>
                      
                      {/* 显示用户信息 */}
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="flex items-center space-x-1">
                          <Avatar className="h-5 w-5">
                            <AvatarImage src={(room as any).customerInfo?.avatarUrl} />
                            <AvatarFallback className="text-xs">
                              {(room as any).customerInfo?.nickName?.charAt(0) || 'C'}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-xs text-gray-500">
                            {(room as any).customerInfo?.nickName || '客户'}
                          </span>
                        </div>
                        <span className="text-xs text-gray-400">↔</span>
                        <div className="flex items-center space-x-1">
                          <Avatar className="h-5 w-5">
                            <AvatarImage src={(room as any).accepterInfo?.avatarUrl} />
                            <AvatarFallback className="text-xs">
                              {(room as any).accepterInfo?.nickName?.charAt(0) || 'A'}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-xs text-gray-500">
                            {(room as any).accepterInfo?.nickName || '接单者'}
                          </span>
                        </div>
                      </div>
                      
                      <div className="text-sm text-gray-600 mb-1 truncate">
                        {room.lastMessage ? (
                          room.lastMessage.type === 'voice' ? (
                            <span className="flex items-center space-x-1">
                              <Volume2 className="h-3 w-3" />
                              <span>语音消息</span>
                            </span>
                          ) : room.lastMessage.type === 'image' ? (
                            <span className="flex items-center space-x-1">
                              <Image className="h-3 w-3" />
                              <span>图片消息</span>
                            </span>
                          ) : (
                            room.lastMessage.content
                          )
                        ) : '暂无消息'}
                      </div>
                      <div className="text-xs text-gray-400">
                        {room.lastMessageTime ? formatTime(room.lastMessageTime) : '无时间'}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </Tabs>
          </CardContent>
        </Card>

        {/* 右侧：消息详情 */}
        <Card className="col-span-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>
                {selectedRoom ? `聊天记录 - ${selectedRoom.orderId}` : '选择聊天室查看消息'}
              </span>
              {selectedRoom && (
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    查看详情
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => (selectedRoom.participants || []).forEach(userId => handleBanUser(userId))}
                  >
                    <Ban className="h-4 w-4 mr-2" />
                    禁言
                  </Button>
                </div>
              )}
            </CardTitle>
            {selectedRoom && (
              <CardDescription>
                参与者: {(selectedRoom.participants || []).join(', ')}
              </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            {selectedRoom ? (
              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {roomMessages.map((message, index) => (
                    <div key={`${message.id}-${index}`} className="flex items-start space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={(message as any).senderInfo?.avatarUrl || "/placeholder.svg?height=32&width=32"} />
                        <AvatarFallback>
                          {(message as any).senderInfo?.nickName?.charAt(0) || message.senderId.slice(-1)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium">
                            {(message as any).senderInfo?.nickName || message.senderId}
                          </span>
                          <span className="text-xs text-gray-400">
                            {formatTime(message.timestamp)}
                          </span>
                          {!message.isRead && (
                            <Badge variant="secondary" className="text-xs">未读</Badge>
                          )}
                        </div>
                        <div className="bg-gray-50 rounded-lg p-3 text-sm">
                          {message.type === 'voice' ? (
                            <div className="flex items-center space-x-3 min-w-[200px]">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePlayAudio(message.content, message.id)}
                                className="flex items-center space-x-2"
                              >
                                {playingAudio === message.id ? (
                                  <Pause className="h-4 w-4" />
                                ) : (
                                  <Play className="h-4 w-4" />
                                )}
                                <Volume2 className="h-4 w-4" />
                              </Button>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-700">语音消息</div>
                                <div className="text-xs text-gray-500">
                                  时长: {getAudioDuration(message.content)}
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDownloadAudio(message.content, message.id)}
                                className="p-1"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : message.type === 'image' ? (
                            <div className="space-y-2">
                              <div className="text-sm text-gray-600">图片消息</div>
                              <img 
                                src={message.content.startsWith('cloud://') 
                                  ? message.content.replace(
                                      'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/',
                                      'https://636c-cloud1-9gsj7t48183e5a9f-1366958750.tcb.qcloud.la/'
                                    )
                                  : message.content
                                }
                                alt="聊天图片"
                                className="max-w-[200px] max-h-[200px] rounded-lg cursor-pointer"
                                onClick={() => window.open(message.content, '_blank')}
                              />
                            </div>
                          ) : (
                            message.content
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="flex items-center justify-center h-[400px] text-gray-500">
                <div className="text-center">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>请选择一个聊天室查看消息记录</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 敏感词管理 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5" />
            敏感词管理
          </CardTitle>
          <CardDescription>
            配置和管理聊天内容过滤规则
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">当前敏感词库</h4>
              <div className="space-y-2">
                {['违法', '欺诈', '色情', '暴力', '赌博', '诈骗', '传销', '毒品'].map((word, index) => (
                  <Badge key={`${word}-${index}`} variant="destructive" className="mr-2 mb-2">
                    {word}
                  </Badge>
                ))}
              </div>
              <Button variant="outline" size="sm" className="mt-3">
                <Plus className="mr-2 h-4 w-4" />
                添加敏感词
              </Button>
            </div>
            <div>
              <h4 className="font-medium mb-3">违规统计</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                  <span className="text-sm">今日违规消息</span>
                  <span className="text-sm font-medium text-red-600">0</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                  <span className="text-sm">本周违规消息</span>
                  <span className="text-sm font-medium text-orange-600">2</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                  <span className="text-sm">违规用户数</span>
                  <span className="text-sm font-medium text-yellow-600">1</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm">已处理违规</span>
                  <span className="text-sm font-medium text-blue-600">15</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}