// 充值页面
import API from '../../../utils/api.js';

const app = getApp();

Page({
  data: {
    userInfo: null,
    currentBalance: 0,

    // 充值金额选项
    amountOptions: [
      { value: 10, label: '10元', popular: false },
      { value: 20, label: '20元', popular: false },
      { value: 50, label: '50元', popular: true },
      { value: 100, label: '100元', popular: true },
      { value: 200, label: '200元', popular: false },
      { value: 500, label: '500元', popular: false }
    ],

    selectedAmount: 0,
    customAmount: '',
    useCustomAmount: false,

    // 支付方式
    paymentMethods: [
      {
        id: 'wechat',
        name: '微信支付',
        icon: '/images/icons/wechat-pay.png',
        selected: true
      }
    ],
    selectedPaymentMethod: 'wechat',

    // 页面状态
    loading: false,
    submitting: false
  },

  onLoad() {
    this.loadUserInfo();
  },

  onShow() {
    // 页面显示时刷新用户信息
    this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({
          userInfo,
          currentBalance: userInfo.balance || 0
        });
      }

      // 获取最新的用户信息
      const result = await API.getUserInfo();
      if (result.success) {
        this.setData({
          userInfo: result.data,
          currentBalance: result.data.balance || 0
        });

        // 更新本地存储
        wx.setStorageSync('userInfo', result.data);
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  // 选择充值金额
  selectAmount(e) {
    const amount = parseInt(e.currentTarget.dataset.amount);
    this.setData({
      selectedAmount: amount,
      useCustomAmount: false,
      customAmount: ''
    });
  },

  // 自定义金额输入
  onCustomAmountInput(e) {
    const value = e.detail.value;
    this.setData({
      customAmount: value,
      selectedAmount: 0,
      useCustomAmount: true
    });
  },

  // 选择支付方式
  selectPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPaymentMethod: method
    });
  },

  // 确认充值
  async confirmRecharge() {
    if (this.data.submitting) return;

    // 获取充值金额
    let amount = 0;
    if (this.data.useCustomAmount) {
      amount = parseFloat(this.data.customAmount);
    } else {
      amount = this.data.selectedAmount;
    }

    // 验证充值金额
    if (!amount || amount <= 0) {
      app.utils.showError('请选择充值金额');
      return;
    }

    if (amount < 1 || amount > 10000) {
      app.utils.showError('充值金额必须在1-10000元之间');
      return;
    }

    this.setData({ submitting: true });

    try {
      app.utils.showLoading('创建充值订单...');

      // 创建充值订单
      const result = await API.createRecharge({
        amount: amount,
        paymentMethod: this.data.selectedPaymentMethod
      });

      if (result.success) {
        const { paymentData } = result.data;

        if (paymentData && this.data.selectedPaymentMethod === 'wechat') {
          // 调起微信支付
          await this.requestWechatPayment(paymentData);
        } else {
          app.utils.showSuccess('充值订单创建成功');
        }
      } else {
        app.utils.showError(result.error || '创建充值订单失败');
      }
    } catch (error) {
      console.error('充值失败:', error);
      app.utils.showError('充值失败，请重试');
    } finally {
      app.utils.hideLoading();
      this.setData({ submitting: false });
    }
  },

  // 调起微信支付
  async requestWechatPayment(paymentData) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        ...paymentData,
        success: (res) => {
          console.log('支付成功:', res);
          app.utils.showSuccess('充值成功');

          // 刷新用户信息
          setTimeout(() => {
            this.loadUserInfo();
          }, 1000);

          resolve(res);
        },
        fail: (err) => {
          console.error('支付失败:', err);
          if (err.errMsg.includes('cancel')) {
            app.utils.showError('支付已取消');
          } else {
            app.utils.showError('支付失败');
          }
          reject(err);
        }
      });
    });
  },

  // 查看充值记录
  viewRechargeHistory() {
    wx.navigateTo({
      url: '/pages/payment/records/records?type=recharge'
    });
  }
});