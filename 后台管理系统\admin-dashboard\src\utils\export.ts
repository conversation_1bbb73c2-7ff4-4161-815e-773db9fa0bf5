// import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import type { ExportField, ExportOptions } from '@/components/ui/data-export';

// 导出数据到Excel或CSV
export async function exportData<T extends Record<string, any>>(
  data: T[],
  fields: ExportField[],
  options: ExportOptions
): Promise<void> {
  if (data.length === 0) {
    throw new Error('没有数据可导出');
  }

  // 过滤和排序字段
  const selectedFields = fields.filter(field => options.fields.includes(field.key));
  
  // 准备导出数据
  const exportData = data.map(item => {
    const row: Record<string, any> = {};
    selectedFields.forEach(field => {
      const value = item[field.key];
      row[field.label] = formatFieldValue(value, field.type);
    });
    return row;
  });

  // 生成文件名
  const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm-ss');
  const filename = options.filename || `导出数据_${timestamp}`;

  if (options.format === 'xlsx') {
    await exportToExcel(exportData, filename);
  } else {
    await exportToCSV(exportData, filename);
  }
}

// 导出到Excel
async function exportToExcel(data: Record<string, any>[], filename: string): Promise<void> {
  try {
    // Excel导出功能暂时不可用，需要安装xlsx依赖
    console.warn('Excel导出功能暂时不可用，请安装xlsx依赖');
    throw new Error('Excel导出功能暂时不可用，请使用CSV导出');
  } catch (error) {
    console.error('Excel导出失败:', error);
    throw new Error('Excel导出失败');
  }
}

// 导出到CSV
async function exportToCSV(data: Record<string, any>[], filename: string): Promise<void> {
  try {
    if (data.length === 0) return;
    
    // 获取表头
    const headers = Object.keys(data[0]);
    
    // 生成CSV内容
    const csvContent = [
      headers.join(','), // 表头
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // 处理包含逗号、引号或换行符的值
          if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');
    
    // 创建Blob并下载
    const blob = new Blob(['\uFEFF' + csvContent], { 
      type: 'text/csv;charset=utf-8;' 
    });
    
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('CSV导出失败:', error);
    throw new Error('CSV导出失败');
  }
}

// 格式化字段值
function formatFieldValue(value: any, type?: string): string {
  if (value === null || value === undefined) {
    return '';
  }

  switch (type) {
    case 'date':
      if (value instanceof Date) {
        return format(value, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN });
      }
      if (typeof value === 'string' || typeof value === 'number') {
        try {
          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            return format(date, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN });
          }
        } catch {
          // 忽略日期解析错误
        }
      }
      return String(value);
      
    case 'number':
      if (typeof value === 'number') {
        return value.toLocaleString('zh-CN');
      }
      return String(value);
      
    case 'boolean':
      if (typeof value === 'boolean') {
        return value ? '是' : '否';
      }
      return String(value);
      
    default:
      return String(value);
  }
}

// 用户数据导出字段定义
export const userExportFields: ExportField[] = [
  { key: 'id', label: '用户ID', required: true, type: 'string' },
  { key: 'nickname', label: '昵称', type: 'string' },
  { key: 'phone', label: '手机号', type: 'string' },
  { key: 'email', label: '邮箱', type: 'string' },
  { key: 'status', label: '状态', type: 'string' },
  { key: 'walletBalance', label: '钱包余额', type: 'number' },
  { key: 'orderCount', label: '订单数量', type: 'number' },
  { key: 'avgRating', label: '平均评分', type: 'number' },
  { key: 'totalSpent', label: '总消费', type: 'number' },
  { key: 'createdAt', label: '注册时间', type: 'date' },
  { key: 'lastLoginAt', label: '最后登录', type: 'date' },
  { key: 'isVerified', label: '已认证', type: 'boolean' },
];

// 订单数据导出字段定义
export const orderExportFields: ExportField[] = [
  { key: '_id', label: '订单ID', required: true, type: 'string' },
  { key: 'orderNo', label: '订单号', type: 'string' },
  { key: 'title', label: '订单标题', required: true, type: 'string' },
  { key: 'description', label: '订单描述', type: 'string' },
  { key: 'reward', label: '订单金额', required: true, type: 'number' },
  { key: 'status', label: '订单状态', required: true, type: 'string' },
  { key: 'orderType', label: '订单类型', type: 'string' },
  { key: 'platform', label: '平台类型', type: 'string' },
  { key: 'customerId', label: '发布者ID', type: 'string' },
  { key: 'accepterId', label: '接单者ID', type: 'string' },
  { key: 'createTime', label: '创建时间', required: true, type: 'date' },
  { key: 'updateTime', label: '更新时间', type: 'date' },
  { key: 'deadline', label: '截止时间', type: 'date' },
];

// 钱包交易导出字段定义
export const walletExportFields: ExportField[] = [
  { key: '_id', label: '交易ID', required: true, type: 'string' },
  { key: 'userId', label: '用户ID', required: true, type: 'string' },
  { key: 'type', label: '交易类型', required: true, type: 'string' },
  { key: 'amount', label: '交易金额', required: true, type: 'number' },
  { key: 'balance', label: '余额', type: 'number' },
  { key: 'description', label: '交易描述', type: 'string' },
  { key: 'orderId', label: '关联订单', type: 'string' },
  { key: 'status', label: '交易状态', type: 'string' },
  { key: 'createTime', label: '交易时间', required: true, type: 'date' },
];

// 评价数据导出字段定义
export const evaluationExportFields: ExportField[] = [
  { key: '_id', label: '评价ID', required: true, type: 'string' },
  { key: 'orderId', label: '订单ID', required: true, type: 'string' },
  { key: 'fromUserId', label: '评价者ID', required: true, type: 'string' },
  { key: 'toUserId', label: '被评价者ID', required: true, type: 'string' },
  { key: 'rating', label: '评分', required: true, type: 'number' },
  { key: 'comment', label: '评价内容', type: 'string' },
  { key: 'type', label: '评价类型', type: 'string' },
  { key: 'createTime', label: '评价时间', required: true, type: 'date' },
];

// 通知数据导出字段定义
export const notificationExportFields: ExportField[] = [
  { key: '_id', label: '通知ID', required: true, type: 'string' },
  { key: 'userId', label: '用户ID', required: true, type: 'string' },
  { key: 'title', label: '通知标题', required: true, type: 'string' },
  { key: 'content', label: '通知内容', type: 'string' },
  { key: 'type', label: '通知类型', type: 'string' },
  { key: 'status', label: '状态', type: 'string' },
  { key: 'createTime', label: '创建时间', required: true, type: 'date' },
  { key: 'readTime', label: '阅读时间', type: 'date' },
];
