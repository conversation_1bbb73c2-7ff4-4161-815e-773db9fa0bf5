// 实名认证云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { realName, idCard, phone, idCardFront, idCardBack } = event;

  try {
    // 验证必填字段
    if (!realName || !idCard || !phone || !idCardFront || !idCardBack) {
      return {
        success: false,
        error: '请填写完整的认证信息'
      };
    }

    // 验证身份证格式
    const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardReg.test(idCard)) {
      return {
        success: false,
        error: '身份证号码格式不正确'
      };
    }

    // 验证手机号格式
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!phoneReg.test(phone)) {
      return {
        success: false,
        error: '手机号码格式不正确'
      };
    }

    // 查找用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const userId = userResult.data[0]._id;

    // 检查身份证是否已被使用
    const existingUser = await db.collection('users').where({
      idCard: idCard,
      _id: db.command.neq(userId)
    }).get();

    if (existingUser.data.length > 0) {
      return {
        success: false,
        error: '该身份证号已被其他用户使用'
      };
    }

    // 更新用户认证信息
    await db.collection('users').doc(userId).update({
      data: {
        realName,
        idCard,
        phone,
        idCardFront,
        idCardBack,
        isVerified: false, // 待审核状态
        certificationStatus: 'pending',
        certificationTime: new Date(),
        updateTime: new Date()
      }
    });

    // 创建认证审核记录
    await db.collection('certifications').add({
      data: {
        userId,
        realName,
        idCard,
        phone,
        idCardFront,
        idCardBack,
        status: 'pending',
        submitTime: new Date(),
        reviewTime: null,
        reviewReason: ''
      }
    });

    return {
      success: true,
      message: '认证信息提交成功，请等待审核'
    };
  } catch (error) {
    console.error('实名认证失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
