// 支付回调处理云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {
    // 微信支付回调数据
    const { 
      outTradeNo, // 商户订单号
      transactionId, // 微信支付交易号
      totalFee, // 支付金额（分）
      resultCode, // 支付结果
      timeEnd // 支付完成时间
    } = event;

    console.log('支付回调数据:', event);

    // 验证支付结果
    if (resultCode !== 'SUCCESS') {
      console.log('支付失败:', event);
      return {
        success: false,
        error: '支付失败'
      };
    }

    // 查找充值记录
    const rechargeResult = await db.collection('recharges').where({
      rechargeNo: outTradeNo
    }).get();

    if (rechargeResult.data.length === 0) {
      console.error('充值记录不存在:', outTradeNo);
      return {
        success: false,
        error: '充值记录不存在'
      };
    }

    const recharge = rechargeResult.data[0];

    // 检查充值记录状态，避免重复处理
    if (recharge.status === 'paid') {
      console.log('充值记录已处理:', outTradeNo);
      return {
        success: true,
        message: '充值记录已处理'
      };
    }

    // 验证金额
    const paidAmount = totalFee / 100; // 转换为元
    if (Math.abs(paidAmount - recharge.amount) > 0.01) {
      console.error('支付金额不匹配:', {
        expected: recharge.amount,
        actual: paidAmount
      });
      return {
        success: false,
        error: '支付金额不匹配'
      };
    }

    // 开始事务处理
    const transaction = await db.startTransaction();

    try {
      // 更新充值记录状态
      await transaction.collection('recharges').doc(recharge._id).update({
        data: {
          status: 'paid',
          payTime: new Date(timeEnd),
          transactionId: transactionId,
          updateTime: new Date()
        }
      });

      // 更新用户余额
      await transaction.collection('users').doc(recharge.userId).update({
        data: {
          balance: db.command.inc(recharge.amount),
          updateTime: new Date()
        }
      });

      // 更新交易记录状态
      await transaction.collection('transactions').where({
        rechargeId: recharge._id,
        type: 'recharge'
      }).update({
        data: {
          status: 'completed',
          transactionId: transactionId,
          updateTime: new Date()
        }
      });

      // 提交事务
      await transaction.commit();

      console.log('充值处理成功:', {
        rechargeNo: outTradeNo,
        amount: recharge.amount,
        userId: recharge.userId
      });

      return {
        success: true,
        message: '充值处理成功'
      };
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('支付回调处理失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
