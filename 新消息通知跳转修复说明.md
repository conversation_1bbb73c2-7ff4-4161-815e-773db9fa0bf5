# 新消息通知跳转修复说明

## 🎯 问题描述
新消息弹窗提醒点击后只会跳转到聊天列表，而不是跳转到相应的聊天室。

## 🔧 修复内容

### 1. 修改通知组件 (components/notification-toast/notification-toast.js)
- **新增属性**: 添加 `chatRoomId` 和 `messageData` 属性
- **修改点击事件**: 在 `onNotificationTap` 方法中传递聊天室ID和消息数据

```javascript
// 新增属性
chatRoomId: {
  type: String,
  value: ''
},
messageData: {
  type: Object,
  value: null
}

// 修改点击事件
onNotificationTap() {
  if (this.data.clickable) {
    this.triggerEvent('tap', {
      type: this.data.type,
      title: this.data.title,
      content: this.data.content,
      chatRoomId: this.data.chatRoomId,
      messageData: this.data.messageData
    });
  }
}
```

### 2. 修改首页通知处理 (pages/index/index.js)
- **保存聊天室信息**: 在 `handleInAppNotification` 中保存 `chatRoomId` 和 `messageData`
- **智能跳转逻辑**: 在 `onNotificationTap` 中根据是否有聊天室ID决定跳转目标

```javascript
// 保存聊天室信息
this.setData({
  notification: {
    show: true,
    type: type || 'message',
    title: title || '',
    content: content || '',
    avatar: avatar,
    duration: duration,
    chatRoomId: chatRoomId, // 保存聊天室ID
    messageData: messageData // 保存消息数据
  }
});

// 智能跳转逻辑
if (type === 'message') {
  if (chatRoomId) {
    // 有聊天室ID，直接跳转到对应聊天室
    wx.navigateTo({
      url: `/chat-package/pages/room/room?roomId=${chatRoomId}`
    });
  } else {
    // 没有聊天室ID，跳转到聊天列表
    wx.switchTab({
      url: '/pages/chat/list/list'
    });
  }
}
```

### 3. 修改首页模板 (pages/index/index.wxml)
- **传递新属性**: 在通知组件中传递 `chatRoomId` 和 `messageData`

```xml
<notification-toast
  show="{{notification.show}}"
  type="{{notification.type}}"
  title="{{notification.title}}"
  content="{{notification.content}}"
  avatar="{{notification.avatar}}"
  duration="{{notification.duration}}"
  chatRoomId="{{notification.chatRoomId}}"
  messageData="{{notification.messageData}}"
  bind:tap="onNotificationTap"
  bind:hide="onNotificationHide"
/>
```

## ✅ 修复效果
1. **精确跳转**: 点击新消息通知直接跳转到对应的聊天室
2. **兼容性**: 如果没有聊天室ID，仍然跳转到聊天列表作为备用方案
3. **数据完整**: 保留消息数据，为后续功能扩展提供支持

## 🔍 技术细节
- 通知数据流: `app.js` → `首页` → `通知组件` → `首页点击处理`
- 跳转路径: `/chat-package/pages/room/room?roomId=${chatRoomId}`
- 备用路径: `/pages/chat/list/list` (TabBar页面)

## 📝 注意事项
- 聊天室页面使用 `wx.navigateTo` 跳转
- 聊天列表页面使用 `wx.switchTab` 跳转（TabBar页面）
- 保持了原有的错误处理和日志记录
