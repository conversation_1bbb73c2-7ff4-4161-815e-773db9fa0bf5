// 通知管理云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action, data } = event;
  
  console.log('📢 [通知管理] 执行操作:', action);
  
  try {
    switch (action) {
      case 'createNotification':
        return await createNotification(data, wxContext);
      case 'getNotificationList':
        return await getNotificationList(data, wxContext);
      case 'markAsRead':
        return await markAsRead(data, wxContext);
      case 'markAllAsRead':
        return await markAllAsRead(wxContext);
      case 'getUnreadCount':
        return await getUnreadCount(wxContext);
      case 'deleteNotification':
        return await deleteNotification(data, wxContext);
      case 'subscribeMessage':
        return await subscribeMessage(data, wxContext);
      default:
        return {
          success: false,
          error: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('❌ [通知管理] 操作失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 创建通知
async function createNotification(data, wxContext) {
  const {
    userId,
    type,
    title,
    content,
    relatedId,
    relatedType,
    priority = 'normal',
    autoExpire = true,
    expireTime
  } = data;
  
  console.log('📢 [创建通知] 参数:', { userId, type, title, priority });
  
  try {
    const notification = {
      userId,
      type, // orderStatus, newOrder, chatMessage, system, evaluation
      title,
      content,
      relatedId, // 相关的订单ID、聊天室ID等
      relatedType, // order, chatRoom, user等
      priority, // low, normal, high, urgent
      isRead: false,
      createTime: new Date(),
      readTime: null,
      expireTime: expireTime ? new Date(expireTime) : (autoExpire ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : null), // 默认30天过期
      _openid: wxContext.OPENID
    };
    
    const result = await db.collection('notifications').add({
      data: notification
    });
    

    
    // 如果是高优先级通知，尝试发送订阅消息
    if (priority === 'high' || priority === 'urgent') {
      await sendSubscriptionMessage(notification);
    }
    
    return {
      success: true,
      data: {
        notificationId: result._id,
        notification: notification
      }
    };
    
  } catch (error) {
    console.error('❌ [创建通知] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 获取通知列表
async function getNotificationList(data, wxContext) {
  const {
    page = 1,
    pageSize = 20,
    type = 'all',
    onlyUnread = false
  } = data;
  
  console.log('📢 [获取通知列表] 参数:', { page, pageSize, type, onlyUnread });
  
  try {
    // 构建查询条件
    let whereCondition = {
      _openid: wxContext.OPENID
    };
    
    // 过滤类型
    if (type !== 'all') {
      whereCondition.type = type;
    }
    
    // 只显示未读
    if (onlyUnread) {
      whereCondition.isRead = false;
    }
    
    // 过滤过期通知
    whereCondition.expireTime = _.or(
      _.eq(null),
      _.gt(new Date())
    );
    
    const result = await db.collection('notifications')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    console.log('✅ [获取通知列表] 查询成功，数量:', result.data.length);
    
    return {
      success: true,
      data: {
        notifications: result.data,
        hasMore: result.data.length === pageSize
      }
    };
    
  } catch (error) {
    console.error('❌ [获取通知列表] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 标记为已读
async function markAsRead(data, wxContext) {
  const { notificationId } = data;
  
  console.log('📢 [标记已读] 通知ID:', notificationId);
  
  try {
    const result = await db.collection('notifications')
      .doc(notificationId)
      .update({
        data: {
          isRead: true,
          readTime: new Date()
        }
      });
    
    console.log('✅ [标记已读] 成功');
    
    return {
      success: true,
      data: result
    };
    
  } catch (error) {
    console.error('❌ [标记已读] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 标记全部为已读
async function markAllAsRead(wxContext) {
  console.log('📢 [标记全部已读] 用户:', wxContext.OPENID);
  
  try {
    const result = await db.collection('notifications')
      .where({
        _openid: wxContext.OPENID,
        isRead: false
      })
      .update({
        data: {
          isRead: true,
          readTime: new Date()
        }
      });
    
    console.log('✅ [标记全部已读] 成功，更新数量:', result.stats.updated);
    
    return {
      success: true,
      data: {
        updatedCount: result.stats.updated
      }
    };
    
  } catch (error) {
    console.error('❌ [标记全部已读] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 获取未读数量
async function getUnreadCount(wxContext) {
  console.log('📢 [获取未读数量] 用户:', wxContext.OPENID);
  
  try {
    const result = await db.collection('notifications')
      .where({
        _openid: wxContext.OPENID,
        isRead: false,
        expireTime: _.or(
          _.eq(null),
          _.gt(new Date())
        )
      })
      .count();
    
    console.log('✅ [获取未读数量] 成功:', result.total);
    
    return {
      success: true,
      data: {
        unreadCount: result.total
      }
    };
    
  } catch (error) {
    console.error('❌ [获取未读数量] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 删除通知
async function deleteNotification(data, wxContext) {
  const { notificationId } = data;
  
  console.log('📢 [删除通知] 通知ID:', notificationId);
  
  try {
    const result = await db.collection('notifications')
      .doc(notificationId)
      .remove();
    
    console.log('✅ [删除通知] 成功');
    
    return {
      success: true,
      data: result
    };
    
  } catch (error) {
    console.error('❌ [删除通知] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 订阅消息
async function subscribeMessage(data, wxContext) {
  const { templateId, page, miniprogramState = 'formal' } = data;
  
  console.log('📢 [订阅消息] 模板ID:', templateId);
  
  try {
    // 这里可以记录用户的订阅状态
    const subscription = {
      userId: wxContext.OPENID,
      templateId,
      subscribeTime: new Date(),
      isActive: true
    };
    
    await db.collection('subscriptions').add({
      data: subscription
    });
    
    console.log('✅ [订阅消息] 记录成功');
    
    return {
      success: true,
      data: subscription
    };
    
  } catch (error) {
    console.error('❌ [订阅消息] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 发送订阅消息
async function sendSubscriptionMessage(notification) {
  try {
    // 这里可以调用微信的订阅消息API
    // 由于需要模板ID和用户授权，这里只是示例
    console.log('📢 [订阅消息] 准备发送:', notification.title);
    
    // 实际发送逻辑需要根据具体的模板配置
    // await cloud.openapi.subscribeMessage.send({
    //   touser: notification.userId,
    //   template_id: 'your_template_id',
    //   page: 'pages/notification/list/list',
    //   data: {
    //     thing1: { value: notification.title },
    //     thing2: { value: notification.content }
    //   }
    // });
    
  } catch (error) {
    console.error('❌ [订阅消息] 发送失败:', error);
  }
}
