// 通知管理工具

class NotificationManager {
  constructor() {
    this.subscribeMessageTemplates = {
      // 订单状态变更通知
      orderStatusChange: 'your_template_id_1',
      // 新订单通知
      newOrder: 'your_template_id_2',
      // 聊天消息通知
      chatMessage: 'your_template_id_3'
    };
  }

  /**
   * 请求订阅消息权限
   * @param {Array} templateIds 模板ID数组
   * @returns {Promise}
   */
  async requestSubscribeMessage(templateIds = []) {
    return new Promise((resolve, reject) => {
      wx.requestSubscribeMessage({
        tmplIds: templateIds,
        success: (res) => {
          console.log('订阅消息权限请求结果:', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('订阅消息权限请求失败:', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 显示本地通知
   * @param {Object} options 通知选项
   */
  showLocalNotification(options) {
    const {
      title = '通知',
      content = '',
      icon = 'none',
      duration = 2000,
      mask = false
    } = options;

    wx.showToast({
      title: content || title,
      icon,
      duration,
      mask
    });
  }

  /**
   * 显示模态通知
   * @param {Object} options 通知选项
   */
  showModalNotification(options) {
    const {
      title = '通知',
      content = '',
      showCancel = false,
      confirmText = '确定',
      cancelText = '取消',
      success = () => {},
      fail = () => {}
    } = options;

    wx.showModal({
      title,
      content,
      showCancel,
      confirmText,
      cancelText,
      success,
      fail
    });
  }

  /**
   * 订单状态变更通知
   * @param {Object} orderInfo 订单信息
   * @param {string} newStatus 新状态
   * @param {string} userRole 用户角色
   */
  notifyOrderStatusChange(orderInfo, newStatus, userRole) {
    // 检查用户是否开启了订单通知
    if (!this.shouldShowNotification('orderStatus')) {
      console.log('📱 [通知] 订单通知已关闭，跳过显示');
      return;
    }

    const statusMessages = {
      accepted: {
        customer: '您的订单已被接单',
        acceptor: '您已成功接单'
      },
      in_progress: {
        customer: '服务已开始',
        acceptor: '您已开始服务'
      },
      completed: {
        customer: '订单已完成，请评价',
        acceptor: '订单已完成'
      },
      cancelled: {
        customer: '订单已取消',
        acceptor: '订单已被取消'
      }
    };

    const message = statusMessages[newStatus]?.[userRole] || '订单状态已更新';

    this.showLocalNotification({
      title: '订单通知',
      content: message,
      icon: 'success'
    });

    // 触发全局事件
    const app = getApp();
    if (app && app.$emit) {
      app.$emit('orderStatusChanged', {
        orderId: orderInfo._id,
        newStatus,
        userRole,
        message
      });
    }
  }

  /**
   * 新订单通知
   * @param {Object} orderInfo 订单信息
   */
  notifyNewOrder(orderInfo) {
    // 检查用户是否开启了新订单通知
    if (!this.shouldShowNotification('newOrder')) {
      console.log('📱 [通知] 新订单通知已关闭，跳过显示');
      return;
    }

    const message = `新订单：${orderInfo.title}`;

    this.showLocalNotification({
      title: '新订单',
      content: message,
      icon: 'none'
    });

    // 触发全局事件
    const app = getApp();
    if (app && app.$emit) {
      app.$emit('newOrderReceived', {
        orderId: orderInfo._id,
        title: orderInfo.title,
        reward: orderInfo.reward
      });
    }
  }

  /**
   * 聊天消息通知
   * @param {Object} messageInfo 消息信息
   */
  notifyChatMessage(messageInfo) {
    // 检查用户是否开启了聊天消息通知
    if (!this.shouldShowNotification('chatMessage')) {
      console.log('📱 [通知] 聊天消息通知已关闭，跳过显示');
      return;
    }

    const { senderName, content, chatRoomId } = messageInfo;
    const message = `${senderName}: ${content}`;

    this.showLocalNotification({
      title: '新消息',
      content: message,
      icon: 'none'
    });

    // 触发全局事件
    const app = getApp();
    if (app && app.$emit) {
      app.$emit('newChatMessage', {
        chatRoomId,
        senderName,
        content
      });
    }
  }

  /**
   * 系统通知
   * @param {Object} options 通知选项
   */
  notifySystem(options) {
    // 检查用户是否开启了系统通知
    if (!this.shouldShowNotification('system')) {
      console.log('📱 [通知] 系统通知已关闭，跳过显示');
      return;
    }

    const {
      title = '系统通知',
      content = '',
      type = 'info', // info, success, warning, error
      showModal = false
    } = options;

    if (showModal) {
      this.showModalNotification({
        title,
        content,
        showCancel: false
      });
    } else {
      const iconMap = {
        info: 'none',
        success: 'success',
        warning: 'none',
        error: 'none'
      };

      this.showLocalNotification({
        title,
        content,
        icon: iconMap[type] || 'none'
      });
    }
  }

  /**
   * 评价通知
   * @param {Object} evaluationInfo 评价信息
   */
  notifyEvaluation(evaluationInfo) {
    // 检查用户是否开启了评价通知
    if (!this.shouldShowNotification('evaluation')) {
      console.log('📱 [通知] 评价通知已关闭，跳过显示');
      return;
    }

    const { evaluatorName, orderTitle, rating } = evaluationInfo;
    const message = `${evaluatorName}对订单"${orderTitle}"给出了${rating}星评价`;

    this.showLocalNotification({
      title: '收到新评价',
      content: message,
      icon: 'success'
    });

    // 触发全局事件
    const app = getApp();
    if (app && app.$emit) {
      app.$emit('evaluationReceived', evaluationInfo);
    }
  }

  /**
   * 批量通知处理
   * @param {Array} notifications 通知数组
   */
  async processBatchNotifications(notifications) {
    for (const notification of notifications) {
      const { type, data } = notification;
      
      switch (type) {
        case 'orderStatus':
          this.notifyOrderStatusChange(data.orderInfo, data.newStatus, data.userRole);
          break;
        case 'newOrder':
          this.notifyNewOrder(data.orderInfo);
          break;
        case 'chatMessage':
          this.notifyChatMessage(data.messageInfo);
          break;
        case 'system':
          this.notifySystem(data);
          break;
      }
      
      // 添加延迟避免通知过于频繁
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  /**
   * 清除所有通知
   */
  clearAllNotifications() {
    wx.hideToast();
    wx.hideLoading();
  }

  /**
   * 检查通知权限
   * @returns {Promise<boolean>}
   */
  async checkNotificationPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          const hasPermission = res.authSetting['scope.subscribeMessage'] !== false;
          resolve(hasPermission);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }

  /**
   * 设置通知偏好
   * @param {Object} preferences 通知偏好设置
   */
  setNotificationPreferences(preferences) {
    const defaultPreferences = {
      orderStatus: true,
      newOrder: true,
      chatMessage: true,
      system: true,
      sound: true,
      vibrate: true
    };

    const finalPreferences = { ...defaultPreferences, ...preferences };
    wx.setStorageSync('notificationPreferences', finalPreferences);
  }

  /**
   * 获取通知偏好
   * @returns {Object}
   */
  getNotificationPreferences() {
    const defaultPreferences = {
      orderStatus: true,
      newOrder: true,
      chatMessage: true,
      system: true,
      sound: true,
      vibrate: true
    };

    const stored = wx.getStorageSync('notificationPreferences');
    return stored ? { ...defaultPreferences, ...stored } : defaultPreferences;
  }

  /**
   * 检查是否应该显示通知
   * @param {string} type 通知类型
   * @returns {boolean}
   */
  shouldShowNotification(type) {
    const preferences = this.getNotificationPreferences();
    return preferences[type] !== false;
  }
}

// 创建全局实例
const notificationManager = new NotificationManager();

export default notificationManager;
