/* 应用内通知组件样式 */
.notification-toast {
  position: fixed;
  top: 100rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 16rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  /* 添加微妙的边框提示用户可以交互 */
  border: 1rpx solid rgba(0, 212, 255, 0.3);
}

/* 悬停效果 */
.notification-toast:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 24rpx;
  gap: 20rpx;
}

/* 头像样式 */
.notification-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

/* 通知图标 */
.notification-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-text {
  font-size: 36rpx;
}

/* 通知文本 */
.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-body {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 关闭按钮 */
.notification-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: background 0.2s ease;
}

.notification-close:active {
  background: rgba(255, 255, 255, 0.2);
}

.close-icon {
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
  font-weight: bold;
}

/* 动画效果 */
.slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

.slide-out {
  animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideIn {
  from {
    transform: translateY(-100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100rpx);
    opacity: 0;
  }
}

/* 不同类型的通知样式 */
.notification-toast[data-type="message"] {
  background: rgba(34, 139, 34, 0.9);
}

.notification-toast[data-type="order"] {
  background: rgba(255, 140, 0, 0.9);
}

.notification-toast[data-type="system"] {
  background: rgba(30, 144, 255, 0.9);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .notification-toast {
    left: 20rpx;
    right: 20rpx;
  }
  
  .notification-content {
    padding: 20rpx;
    gap: 16rpx;
  }
  
  .notification-avatar,
  .notification-icon {
    width: 64rpx;
    height: 64rpx;
    border-radius: 32rpx;
  }
  
  .icon-text {
    font-size: 28rpx;
  }
}

/* 进度条样式 */
.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0 0 16rpx 16rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff 0%, #00ff88 100%);
  animation: progress-countdown linear forwards;
  transform-origin: left;
}

.progress-bar.paused {
  animation-play-state: paused;
}

@keyframes progress-countdown {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* 暂停提示样式 */
.pause-indicator {
  position: absolute;
  top: -40rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.pause-text {
  color: #00d4ff;
  font-size: 24rpx;
  font-weight: 500;
}
