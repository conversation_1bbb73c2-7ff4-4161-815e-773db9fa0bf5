/* 用户评价历史页面 - 优化科技主题样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e13 0%, #1a2332 50%, #0f1419 100%);
  position: relative;
}

/* 科技感装饰背景 - 优化 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background:
    radial-gradient(circle at 25% 75%, rgba(0, 212, 255, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 75% 25%, rgba(255, 107, 53, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.04) 0%, transparent 70%);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.08) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.08) 1rpx, transparent 1rpx);
  background-size: 120rpx 120rpx;
  animation: dataFlow 25s linear infinite;
}

@keyframes dataFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(120rpx, 120rpx); }
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 212, 255, 0.3);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 24rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 主要内容 */
.main-content {
  padding: 32rpx;
  position: relative;
  z-index: 1;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: rgba(0, 0, 0, 0.4);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
  backdrop-filter: blur(10rpx);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  box-shadow: 0 4rpx 16rpx rgba(0, 212, 255, 0.3);
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.tab-item.active .tab-text {
  color: #ffffff;
  text-shadow: 0 0 8rpx rgba(255, 255, 255, 0.5);
}

/* 统计卡片 */
.stats-card {
  background: rgba(0, 0, 0, 0.4);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.card-header {
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  text-shadow: 0 0 8rpx rgba(0, 212, 255, 0.5);
}

.stats-overview {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
  text-shadow: 0 0 12rpx rgba(0, 212, 255, 0.6);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 评分分布 */
.rating-distribution {
  margin-top: 32rpx;
}

.distribution-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 20rpx;
}

.distribution-bar {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.bar-label {
  width: 80rpx;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.bar-container {
  flex: 1;
  height: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  margin: 0 16rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
  border-radius: 8rpx;
  transition: width 0.6s ease;
}

.bar-count {
  width: 60rpx;
  text-align: right;
  font-size: 24rpx;
  color: var(--text-tertiary);
}

/* 角色统计 */
.role-stats {
  display: flex;
  gap: 16rpx;
}

.role-stat-card {
  flex: 1;
  background: rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(0, 212, 255, 0.2);
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}

.role-title {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 12rpx;
}

.role-rating {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.role-count {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

/* 评价列表 - 优化设计 */
.evaluation-list {
  position: relative;
  z-index: 1;
}

.evaluation-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1rpx solid rgba(0, 212, 255, 0.25);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  backdrop-filter: blur(15rpx);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.evaluation-item:active {
  transform: translateY(1rpx);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

.eval-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.role-badge {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.8) 0%, rgba(0, 150, 200, 0.8) 100%);
  color: #ffffff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.time-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.5);
  letter-spacing: 0.3rpx;
}

.eval-rating {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.stars {
  font-size: 32rpx;
  color: #ffd700;
  margin-right: 12rpx;
  text-shadow: 0 0 8rpx rgba(255, 215, 0, 0.5);
}

.rating-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.eval-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.eval-tag {
  background: rgba(0, 255, 136, 0.2);
  color: var(--accent-color);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  border: 1rpx solid rgba(0, 255, 136, 0.3);
}

.eval-content {
  margin-top: 16rpx;
}

.content-text {
  font-size: 26rpx;
  color: var(--text-primary);
  line-height: 1.6;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 32rpx;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-tertiary);
}
