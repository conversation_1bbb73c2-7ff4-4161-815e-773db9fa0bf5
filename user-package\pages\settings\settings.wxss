/* pages/user/settings/settings.wxss */

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #1a1a1a;
  padding-top: env(safe-area-inset-top);
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.back-icon {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: bold;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
}

.settings-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx 0;
}

/* 确保自定义导航栏页面有正确的顶部间距 */
.settings-container.page-with-custom-nav {
  padding-top: calc(44px + env(safe-area-inset-top) + 20rpx);
}

.settings-group {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 20rpx 20rpx 20rpx;
}

.group-title {
  padding: 30rpx 30rpx 20rpx 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.2s;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f8f8;
}

.setting-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  border-radius: 8rpx;
}

.setting-text {
  font-size: 32rpx;
  color: #333333;
}

.setting-action {
  display: flex;
  align-items: center;
}

.setting-status {
  font-size: 28rpx;
  margin-right: 16rpx;
}

.setting-status.verified {
  color: #52c41a;
  font-weight: 500;
}

.setting-status.unverified {
  color: #ff4d4f;
  font-weight: 500;
}

.setting-desc {
  font-size: 28rpx;
  color: #999999;
  margin-right: 16rpx;
}

.setting-arrow {
  font-size: 32rpx;
  color: #cccccc;
  font-weight: bold;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}

/* 退出登录按钮 */
.logout-section {
  padding: 40rpx 20rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background-color: #ff4d4f;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:active {
  background-color: #d9363e;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .settings-group {
    margin: 0 10rpx 20rpx 10rpx;
  }
  
  .setting-item {
    padding: 24rpx 20rpx;
  }
  
  .group-title {
    padding: 24rpx 20rpx 16rpx 20rpx;
    font-size: 30rpx;
  }
  
  .setting-text {
    font-size: 30rpx;
  }
}
