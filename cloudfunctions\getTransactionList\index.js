// 获取交易记录云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { page = 1, pageSize = 20, type, startDate, endDate } = event;

  try {
    // 查找用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 构建查询条件
    let whereCondition = {
      userId: user._id
    };

    // 按类型筛选
    if (type && type !== 'all') {
      whereCondition.type = type;
    }

    // 按时间范围筛选
    if (startDate && endDate) {
      whereCondition.createTime = db.command.and([
        db.command.gte(new Date(startDate)),
        db.command.lte(new Date(endDate))
      ]);
    }

    // 查询交易记录
    const transactionResult = await db.collection('transactions')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();

    // 获取关联的订单、充值、提现信息
    const transactions = await Promise.all(
      transactionResult.data.map(async (transaction) => {
        let relatedInfo = null;

        // 获取订单信息
        if (transaction.orderId) {
          const orderResult = await db.collection('orders').doc(transaction.orderId).get();
          if (orderResult.data) {
            relatedInfo = {
              type: 'order',
              orderNo: orderResult.data.orderNo,
              serviceType: orderResult.data.serviceType
            };
          }
        }

        // 获取充值信息
        if (transaction.rechargeId) {
          const rechargeResult = await db.collection('recharges').doc(transaction.rechargeId).get();
          if (rechargeResult.data) {
            relatedInfo = {
              type: 'recharge',
              rechargeNo: rechargeResult.data.rechargeNo,
              paymentMethod: rechargeResult.data.paymentMethod
            };
          }
        }

        // 获取提现信息
        if (transaction.withdrawId) {
          const withdrawResult = await db.collection('withdraws').doc(transaction.withdrawId).get();
          if (withdrawResult.data) {
            relatedInfo = {
              type: 'withdraw',
              withdrawNo: withdrawResult.data.withdrawNo,
              withdrawMethod: withdrawResult.data.withdrawMethod
            };
          }
        }

        return {
          ...transaction,
          createTimeText: formatTime(transaction.createTime),
          updateTimeText: transaction.updateTime ? formatTime(transaction.updateTime) : '',
          typeText: getTypeText(transaction.type),
          statusText: getStatusText(transaction.status),
          relatedInfo
        };
      })
    );

    // 计算统计信息
    const stats = await calculateTransactionStats(user._id, type, startDate, endDate);

    return {
      success: true,
      data: {
        list: transactions,
        hasMore: transactions.length === pageSize,
        stats
      }
    };
  } catch (error) {
    console.error('获取交易记录失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 计算交易统计
async function calculateTransactionStats(userId, type, startDate, endDate) {
  try {
    let whereCondition = { userId };

    if (type && type !== 'all') {
      whereCondition.type = type;
    }

    if (startDate && endDate) {
      whereCondition.createTime = db.command.and([
        db.command.gte(new Date(startDate)),
        db.command.lte(new Date(endDate))
      ]);
    }

    // 并行查询各种类型的交易统计
    const [
      totalIncome,
      totalExpense,
      rechargeAmount,
      withdrawAmount,
      orderIncome,
      orderExpense,
      // 添加计数查询
      rechargeCount,
      withdrawCount,
      incomeCount,
      paymentCount
    ] = await Promise.all([
      // 总收入
      db.collection('transactions').where({
        ...whereCondition,
        amount: db.command.gt(0),
        status: 'completed'
      }).get(),

      // 总支出
      db.collection('transactions').where({
        ...whereCondition,
        amount: db.command.lt(0),
        status: 'completed'
      }).get(),

      // 充值金额
      db.collection('transactions').where({
        ...whereCondition,
        type: 'recharge',
        status: 'completed'
      }).get(),

      // 提现金额
      db.collection('transactions').where({
        ...whereCondition,
        type: 'withdraw',
        status: 'completed'
      }).get(),

      // 订单收入
      db.collection('transactions').where({
        ...whereCondition,
        type: 'income',
        status: 'completed'
      }).get(),

      // 订单支出
      db.collection('transactions').where({
        ...whereCondition,
        type: 'payment',
        status: 'completed'
      }).get(),

      // 充值计数
      db.collection('transactions').where({
        userId,
        type: 'recharge',
        status: 'completed'
      }).count(),

      // 提现计数
      db.collection('transactions').where({
        userId,
        type: 'withdraw',
        status: 'completed'
      }).count(),

      // 收入计数
      db.collection('transactions').where({
        userId,
        type: 'income',
        status: 'completed'
      }).count(),

      // 支出计数
      db.collection('transactions').where({
        userId,
        type: 'payment',
        status: 'completed'
      }).count()
    ]);

    return {
      totalIncome: totalIncome.data.reduce((sum, t) => sum + t.amount, 0),
      totalExpense: Math.abs(totalExpense.data.reduce((sum, t) => sum + t.amount, 0)),
      rechargeAmount: rechargeAmount.data.reduce((sum, t) => sum + t.amount, 0),
      withdrawAmount: Math.abs(withdrawAmount.data.reduce((sum, t) => sum + t.amount, 0)),
      orderIncome: orderIncome.data.reduce((sum, t) => sum + t.amount, 0),
      orderExpense: Math.abs(orderExpense.data.reduce((sum, t) => sum + t.amount, 0)),
      transactionCount: totalIncome.data.length + totalExpense.data.length,
      // 添加各类型的计数
      rechargeCount: rechargeCount.total,
      withdrawCount: withdrawCount.total,
      incomeCount: incomeCount.total,
      paymentCount: paymentCount.total
    };
  } catch (error) {
    console.error('计算交易统计失败:', error);
    return {
      totalIncome: 0,
      totalExpense: 0,
      rechargeAmount: 0,
      withdrawAmount: 0,
      orderIncome: 0,
      orderExpense: 0,
      transactionCount: 0
    };
  }
}

// 格式化时间
function formatTime(dateStr) {
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}`;
}

// 获取交易类型文本
function getTypeText(type) {
  const typeMap = {
    'recharge': '充值',
    'withdraw': '提现',
    'payment': '支付',
    'income': '收入',
    'refund': '退款'
  };
  
  return typeMap[type] || type;
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消',
    'frozen': '已冻结',
    'refunded': '已退款'
  };
  
  return statusMap[status] || status;
}
