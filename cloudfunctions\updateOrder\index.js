// 更新订单云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 清理文本内容，去除多余空格和换行符
function cleanTextContent(text) {
  if (!text) return '';
  // 将换行符替换为空格，然后去除多余的空格
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { orderId, title, content, reward, platformType, duration, rounds, serviceType, tags, orderType, scheduledDate, scheduledTime } = event;

  console.log('🔍 [updateOrder云函数] 接收到的事件数据:', event);
  console.log('🔍 [updateOrder云函数] platformType字段:', platformType);

  try {
    // 验证必填字段
    if (!orderId) {
      return {
        success: false,
        error: '订单ID不能为空'
      };
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 查找订单
    const orderResult = await db.collection('orders').doc(orderId).get();

    if (!orderResult.data) {
      return {
        success: false,
        error: '订单不存在'
      };
    }

    const order = orderResult.data;

    // 检查用户权限 - 只有订单发布者可以修改订单
    if (order.customerId !== user._id) {
      return {
        success: false,
        error: '您没有权限修改此订单'
      };
    }

    // 检查订单状态 - 只有待接单状态的订单可以修改
    if (order.status !== 'pending') {
      return {
        success: false,
        error: '只有待接单状态的订单可以修改'
      };
    }

    // 构建更新数据
    const updateData = {
      updateTime: new Date()
    };

    // 更新基本信息
    if (title !== undefined) {
      updateData.title = cleanTextContent(title);
    }
    if (content !== undefined) {
      updateData.content = cleanTextContent(content);
      updateData.requirements = cleanTextContent(content); // 兼容字段
    }
    if (reward !== undefined) {
      const totalAmount = parseFloat(reward);
      updateData.reward = totalAmount;
      updateData.pricing = {
        totalAmount: totalAmount,
        accepterAmount: totalAmount * 0.8, // 80%给接单者
        platformAmount: totalAmount * 0.2   // 20%平台费用
      };
    }
    // 处理平台类型
    if (platformType !== undefined) {
      updateData.platformType = platformType;
      console.log('🔍 [updateOrder云函数] 设置platformType:', platformType);
    } else {
      console.log('⚠️ [updateOrder云函数] platformType为undefined，不更新此字段');
    }
    // 处理服务类型和时间信息
    if (serviceType !== undefined) {
      updateData.serviceType = serviceType;
    }
    if (duration !== undefined) {
      updateData.duration = serviceType === 'rounds' ? null : parseFloat(duration);
    }
    if (rounds !== undefined) {
      updateData.rounds = serviceType === 'rounds' ? parseInt(rounds) : null;
    }
    if (tags !== undefined) {
      updateData.tags = tags;
    }
    if (orderType !== undefined) {
      updateData.orderType = orderType;
    }
    if (scheduledDate !== undefined) {
      updateData.scheduledDate = scheduledDate;
    }
    if (scheduledTime !== undefined) {
      updateData.scheduledTime = scheduledTime;
    }

    console.log('🔍 [updateOrder云函数] 最终更新数据:', updateData);

    // 更新订单
    await db.collection('orders').doc(orderId).update({
      data: updateData
    });

    console.log('✅ [updateOrder云函数] 订单更新成功');

    return {
      success: true,
      message: '订单更新成功',
      data: {
        orderId: orderId
      }
    };
  } catch (error) {
    console.error('更新订单失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
