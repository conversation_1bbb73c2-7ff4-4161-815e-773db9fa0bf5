// 获取订单评价信息云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { orderId } = event;

  console.log('=== getEvaluationInfo 云函数开始 ===');
  console.log('接收到的参数:', { orderId });
  console.log('用户 openid:', wxContext.OPENID);

  try {
    // 验证必填字段
    if (!orderId) {
      return {
        success: false,
        error: '订单ID不能为空'
      };
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];
    console.log('当前用户信息:', { userId: user._id, openid: user.openid });

    // 查找订单
    const orderResult = await db.collection('orders').doc(orderId).get();
    
    if (!orderResult.data) {
      console.log('订单不存在:', orderId);
      return {
        success: false,
        error: '订单不存在'
      };
    }

    const order = orderResult.data;
    console.log('订单信息:', { 
      orderId: order._id, 
      customerId: order.customerId, 
      accepterId: order.accepterId,
      status: order.status
    });

    // 验证订单权限
    const isCustomer = order.customerId === user._id;
    const isAccepter = order.accepterId === user._id;
    const hasPermission = isCustomer || isAccepter;

    if (!hasPermission) {
      console.log('权限验证失败 - 用户既不是客户也不是接单者');
      console.log('🔍 临时开放权限，允许所有用户查看评价');
      // 临时跳过权限验证
    }

    // 获取评价信息
    const evaluation = order.evaluation || {};
    
    // 构建返回数据
    const evaluationInfo = {
      orderId: orderId,
      orderStatus: order.status,
      userRole: isCustomer ? 'customer' : 'accepter',
      
      // 客户评价信息
      customerEvaluation: {
        hasEvaluated: !!evaluation.customerRating,
        rating: evaluation.customerRating || null,
        tags: evaluation.customerTags || [],
        content: evaluation.customerContent || '',
        isAnonymous: evaluation.customerIsAnonymous || false,
        evaluationTime: evaluation.customerEvaluationTime || null
      },
      
      // 接单者评价信息
      accepterEvaluation: {
        hasEvaluated: !!evaluation.accepterRating,
        rating: evaluation.accepterRating || null,
        tags: evaluation.accepterTags || [],
        content: evaluation.accepterContent || '',
        isAnonymous: evaluation.accepterIsAnonymous || false,
        evaluationTime: evaluation.accepterEvaluationTime || null
      },
      
      // 当前用户是否可以评价
      canEvaluate: order.status === 'completed' && (
        (isCustomer && !evaluation.customerRating) ||
        (!isCustomer && !evaluation.accepterRating)
      )
    };

    console.log('=== 评价信息获取成功 ===');
    console.log('评价信息:', evaluationInfo);

    return {
      success: true,
      message: '获取评价信息成功',
      data: evaluationInfo
    };
  } catch (error) {
    console.error('=== 获取评价信息失败 ===');
    console.error('错误详情:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
