<!-- 标签映射脚本 -->
<wxs module="tagUtils">
  var tagMap = {
    'high_winrate': '高胜率',
    'voice_chat': '可语音',
    'humorous': '幽默风趣',
    'professional': '专业指导',
    'newbie_friendly': '新手友好',
    'urgent': '急单'
  };

  function getTagLabel(tagValue) {
    return tagMap[tagValue] || tagValue;
  }

  function mapTags(tags) {
    if (!tags || tags.length === 0) {
      return [];
    }
    var result = [];
    for (var i = 0; i < tags.length; i++) {
      result.push(getTagLabel(tags[i]));
    }
    return result;
  }

  function truncateText(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  function getOrderType(orderData) {
    // 首先检查 orderType 字段
    if (orderData.orderType === 'scheduled') {
      return '预约订单';
    }
    if (orderData.orderType === 'immediate') {
      return '即时订单';
    }

    // 如果没有 orderType 字段，检查是否有有效的预约时间
    if (orderData.scheduledDate && orderData.scheduledDate.trim() &&
        orderData.scheduledTime && orderData.scheduledTime.trim()) {
      return '预约订单';
    }

    // 默认为即时订单
    return '即时订单';
  }

  function getPlatformType(orderData) {
    // 检查平台类型
    if (orderData.platformType === 'mobile') {
      return '平台类型：手游';
    }

    // 默认为电脑
    return '平台类型：电脑';
  }

  module.exports = {
    getTagLabel: getTagLabel,
    mapTags: mapTags,
    truncateText: truncateText,
    getOrderType: getOrderType,
    getPlatformType: getPlatformType
  };
</wxs>

<!-- 三角洲行动战术任务订单卡片 -->
<view class="order-card" bindtap="onCardTap" data-id="{{orderData._id}}">
  <!-- 左侧装饰条 -->
  <view class="tactical-strip"></view>

  <!-- 信息展示区 -->
  <view class="info-panel">
    <!-- 顶部标题区域 -->
    <view class="card-header">
      <view class="mission-title">
        <text class="skull-icon">💀</text>
        <text class="title-text">{{displayTitle}}</text>
      </view>

      <view class="mission-price">¥{{orderData.pricing.totalAmount || orderData.totalAmount || orderData.reward || 5}}</view>
    </view>

    <!-- 左侧信息列 -->
    <view class="info-item">
      <view class="info-icon">🕐</view>
      <view class="info-label" wx:if="{{orderData.serviceType === 'rounds' || (orderData.rounds && !orderData.serviceType)}}">局数：</view>
      <view class="info-label" wx:else>时长：</view>
      <view class="info-value" wx:if="{{orderData.serviceType === 'rounds' || (orderData.rounds && !orderData.serviceType)}}">{{orderData.rounds || orderData.requirements.rounds || 5}}局</view>
      <view class="info-value" wx:else>{{orderData.duration || orderData.requirements.duration || 1}}小时</view>
    </view>

    <!-- 右侧信息列 - 订单类型 -->
    <view class="info-item order-type-item">
      <view class="order-type-tag">{{tagUtils.getOrderType(orderData)}}</view>
    </view>

    <!-- 任务需求区域 -->
    <view class="requirements">
      <view>
        任务需求: <text class="highlight">{{tagUtils.truncateText(orderData.content || orderData.requirements.description || '暂无需求描述', 20)}}</text>
      </view>
    </view>

    <!-- 订单标签区域 -->
    <view class="order-tags-container">
      <view class="order-tags">
        <view class="tag" wx:for="{{tagUtils.mapTags(orderData.tags || orderData.displayTags)}}" wx:key="index">
          {{item}}
        </view>
      </view>

      <!-- 发布时间区域 -->
      <view class="publish-time">
        <view class="time-icon">📅</view>
        <view class="time-text">{{formattedCreateTime}}</view>
      </view>
    </view>


  </view>

  <!-- 右侧操作区 -->
  <view class="action-panel">
    <!-- 状态显示区 -->
    <view class="status-display">
      <view>{{orderData.statusText || '待接单'}}</view>
      <view class="status-tag">{{tagUtils.getPlatformType(orderData)}}</view>
    </view>

    <!-- 操作按钮区 -->
    <view class="action-buttons">
      <!-- 如果是订单发布者 -->
      <block wx:if="{{orderData.isOwner}}">
        <!-- 待接单状态：显示修改和取消按钮 -->
        <block wx:if="{{orderData.status === 'pending' || orderData.status === 'waiting_match'}}">
          <view class="action-btn btn-modify"
                catchtap="onEditOrder"
                data-id="{{orderData._id}}">
            <text class="btn-icon">✏️</text>
            <text>修改订单</text>
          </view>

          <view class="action-btn btn-cancel"
                catchtap="onCancelOrder"
                data-id="{{orderData._id}}">
            <text class="btn-icon">❌</text>
            <text>取消订单</text>
          </view>
        </block>

        <!-- 已接单状态：只显示取消按钮 -->
        <block wx:elif="{{orderData.status === 'accepted'}}">
          <view class="action-btn btn-contact"
                catchtap="onContactAccepter"
                data-id="{{orderData._id}}">
            <text class="btn-icon">💬</text>
            <text>联系接单者</text>
          </view>

          <view class="action-btn btn-cancel"
                catchtap="onCancelOrder"
                data-id="{{orderData._id}}">
            <text class="btn-icon">❌</text>
            <text>取消订单</text>
          </view>
        </block>

        <!-- 进行中状态：显示聊天按钮 -->
        <block wx:elif="{{orderData.status === 'in_progress'}}">
          <view class="action-btn btn-chat"
                catchtap="onEnterChat"
                data-id="{{orderData._id}}">
            <text class="btn-icon">💬</text>
            <text>进入聊天</text>
          </view>

          <view class="action-btn btn-detail"
                catchtap="onViewDetail"
                data-id="{{orderData._id}}">
            <text class="btn-icon">📋</text>
            <text>订单详情</text>
          </view>
        </block>

        <!-- 待确认状态：根据用户角色显示不同按钮 -->
        <block wx:elif="{{orderData.status === 'proof_submitted'}}">
          <view class="action-btn btn-confirm"
                catchtap="onViewDetail"
                data-id="{{orderData._id}}">
            <text class="btn-icon">📋</text>
            <text>查看证明</text>
          </view>

          <view class="action-btn btn-detail"
                catchtap="onViewDetail"
                data-id="{{orderData._id}}">
            <text class="btn-icon">📋</text>
            <text>订单详情</text>
          </view>
        </block>

        <!-- 已完成状态：显示评价和查看详情按钮 -->
        <block wx:elif="{{orderData.status === 'completed'}}">
          <view class="action-btn btn-evaluate"
                catchtap="onEvaluateOrder"
                data-id="{{orderData._id}}"
                wx:if="{{!orderData.evaluation || !orderData.evaluation.customerRating}}">
            <text class="btn-icon">⭐</text>
            <text>评价订单</text>
          </view>

          <view class="action-btn btn-detail"
                catchtap="onViewDetail"
                data-id="{{orderData._id}}">
            <text class="btn-icon">📋</text>
            <text>订单详情</text>
          </view>
        </block>

        <!-- 其他状态：只显示查看详情 -->
        <block wx:else>
          <view class="action-btn btn-detail"
                catchtap="onViewDetail"
                data-id="{{orderData._id}}">
            <text class="btn-icon">📋</text>
            <text>订单详情</text>
          </view>
        </block>
      </block>

      <!-- 如果不是订单发布者 -->
      <block wx:else>
        <view class="action-btn btn-detail"
              catchtap="onViewDetail"
              data-id="{{orderData._id}}">
          <text class="btn-icon">📋</text>
          <text>订单详情</text>
        </view>

        <view class="action-btn btn-grab"
              catchtap="onGrabOrder"
              data-id="{{orderData._id}}"
              wx:if="{{orderData.status === 'pending'}}">
          <text class="btn-icon">🎯</text>
          <text>立即抢单</text>
        </view>
      </block>
    </view>
  </view>


</view>
