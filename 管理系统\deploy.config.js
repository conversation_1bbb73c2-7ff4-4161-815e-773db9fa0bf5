// 部署配置文件
const deployConfig = {
  // 环境配置
  environments: {
    development: {
      name: '开发环境',
      apiUrl: 'https://cloud1-9gsj7t48183e5a9f.tcb-api.tencentcloudapi.com/web',
      cloudEnv: 'cloud1-9gsj7t48183e5a9f',
      buildCommand: 'npm run dev',
      port: 3001,
      publicPath: '/',
      enableHMR: true,
      enableSourceMap: true,
      enableMock: true
    },
    
    staging: {
      name: '测试环境',
      apiUrl: 'https://cloud1-9gsj7t48183e5a9f.tcb-api.tencentcloudapi.com/web',
      cloudEnv: 'cloud1-9gsj7t48183e5a9f',
      buildCommand: 'npm run build:staging',
      publicPath: '/admin/',
      enableHMR: false,
      enableSourceMap: true,
      enableMock: false
    },
    
    production: {
      name: '生产环境',
      apiUrl: 'https://cloud1-9gsj7t48183e5a9f.tcb-api.tencentcloudapi.com/web',
      cloudEnv: 'cloud1-9gsj7t48183e5a9f',
      buildCommand: 'npm run build',
      publicPath: '/admin/',
      enableHMR: false,
      enableSourceMap: false,
      enableMock: false
    }
  },

  // 构建配置
  build: {
    // 输出目录
    outDir: 'dist',
    
    // 资源目录
    assetsDir: 'assets',
    
    // 代码分割
    chunkSizeWarningLimit: 1000,
    
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    
    // Rollup 配置
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          router: ['react-router-dom'],
          utils: ['axios', 'dayjs']
        }
      }
    }
  },

  // 服务器配置
  server: {
    host: '0.0.0.0',
    port: 3001,
    strictPort: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'https://cloud1-9gsj7t48183e5a9f.tcb-api.tencentcloudapi.com/web',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },

  // 预览服务器配置
  preview: {
    host: '0.0.0.0',
    port: 4173,
    strictPort: true,
    cors: true
  },

  // 优化配置
  optimization: {
    // 依赖预构建
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'antd',
        'react-router-dom',
        'axios'
      ]
    },
    
    // 构建缓存
    cacheDir: 'node_modules/.vite',
    
    // CSS 代码分割
    cssCodeSplit: true,
    
    // 生成 manifest
    manifest: true
  },

  // 部署脚本
  deployScripts: {
    // 构建前检查
    preBuild: [
      'echo "🔍 检查代码质量..."',
      'npm run lint',
      'echo "🧪 运行测试..."',
      'npm run test',
      'echo "📦 清理旧构建文件..."',
      'rm -rf dist'
    ],
    
    // 构建后处理
    postBuild: [
      'echo "📊 分析构建结果..."',
      'npm run analyze',
      'echo "🗜️ 压缩静态资源..."',
      'npm run compress',
      'echo "✅ 构建完成！"'
    ],
    
    // 部署到云端
    deploy: [
      'echo "🚀 开始部署到云端..."',
      'npm run upload',
      'echo "🔄 更新CDN缓存..."',
      'npm run cdn:refresh',
      'echo "✅ 部署完成！"'
    ]
  },

  // 监控配置
  monitoring: {
    // 性能监控
    performance: {
      enabled: true,
      thresholds: {
        fcp: 2000,  // First Contentful Paint
        lcp: 3000,  // Largest Contentful Paint
        fid: 100,   // First Input Delay
        cls: 0.1    // Cumulative Layout Shift
      }
    },
    
    // 错误监控
    errorTracking: {
      enabled: true,
      dsn: process.env.SENTRY_DSN || '',
      environment: process.env.NODE_ENV || 'development'
    },
    
    // 用户行为分析
    analytics: {
      enabled: true,
      trackingId: process.env.GA_TRACKING_ID || ''
    }
  },

  // 安全配置
  security: {
    // CSP 配置
    contentSecurityPolicy: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      'style-src': ["'self'", "'unsafe-inline'"],
      'img-src': ["'self'", 'data:', 'https:'],
      'connect-src': ["'self'", 'https://cloud1-9gsj7t48183e5a9f.tcb-api.tencentcloudapi.com']
    },
    
    // HTTPS 重定向
    httpsRedirect: true,
    
    // 安全头
    securityHeaders: {
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    }
  },

  // 缓存策略
  caching: {
    // 静态资源缓存
    staticAssets: {
      maxAge: 31536000, // 1年
      immutable: true
    },
    
    // HTML 缓存
    html: {
      maxAge: 0,
      noCache: true
    },
    
    // API 缓存
    api: {
      maxAge: 300, // 5分钟
      staleWhileRevalidate: 600 // 10分钟
    }
  }
};

// 根据环境获取配置
function getConfig(env = 'development') {
  const baseConfig = deployConfig.environments[env];
  if (!baseConfig) {
    throw new Error(`未找到环境配置: ${env}`);
  }
  
  return {
    ...deployConfig,
    current: baseConfig
  };
}

// 验证配置
function validateConfig(config) {
  const required = ['apiUrl', 'cloudEnv', 'buildCommand'];
  const missing = required.filter(key => !config[key]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的配置项: ${missing.join(', ')}`);
  }
  
  return true;
}

module.exports = {
  deployConfig,
  getConfig,
  validateConfig
};
