import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  User, 
  Wallet, 
  Star, 
  MessageSquare,
  Clock
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface Activity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
}

interface RecentActivitiesProps {
  activities: Activity[];
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'order':
      return <ShoppingCart className="h-4 w-4" />;
    case 'user':
      return <User className="h-4 w-4" />;
    case 'payment':
      return <Wallet className="h-4 w-4" />;
    case 'evaluation':
      return <Star className="h-4 w-4" />;
    case 'message':
      return <MessageSquare className="h-4 w-4" />;
    default:
      return <Clock className="h-4 w-4" />;
  }
};

const getActivityColor = (type: string) => {
  switch (type) {
    case 'order':
      return 'bg-blue-50 text-blue-600 border-blue-200';
    case 'user':
      return 'bg-green-50 text-green-600 border-green-200';
    case 'payment':
      return 'bg-yellow-50 text-yellow-600 border-yellow-200';
    case 'evaluation':
      return 'bg-purple-50 text-purple-600 border-purple-200';
    case 'message':
      return 'bg-pink-50 text-pink-600 border-pink-200';
    default:
      return 'bg-gray-50 text-gray-600 border-gray-200';
  }
};

export default function RecentActivities({ activities }: RecentActivitiesProps) {
  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors">
          <div className={`p-2 rounded-full border ${getActivityColor(activity.type)}`}>
            {getActivityIcon(activity.type)}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 mb-1">
              {activity.description}
            </p>
            <p className="text-xs text-gray-500 flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {formatDistanceToNow(new Date(activity.timestamp), { 
                addSuffix: true,
                locale: zhCN 
              })}
            </p>
          </div>
          <Badge variant="secondary" className="text-xs">
            {activity.type === 'order' && '订单'}
            {activity.type === 'user' && '用户'}
            {activity.type === 'payment' && '支付'}
            {activity.type === 'evaluation' && '评价'}
            {activity.type === 'message' && '系统'}
            {!['order', 'user', 'payment', 'evaluation', 'message'].includes(activity.type) && '其他'}
          </Badge>
        </div>
      ))}
      
      {activities.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>暂无最近活动</p>
        </div>
      )}
    </div>
  );
}