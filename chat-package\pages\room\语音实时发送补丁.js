// 语音实时发送补丁 - 直接替换关键方法
// 使用方法：在 room.js 的 onLoad 中调用 applyRealtimePatch(this)

const voiceOptimizer = require('../../utils/voice-optimizer');
const API = require('../../utils/api');
const formatTime = require('../../utils/formatTime');

function applyRealtimePatch(pageInstance) {
  console.log('🚀 [实时补丁] 应用语音实时发送补丁');
  
  // 替换语音发送方法
  pageInstance.sendVoiceMessage = async function(voicePath, duration) {
    console.log('⚡ [实时发送] 开始真正实时语音发送');
    
    try {
      // 🎯 关键：立即显示消息，无任何加载状态
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const durationInSeconds = Math.ceil(duration / 1000);
      
      // 立即创建并显示消息
      const instantMessage = {
        _id: messageId,
        type: 'voice',
        content: voicePath, // 先使用本地路径
        duration: durationInSeconds,
        isSelf: true,
        createTime: new Date(),
        formattedTime: formatTime(new Date()),
        senderInfo: this.data.userInfo,
        // 🚀 关键：完全不显示任何加载状态
        isUploading: false,
        isTemp: false,
        isFailed: false,
        isRealtime: true
      };

      // 立即添加到消息列表
      const messageList = [...this.data.messageList, instantMessage];
      this.setData({ messageList });
      this.scrollToBottom();
      
      console.log('📱 [实时显示] 消息立即显示完成，用户感知延迟: 0ms');
      
      // 🔄 后台静默处理
      this.silentBackgroundProcess(voicePath, duration, messageId);
      
    } catch (error) {
      console.error('❌ [实时发送] 发送失败:', error);
    }
  };
  
  // 添加静默后台处理方法
  pageInstance.silentBackgroundProcess = async function(voicePath, duration, messageId) {
    try {
      console.log('🔇 [静默处理] 开始后台上传，用户无感知');
      
      // 静默上传
      const uploadResult = await this.uploadVoiceToCloud(voicePath, duration);
      
      // 静默发送到服务器
      const result = await API.sendVoiceMessage(
        this.data.roomId,
        uploadResult.fileID,
        uploadResult.duration,
        this.data.userInfo
      );
      
      // 静默更新消息
      this.silentUpdateMessage(messageId, {
        _id: result._id,
        content: uploadResult.fileID,
        isRealtime: false
      });
      
      console.log('✅ [静默处理] 后台处理完成，用户完全无感知');
      
    } catch (error) {
      console.error('❌ [静默处理] 后台处理失败:', error);
      // 静默重试
      setTimeout(() => {
        this.silentBackgroundProcess(voicePath, duration, messageId);
      }, 2000);
    }
  };
  
  // 添加静默更新方法
  pageInstance.silentUpdateMessage = function(messageId, updates) {
    const messageList = this.data.messageList.map(msg => {
      if (msg._id === messageId) {
        return { ...msg, ...updates };
      }
      return msg;
    });
    
    this.setData({ messageList });
  };
  
  // 替换语音播放方法 - 支持本地和云端路径
  pageInstance.onVoicePlay = function(e) {
    const { src, messageId } = e.currentTarget.dataset;
    
    // 停止当前播放
    wx.stopVoice();
    
    // 更新播放状态
    this.updateVoicePlayingState(messageId, true);
    
    // 智能播放：自动识别本地路径和云端路径
    if (src.startsWith('http') || src.startsWith('cloud://')) {
      // 云端路径，下载后播放
      wx.downloadFile({
        url: src,
        success: (res) => {
          this.playVoiceFile(res.tempFilePath, messageId);
        },
        fail: (error) => {
          console.error('❌ [语音播放] 下载失败:', error);
          this.updateVoicePlayingState(messageId, false);
        }
      });
    } else {
      // 本地路径，直接播放
      this.playVoiceFile(src, messageId);
    }
  };
  
  // 添加播放语音文件方法
  pageInstance.playVoiceFile = function(filePath, messageId) {
    wx.playVoice({
      filePath: filePath,
      success: () => {
        console.log('🎵 [语音播放] 播放完成');
        this.updateVoicePlayingState(messageId, false);
      },
      fail: (error) => {
        console.error('❌ [语音播放] 播放失败:', error);
        this.updateVoicePlayingState(messageId, false);
      }
    });
  };
  
  // 添加更新播放状态方法
  pageInstance.updateVoicePlayingState = function(messageId, isPlaying) {
    const messageList = this.data.messageList.map(msg => {
      if (msg._id === messageId) {
        return { ...msg, isPlaying };
      } else {
        return { ...msg, isPlaying: false };
      }
    });
    
    this.setData({ messageList });
  };
  
  console.log('✅ [实时补丁] 语音实时发送补丁应用完成');
}

module.exports = {
  applyRealtimePatch
};