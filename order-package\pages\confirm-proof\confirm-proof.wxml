<!--客户确认订单完成页面-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-bg"></view>
      <view class="header-content">
        <text class="page-title">确认任务完成</text>
        <text class="page-subtitle">请查看接单者提交的完成证明</text>
      </view>
    </view>

    <!-- 订单信息卡片 -->
    <view class="order-card">
      <view class="card-header">
        <text class="order-title">{{orderInfo.title}}</text>
        <text class="order-no">订单号：{{orderInfo.orderNo}}</text>
      </view>
      <view class="order-info">
        <text class="info-item">服务金额：¥{{orderInfo.pricing.totalAmount}}</text>
        <text class="info-item">提交时间：{{formatTime(orderInfo.proofSubmitTime)}}</text>
      </view>
    </view>

    <!-- 自动确认倒计时 -->
    <view class="countdown-card" wx:if="{{countdown}}">
      <view class="countdown-icon">⏰</view>
      <view class="countdown-content">
        <text class="countdown-title">自动确认倒计时</text>
        <text class="countdown-time">{{countdown}}</text>
        <text class="countdown-tip">超时将自动确认并结算</text>
      </view>
    </view>

    <!-- 完成证明展示 -->
    <view class="proof-section">
      <view class="section-title">
        <text class="title-text">完成证明</text>
        <text class="title-icon">📋</text>
      </view>

      <!-- 图片证明 -->
      <view class="proof-images" wx:if="{{completionProof.images && completionProof.images.length > 0}}">
        <text class="proof-label">截图证明</text>
        <view class="image-grid">
          <view 
            class="image-item" 
            wx:for="{{completionProof.images}}" 
            wx:key="fileID"
            bindtap="previewImage"
            data-index="{{index}}"
          >
            <image 
              class="proof-image" 
              src="{{item.fileID}}" 
              mode="aspectFill"
            ></image>
            <view class="image-overlay">
              <text class="preview-text">点击预览</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 视频证明 -->
      <view class="proof-video" wx:if="{{completionProof.video}}">
        <text class="proof-label">录屏证明</text>
        <view class="video-container">
          <video 
            class="proof-video-player"
            src="{{completionProof.video.fileID}}"
            controls
            bindplay="playVideo"
          ></video>
          <view class="video-info">
            <text class="video-size">大小: {{completionProof.video.sizeText}}</text>
            <text class="video-duration" wx:if="{{completionProof.video.duration}}">时长: {{completionProof.video.durationText}}</text>
          </view>
        </view>
      </view>

      <!-- 文字说明 -->
      <view class="proof-description" wx:if="{{completionProof.description}}">
        <text class="proof-label">文字说明</text>
        <view class="description-content">
          <text class="description-text">{{completionProof.description}}</text>
        </view>
      </view>

      <!-- 无证明提示 -->
      <view class="no-proof" wx:if="{{!completionProof || (!completionProof.images || completionProof.images.length === 0) && !completionProof.video}}">
        <text class="no-proof-text">暂无完成证明</text>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <view class="action-tips">
        <text class="tip-text">请仔细查看完成证明，确认任务是否按要求完成</text>
      </view>
      
      <view class="button-group">
        <!-- 确认完成按钮 -->
        <button 
          class="action-btn confirm-btn cyber-btn primary"
          bindtap="confirmOrder"
          disabled="{{confirming || disputing}}"
        >
          <text class="btn-text" wx:if="{{!confirming}}">确认完成</text>
          <text class="btn-text" wx:else>确认中...</text>
          <view class="btn-glow" wx:if="{{!confirming}}"></view>
        </button>

        <!-- 申请仲裁按钮 -->
        <button 
          class="action-btn dispute-btn cyber-btn secondary"
          bindtap="disputeOrder"
          disabled="{{confirming || disputing}}"
        >
          <text class="btn-text" wx:if="{{!disputing}}">申请仲裁</text>
          <text class="btn-text" wx:else>提交中...</text>
        </button>
      </view>

      <view class="action-notes">
        <text class="note-item">• 确认完成后将立即进行资金结算</text>
        <text class="note-item">• 申请仲裁后将由平台客服介入处理</text>
        <text class="note-item">• 超时未操作将自动确认完成</text>
      </view>
    </view>
  </view>
</view>
