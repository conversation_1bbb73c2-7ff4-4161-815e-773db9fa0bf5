import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth, type Permission, type Role } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  role?: Role;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({ 
  children, 
  permission, 
  permissions, 
  role, 
  fallback 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, hasPermission, hasAnyPermission, hasRole } = useAuth();
  const location = useLocation();

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>正在验证权限...</span>
        </div>
      </div>
    );
  }

  // 未登录，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 检查权限
  let hasAccess = true;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    hasAccess = hasAnyPermission(permissions);
  } else if (role) {
    hasAccess = hasRole(role);
  }

  // 权限不足
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">权限不足</h1>
          <p className="text-gray-600 mb-4">您没有访问此页面的权限</p>
          <button
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            返回上一页
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// 权限检查组件 - 用于在页面内部隐藏/显示特定功能
interface PermissionCheckProps {
  permission?: Permission;
  permissions?: Permission[];
  role?: Role;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionCheck({ 
  permission, 
  permissions, 
  role, 
  fallback = null, 
  children 
}: PermissionCheckProps) {
  const { hasPermission, hasAnyPermission, hasRole } = useAuth();

  let hasAccess = true;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    hasAccess = hasAnyPermission(permissions);
  } else if (role) {
    hasAccess = hasRole(role);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

// 高阶组件：为组件添加权限检查
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  permission?: Permission,
  permissions?: Permission[],
  role?: Role
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <ProtectedRoute permission={permission} permissions={permissions} role={role}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// 权限按钮组件
interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  permission?: Permission;
  permissions?: Permission[];
  role?: Role;
  children: React.ReactNode;
}

export function PermissionButton({ 
  permission, 
  permissions, 
  role, 
  children, 
  ...props 
}: PermissionButtonProps) {
  const { hasPermission, hasAnyPermission, hasRole } = useAuth();

  let hasAccess = true;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    hasAccess = hasAnyPermission(permissions);
  } else if (role) {
    hasAccess = hasRole(role);
  }

  if (!hasAccess) {
    return null;
  }

  return <button {...props}>{children}</button>;
}
