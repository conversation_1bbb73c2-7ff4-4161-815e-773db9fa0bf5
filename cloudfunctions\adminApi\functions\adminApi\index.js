const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// CORS响应头
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400'
};

// 主函数入口
exports.main = async (event, context) => {
  // 处理OPTIONS预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // 解析请求体
    let requestData;
    if (typeof event.body === 'string') {
      requestData = JSON.parse(event.body);
    } else {
      requestData = event.body || event;
    }

    const { action, data } = requestData;
    
    // 验证管理员权限（简化版本，实际项目中应该更严格）
    if (action !== 'adminLogin' && !await verifyAdminToken(event.headers?.authorization)) {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          error: '权限不足'
        })
      };
    }

    let result;
    
    // 根据action分发到不同的处理函数
    switch (action) {
      // 认证相关
      case 'adminLogin':
        result = await handleAdminLogin(data);
        break;
      case 'verifyToken':
        result = await handleVerifyToken(event.headers?.authorization);
        break;
      case 'adminLogout':
        result = await handleAdminLogout();
        break;

      // 仪表盘相关
      case 'getDashboardStats':
        result = await handleGetDashboardStats();
        break;
      case 'getChartData':
        result = await handleGetChartData(data);
        break;
      case 'getRecentActivities':
        result = await handleGetRecentActivities();
        break;

      // 用户管理
      case 'getUserList':
        result = await handleGetUserList(data);
        break;
      case 'getUserDetail':
        result = await handleGetUserDetail(data);
        break;
      case 'updateUserStatus':
        result = await handleUpdateUserStatus(data);
        break;
      case 'getUserStats':
        result = await handleGetUserStats();
        break;

      // 订单管理
      case 'getOrderList':
        result = await handleGetOrderList(data);
        break;
      case 'getOrderDetail':
        result = await handleGetOrderDetail(data);
        break;
      case 'updateOrderStatus':
        result = await handleUpdateOrderStatus(data);
        break;
      case 'getOrderStats':
        result = await handleGetOrderStats();
        break;

      // 评价管理
      case 'getEvaluationList':
        result = await handleGetEvaluationList(data);
        break;
      case 'approveEvaluation':
        result = await handleApproveEvaluation(data);
        break;
      case 'rejectEvaluation':
        result = await handleRejectEvaluation(data);
        break;
      case 'getEvaluationStats':
        result = await handleGetEvaluationStats();
        break;

      // 钱包管理
      case 'getTransactionList':
        result = await handleGetTransactionList(data);
        break;
      case 'approveWithdraw':
        result = await handleApproveWithdraw(data);
        break;
      case 'rejectWithdraw':
        result = await handleRejectWithdraw(data);
        break;
      case 'getWalletStats':
        result = await handleGetWalletStats();
        break;

      // 通知管理
      case 'getNotificationList':
        result = await handleGetNotificationList(data);
        break;
      case 'createNotification':
        result = await handleCreateNotification(data);
        break;
      case 'deleteNotification':
        result = await handleDeleteNotification(data);
        break;

      // 聊天管理
      case 'getChatRoomList':
        result = await handleGetChatRoomList(data);
        break;
      case 'getChatMessages':
        result = await handleGetChatMessages(data);
        break;
      case 'banUser':
        result = await handleBanUser(data);
        break;
      case 'getChatStats':
        result = await handleGetChatStats();
        break;

      default:
        result = {
          success: false,
          error: '未知的操作类型'
        };
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(result)
    };

  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message || '服务器内部错误'
      })
    };
  }
};

// 验证管理员token
async function verifyAdminToken(authorization) {
  if (!authorization) return false;

  // 需要实现真实的token验证逻辑
  return false;
}

// 管理员登录
async function handleAdminLogin(data) {
  const { username, password } = data;

  // 返回错误信息，需要实现真实的管理员认证系统
  return {
    success: false,
    error: '管理员认证系统需要实现，请联系开发人员'
  };
}

// 验证token
async function handleVerifyToken(authorization) {
  // 返回错误信息，需要实现真实的token验证系统
  return {
    success: false,
    error: 'Token验证系统需要实现，请联系开发人员'
  };
}

// 管理员登出
async function handleAdminLogout() {
  return {
    success: true,
    message: '登出成功'
  };
}

// 获取仪表盘统计数据
async function handleGetDashboardStats() {
  try {
    console.log('开始获取仪表盘统计数据...');

    // 获取今日开始时间（使用Date对象，考虑时区）
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
    const todayStart = today;

    console.log('🕐 当前时间:', now.toISOString());
    console.log('🕐 今日开始时间:', todayStart.toISOString());
    console.log('🕐 当前时区偏移:', now.getTimezoneOffset(), '分钟');

    // 获取用户统计
    console.log('获取用户统计...');
    const userStats = await db.collection('users').count();
    const activeUsers = await db.collection('users')
      .where({
        status: 'active',
        lastLoginTime: _.gte(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
      })
      .count();

    // 获取今日新用户
    console.log('🔍 查询今日新用户，todayStart:', todayStart);
    const newUsersToday = await db.collection('users')
      .where({
        createTime: _.gte(todayStart)
      })
      .count();
    console.log('📊 今日新用户查询结果:', newUsersToday);

    // 获取订单统计
    console.log('获取订单统计...');
    const orderStats = await db.collection('orders').count();
    const completedOrders = await db.collection('orders')
      .where({ status: 'completed' })
      .count();

    // 获取今日订单 - 尝试多种查询方式
    console.log('🔍 查询今日订单，todayStart:', todayStart);

    // 方式1：使用 _.gte
    const todayOrders1 = await db.collection('orders')
      .where({
        createTime: _.gte(todayStart)
      })
      .count();
    console.log('📊 方式1 (_.gte) 今日订单查询结果:', todayOrders1);

    // 方式2：使用 db.command.gte
    const todayOrders2 = await db.collection('orders')
      .where({
        createTime: db.command.gte(todayStart)
      })
      .count();
    console.log('📊 方式2 (db.command.gte) 今日订单查询结果:', todayOrders2);

    // 使用方式2的结果
    const todayOrders = todayOrders2;

    // 获取收入统计
    const revenueResult = await db.collection('orders')
      .where({ status: 'completed' })
      .field({ amount: true })
      .get();

    const totalRevenue = revenueResult.data.reduce((sum, order) => sum + (order.amount || 0), 0);

    // 获取评价统计
    const evaluationStats = await db.collection('evaluations').count();
    const avgScoreResult = await db.collection('evaluations')
      .field({ score: true })
      .get();

    const avgScore = avgScoreResult.data.length > 0
      ? avgScoreResult.data.reduce((sum, eval) => sum + eval.score, 0) / avgScoreResult.data.length
      : 0;

    const result = {
      success: true,
      data: {
        totalUsers: userStats.total || 0,
        activeUsers: activeUsers.total || 0,
        totalOrders: orderStats.total || 0,
        completedOrders: completedOrders.total || 0,
        totalRevenue: totalRevenue,
        averageScore: Number(avgScore.toFixed(1)),
        newUsersToday: newUsersToday.total || 0,
        ordersToday: todayOrders.total || 0,
      }
    };

    console.log('仪表盘统计数据获取成功:', result);
    console.log('🔍 最终返回的今日订单数:', result.data.ordersToday);
    console.log('🔍 最终返回的今日新用户数:', result.data.newUsersToday);
    return result;

  } catch (error) {
    console.error('获取仪表盘统计失败:', error);
    // 返回错误信息，不再使用模拟数据
    return {
      success: false,
      error: '获取仪表盘统计失败: ' + error.message
    };
  }
}

// 获取图表数据
async function handleGetChartData(data) {
  const { type } = data;

  // 返回错误信息，需要实现真实的图表数据查询
  return {
    success: false,
    error: '图表数据功能需要实现，请联系开发人员'
  };
}

// 获取最近活动
async function handleGetRecentActivities() {
  // 返回错误信息，需要实现真实的活动数据查询
  return {
    success: false,
    error: '最近活动功能需要实现，请联系开发人员'
  };
}

// 获取用户列表
async function handleGetUserList(data) {
  try {
    const { page = 1, limit = 20, search, status } = data || {};
    
    let query = db.collection('users');
    
    // 添加搜索条件
    if (search) {
      query = query.where({
        nickname: db.RegExp({
          regexp: search,
          options: 'i'
        })
      });
    }
    
    // 添加状态筛选
    if (status && status !== 'all') {
      query = query.where({ status });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createdAt', 'desc')
      .get();
    
    const total = await query.count();
    
    return {
      success: true,
      data: {
        users: result.data,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return {
      success: false,
      error: '获取用户列表失败'
    };
  }
}

// 获取用户详情
async function handleGetUserDetail(data) {
  try {
    const { userId } = data;
    
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空'
      };
    }

    const result = await db.collection('users').doc(userId).get();
    
    if (!result.data) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = result.data;
    
    return {
      success: true,
      data: {
        _id: user._id,
        openid: user.openid,
        nickname: user.nickName || user.nickname,
        name: user.name,
        avatar: user.avatarUrl || user.avatar,
        avatarUrl: user.avatarUrl,
        phone: user.phone,
        isVerified: user.isVerified || false,
        status: user.status || 'active',
        createTime: user.createTime,
        updateTime: user.updateTime,
        balance: user.balance || 0,
        creditScore: user.creditScore || 0,
        gameNickName: user.gameNickName,
        bio: user.bio,
        contactInfo: user.contactInfo,
        settings: user.settings,
        orderCount: user.orderCount || 0
      }
    };
  } catch (error) {
    console.error('获取用户详情失败:', error);
    return {
      success: false,
      error: '获取用户详情失败'
    };
  }
}

// 更新用户状态
async function handleUpdateUserStatus(data) {
  try {
    const { userId, status } = data;
    
    await db.collection('users').doc(userId).update({
      status,
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '用户状态更新成功'
    };
  } catch (error) {
    console.error('更新用户状态失败:', error);
    return {
      success: false,
      error: '更新用户状态失败'
    };
  }
}

// 获取用户统计
async function handleGetUserStats() {
  try {
    const totalUsers = await db.collection('users').count();
    const activeUsers = await db.collection('users').where({ status: 'active' }).count();
    const newUsersThisMonth = await db.collection('users')
      .where({
        createdAt: _.gte(new Date(new Date().getFullYear(), new Date().getMonth(), 1))
      })
      .count();
    
    return {
      success: true,
      data: {
        total: totalUsers.total,
        active: activeUsers.total,
        newThisMonth: newUsersThisMonth.total
      }
    };
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return {
      success: false,
      error: '获取用户统计失败'
    };
  }
}

// 获取订单列表
async function handleGetOrderList(data) {
  try {
    const { page = 1, limit = 20, status, search } = data || {};
    
    let query = db.collection('orders');
    
    if (status && status !== 'all') {
      query = query.where({ status });
    }
    
    if (search) {
      query = query.where({
        title: db.RegExp({
          regexp: search,
          options: 'i'
        })
      });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createdAt', 'desc')
      .get();
    
    const total = await query.count();
    
    return {
      success: true,
      data: {
        orders: result.data,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return {
      success: false,
      error: '获取订单列表失败'
    };
  }
}

// 获取订单详情
async function handleGetOrderDetail(data) {
  try {
    const { orderId } = data;
    const result = await db.collection('orders').doc(orderId).get();
    
    return {
      success: true,
      data: {
        order: result.data
      }
    };
  } catch (error) {
    console.error('获取订单详情失败:', error);
    return {
      success: false,
      error: '获取订单详情失败'
    };
  }
}

// 更新订单状态
async function handleUpdateOrderStatus(data) {
  try {
    const { orderId, status } = data;
    
    await db.collection('orders').doc(orderId).update({
      status,
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '订单状态更新成功'
    };
  } catch (error) {
    console.error('更新订单状态失败:', error);
    return {
      success: false,
      error: '更新订单状态失败'
    };
  }
}

// 获取订单统计
async function handleGetOrderStats() {
  try {
    const totalOrders = await db.collection('orders').count();
    const pendingOrders = await db.collection('orders').where({ status: 'pending' }).count();
    const completedOrders = await db.collection('orders').where({ status: 'completed' }).count();
    
    return {
      success: true,
      data: {
        total: totalOrders.total,
        pending: pendingOrders.total,
        completed: completedOrders.total
      }
    };
  } catch (error) {
    console.error('获取订单统计失败:', error);
    return {
      success: false,
      error: '获取订单统计失败'
    };
  }
}

// 获取评价列表
async function handleGetEvaluationList(data) {
  try {
    const { page = 1, limit = 20, status } = data || {};

    let query = db.collection('evaluations');

    if (status && status !== 'all') {
      query = query.where({ status });
    }

    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createTime', 'desc')
      .get();

    const total = await query.count();

    // 获取所有相关用户ID
    const userIds = new Set();
    result.data.forEach(evaluation => {
      if (evaluation.evaluatorId) userIds.add(evaluation.evaluatorId);
      if (evaluation.customerId) userIds.add(evaluation.customerId);
      if (evaluation.accepterId) userIds.add(evaluation.accepterId);
    });

    // 批量获取用户信息
    const userInfoMap = {};
    if (userIds.size > 0) {
      const userIdsArray = Array.from(userIds);
      const userResults = await Promise.allSettled(
        userIdsArray.map(userId =>
          db.collection('users').doc(userId).get()
        )
      );

      userResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.data) {
          const userId = userIdsArray[index];
          const userData = result.value.data;
          console.log('获取到用户数据:', {
            userId,
            nickName: userData.nickName,
            avatarUrl: userData.avatarUrl
          });
          userInfoMap[userId] = {
            nickName: userData.nickName || userData.nickname || '未知用户',
            avatarUrl: userData.avatarUrl || '/placeholder.svg?height=32&width=32'
          };
        } else {
          console.log('获取用户信息失败:', {
            userId: userIdsArray[index],
            error: result.reason
          });
        }
      });
    }

    // 为评价数据添加用户信息
    const evaluationsWithUserInfo = result.data.map(evaluation => {
      console.log('处理评价数据:', {
        evaluationId: evaluation._id,
        evaluatorId: evaluation.evaluatorId,
        customerId: evaluation.customerId,
        accepterId: evaluation.accepterId,
        evaluatorType: evaluation.evaluatorType
      });

      // 根据evaluatorType确定评价者和被评价者
      let evaluatorInfo, evaluatedInfo;

      if (evaluation.evaluatorType === 'customer') {
        // 客户评价接单者
        evaluatorInfo = userInfoMap[evaluation.customerId] || {
          nickName: '未知客户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
        evaluatedInfo = userInfoMap[evaluation.accepterId] || {
          nickName: '未知接单者',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
      } else if (evaluation.evaluatorType === 'accepter') {
        // 接单者评价客户
        evaluatorInfo = userInfoMap[evaluation.accepterId] || {
          nickName: '未知接单者',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
        evaluatedInfo = userInfoMap[evaluation.customerId] || {
          nickName: '未知客户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
      } else {
        // 兼容旧数据，使用evaluatorId
        evaluatorInfo = userInfoMap[evaluation.evaluatorId] || {
          nickName: '未知用户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
        evaluatedInfo = userInfoMap[evaluation.accepterId] || userInfoMap[evaluation.customerId] || {
          nickName: '未知用户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
      }

      console.log('用户信息映射结果:', {
        evaluatorInfo,
        evaluatedInfo
      });

      return {
        ...evaluation,
        evaluatorInfo,
        evaluatedInfo
      };
    });

    return {
      success: true,
      data: {
        evaluations: evaluationsWithUserInfo,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取评价列表失败:', error);
    return {
      success: false,
      error: '获取评价列表失败'
    };
  }
}

// 审核通过评价
async function handleApproveEvaluation(data) {
  try {
    const { evaluationId } = data;
    
    await db.collection('evaluations').doc(evaluationId).update({
      status: 'approved',
      reviewedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '评价审核通过'
    };
  } catch (error) {
    console.error('审核评价失败:', error);
    return {
      success: false,
      error: '审核评价失败'
    };
  }
}

// 拒绝评价
async function handleRejectEvaluation(data) {
  try {
    const { evaluationId, reason } = data;
    
    await db.collection('evaluations').doc(evaluationId).update({
      status: 'rejected',
      rejectReason: reason,
      reviewedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '评价已拒绝'
    };
  } catch (error) {
    console.error('拒绝评价失败:', error);
    return {
      success: false,
      error: '拒绝评价失败'
    };
  }
}

// 获取评价统计
async function handleGetEvaluationStats() {
  try {
    const totalEvaluations = await db.collection('evaluations').count();
    const pendingEvaluations = await db.collection('evaluations').where({ status: 'pending' }).count();
    
    const scoresResult = await db.collection('evaluations')
      .where({ status: 'approved' })
      .field({ score: true })
      .get();
    
    const averageScore = scoresResult.data.length > 0 
      ? scoresResult.data.reduce((sum, eval) => sum + eval.score, 0) / scoresResult.data.length 
      : 0;
    
    const lowScoreCount = scoresResult.data.filter(eval => eval.score <= 2).length;
    
    return {
      success: true,
      data: {
        total: totalEvaluations.total,
        pending: pendingEvaluations.total,
        averageScore: Number(averageScore.toFixed(1)),
        lowScoreCount
      }
    };
  } catch (error) {
    console.error('获取评价统计失败:', error);
    return {
      success: false,
      error: '获取评价统计失败'
    };
  }
}

// 获取交易记录列表
async function handleGetTransactionList(data) {
  try {
    const { page = 1, limit = 20, type, status } = data || {};
    
    let query = db.collection('transactions');
    
    if (type && type !== 'all') {
      query = query.where({ type });
    }
    
    if (status && status !== 'all') {
      query = query.where({ status });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createdAt', 'desc')
      .get();
    
    const total = await query.count();
    
    return {
      success: true,
      data: {
        transactions: result.data,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取交易记录失败:', error);
    return {
      success: false,
      error: '获取交易记录失败'
    };
  }
}

// 审核提现申请
async function handleApproveWithdraw(data) {
  try {
    const { transactionId } = data;
    
    await db.collection('transactions').doc(transactionId).update({
      status: 'completed',
      processedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '提现申请已通过'
    };
  } catch (error) {
    console.error('审核提现失败:', error);
    return {
      success: false,
      error: '审核提现失败'
    };
  }
}

// 拒绝提现申请
async function handleRejectWithdraw(data) {
  try {
    const { transactionId, reason } = data;
    
    await db.collection('transactions').doc(transactionId).update({
      status: 'failed',
      rejectReason: reason,
      processedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '提现申请已拒绝'
    };
  } catch (error) {
    console.error('拒绝提现失败:', error);
    return {
      success: false,
      error: '拒绝提现失败'
    };
  }
}

// 获取钱包统计
async function handleGetWalletStats() {
  try {
    const totalTransactions = await db.collection('transactions').count();
    const pendingWithdraws = await db.collection('transactions')
      .where({ type: 'withdraw', status: 'pending' })
      .count();
    
    const rechargeResult = await db.collection('transactions')
      .where({ type: 'recharge', status: 'completed' })
      .field({ amount: true })
      .get();
    
    const withdrawResult = await db.collection('transactions')
      .where({ type: 'withdraw', status: 'completed' })
      .field({ amount: true })
      .get();
    
    const totalRecharge = rechargeResult.data.reduce((sum, t) => sum + t.amount, 0);
    const totalWithdraw = withdrawResult.data.reduce((sum, t) => sum + t.amount, 0);
    
    return {
      success: true,
      data: {
        totalTransactions: totalTransactions.total,
        pendingWithdraws: pendingWithdraws.total,
        totalRecharge,
        totalWithdraw,
        balance: totalRecharge - totalWithdraw
      }
    };
  } catch (error) {
    console.error('获取钱包统计失败:', error);
    return {
      success: false,
      error: '获取钱包统计失败'
    };
  }
}

// 获取通知列表
async function handleGetNotificationList(data) {
  try {
    const { page = 1, limit = 20, type, search } = data || {};
    
    let query = db.collection('notifications');
    
    if (type && type !== 'all') {
      query = query.where({ type });
    }
    
    if (search) {
      query = query.where({
        title: db.RegExp({
          regexp: search,
          options: 'i'
        })
      });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createdAt', 'desc')
      .get();
    
    const total = await query.count();
    
    return {
      success: true,
      data: {
        notifications: result.data,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取通知列表失败:', error);
    return {
      success: false,
      error: '获取通知列表失败'
    };
  }
}

// 创建通知
async function handleCreateNotification(data) {
  try {
    const { title, content, type, isBroadcast, userId } = data;
    
    const notification = {
      title,
      content,
      type,
      userId: isBroadcast ? null : userId,
      isRead: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await db.collection('notifications').add({
      data: notification
    });
    
    return {
      success: true,
      data: {
        id: result._id,
        ...notification
      },
      message: '通知创建成功'
    };
  } catch (error) {
    console.error('创建通知失败:', error);
    return {
      success: false,
      error: '创建通知失败'
    };
  }
}

// 删除通知
async function handleDeleteNotification(data) {
  try {
    const { notificationId } = data;
    
    await db.collection('notifications').doc(notificationId).remove();
    
    return {
      success: true,
      message: '通知删除成功'
    };
  } catch (error) {
    console.error('删除通知失败:', error);
    return {
      success: false,
      error: '删除通知失败'
    };
  }
}

// 获取聊天室列表
async function handleGetChatRoomList(data) {
  try {
    const { page = 1, limit = 100, status, search } = data || {};
    
    let query = db.collection('chatRooms');
    
    if (status && status !== 'all' && status !== 'active') {
      query = query.where({ status });
    }
    
    if (search) {
      // 搜索订单标题或订单号
      query = query.where({
        $or: [
          {
            'orderInfo.title': db.RegExp({
              regexp: search,
              options: 'i'
            })
          },
          {
            orderNo: db.RegExp({
              regexp: search,
              options: 'i'
            })
          }
        ]
      });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('updateTime', 'desc')
      .get();
    
    const total = await query.count();
    
    console.log('聊天室查询结果:', {
      count: result.data.length,
      total: total.total,
      sample: result.data[0]
    });
    
    return {
      success: true,
      data: {
        chatRooms: result.data,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取聊天室列表失败:', error);
    
    // 返回错误信息，不再使用模拟数据
    return {
      success: false,
      error: '获取聊天室列表失败: ' + error.message
    };
  }
}

// 获取聊天消息
async function handleGetChatMessages(data) {
  try {
    const { roomId, page = 1, limit = 50 } = data;
    
    console.log('获取聊天消息，roomId:', roomId, 'page:', page, 'limit:', limit);
    
    if (!roomId) {
      console.error('roomId 参数缺失');
      return {
        success: false,
        error: 'roomId 参数缺失'
      };
    }
    
    // 从 messages 表中获取聊天消息，使用 chatRoomId 字段匹配
    console.log('开始查询 messages 表...');
    const result = await db.collection('messages')
      .where({ chatRoomId: roomId })
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createTime', 'asc')
      .get();
    
    console.log('聊天消息查询结果:', {
      count: result.data.length,
      roomId: roomId,
      sample: result.data[0] || '无数据'
    });
    
    // 如果没有找到消息，返回空数组而不是错误
    if (!result.data || result.data.length === 0) {
      console.log('该聊天室暂无消息记录');
      return {
        success: true,
        data: {
          messages: []
        }
      };
    }
    
    // 转换数据格式以匹配前端期望的结构
    const transformedMessages = result.data.map(msg => {
      console.log('转换消息:', msg._id, msg.content);
      return {
        id: msg._id,
        roomId: msg.chatRoomId,
        senderId: msg.senderId,
        content: msg.content,
        type: msg.type || 'text',
        timestamp: msg.createTime,
        isRead: true, // 假设消息都已读
        senderInfo: msg.senderInfo || {
          nickName: '未知用户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        }
      };
    });
    
    console.log('转换后的消息数量:', transformedMessages.length);
    
    return {
      success: true,
      data: {
        messages: transformedMessages
      }
    };
  } catch (error) {
    console.error('获取聊天消息详细错误:', error);
    console.error('错误堆栈:', error.stack);
    return {
      success: false,
      error: `获取聊天消息失败: ${error.message}`
    };
  }
}

// 禁言用户
async function handleBanUser(data) {
  try {
    const { userId } = data;
    
    await db.collection('users').doc(userId).update({
      isBanned: true,
      bannedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '用户已被禁言'
    };
  } catch (error) {
    console.error('禁言用户失败:', error);
    return {
      success: false,
      error: '禁言用户失败'
    };
  }
}

// 获取聊天统计
async function handleGetChatStats() {
  try {
    const totalRooms = await db.collection('chatRooms').count();
    const activeRooms = await db.collection('chatRooms').where({ status: 'active' }).count();
    const totalMessages = await db.collection('chatMessages').count();
    
    return {
      success: true,
      data: {
        totalRooms: totalRooms.total,
        activeRooms: activeRooms.total,
        totalMessages: totalMessages.total
      }
    };
  } catch (error) {
    console.error('获取聊天统计失败:', error);
    return {
      success: false,
      error: '获取聊天统计失败'
    };
  }
}
