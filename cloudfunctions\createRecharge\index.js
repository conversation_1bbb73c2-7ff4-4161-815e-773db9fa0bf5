// 创建充值订单云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { amount, paymentMethod = 'wechat' } = event;

  try {
    // 验证必填字段
    if (!amount || amount <= 0) {
      return {
        success: false,
        error: '充值金额必须大于0'
      };
    }

    // 验证充值金额范围
    if (amount < 1 || amount > 10000) {
      return {
        success: false,
        error: '充值金额必须在1-10000元之间'
      };
    }

    // 查找用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 生成充值订单号
    const rechargeNo = generateRechargeNo();

    // 创建充值记录
    const rechargeData = {
      rechargeNo,
      userId: user._id,
      amount: parseFloat(amount),
      paymentMethod,
      status: 'pending', // pending, paid, failed, cancelled
      createTime: new Date(),
      updateTime: new Date(),
      payTime: null,
      transactionId: null // 微信支付交易号
    };

    const rechargeResult = await db.collection('recharges').add({
      data: rechargeData
    });

    // 创建交易记录
    await db.collection('transactions').add({
      data: {
        userId: user._id,
        rechargeId: rechargeResult._id,
        type: 'recharge',
        amount: parseFloat(amount),
        status: 'pending',
        description: `余额充值 - ${rechargeNo}`,
        createTime: new Date()
      }
    });

    // 如果是微信支付，创建支付订单
    let paymentData = null;
    if (paymentMethod === 'wechat') {
      try {
        // 调用微信支付统一下单接口
        paymentData = await createWechatPayment({
          openid: wxContext.OPENID,
          amount: parseFloat(amount),
          orderNo: rechargeNo,
          description: `三角洲陪玩充值-${amount}元`
        });
      } catch (payError) {
        console.error('创建微信支付订单失败:', payError);
        // 支付订单创建失败，但充值记录已创建，可以稍后重试
      }
    }

    return {
      success: true,
      data: {
        rechargeId: rechargeResult._id,
        rechargeNo,
        amount: parseFloat(amount),
        paymentData
      }
    };
  } catch (error) {
    console.error('创建充值订单失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 生成充值订单号
function generateRechargeNo() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `RC${year}${month}${day}${hour}${minute}${second}${random}`;
}

// 创建微信支付订单
async function createWechatPayment({ openid, amount, orderNo, description }) {
  // 注意：这里需要配置微信支付相关参数
  // 在实际项目中需要：
  // 1. 在云开发控制台配置微信支付
  // 2. 设置商户号、API密钥等
  // 3. 调用云开发的支付API
  
  try {
    // 使用云开发的支付API
    const result = await cloud.cloudPay.unifiedOrder({
      spbillCreateIp: '127.0.0.1', // 终端IP
      subMchId: '', // 子商户号，如果有的话
      totalFee: Math.round(amount * 100), // 金额，单位为分
      outTradeNo: orderNo, // 商户订单号
      body: description, // 商品描述
      openid: openid, // 用户openid
      tradeType: 'JSAPI' // 交易类型
    });

    return {
      timeStamp: result.timeStamp,
      nonceStr: result.nonceStr,
      package: result.package,
      signType: result.signType,
      paySign: result.paySign
    };
  } catch (error) {
    console.error('微信支付下单失败:', error);
    throw error;
  }
}
