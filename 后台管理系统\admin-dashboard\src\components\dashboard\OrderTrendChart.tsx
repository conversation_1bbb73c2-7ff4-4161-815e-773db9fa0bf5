import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface OrderTrendData {
  date: string;
  count: number;
  amount: number;
}

interface OrderTrendChartProps {
  data: OrderTrendData[];
}

export default function OrderTrendChart({ data }: OrderTrendChartProps) {
  console.log('📊 OrderTrendChart 接收到的数据:', data);

  const formatDate = (dateStr: string) => {
    // 如果已经是 M/d 格式，直接返回
    if (dateStr.includes('/') && !dateStr.includes('-')) {
      return dateStr;
    }
    // 否则尝试解析为日期
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  // 如果没有数据，显示空状态
  if (!data || data.length === 0) {
    return (
      <div className="h-80 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <div className="text-lg mb-2">📊</div>
          <div>暂无订单趋势数据</div>
        </div>
      </div>
    );
  }

  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'amount') {
      return [`¥${value.toLocaleString()}`, '订单金额'];
    }
    return [value, '订单数量'];
  };

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="date" 
            tickFormatter={formatDate}
            stroke="#666"
            fontSize={12}
          />
          <YAxis 
            yAxisId="count"
            orientation="left"
            stroke="#666"
            fontSize={12}
          />
          <YAxis 
            yAxisId="amount"
            orientation="right"
            stroke="#666"
            fontSize={12}
            tickFormatter={(value) => `¥${((value || 0) / 1000).toFixed(0)}k`}
          />
          <Tooltip 
            formatter={formatTooltipValue}
            labelFormatter={(label) => `日期: ${formatDate(label)}`}
            contentStyle={{
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            }}
          />
          <Line
            yAxisId="count"
            type="monotone"
            dataKey="count"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
          />
          <Line
            yAxisId="amount"
            type="monotone"
            dataKey="amount"
            stroke="#10b981"
            strokeWidth={2}
            dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}