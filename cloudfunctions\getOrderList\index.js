// 获取订单列表云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 创建示例数据
 */
async function createSampleData() {
  try {
    return { status: 'created', count: 0 };
  } catch (error) {
    console.error('创建示例数据失败:', error);
    return { status: 'error', error: error.message };
  }
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { page = 1, pageSize = 10, status = '', role = 'customer' } = event;

  try {
    // 查找用户
    let userResult;
    let userId;

    try {
      userResult = await db.collection('users').where({
        openid: wxContext.OPENID
      }).get();

      if (userResult.data.length === 0) {
        return {
          success: false,
          error: '用户不存在'
        };
      }

      userId = userResult.data[0]._id;
    } catch (userError) {
      // 如果用户集合不存在，先创建示例数据
      if (userError.errCode === -502005 || userError.errCode === -502002) {
        console.log('用户集合不存在，创建示例数据...');
        await createSampleData();

        // 重新尝试查找用户
        try {
          userResult = await db.collection('users').where({
            openid: wxContext.OPENID
          }).get();

          if (userResult.data.length === 0) {
            // 如果还是没有当前用户，创建一个临时用户ID
            userId = 'temp_user_' + Date.now();
          } else {
            userId = userResult.data[0]._id;
          }
        } catch (retryError) {
          userId = 'temp_user_' + Date.now();
        }
      } else {
        throw userError;
      }
    }

    // 构建查询条件
    let whereCondition = {};

    // 先设置角色条件
    if (role === 'customer') {
      whereCondition.customerId = userId;
    } else if (role === 'companion') {
      whereCondition.accepterId = userId;
    } else if (role === 'all') {
      // 查询用户作为客户或接单者的所有订单
      whereCondition = _.or([
        { customerId: userId },
        { accepterId: userId }
      ]);
    } else {
      // 默认查询用户作为客户或接单者的所有订单
      whereCondition = _.or([
        { customerId: userId },
        { accepterId: userId }
      ]);
    }

    // 状态筛选
    if (status) {
      // 如果已经有复杂的查询条件（如_.or），需要用_.and组合
      if (whereCondition.$or) {
        whereCondition = _.and([
          whereCondition,
          { status: status }
        ]);
      } else {
        whereCondition.status = status;
      }
    }

    // 特殊处理：如果是companion角色查询accepted状态，直接构建简单条件
    if (role === 'companion' && status === 'accepted') {
      whereCondition = {
        accepterId: userId,
        status: 'accepted'
      };
    }

    // 特殊处理：如果是all角色查询特定状态，确保状态筛选正确
    if (role === 'all' && status) {
      whereCondition = _.and([
        _.or([
          { customerId: userId },
          { accepterId: userId }
        ]),
        { status: status }
      ]);
    }

    console.log('📋 [getOrderList] 查询条件:', JSON.stringify(whereCondition, null, 2));
    console.log('📋 [getOrderList] 参数:', { page, pageSize, status, role, userId });
    console.log('🎯 [getOrderList] 期望查询状态:', status);

    // 查询订单
    let orderResult;
    try {
      orderResult = await db.collection('orders')
        .where(whereCondition)
        .orderBy('createTime', 'desc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get();
    } catch (orderError) {
      // 如果订单集合不存在，先创建示例数据
      if (orderError.errCode === -502005 || orderError.errCode === -502002) {
        console.log('订单集合不存在，创建示例数据...');
        await createSampleData();

        // 重新尝试查询
        orderResult = await db.collection('orders')
          .where(whereCondition)
          .orderBy('createTime', 'desc')
          .skip((page - 1) * pageSize)
          .limit(pageSize)
          .get();
      } else {
        throw orderError;
      }
    }

    // 获取相关的用户信息
    const customerIds = [...new Set(orderResult.data.map(order => order.customerId))];
    const accepterIds = [...new Set(orderResult.data.map(order => order.accepterId).filter(id => id))];
    const allUserIds = [...new Set([...customerIds, ...accepterIds])];

    // 查询用户信息
    let usersResult = { data: [] };
    if (allUserIds.length > 0) {
      usersResult = await db.collection('users')
        .where({
          _id: _.in(allUserIds)
        })
        .field({
          _id: true,
          nickName: true,
          avatarUrl: true,
          openid: true
        })
        .get();
    }

    // 创建用户映射
    const userMap = {};
    usersResult.data.forEach(user => {
      userMap[user._id] = user;
    });

    // 检查查询结果的状态分布
    const statusCounts = {};
    orderResult.data.forEach(order => {
      statusCounts[order.status] = (statusCounts[order.status] || 0) + 1;
    });
    console.log('📊 [getOrderList] 查询结果状态分布:', statusCounts);
    console.log('🎯 [getOrderList] 期望状态:', status);

    // 检查是否有状态不匹配的订单
    if (status) {
      const mismatchedOrders = orderResult.data.filter(order => order.status !== status);
      if (mismatchedOrders.length > 0) {
        console.warn('⚠️ [getOrderList] 发现状态不匹配的订单:', mismatchedOrders.map(o => ({
          id: o._id,
          title: o.title,
          expectedStatus: status,
          actualStatus: o.status
        })));
      }
    }

    // 合并数据
    const orderList = orderResult.data.map(order => {
      const customer = userMap[order.customerId] || {};
      const accepter = order.accepterId ? userMap[order.accepterId] : null;

      return {
        ...order,
        customerInfo: {
          _id: customer._id,
          nickName: customer.nickName || '未知用户',
          avatarUrl: customer.avatarUrl || '',
          openid: customer.openid || ''
        },
        accepterInfo: accepter ? {
          _id: accepter._id,
          nickName: accepter.nickName || '未知用户',
          avatarUrl: accepter.avatarUrl || '',
        } : null
      };
    });

    return {
      success: true,
      data: {
        list: orderList,
        page,
        pageSize,
        total: orderResult.data.length
      }
    };
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
