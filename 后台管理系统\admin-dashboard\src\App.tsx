import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/AuthContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import Layout from '@/components/layout/Layout';
import LoginPage from '@/pages/auth/LoginPage';
import DashboardPage from '@/pages/dashboard/DashboardPage';
import UsersPage from '@/pages/users/UsersPage';
import OrdersPage from '@/pages/orders/OrdersPage';
import ChatPage from '@/pages/chat/ChatPage';
import WalletPage from '@/pages/wallet/WalletPage';
import NotificationsPage from '@/pages/notifications/NotificationsPage';
import EvaluationsPage from '@/pages/evaluations/EvaluationsPage';
import OperationLogsPage from '@/pages/system/OperationLogsPage';
import ApiTestPage from '@/pages/debug/ApiTestPage';

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="admin-ui-theme">
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              {/* 登录页面 */}
              <Route path="/login" element={<LoginPage />} />

              {/* 受保护的管理页面 */}
              <Route
                path="/*"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <Routes>
                        <Route path="/" element={<Navigate to="/dashboard" replace />} />
                        <Route
                          path="/dashboard"
                          element={
                            <ProtectedRoute permission="dashboard.view">
                              <DashboardPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/users"
                          element={
                            <ProtectedRoute permission="users.view">
                              <UsersPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/orders"
                          element={
                            <ProtectedRoute permission="orders.view">
                              <OrdersPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/chat"
                          element={
                            <ProtectedRoute permission="chat.view">
                              <ChatPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/wallet"
                          element={
                            <ProtectedRoute permission="wallet.view">
                              <WalletPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/notifications"
                          element={
                            <ProtectedRoute permission="notifications.view">
                              <NotificationsPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/evaluations"
                          element={
                            <ProtectedRoute permission="evaluations.view">
                              <EvaluationsPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/system/logs"
                          element={
                            <ProtectedRoute permission="system.logs">
                              <OperationLogsPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route path="/debug/api" element={<ApiTestPage />} />
                      </Routes>
                    </Layout>
                  </ProtectedRoute>
                }
              />
            </Routes>
          </div>
          <Toaster />
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;