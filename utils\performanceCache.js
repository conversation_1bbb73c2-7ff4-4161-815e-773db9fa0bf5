/**
 * 性能缓存管理工具
 * 用于优化消息发送和聊天相关的性能
 */

class PerformanceCache {
  constructor() {
    this.permissionCache = new Map(); // 权限缓存
    this.messageCache = new Map(); // 消息缓存
    this.userInfoCache = new Map(); // 用户信息缓存
    this.chatRoomCache = new Map(); // 聊天室信息缓存
    
    // 缓存配置
    this.config = {
      permissionTTL: 5 * 60 * 1000, // 权限缓存5分钟
      messageTTL: 2 * 60 * 1000, // 消息缓存2分钟
      userInfoTTL: 10 * 60 * 1000, // 用户信息缓存10分钟
      chatRoomTTL: 5 * 60 * 1000, // 聊天室信息缓存5分钟
      maxCacheSize: 1000 // 最大缓存条目数
    };
    
    console.log('🚀 [性能缓存] 缓存管理器初始化完成');
  }

  /**
   * 通用缓存方法
   */
  _setCache(cache, key, data, ttl) {
    try {
      // 限制缓存大小
      if (cache.size >= this.config.maxCacheSize) {
        const firstKey = cache.keys().next().value;
        cache.delete(firstKey);
      }

      cache.set(key, {
        data: data,
        timestamp: Date.now(),
        ttl: ttl
      });

      console.log(`📦 [缓存] 数据已缓存: ${key}`);
    } catch (error) {
      console.error('📦 [缓存] 设置缓存失败:', error);
    }
  }

  _getCache(cache, key) {
    try {
      const cached = cache.get(key);
      if (!cached) return null;

      const { data, timestamp, ttl } = cached;
      
      // 检查是否过期
      if (Date.now() - timestamp > ttl) {
        cache.delete(key);
        console.log(`📦 [缓存] 缓存已过期: ${key}`);
        return null;
      }

      console.log(`📦 [缓存] 命中缓存: ${key}`);
      return data;
    } catch (error) {
      console.error('📦 [缓存] 获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 权限缓存
   */
  setPermissionCache(chatRoomId, userId, permissionData) {
    const key = `${chatRoomId}_${userId}`;
    this._setCache(this.permissionCache, key, permissionData, this.config.permissionTTL);
  }

  getPermissionCache(chatRoomId, userId) {
    const key = `${chatRoomId}_${userId}`;
    return this._getCache(this.permissionCache, key);
  }

  /**
   * 消息缓存
   */
  setMessageCache(chatRoomId, page, messages) {
    const key = `${chatRoomId}_page_${page}`;
    this._setCache(this.messageCache, key, messages, this.config.messageTTL);
  }

  getMessageCache(chatRoomId, page) {
    const key = `${chatRoomId}_page_${page}`;
    return this._getCache(this.messageCache, key);
  }

  /**
   * 用户信息缓存
   */
  setUserInfoCache(userId, userInfo) {
    this._setCache(this.userInfoCache, userId, userInfo, this.config.userInfoTTL);
  }

  getUserInfoCache(userId) {
    return this._getCache(this.userInfoCache, userId);
  }

  /**
   * 聊天室信息缓存
   */
  setChatRoomCache(chatRoomId, chatRoomInfo) {
    this._setCache(this.chatRoomCache, chatRoomId, chatRoomInfo, this.config.chatRoomTTL);
  }

  getChatRoomCache(chatRoomId) {
    return this._getCache(this.chatRoomCache, chatRoomId);
  }

  /**
   * 清除特定缓存
   */
  clearPermissionCache(chatRoomId, userId) {
    const key = `${chatRoomId}_${userId}`;
    this.permissionCache.delete(key);
    console.log(`🗑️ [缓存] 已清除权限缓存: ${key}`);
  }

  clearMessageCache(chatRoomId) {
    // 清除该聊天室的所有消息缓存
    for (const key of this.messageCache.keys()) {
      if (key.startsWith(`${chatRoomId}_page_`)) {
        this.messageCache.delete(key);
      }
    }
    console.log(`🗑️ [缓存] 已清除消息缓存: ${chatRoomId}`);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.permissionCache.clear();
    this.messageCache.clear();
    this.userInfoCache.clear();
    this.chatRoomCache.clear();
    console.log('🗑️ [缓存] 已清除所有缓存');
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      permissionCache: this.permissionCache.size,
      messageCache: this.messageCache.size,
      userInfoCache: this.userInfoCache.size,
      chatRoomCache: this.chatRoomCache.size,
      total: this.permissionCache.size + this.messageCache.size + 
             this.userInfoCache.size + this.chatRoomCache.size
    };
  }

  /**
   * 预热缓存 - 提前加载常用数据
   */
  async preloadCache(chatRoomId, userId) {
    console.log('🔥 [缓存预热] 开始预热缓存');
    
    try {
      // 这里可以添加预加载逻辑
      // 比如预加载用户权限、聊天室信息等
      console.log('🔥 [缓存预热] 预热完成');
    } catch (error) {
      console.error('🔥 [缓存预热] 预热失败:', error);
    }
  }
}

// 创建全局缓存实例
const performanceCache = new PerformanceCache();

// 导出缓存实例
module.exports = performanceCache;
